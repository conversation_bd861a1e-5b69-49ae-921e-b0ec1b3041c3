package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComLogoffAndX.class */
class SmbComLogoffAndX extends AndXServerMessageBlock {
    SmbComLogoffAndX(ServerMessageBlock andx) {
        super(andx);
        this.command = (byte) 116;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComLogoffAndX[" + super.toString() + "]");
    }
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">
    <span class="pl_type">[플러그인 전용]</span>            
    <h3 class="title">DEXT5 Upload :: SetDefaultSavePath</h3>
    <p class="ttl">void SetDefaultSavePath(path, uploadName)</p>
    <p class="txt">
        다운로드 경로를 설정합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
       없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">path</span>&nbsp;&nbsp;다운로드 경로(물리적경로), 빈 string 값인 경우 다운로드 경로가 삭제 됩니다.<br/>
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;설정할 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="upload1/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function setDefaultSavePath() { 
            // c:\temp\folder1 이라는 폴더에 파일이 저장
            DEXT5UPLOAD.SetDefaultSavePath('c:\\temp\\folder1', 'upload1');

            // 빈 string 값으로 설정하는 경우 경로설정 값이 삭제되고 파일 다운로드 시 경로를 선택하기 위한 dialog 창이 나타납니다.
            DEXT5UPLOAD.SetDefaultSavePath('', 'upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


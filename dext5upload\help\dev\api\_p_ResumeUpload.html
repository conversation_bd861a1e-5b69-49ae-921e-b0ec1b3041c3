﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">
    <span class="pl_type">[플러그인 전용]</span>               
    <h3 class="title">DEXT5 Upload :: Config :: ResumeUpload</h3>
    <p class="ttl">config.ResumeUpload</p>
    <p class="txt">
        업로드 이어올리기를 설정합니다.<br/>
        파일을 업로드 중에 네트워크가 단절되거나 사용자 요청에 의해 중지 되어도 이어올리기가 가능합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0" 이고, "1"로 설정시 이어올리기가 가능합니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 이어 올리기를 설정합니다.
        DEXT5UPLOAD.config.ResumeUpload = '1';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


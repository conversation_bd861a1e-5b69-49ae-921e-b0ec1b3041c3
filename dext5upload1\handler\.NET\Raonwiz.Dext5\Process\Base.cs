﻿using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Web;
using Ionic.Zip;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;
using Raonwiz.Dext5.Security;

namespace Raonwiz.Dext5.Process
{
	// Token: 0x02000003 RID: 3
	public abstract class Base
	{
		// Token: 0x06000009 RID: 9 RVA: 0x00002E18 File Offset: 0x00001018
		public Base(HttpContext context)
		{
			this.hContext = context;
		}

		// Token: 0x0600000A RID: 10
		public abstract object Run(UploadHandlerBeforeInitializeDelegate UploadBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx UploadBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx UploadCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError);

		// Token: 0x0600000B RID: 11 RVA: 0x00002F29 File Offset: 0x00001129
		public void SetHtml5SliceAppend(bool isSliceAppend)
		{
			this._b_html5SliceAppend = isSliceAppend;
		}

		// Token: 0x0600000C RID: 12 RVA: 0x00002F32 File Offset: 0x00001132
		public void SetDextParam(ParamEntity dextParam)
		{
			this._entity_dextParam = dextParam;
		}

		// Token: 0x0600000D RID: 13 RVA: 0x00002F3B File Offset: 0x0000113B
		public void SetDebugMode(bool isDebug, string debugFilePath)
		{
			this._b_IsDebug = isDebug;
			this._str_DebugFilePath = debugFilePath;
		}

		// Token: 0x0600000E RID: 14 RVA: 0x00002F4B File Offset: 0x0000114B
		public void SetExternalWebFileDirectDownload(bool isExternalWebFileDirectDownload)
		{
			this._b_externalWebFileDirectDownload = isExternalWebFileDirectDownload;
		}

		// Token: 0x0600000F RID: 15 RVA: 0x00002F54 File Offset: 0x00001154
		public void SetUseExternalDownload(bool isUseExternalDownload)
		{
			this._b_useExternalDownload = isUseExternalDownload;
		}

		// Token: 0x06000010 RID: 16 RVA: 0x00002F60 File Offset: 0x00001160
		public void SetEnableZip64(string strEnableZip64)
		{
			string text = strEnableZip64.ToLower();
			if (text.Equals("default"))
			{
				this._enableZip64 = Zip64Option.Default;
				return;
			}
			if (text.Equals("asnecessary"))
			{
				this._enableZip64 = Zip64Option.AsNecessary;
				return;
			}
			if (text.Equals("never"))
			{
				this._enableZip64 = Zip64Option.Default;
				return;
			}
			if (text.Equals("always"))
			{
				this._enableZip64 = Zip64Option.Always;
				return;
			}
			this._enableZip64 = Zip64Option.Default;
		}

		// Token: 0x06000011 RID: 17 RVA: 0x00002FD0 File Offset: 0x000011D0
		public void SetNetworkCredentials(string sMethod, string userID, string userPassword, string domain)
		{
			if (!string.IsNullOrEmpty(userID))
			{
				if (domain == "")
				{
					domain = Environment.MachineName;
				}
				this._str_Credentials_Method = sMethod.ToLower();
				this._str_Credentials_UserID = userID;
				this._str_Credentials_UserPassword = userPassword;
				this._str_Credentials_Domain = domain;
				if (this._str_Credentials_Method == "impersonate")
				{
					this._impersonator = new Dext5Impersonator(this._str_Credentials_UserID, this._str_Credentials_Domain, this._str_Credentials_UserPassword);
					return;
				}
				this._uncAccessWithCredentials = new Dext5UNCAccessWithCredentials();
			}
		}

		// Token: 0x06000012 RID: 18 RVA: 0x00003057 File Offset: 0x00001257
		public void ImpersonatorDispose()
		{
			if (this._impersonator != null)
			{
				this._impersonator.Dispose();
			}
			if (this._uncAccessWithCredentials != null)
			{
				this._uncAccessWithCredentials.Dispose();
			}
		}

		// Token: 0x06000013 RID: 19 RVA: 0x00003080 File Offset: 0x00001280
		protected void ClientResponse(string prefixMessage, string responseMessage, string debugMessage = "")
		{
			this.hContext.Response.Clear();
			this.hContext.Response.Write(prefixMessage + Dext5Parameter.MakeParameter(responseMessage));
			if (this._b_IsDebug)
			{
				if (string.IsNullOrEmpty(debugMessage))
				{
					debugMessage = responseMessage;
				}
				StackTrace stackTrace = new StackTrace();
				if (stackTrace.FrameCount > 1)
				{
					MethodBase method = stackTrace.GetFrame(1).GetMethod();
					debugMessage = "[" + method.ReflectedType.FullName + "] " + debugMessage;
				}
				LogUtil.DextDebug(prefixMessage + debugMessage, this._str_DebugFilePath);
			}
		}

		// Token: 0x06000014 RID: 20 RVA: 0x00003118 File Offset: 0x00001318
		protected bool CheckCaller(string mode)
		{
			bool result = false;
			if (mode == "html5")
			{
				string text = string.Empty;
				string text2 = string.Empty;
				try
				{
					text = this.hContext.Request.UrlReferrer.LocalPath;
					text2 = this.hContext.Request.UserAgent;
				}
				catch
				{
					try
					{
						text2 = this.hContext.Request.UserAgent;
						if (text2.IndexOf("Mozilla") > -1 || text2.IndexOf("AppleWebKit") > -1)
						{
							text = "hybird";
						}
					}
					catch
					{
					}
				}
				if (!string.IsNullOrEmpty(text))
				{
					if (text.ToLower().IndexOf("dext5upload.uploadchunk.min.js") > -1)
					{
						result = true;
					}
					else if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(text2))
					{
						result = true;
					}
				}
			}
			else if (mode == "html4")
			{
				string value = string.Empty;
				string text3 = string.Empty;
				try
				{
					value = this.hContext.Request.UrlReferrer.LocalPath;
					text3 = this.hContext.Request.UserAgent;
				}
				catch
				{
					try
					{
						text3 = this.hContext.Request.UserAgent;
						if (text3.IndexOf("Mozilla") > -1 || text3.IndexOf("AppleWebKit") > -1)
						{
							value = "hybird";
						}
					}
					catch
					{
					}
				}
				if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(text3))
				{
					result = true;
				}
			}
			else if (mode == "html4SWF")
			{
				string text4 = string.Empty;
				try
				{
					text4 = this.hContext.Request.UserAgent;
				}
				catch
				{
				}
				if (!string.IsNullOrEmpty(text4) && (text4.IndexOf("Shockwave Flash") > -1 || text4.IndexOf("Mozilla") > -1 || text4.IndexOf("AppleWebKit") > -1))
				{
					result = true;
				}
			}
			else if (mode == "ieplugin")
			{
				string userAgent = this.hContext.Request.UserAgent;
				if (!string.IsNullOrEmpty(userAgent) && userAgent.ToLower().IndexOf("dext5") > -1)
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x06000015 RID: 21 RVA: 0x00003370 File Offset: 0x00001570
		protected bool CheckFileExtension(string fileWhiteList, string fileBlackList, string allowExtensionSpecialSymbol, string pFileName = null)
		{
			string text = string.Empty;
			if (pFileName != null)
			{
				text = pFileName;
			}
			else
			{
				text = this._entity_dextParam.fileName;
				text = text.Normalize(NormalizationForm.FormC);
			}
			if (!string.IsNullOrEmpty(text))
			{
				text = text.ToLower();
				string text2 = "";
				int num = text.LastIndexOf('.');
				if (num > -1)
				{
					string text3 = ";|%00|%zz|[^0-9a-zA-Z";
					if (!string.IsNullOrEmpty(allowExtensionSpecialSymbol))
					{
						string[] array = allowExtensionSpecialSymbol.Split(new char[]
						{
							','
						});
						int num2 = array.Length;
						for (int i = 0; i < num2; i++)
						{
							text3 += array[i];
						}
					}
					text3 += "]";
					text2 = text.Substring(text.LastIndexOf('.') + 1);
					text2 = Regex.Replace(text2, text3, "");
				}
				string checkFileExtension = this._entity_dextParam.checkFileExtension;
				string a = string.Empty;
				if (!string.IsNullOrEmpty(fileWhiteList) || !string.IsNullOrEmpty(fileBlackList))
				{
					if (!string.IsNullOrEmpty(fileWhiteList))
					{
						a = "1";
					}
					else if (!string.IsNullOrEmpty(fileBlackList))
					{
						a = "0";
					}
				}
				else if (!string.IsNullOrEmpty(checkFileExtension))
				{
					string[] array2 = checkFileExtension.Split(new char[]
					{
						'|'
					});
					if (array2[0] == "0")
					{
						a = "0";
						fileBlackList = array2[1];
					}
					else if (array2[0] == "1")
					{
						a = "1";
						fileWhiteList = array2[1];
					}
				}
				if (a == "0")
				{
					if (!string.IsNullOrEmpty(fileBlackList))
					{
						string[] array3 = fileBlackList.ToLower().Split(new char[]
						{
							','
						});
						if (array3.Length > 0)
						{
							int num3 = Array.IndexOf<string>(array3, text2);
							if (num3 > -1)
							{
								return false;
							}
						}
					}
				}
				else if (a == "1" && !string.IsNullOrEmpty(fileWhiteList))
				{
					string[] array3 = fileWhiteList.ToLower().Split(new char[]
					{
						','
					});
					if (array3.Length > 0)
					{
						int num4 = Array.IndexOf<string>(array3, text2);
						if (num4 < 0)
						{
							return false;
						}
					}
				}
				return true;
			}
			return false;
		}

		// Token: 0x06000016 RID: 22 RVA: 0x00003578 File Offset: 0x00001778
		protected bool CheckBlackWord(string[] fileBlackWordList, string pFileFullPath = null)
		{
			bool result = true;
			if (fileBlackWordList == null)
			{
				return true;
			}
			string text = string.Empty;
			if (pFileFullPath != null)
			{
				if (pFileFullPath.LastIndexOf(this.m_PathChar) > -1)
				{
					text = pFileFullPath.Substring(pFileFullPath.LastIndexOf(this.m_PathChar));
				}
				else
				{
					text = pFileFullPath;
				}
			}
			else
			{
				text = this._entity_dextParam.fileName;
				text = text.Normalize(NormalizationForm.FormC);
			}
			foreach (string value in fileBlackWordList)
			{
				if (text.IndexOf(value) > -1)
				{
					result = false;
					break;
				}
			}
			return result;
		}

		// Token: 0x06000017 RID: 23 RVA: 0x000035FC File Offset: 0x000017FC
		protected string GetTempPath(string tempPath)
		{
			string text = tempPath;
			if (string.IsNullOrEmpty(text))
			{
				text = Path.Combine(HttpContext.Current.Server.MapPath("./"), "temp");
			}
			this.CreateFolder(text);
			return text;
		}

		// Token: 0x06000018 RID: 24 RVA: 0x0000363C File Offset: 0x0000183C
		protected string GetTempFileFolder(string tempPath, string directoryname)
		{
			string text = this.GetTempPath(tempPath) + this.m_PathChar + directoryname;
			this.CreateFolder(text);
			return text;
		}

		// Token: 0x06000019 RID: 25 RVA: 0x0000366C File Offset: 0x0000186C
		protected void CreateFolder(string directorypath)
		{
			if (this._str_Credentials_Method == "impersonate")
			{
				if (!Directory.Exists(directorypath))
				{
					Directory.CreateDirectory(directorypath);
					return;
				}
			}
			else if (this._str_Credentials_Method == "netuse")
			{
				try
				{
					if (!Directory.Exists(directorypath))
					{
						Directory.CreateDirectory(directorypath);
					}
				}
				catch
				{
					this._uncAccessWithCredentials.NetUseWithCredentials(directorypath, this._str_Credentials_UserID, this._str_Credentials_Domain, this._str_Credentials_UserPassword);
				}
			}
		}

		// Token: 0x0600001A RID: 26 RVA: 0x000036F0 File Offset: 0x000018F0
		protected string GetFileLocationInfo(string tempPath, string physicalPath, string virtualPath, bool bErrorWrite, string pFileName = null, string pGUID = null, string pFolderNameRule = null, string pFileNameRule = null, string pFIleNameRuleEx = null, string pD5Prefix = null, string pD5Subfix = null)
		{
			string text = this._entity_dextParam.fileName;
			if (string.IsNullOrEmpty(text))
			{
				text = pFileName;
			}
			else
			{
				text = text.Normalize(NormalizationForm.FormC);
			}
			string text2 = this._entity_dextParam.GUID;
			if (string.IsNullOrEmpty(text2))
			{
				text2 = pGUID;
			}
			string text3 = this._entity_dextParam.folderNameRule;
			if (string.IsNullOrEmpty(text3))
			{
				text3 = pFolderNameRule;
			}
			string text4 = this._entity_dextParam.fileNameRule;
			if (string.IsNullOrEmpty(text4))
			{
				text4 = pFileNameRule;
			}
			string text5 = this._entity_dextParam.fileNameRuleEx;
			if (string.IsNullOrEmpty(text5))
			{
				text5 = pFIleNameRuleEx;
			}
			string text6 = this._entity_dextParam.folderPath;
			if (string.IsNullOrEmpty(text6))
			{
				text6 = null;
			}
			string text7 = this._entity_dextParam.filePrefix;
			if (string.IsNullOrEmpty(text7))
			{
				text7 = pD5Prefix;
			}
			string text8 = this._entity_dextParam.fileSubfix;
			if (string.IsNullOrEmpty(text8))
			{
				text8 = pD5Subfix;
			}
			string text9 = Path.GetFileNameWithoutExtension(text);
			string extension = Path.GetExtension(text);
			string text10 = this.GetTempFileFolder(tempPath, "");
			if (string.IsNullOrEmpty(text3) || !(text3 == "d5temp"))
			{
				if (!string.IsNullOrEmpty(physicalPath))
				{
					if (physicalPath.IndexOf("http://") == 0 || physicalPath.IndexOf("https://") == 0)
					{
						if (bErrorWrite)
						{
							this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
							return null;
						}
						return "error|006|Not found directory on server";
					}
					else
					{
						if (!Directory.Exists(physicalPath))
						{
							this.CreateFolder(physicalPath);
						}
						if (Directory.Exists(physicalPath))
						{
							text10 = physicalPath + this.m_PathChar;
						}
						else
						{
							if (bErrorWrite)
							{
								this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
								return null;
							}
							return "error|006|Not found directory on server";
						}
					}
				}
				else if (!string.IsNullOrEmpty(virtualPath))
				{
					string text11 = string.Empty;
					try
					{
						text11 = this.hContext.Request.MapPath(virtualPath);
					}
					catch (Exception)
					{
						if (bErrorWrite)
						{
							this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
							return null;
						}
						return "error|007|Not found virtual directory on server";
					}
					if (!Directory.Exists(text11))
					{
						this.CreateFolder(text11);
					}
					if (Directory.Exists(text11))
					{
						text10 = text11 + this.m_PathChar;
						goto IL_253;
					}
					if (bErrorWrite)
					{
						this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
						return null;
					}
					return "error|007|Not found directory on server";
				}
				IL_253:
				if (!string.IsNullOrEmpty(text3))
				{
					string text12 = string.Empty;
					if (text12.IndexOf(this.m_PathChar) < 0 && text3.IndexOf(':') < 0 && text3.IndexOf('*') < 0 && text3.IndexOf('?') < 0 && text3.IndexOf('"') < 0 && text3.IndexOf('<') < 0 && text3.IndexOf('>') < 0 && text3.IndexOf('|') < 0)
					{
						if (text3.IndexOf("/") > -1)
						{
							string text13 = string.Empty;
							foreach (string text14 in text3.Split(new char[]
							{
								'/'
							}))
							{
								if (!string.IsNullOrEmpty(text14))
								{
									string text15 = text14.ToLower();
									if (text15.Equals("yyyy") || text15.Equals("mm") || text15.Equals("dd") || text15.Equals("yyyymm") || text15.Equals("yyyymmdd"))
									{
										string a;
										if ((a = text15) != null)
										{
											if (!(a == "yyyy"))
											{
												if (!(a == "mm"))
												{
													if (!(a == "dd"))
													{
														if (!(a == "yyyymm"))
														{
															if (a == "yyyymmdd")
															{
																text13 = text13 + DateTime.Now.ToString("yyyyMMdd") + this.m_PathChar;
															}
														}
														else
														{
															text13 = text13 + DateTime.Now.ToString("yyyyMM") + this.m_PathChar;
														}
													}
													else
													{
														text13 = text13 + DateTime.Now.ToString("dd") + this.m_PathChar;
													}
												}
												else
												{
													text13 = text13 + DateTime.Now.ToString("MM") + this.m_PathChar;
												}
											}
											else
											{
												text13 = text13 + DateTime.Now.ToString("yyyy") + this.m_PathChar;
											}
										}
									}
									else
									{
										text13 = text13 + text14 + this.m_PathChar;
									}
								}
							}
							text12 = text13;
						}
						else
						{
							text12 = text3 + this.m_PathChar;
						}
					}
					text10 += text12;
				}
			}
			if (!string.IsNullOrEmpty(text6))
			{
				text10 = Path.Combine(text10, text6);
			}
			if (!Directory.Exists(text10))
			{
				this.CreateFolder(text10);
			}
			if (!string.IsNullOrEmpty(text4))
			{
				if (text4.Equals("GUID"))
				{
					text9 = text2;
				}
				else if (text4.Equals("REALFILENAME"))
				{
					text9 = Path.GetFileNameWithoutExtension(text);
				}
			}
			if (!string.IsNullOrEmpty(text7))
			{
				text9 = text7 + text9;
			}
			if (!string.IsNullOrEmpty(text8))
			{
				text9 += text8;
			}
			string text16 = text10 + text9 + extension;
			string str = text9 + extension;
			if (File.Exists(text16))
			{
				if (!string.IsNullOrEmpty(text5))
				{
					string text17 = text5;
					if (text17.Equals("i"))
					{
						int num = 0;
						while (File.Exists(text16))
						{
							num++;
							text17 = " (" + num + ")";
							text16 = text10 + text9 + text17 + extension;
						}
					}
					else
					{
						text16 = text10 + text9 + text17 + extension;
						while (File.Exists(text16))
						{
							text17 += text5;
							text16 = text10 + text9 + text17 + extension;
						}
					}
					str = text9 + text17 + extension;
				}
				else if (string.IsNullOrEmpty(this._entity_dextParam.kcmd))
				{
					File.Delete(text16);
				}
			}
			return text16 + "|" + str;
		}

		// Token: 0x0600001B RID: 27 RVA: 0x00003D68 File Offset: 0x00001F68
		protected string GenerateUniqueKey()
		{
			Thread.Sleep(1);
			string str = new Random().Next(0, 999).ToString("000");
			string text = DateTime.Now.Year.ToString("00");
			if (text.Length > 2)
			{
				text = text.Substring(2, 2);
			}
			str += text;
			str += DateTime.Now.Month.ToString("00");
			str += DateTime.Now.Day.ToString("00");
			str += DateTime.Now.Hour.ToString("00");
			str += DateTime.Now.Minute.ToString("00");
			str += DateTime.Now.Second.ToString("00");
			str += DateTime.Now.Millisecond.ToString("000");
			return str + new Random().Next(0, 999).ToString("000");
		}

		// Token: 0x0600001C RID: 28 RVA: 0x00003EC8 File Offset: 0x000020C8
		protected void ForceDeleteDirectory(string path)
		{
			DirectoryInfo directoryInfo = new DirectoryInfo(path)
			{
				Attributes = FileAttributes.Normal
			};
			foreach (FileSystemInfo fileSystemInfo in directoryInfo.GetFileSystemInfos())
			{
				fileSystemInfo.Attributes = FileAttributes.Normal;
			}
			directoryInfo.Delete(true);
		}

		// Token: 0x0600001D RID: 29 RVA: 0x00003F1C File Offset: 0x0000211C
		protected bool IsMobile()
		{
			string userAgent = this.hContext.Request.UserAgent;
			bool result = false;
			string[] array = new string[]
			{
				"iphone",
				"ipod",
				"ipad",
				"android",
				"blackberry",
				"windows ce",
				"nokia",
				"webos",
				"opera mini",
				"sonyericsson",
				"opera mobi",
				"iemobile",
				"windows phone"
			};
			for (int i = 0; i < array.Length; i++)
			{
				if (userAgent.ToLower().Contains(array[i]))
				{
					result = true;
					break;
				}
			}
			return result;
		}

		// Token: 0x0600001E RID: 30 RVA: 0x00003FE4 File Offset: 0x000021E4
		protected bool IsCrossDomain()
		{
			bool result = false;
			string value = this.hContext.Request.Headers["Origin"];
			if (!string.IsNullOrEmpty(value) && this.hContext.Request.Url.ToString().IndexOf(value) < 0)
			{
				result = true;
			}
			return result;
		}

		// Token: 0x0600001F RID: 31 RVA: 0x00004038 File Offset: 0x00002238
		public string[] GetRequestValue(HttpContext context, string keyName)
		{
			string[] array = null;
			string value = context.Request[keyName];
			if (!string.IsNullOrEmpty(value))
			{
				array = context.Request.Form.GetValues(keyName);
				if (array == null)
				{
					array = context.Request.QueryString.GetValues(keyName);
				}
			}
			return array;
		}

		// Token: 0x06000020 RID: 32 RVA: 0x00004084 File Offset: 0x00002284
		protected int CheckFileExtensionDetector(string filePath)
		{
			FileExtensionDetector fileExtensionDetector = new FileExtensionDetector();
			int num = fileExtensionDetector.checkFileExtension(filePath, "");
			if (num == 0 && File.Exists(filePath))
			{
				File.Delete(filePath);
			}
			return num;
		}

		// Token: 0x06000021 RID: 33 RVA: 0x000040B8 File Offset: 0x000022B8
		protected string getIntegrityHashValue(string pFilePath)
		{
			string s = this._UploadServerVersion.Substring(0, 11);
			ASCIIEncoding asciiencoding = new ASCIIEncoding();
			byte[] bytes = asciiencoding.GetBytes(s);
			string text = string.Empty;
			using (HMACSHA256 hmacsha = new HMACSHA256(bytes))
			{
				using (FileStream fileStream = new FileStream(pFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
				{
					byte[] array = hmacsha.ComputeHash(fileStream);
					foreach (byte b in array)
					{
						text += string.Format("{0:x2}", b);
					}
					fileStream.Close();
				}
			}
			return text;
		}

		// Token: 0x06000022 RID: 34 RVA: 0x0000417C File Offset: 0x0000237C
		protected string getIntegrityHashValueToUrl(string pFileUrl)
		{
			string text = string.Empty;
			try
			{
				HttpWebResponse httpWebResponse = null;
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(pFileUrl);
				httpWebRequest.Timeout = 30000;
				httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				string s = this._UploadServerVersion.Substring(0, 11);
				ASCIIEncoding asciiencoding = new ASCIIEncoding();
				byte[] bytes = asciiencoding.GetBytes(s);
				using (HMACSHA256 hmacsha = new HMACSHA256(bytes))
				{
					byte[] array = hmacsha.ComputeHash(responseStream);
					foreach (byte b in array)
					{
						text += string.Format("{0:x2}", b);
					}
				}
				responseStream.Close();
				httpWebResponse.Close();
			}
			catch
			{
			}
			return text;
		}

		// Token: 0x06000023 RID: 35 RVA: 0x00004264 File Offset: 0x00002464
		protected void decryptFile(string pFilePath, Stream pFileStream, FileMode pFileMode, string ps)
		{
			string s = this._UploadServerVersion.Substring(0, 11);
			if (pFileStream == null)
			{
				return;
			}
			byte[] array = new byte[this._cipherRoanKeySize];
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			Array.Copy(bytes, array, Math.Min(array.Length, bytes.Length));
			RijndaelManaged rijndaelManaged = new RijndaelManaged();
			rijndaelManaged.KeySize = 128;
			rijndaelManaged.BlockSize = 128;
			rijndaelManaged.Mode = CipherMode.CBC;
			rijndaelManaged.Padding = PaddingMode.PKCS7;
			rijndaelManaged.Key = array;
			rijndaelManaged.IV = array;
			try
			{
				ICryptoTransform transform = rijndaelManaged.CreateDecryptor(rijndaelManaged.Key, rijndaelManaged.IV);
				using (FileStream fileStream = new FileStream(pFilePath, pFileMode))
				{
					if (!string.IsNullOrEmpty(ps))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Upload PluginFileResumeSave decryptFile :: X-Raon-Ps => " + ps, this._str_DebugFilePath);
						}
						fileStream.Seek(Convert.ToInt64(ps), SeekOrigin.Begin);
					}
					using (CryptoStream cryptoStream = new CryptoStream(fileStream, transform, CryptoStreamMode.Write))
					{
						byte[] array2 = new byte[this.iBufferSize];
						for (int i = pFileStream.Read(array2, 0, array2.Length); i > 0; i = pFileStream.Read(array2, 0, array2.Length))
						{
							cryptoStream.Write(array2, 0, i);
						}
						cryptoStream.FlushFinalBlock();
						cryptoStream.Close();
					}
					fileStream.Close();
				}
				pFileStream.Close();
			}
			finally
			{
				if (pFileStream != null)
				{
					((IDisposable)pFileStream).Dispose();
				}
			}
		}

		// Token: 0x06000024 RID: 36 RVA: 0x000043F8 File Offset: 0x000025F8
		protected void CopyStreamToStream(Stream src, Stream dest)
		{
			byte[] array = new byte[65536];
			int count;
			while ((count = src.Read(array, 0, array.Length)) > 0)
			{
				dest.Write(array, 0, count);
			}
		}

		// Token: 0x06000025 RID: 37 RVA: 0x0000442C File Offset: 0x0000262C
		protected void MergeChunkFile(string sourcePath, string targetPath)
		{
			string[] array = sourcePath.Split(new char[]
			{
				'.'
			});
			string value = array[array.Length - 2];
			string value2 = array[array.Length - 3];
			using (FileStream fileStream = File.OpenRead(sourcePath))
			{
				using (FileStream fileStream2 = new FileStream(targetPath, FileMode.Open, FileAccess.Write, FileShare.ReadWrite))
				{
					long offset = Convert.ToInt64(value) * (Convert.ToInt64(value2) - 1L);
					fileStream2.Seek(offset, SeekOrigin.Begin);
					byte[] buffer = new byte[51200];
					int num;
					do
					{
						num = fileStream.Read(buffer, 0, 51200);
						if (num > 0)
						{
							fileStream2.Write(buffer, 0, num);
						}
					}
					while (num > 0);
					fileStream2.Close();
				}
				fileStream.Close();
			}
		}

		// Token: 0x06000026 RID: 38 RVA: 0x00004508 File Offset: 0x00002708
		protected string[] InitializeEventFileExec(string newfilelocation, string responsefilename)
		{
			string directoryName = Path.GetDirectoryName(newfilelocation);
			if (!Directory.Exists(directoryName))
			{
				this.CreateFolder(directoryName);
			}
			if (File.Exists(newfilelocation))
			{
				string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newfilelocation);
				string extension = Path.GetExtension(newfilelocation);
				if (!string.IsNullOrEmpty(this._entity_dextParam.fileNameRuleEx))
				{
					string text = this._entity_dextParam.fileNameRuleEx;
					if (text.Equals("i"))
					{
						int num = 0;
						while (File.Exists(newfilelocation))
						{
							num++;
							text = " (" + num + ")";
							newfilelocation = string.Concat(new object[]
							{
								directoryName,
								this.m_PathChar,
								fileNameWithoutExtension,
								text,
								extension
							});
						}
					}
					else
					{
						while (File.Exists(newfilelocation))
						{
							text += this._entity_dextParam.fileNameRuleEx;
							newfilelocation = string.Concat(new object[]
							{
								directoryName,
								this.m_PathChar,
								fileNameWithoutExtension,
								text,
								extension
							});
						}
					}
					responsefilename = fileNameWithoutExtension + text + extension;
				}
				else
				{
					File.Delete(newfilelocation);
				}
			}
			return new string[]
			{
				newfilelocation,
				responsefilename
			};
		}

		// Token: 0x06000027 RID: 39 RVA: 0x0000464C File Offset: 0x0000284C
		protected string GetHeaderFileName(string fileName)
		{
			string text = fileName;
			string text2 = this.hContext.Request.UserAgent.ToLower();
			if (text2.IndexOf("msie") >= 0 || text2.IndexOf("trident") >= 0 || text2.IndexOf("edge/") >= 0)
			{
				if (text2.IndexOf("msie") > -1)
				{
					try
					{
						string text3 = text2.Substring(text2.IndexOf("msie"));
						text3 = text3.Substring(0, text3.IndexOf(";"));
						text3 = text3.Substring(5);
						double num = Convert.ToDouble(text3);
						if (text2.IndexOf("msie") >= 0 && text2.IndexOf("trident") == -1 && num < 8.0)
						{
							string filenameTitle = fileName.Substring(0, fileName.LastIndexOf("."));
							string text4 = fileName.Substring(fileName.LastIndexOf("."));
							text = this.GetIE8orOlderDownloadFileNameByPercentUTF8(filenameTitle);
							if (!string.IsNullOrEmpty(text4) && text4 != "")
							{
								text += text4;
							}
						}
						else
						{
							text = HttpUtility.UrlEncode(fileName);
						}
						goto IL_11E;
					}
					catch (Exception)
					{
						text = HttpUtility.UrlEncode(fileName);
						goto IL_11E;
					}
				}
				text = HttpUtility.UrlEncode(fileName);
				IL_11E:
				text = text.Replace("+", "%20");
			}
			else if (text2.IndexOf("chrome") >= 0)
			{
				StringBuilder stringBuilder = new StringBuilder();
				foreach (char c in fileName)
				{
					if (c > '~')
					{
						stringBuilder.Append(HttpUtility.UrlEncode(string.Concat(c)));
					}
					else
					{
						stringBuilder.Append(c);
					}
				}
				text = stringBuilder.ToString();
			}
			return text;
		}

		// Token: 0x06000028 RID: 40 RVA: 0x00004804 File Offset: 0x00002A04
		protected string GetIE8orOlderDownloadFileNameByPercentUTF8(string filenameTitle)
		{
			string text = "";
			int num = 135;
			try
			{
				int num2 = 0;
				for (int i = 0; i < filenameTitle.Length; i++)
				{
					string str = filenameTitle.Substring(i, 1);
					string text2 = HttpUtility.UrlEncode(str);
					if (text2 == "+")
					{
						num2 += 3;
					}
					else
					{
						num2 += text2.Length;
					}
					if (num2 >= num)
					{
						text = (text ?? "");
						break;
					}
					text += text2;
				}
			}
			catch (Exception)
			{
				text = HttpUtility.UrlEncode(filenameTitle);
			}
			return text;
		}

		// Token: 0x06000029 RID: 41 RVA: 0x000048AC File Offset: 0x00002AAC
		protected string CheckExternalWebFile(string urlAddress)
		{
			string result = string.Empty;
			try
			{
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(urlAddress);
				httpWebRequest.UserAgent = "DEXT5" + Guid.NewGuid();
				httpWebRequest.Timeout = 60000;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				result = urlAddress;
				httpWebResponse.Close();
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x0600002A RID: 42 RVA: 0x00004920 File Offset: 0x00002B20
		protected Stream GetOutsideUrlFileStream(string urlAddress)
		{
			Stream result = null;
			try
			{
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(urlAddress);
				httpWebRequest.Timeout = 30000;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				result = httpWebResponse.GetResponseStream();
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x0600002B RID: 43 RVA: 0x00004974 File Offset: 0x00002B74
		protected string DownloadOutsideUrlFile(string urlAddress, string fileName, string contentDispositionType, bool bUseDownloadServerFileName)
		{
			string text = string.Empty;
			try
			{
				string text2 = fileName.Substring(fileName.LastIndexOf('.') + 1);
				string text3 = this.GenerateUniqueKey();
				for (;;)
				{
					text = string.Concat(new object[]
					{
						this.tempPath,
						this.m_PathChar,
						text3,
						".",
						text2
					});
					if (!File.Exists(text))
					{
						break;
					}
					text3 = this.GenerateUniqueKey();
				}
				HttpWebResponse httpWebResponse = null;
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(urlAddress);
				bool flag = false;
				if (this._entity_dextParam.resumeMode == "1" && contentDispositionType == "attachment")
				{
					flag = true;
				}
				string text4 = this.hContext.Request.Headers["X-Raon-Fde"];
				string[] array = this.GetResponseStartPosition(this.hContext.Request, fileName).Split(new char[]
				{
					'|'
				});
				if (!flag || string.IsNullOrEmpty(text4) || !(text4 == "securedAes"))
				{
					httpWebRequest.AddRange(Convert.ToInt32(array[0]));
				}
				httpWebRequest.Timeout = 30000;
				httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				if (this._b_externalWebFileDirectDownload)
				{
					string headerFileName = this.GetHeaderFileName(fileName);
					string mimeType = Dext5Mime.GetMimeType(headerFileName);
					this.hContext.Response.Clear();
					this.hContext.Response.BufferOutput = false;
					this.hContext.Response.ContentType = mimeType;
					this.hContext.Response.AddHeader("Content-Disposition", contentDispositionType + "; filename=\"" + headerFileName + "\"");
					if (bUseDownloadServerFileName)
					{
						this.hContext.Response.AddHeader("X-Raon-FName", HttpUtility.UrlEncode(fileName).Replace("+", "%20"));
					}
					if (contentDispositionType == "inline" && (mimeType == "text/plain" || mimeType == "text/html"))
					{
						Encoding textFileEncodingUrl = this.GetTextFileEncodingUrl(urlAddress);
						this.hContext.Response.Charset = textFileEncodingUrl.WebName;
					}
					long num;
					string lastModified;
					string text5;
					if (array[0] == "0" && array[1] == "0")
					{
						num = httpWebResponse.ContentLength;
						lastModified = "";
						text5 = httpWebResponse.ContentLength.ToString();
					}
					else if (array[0] != "0" && array[1] == "0")
					{
						num = Convert.ToInt64(array[0]) + httpWebResponse.ContentLength;
						lastModified = "";
						text5 = httpWebResponse.ContentLength.ToString();
					}
					else
					{
						if (array[1] == "0")
						{
							num = httpWebResponse.ContentLength;
							lastModified = "";
						}
						else
						{
							num = Convert.ToInt64(array[1]);
							lastModified = array[2];
						}
						text5 = array[1];
					}
					long num2 = 0L;
					long num3 = 0L;
					if (!string.IsNullOrEmpty(text4) && text4 == "securedAes")
					{
						num2 = (long)this._cipherRoanKeySize - num % (long)this._cipherRoanKeySize;
					}
					long num4 = 0L;
					long num5 = -1L;
					if (flag)
					{
						HttpResponseHeader responseHeader = Base.GetResponseHeader(this.hContext.Request, httpWebResponse, fileName, headerFileName, num, lastModified);
						this.hContext.Response.AppendHeader("Accept-Ranges", responseHeader.AcceptRanges);
						this.hContext.Response.AppendHeader("Connection", responseHeader.Connection);
						this.hContext.Response.ContentEncoding = responseHeader.ContentEncoding;
						if (this.hContext.Request.Headers["Range"] != null && !string.IsNullOrEmpty(responseHeader.ContentRange) && (string.IsNullOrEmpty(text4) || !(text4 == "securedAes")))
						{
							this.hContext.Response.AppendHeader("Content-Range", responseHeader.ContentRange);
							this.hContext.Response.StatusCode = 206;
						}
						this.hContext.Response.ContentType = responseHeader.ContentType;
						this.hContext.Response.AppendHeader("Etag", "\"" + responseHeader.Etag + "\"");
						this.hContext.Response.AppendHeader("Last-Modified", responseHeader.LastModified);
						if (string.IsNullOrEmpty(text4) || !(text4 == "securedAes"))
						{
							if (text5 == "0")
							{
								this.hContext.Response.AppendHeader("Content-Length", responseHeader.ContentLength);
								this.hContext.Response.AppendHeader("X-Raon-Cl", responseHeader.ContentLength);
							}
							else
							{
								this.hContext.Response.AppendHeader("Content-Length", text5);
								this.hContext.Response.AppendHeader("X-Raon-Cl", text5);
							}
						}
					}
					else
					{
						if (this.hContext.Request.Headers["Range"] != null)
						{
							string[] array2 = this.hContext.Request.Headers["Range"].Split(new char[]
							{
								'=',
								'-'
							});
							num4 = Convert.ToInt64(array2[1]);
							if (num4 < 0L || num4 >= num)
							{
								num4 = 0L;
							}
							if (array2.Length == 3 && !string.IsNullOrEmpty(array2[2]))
							{
								num5 = Convert.ToInt64(array2[2]);
								if (num5 <= 0L || num5 >= num)
								{
									if (num4 == 0L)
									{
										num5 = num - 1L;
									}
									else
									{
										num5 = -1L;
									}
								}
							}
						}
						if (this.hContext.Request.Headers["Range"] != null)
						{
							this.hContext.Response.StatusCode = 206;
							if (num5 == -1L)
							{
								this.hContext.Response.AppendHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num4, num - 1L, num));
							}
							else
							{
								this.hContext.Response.AppendHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num4, num5, num));
							}
						}
						if (!string.IsNullOrEmpty(text4) && text4 == "securedAes")
						{
							if (num5 == -1L)
							{
								this.hContext.Response.AppendHeader("Content-Length", (httpWebResponse.ContentLength + num2).ToString());
								this.hContext.Response.AppendHeader("X-Raon-Cl", (httpWebResponse.ContentLength + num2).ToString());
								num3 = httpWebResponse.ContentLength + num2;
							}
							else
							{
								this.hContext.Response.AppendHeader("Content-Length", (num5 - num4 + 1L + num2).ToString());
								this.hContext.Response.AppendHeader("X-Raon-Cl", (num5 - num4 + 1L + num2).ToString());
								num3 = num5 - num4 + 1L + num2;
							}
						}
						else if (num5 == -1L)
						{
							this.hContext.Response.AppendHeader("Content-Length", httpWebResponse.ContentLength.ToString());
							this.hContext.Response.AppendHeader("X-Raon-Cl", httpWebResponse.ContentLength.ToString());
						}
						else
						{
							this.hContext.Response.AppendHeader("Content-Length", (num5 - num4 + 1L).ToString());
							this.hContext.Response.AppendHeader("X-Raon-Cl", (num5 - num4 + 1L).ToString());
						}
					}
					string text6 = this.hContext.Request.Headers["X-Raon-Fdi"];
					if (!string.IsNullOrEmpty(text6) && text6 == "hmc-sha256")
					{
						string integrityHashValueToUrl = this.getIntegrityHashValueToUrl(urlAddress);
						this.hContext.Response.AddHeader("X-Raon-Fdi", integrityHashValueToUrl);
					}
					if (flag && !string.IsNullOrEmpty(text4) && text4 == "securedAes")
					{
						string text7 = this.hContext.Request.Headers["X-Raon-Guid"];
						int num6 = fileName.LastIndexOf(".");
						string text8 = fileName;
						string text9 = "";
						if (num6 > -1)
						{
							text8 = fileName.Substring(0, num6);
							text9 = fileName.Substring(num6, fileName.Length - num6);
						}
						string text10 = string.Concat(new object[]
						{
							this.tempPath,
							this.m_PathChar,
							text7,
							this.m_PathChar,
							text8,
							"_encrypt",
							text9
						});
						if (!Directory.Exists(this.tempPath + this.m_PathChar + text7))
						{
							Directory.CreateDirectory(this.tempPath + this.m_PathChar + text7);
						}
						if (!File.Exists(text10))
						{
							string s = this._UploadServerVersion.Substring(0, 11);
							byte[] array3 = new byte[this._cipherRoanKeySize];
							byte[] bytes = Encoding.UTF8.GetBytes(s);
							Array.Copy(bytes, array3, Math.Min(array3.Length, bytes.Length));
							ICryptoTransform transform = new RijndaelManaged
							{
								KeySize = 128,
								BlockSize = 128,
								Mode = CipherMode.CBC,
								Padding = PaddingMode.PKCS7,
								Key = array3,
								IV = array3
							}.CreateEncryptor();
							using (FileStream fileStream = new FileStream(text10, FileMode.Create))
							{
								using (CryptoStream cryptoStream = new CryptoStream(fileStream, transform, CryptoStreamMode.Write))
								{
									byte[] array4 = new byte[this.iBufferSize];
									for (int i = responseStream.Read(array4, 0, array4.Length); i > 0; i = responseStream.Read(array4, 0, array4.Length))
									{
										cryptoStream.Write(array4, 0, i);
									}
									cryptoStream.FlushFinalBlock();
									cryptoStream.Close();
								}
							}
						}
						if (this.hContext.Request.Headers["Range"] != null)
						{
							this.hContext.Response.StatusCode = 206;
						}
						FileInfo fileInfo = new FileInfo(text10);
						if (this.hContext.Request.Headers["Range"] != null)
						{
							string[] array5 = this.hContext.Request.Headers["Range"].Split(new char[]
							{
								'=',
								'-'
							});
							num4 = Convert.ToInt64(array5[1]);
							if (num4 < 0L || num4 >= fileInfo.Length)
							{
								num4 = 0L;
							}
							if (array5.Length == 3 && !string.IsNullOrEmpty(array5[2]))
							{
								num5 = Convert.ToInt64(array5[2]);
								if (num5 <= 0L || num5 >= fileInfo.Length)
								{
									if (num4 == 0L)
									{
										num5 = fileInfo.Length - 1L;
									}
									else
									{
										num5 = -1L;
									}
								}
							}
							if (num5 == -1L)
							{
								this.hContext.Response.AppendHeader("Content-Range", string.Format(" bytes {0}-{1}/{2}", num4, fileInfo.Length - 1L, fileInfo.Length));
							}
							else
							{
								this.hContext.Response.AppendHeader("Content-Range", string.Format(" bytes {0}-{1}/{2}", num4, num5, fileInfo.Length));
							}
						}
						if (num5 == -1L)
						{
							this.hContext.Response.AppendHeader("Content-Length", (fileInfo.Length - num4).ToString());
							this.hContext.Response.AppendHeader("X-Raon-Cl", (fileInfo.Length - num4).ToString());
						}
						else
						{
							this.hContext.Response.AppendHeader("Content-Length", (num5 - num4 + 1L).ToString());
							this.hContext.Response.AppendHeader("X-Raon-Cl", (num5 - num4 + 1L).ToString());
						}
						Stream stream = new FileStream(text10, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
						if (num4 > 0L)
						{
							stream.Position = num4;
						}
						byte[] array6 = new byte[this.iBufferSize];
						long num7 = fileInfo.Length;
						while (num7 > 0L)
						{
							if (this.hContext.Response.IsClientConnected)
							{
								int num8 = stream.Read(array6, 0, array6.Length);
								this.hContext.Response.OutputStream.Write(array6, 0, num8);
								num7 -= (long)num8;
							}
							else
							{
								num7 = -1L;
							}
						}
						this.hContext.Response.Flush();
					}
					else
					{
						if (!string.IsNullOrEmpty(text4) && text4 == "securedAes")
						{
							try
							{
								string s2 = this._UploadServerVersion.Substring(0, 11);
								byte[] array7 = new byte[this._cipherRoanKeySize];
								byte[] bytes2 = Encoding.UTF8.GetBytes(s2);
								Array.Copy(bytes2, array7, Math.Min(array7.Length, bytes2.Length));
								ICryptoTransform transform2 = new RijndaelManaged
								{
									KeySize = 128,
									BlockSize = 128,
									Mode = CipherMode.CBC,
									Padding = PaddingMode.PKCS7,
									Key = array7,
									IV = array7
								}.CreateEncryptor();
								byte[] array8 = new byte[this.iBufferSize];
								using (CryptoStream cryptoStream2 = new CryptoStream(this.hContext.Response.OutputStream, transform2, CryptoStreamMode.Write))
								{
									if (num3 > 0L)
									{
										while (num3 > 0L)
										{
											if (num3 < (long)this.iBufferSize)
											{
												array8 = new byte[num3];
											}
											int num9 = responseStream.Read(array8, 0, array8.Length);
											if (num9 <= 0)
											{
												break;
											}
											cryptoStream2.Write(array8, 0, num9);
											num3 -= (long)num9;
										}
									}
									cryptoStream2.FlushFinalBlock();
									cryptoStream2.Close();
									this.hContext.Response.Flush();
								}
								goto IL_F08;
							}
							catch
							{
								goto IL_F08;
							}
						}
						long num10 = Convert.ToInt64(this.hContext.Response.Headers["Content-Length"]);
						byte[] array9 = new byte[this.iBufferSize];
						while (num10 > 0L)
						{
							if (this.hContext.Response.IsClientConnected)
							{
								int num11 = responseStream.Read(array9, 0, array9.Length);
								this.hContext.Response.OutputStream.Write(array9, 0, num11);
								num10 -= (long)num11;
							}
							else
							{
								num10 = -1L;
							}
						}
						this.hContext.Response.Flush();
					}
					IL_F08:
					text = string.Empty;
				}
				else
				{
					this.hContext.Response.AppendHeader("Content-Length", httpWebResponse.ContentLength.ToString());
					byte[] array10 = new byte[this.iBufferSize];
					FileStream fileStream2 = new FileStream(text, FileMode.OpenOrCreate, FileAccess.Write);
					long num12 = httpWebResponse.ContentLength;
					while (num12 > 0L)
					{
						if (this.hContext.Response.IsClientConnected)
						{
							int num13 = responseStream.Read(array10, 0, array10.Length);
							fileStream2.Write(array10, 0, num13);
							fileStream2.Flush();
							num12 -= (long)num13;
						}
						else
						{
							num12 = -1L;
						}
					}
					fileStream2.Close();
				}
				responseStream.Close();
				httpWebResponse.Close();
			}
			catch
			{
				text = string.Empty;
			}
			return text;
		}

		// Token: 0x0600002C RID: 44 RVA: 0x000059C4 File Offset: 0x00003BC4
		private string GetResponseStartPosition(HttpRequest httpRequest, string fileName)
		{
			string text = "0";
			string text2 = "0";
			string text3 = "";
			string str = HttpUtility.UrlEncode(fileName, Encoding.UTF8);
			if (httpRequest.Headers["Range"] != null)
			{
				string[] array = httpRequest.Headers["Range"].Split(new char[]
				{
					'=',
					'-'
				});
				text = array[1];
			}
			if (httpRequest.Headers["If-Range"] != null)
			{
				string text4 = httpRequest.Headers["If-Range"].Replace("\"", "");
				if (text4.IndexOf(str + "|") != 0)
				{
					text = "0";
				}
				else
				{
					text2 = text4.Split(new char[]
					{
						'|'
					})[1];
					text3 = text4.Split(new char[]
					{
						'|'
					})[2];
				}
			}
			return string.Concat(new string[]
			{
				text,
				"|",
				text2,
				"|",
				text3
			});
		}

		// Token: 0x0600002D RID: 45 RVA: 0x00005AE8 File Offset: 0x00003CE8
		private static HttpResponseHeader GetResponseHeader(HttpRequest httpRequest, HttpWebResponse response, string fileName, string headerFileName, long fileLength, string lastModified)
		{
			if (httpRequest == null)
			{
				return null;
			}
			if (response == null)
			{
				return null;
			}
			long num = 0L;
			long num2 = -1L;
			string text = response.LastModified.ToString();
			lastModified = text;
			string text2 = string.Concat(new string[]
			{
				HttpUtility.UrlEncode(fileName, Encoding.UTF8),
				"|",
				fileLength.ToString(),
				"|",
				lastModified
			});
			string contentDisposition = "attachment;filename=\"" + headerFileName + "\"";
			if (httpRequest.Headers["Range"] != null)
			{
				string[] array = httpRequest.Headers["Range"].Split(new char[]
				{
					'=',
					'-'
				});
				num = Convert.ToInt64(array[1]);
				if (num < 0L || num >= fileLength)
				{
					return null;
				}
				if (array.Length == 3 && !string.IsNullOrEmpty(array[2]))
				{
					num2 = Convert.ToInt64(array[2]);
					if (num2 <= 0L || num2 >= fileLength)
					{
						return null;
					}
				}
			}
			if (httpRequest.Headers["If-Range"] != null && httpRequest.Headers["If-Range"].Replace("\"", "") != text2)
			{
				num = 0L;
			}
			string contentLength = string.Empty;
			if (num2 == -1L)
			{
				contentLength = (fileLength - num).ToString();
			}
			else
			{
				contentLength = (num2 - num + 1L).ToString();
			}
			string contentRange;
			if (num2 == -1L)
			{
				contentRange = string.Format(" bytes {0}-{1}/{2}", num, fileLength - 1L, fileLength);
			}
			else
			{
				contentRange = string.Format(" bytes {0}-{1}/{2}", num, num2, fileLength);
			}
			return new HttpResponseHeader
			{
				AcceptRanges = "bytes",
				Connection = "Keep-Alive",
				ContentDisposition = contentDisposition,
				ContentEncoding = Encoding.UTF8,
				ContentLength = contentLength,
				ContentRange = contentRange,
				ContentType = Dext5Mime.GetMimeType(headerFileName),
				Etag = text2,
				LastModified = text
			};
		}

		// Token: 0x0600002E RID: 46 RVA: 0x00005D10 File Offset: 0x00003F10
		private Encoding GetTextFileEncodingUrl(string sUrl)
		{
			Encoding result = Encoding.Default;
			try
			{
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(sUrl);
				httpWebRequest.Timeout = 30000;
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				byte[] array = new byte[4];
				responseStream.Read(array, 0, array.Length);
				if ((array[0] & 255) == 239 && (array[1] & 255) == 187 && (array[2] & 255) == 191)
				{
					result = Encoding.UTF8;
				}
				else if ((array[0] & 255) == 254 && (array[1] & 255) == 255)
				{
					result = Encoding.GetEncoding(1201);
				}
				else if ((array[0] & 255) == 255 && (array[1] & 255) == 254)
				{
					result = Encoding.GetEncoding(1200);
				}
				else if ((array[0] & 255) == 0 && (array[1] & 255) == 0 && (array[0] & 255) == 254 && (array[1] & 255) == 255)
				{
					result = Encoding.GetEncoding(12001);
				}
				else if ((array[0] & 255) == 255 && (array[1] & 255) == 254 && (array[0] & 255) == 0 && (array[1] & 255) == 0)
				{
					result = Encoding.GetEncoding(12000);
				}
				else
				{
					responseStream.Close();
					httpWebResponse.Close();
					httpWebRequest = (HttpWebRequest)WebRequest.Create(sUrl);
					httpWebRequest.Timeout = 30000;
					httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
					responseStream = httpWebResponse.GetResponseStream();
					if (!this.getIsUtf8(responseStream))
					{
						result = Encoding.Default;
					}
					else
					{
						result = Encoding.UTF8;
					}
				}
				responseStream.Close();
				httpWebResponse.Close();
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x0600002F RID: 47 RVA: 0x00005F1C File Offset: 0x0000411C
		private bool getIsUtf8(Stream fs)
		{
			bool result = true;
			byte[] array = new byte[this.iBufferSize];
			while (fs.Read(array, 0, array.Length) > 0)
			{
				bool flag = true;
				int num = 0;
				for (int i = 0; i < array.Length; i++)
				{
					int num2 = (int)(array[i] & byte.MaxValue);
					if ((num2 & 128) != 0)
					{
						flag = false;
					}
					if (num == 0)
					{
						if (num2 >= 128)
						{
							do
							{
								num2 = (num2 << 1 & 255);
								num++;
							}
							while ((num2 & 128) != 0);
							num--;
							if (num == 0)
							{
								result = false;
								break;
							}
						}
					}
					else
					{
						if ((num2 & 192) != 128)
						{
							result = false;
							break;
						}
						num--;
					}
				}
				if (num > 0)
				{
					result = false;
				}
				if (flag)
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x06000030 RID: 48 RVA: 0x00005FC8 File Offset: 0x000041C8
		protected string FileLocationInfoReadWrite(string mode, string strGuid, string pNewfilelocation)
		{
			string path = string.Concat(new object[]
			{
				this.GetTempFileFolder(this.tempPath, strGuid),
				this.m_PathChar,
				strGuid,
				this.m_strHSTempSuffix
			});
			if (mode == "R")
			{
				return Dext5Encoding.Base64Decoding(File.ReadAllText(path));
			}
			if (mode == "W")
			{
				File.WriteAllText(path, Dext5Encoding.Base64Encoding(pNewfilelocation));
				return "";
			}
			return "";
		}

		// Token: 0x04000003 RID: 3
		protected string _EditorServerVersion = "3.5." + 1057868 + ".1425.01";

		// Token: 0x04000004 RID: 4
		protected string _UploadServerVersion = "2.7." + 1192240 + ".1400.01";

		// Token: 0x04000005 RID: 5
		protected string _PhotoServerVersion = "2.0." + 1125082 + ".0933.01";

		// Token: 0x04000006 RID: 6
		protected int _cipherRoanKeySize = 16;

		// Token: 0x04000007 RID: 7
		protected HttpContext hContext;

		// Token: 0x04000008 RID: 8
		protected char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x04000009 RID: 9
		protected bool _b_IsDebug;

		// Token: 0x0400000A RID: 10
		protected string _str_DebugFilePath = string.Empty;

		// Token: 0x0400000B RID: 11
		protected ParamEntity _entity_dextParam;

		// Token: 0x0400000C RID: 12
		protected string _str_Credentials_Method = "impersonate";

		// Token: 0x0400000D RID: 13
		protected string _str_Credentials_UserID = string.Empty;

		// Token: 0x0400000E RID: 14
		protected string _str_Credentials_UserPassword = string.Empty;

		// Token: 0x0400000F RID: 15
		protected string _str_Credentials_Domain = string.Empty;

		// Token: 0x04000010 RID: 16
		protected string _str_ResponseCustomValue = "";

		// Token: 0x04000011 RID: 17
		protected string _str_ResponseGroupId = "";

		// Token: 0x04000012 RID: 18
		protected bool _b_externalWebFileDirectDownload;

		// Token: 0x04000013 RID: 19
		protected bool _b_useExternalDownload = true;

		// Token: 0x04000014 RID: 20
		protected Zip64Option _enableZip64 = Zip64Option.AsNecessary;

		// Token: 0x04000015 RID: 21
		protected bool _b_html5SliceAppend;

		// Token: 0x04000016 RID: 22
		protected string tempPath = string.Empty;

		// Token: 0x04000017 RID: 23
		protected int iBufferSize = 8192;

		// Token: 0x04000018 RID: 24
		protected int iResumeCheckSize = 10485760;

		// Token: 0x04000019 RID: 25
		protected string m_strHSTempSuffix = ".raon.tmp";

		// Token: 0x0400001A RID: 26
		public Dext5Impersonator _impersonator;

		// Token: 0x0400001B RID: 27
		public Dext5UNCAccessWithCredentials _uncAccessWithCredentials;
	}
}

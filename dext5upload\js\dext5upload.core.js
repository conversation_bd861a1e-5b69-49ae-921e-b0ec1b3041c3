var dext_console=window.console||{log:function(){}};_dext5_uploader=null;
var RESULTFILELIST=[],RESULTFILELISTCLON=[],uploadForms=[],uploaders=[],TOTALFILELISTSIZE=0,TOTALFILELISTNUM=0,PREVIOUSUPLOADEDSIZE=0,UPLOADIDX=0,TOTALUPLOADSIZE=0,TOTALUPLOADNUM=0,G_socketArr=[],G_socket_worker=[],G_curr_socket=null,fileworkers=[],uploadworkers=[],upload_complete_count=0,intervalObj,G_IntervalGetSize=null,basicExtIcon="ai avi bmp css doc eml exe fla flv gif gul html hwp jpg mp3 mp4 mpg msg pdf png ppt psd swf tif txt wmv xls zip".split(" "),G_ImageFileExt="bmp,gif,jpeg,jpg,png",
G_StrCustomHeaderColor="",G_StrCustomHeaderColor2="",G_StrCustomFooterColor="",G_StrCustomProgressBarColor="",G_StrCustomProgressBarColor2="",G_StrCustomTextColor="",G_UploadStartTime,G_FileHandlerControl=0,G_FormData=[],G_downForm_submit=!1,G_zipInterval=null,G_zipIntervalData={downloadUrl:"",zipGuid:"",downloadLIST:[]},G_oneFileUploadedSize=0,G_Complete_Socket_worker=0,G_SocketWorkerPool=[],G_SocketUploadWorkArray=[],G_SocketWorkerCloseCount=0,G_numberOfServerUploadedChunks=0,G_FDIData="",G_FolderCount=
"",G_CompleteImageQuality=!1,G_changeCount=0,G_ImageWorkerArr=[],G_ChnageImageArr=[],G_DeleteItemCount=0,G_SelectedItemCount=0,UPLOADTOP=parent;setUPLOADWINwindow();setUPLOADWINdocument();var SIL=[],Z=[],___=UPLOADTOP.UPLOADWIN.location,_0_,_1_,_7_;UPLOADTOP.DEXT5UPLOAD=parent.DEXT5UPLOAD;parent.UPLOADTOP=UPLOADTOP;
var G_LocalFileObjectLocalPath={},G_LocalFileObjectMark={},G_UploadedFileListObj=[],G_DeletedFileListObj=[],_R_O_="c",R=[],T=[],K__="zu",G_AP1=[],G_AP2=[],G_RetryHybridCmdCount=0,G_UseAddLocalFileObject="0",G_LocalFileObject=[],G_TagID=[],G_PreviewDialogPosition={top:-1,left:-1};SIL[0]="t";K__+="vw";Array.prototype.indexOf||(Array.prototype.indexOf=function(b,a){a=a||0;for(var c=this.length;a<c;){if(this[a]===b)return a;++a}return-1});
String.prototype.insertAt||(String.prototype.insertAt=function(b,a){return this.substr(0,b)+a+this.substr(b)});String.prototype.replaceAt||(String.prototype.replaceAt=function(b,a){return this.substr(0,b)+a+this.substr(b+a.length)});function setUPLOADWINwindow(){try{topURL=top.location.href,"undefined"==typeof topURL?(UPLOADTOP.UPLOADWIN=parent,UPLOADTOP.IsCrossDomain=!0):(UPLOADTOP.UPLOADWIN=parent,UPLOADTOP.IsCrossDomain=!1)}catch(b){UPLOADTOP.UPLOADWIN=parent,UPLOADTOP.IsCrossDomain=!0}}K__+="CDE";
G_AP2[0]="r";function setUPLOADWINdocument(){try{topURL=top.location.href,"undefined"==typeof topURL?(UPLOADTOP.UPLOADDOC=parent.document,UPLOADTOP.IsCrossDomain=!0):(UPLOADTOP.UPLOADDOC=parent.document,UPLOADTOP.IsCrossDomain=!1)}catch(b){UPLOADTOP.UPLOADDOC=parent.document,UPLOADTOP.IsCrossDomain=!0}}
function Dext5Upload_Uploader(b,a,c){this._config=a;this._frame=c;this.frameWin=window;this.ID=b;UPLOADTOP.DEXT5UPLOAD.DEXTMULTIPLE[c.frameID]=this;UPLOADTOP.G_CURRUPLOADER=this;this.newOriginalName=[];this.newUploadName=[];this.newUploadPath=[];this.newLogicalPath=[];this.newSize=[];this.newGuid=[];this.newStatus=[];this.newOrder=[];this.newLocalPath=[];this.newMark=[];this.____={};this.newResponseCustomValue=[];this.newLargeFiles=[];this.delUniqKey=[];this.delOriginalName=[];this.delSize=[];this.delLargeFiles=
[];this.nonDelUniqKey=[];this.nonDelOriginalName=[];this.nonDelSize=[];this.nonDelOrder=[];this.nonDelLargeFiles=[];this.nonDelCustomValue=[];this.fileListHeight=0;this.TagID=UPLOADTOP.DEXT5UPLOAD.util.makeGuidTagName();_R_O_+="/";_R_O_+="=";G_AP2[6]="z";this.fileObj_html4={fileObj:null,markValue:"",fileSize:0};this.uploadModeFormTags=null;this.isUploadComplete=this.isDownloadReady=!1;this.thumbsImgExtArr="bmp jpg jpeg png gif tif tiff".split(" ");this.xhr_onprogress=null;this.transferCompleteEvt=
!1;this.hybridUrl="http://127.0.0.1:";this.hybridPort="";this.httpHeaderArr=[];this.downEventkeyArr=[];this.hybridLastCmd="";this.isHybridOpenResponse=this.checkDownloadFail=!1;this.dialogWindow=null!=UPLOADTOP.DEXT5UPLOAD.config.DialogWindow?UPLOADTOP.DEXT5UPLOAD.config.DialogWindow:null;null!=this.dialogWindow&&(this.dialogWindow.UPLOADWIN=UPLOADTOP.UPLOADWIN,this.dialogWindow.UPLOADDOC=UPLOADTOP.UPLOADDOC,this.dialogWindow.G_CURRUPLOADER=UPLOADTOP.G_CURRUPLOADER);this.commonToolCanvas=document.createElement("canvas");
this.checkThumbsArr=[];this.checkThumbsArrIdx=0;this.pluginInstallGuidePopup=null;try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_onLanguageDefinition(b,Dext5Upload_Lang)}catch(e){}}G_AP1[2]=9;
Dext5Upload_Uploader.prototype={init:function(b){b=UPLOADTOP.DEXT5UPLOAD.util._setDext5Uploader(b).frameWin;b.uploaders=[];b.TOTALFILELISTSIZE=0;b.TOTALFILELISTNUM=0;b.UPLOADIDX=0;b.TOTALUPLOADNUM=0;b.upload_complete_count=0;b.PREVIOUSUPLOADEDSIZE=0;b.TOTALUPLOADSIZE=0;b.calcTotalSize();b=getDialogDocument();b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML="";b.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width="0%";b.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=
""},reset:function(b){b=UPLOADTOP.DEXT5UPLOAD.util._setDext5Uploader(b);var a=b.frameWin;a.uploaders=[];a.TOTALFILELISTSIZE=0;a.TOTALFILELISTNUM=0;a.UPLOADIDX=0;a.TOTALUPLOADNUM=0;a.upload_complete_count=0;a.PREVIOUSUPLOADEDSIZE=0;a.TOTALUPLOADSIZE=0;a.calcTotalSize();try{var c=getDialogDocument();c.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML="";c.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width="0%";c.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=
""}catch(e){}a.RESULTFILELIST=[];a.calcTotalSize();a.displayTotalSizeAndNum();try{a.document.getElementById("file_list").innerHTML="","upload"==b._config.mode&&(a.document.getElementById("file_temp").style.display="")}catch(f){}"html4"!=b._config.userRunTimeMode&&(a.document.getElementById("total_size").innerHTML="0 byte");if(-1<b._config.userRunTimeMode.indexOf("html5")){if(c=document.getElementById("file_"+b.TagID))c.value="",c.outerHTML=c.outerHTML}else if("html4"==b._config.userRunTimeMode&&"1"==
b._config.uploadMethodHtml4&&(c=document.getElementById("files_container")))for(b=c.childNodes.length,a=0;a<b;a++)a!=b-1&&c.removeChild(c.childNodes[0])},cancel:function(){if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4){var b=RESULTFILELIST[uploaders[UPLOADIDX]].guid,a=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,a=-1<a.indexOf("?")?a+"&":a+"?",a=a+("dext5CMD=uh4fc&g="+
b+"&c="+b);UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain?UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,a,"download_frame"):UPLOADTOP.DEXT5UPLOAD.ajax.postData(a)}PREVIOUSUPLOADEDSIZE=0;uploaders=[];upload_complete_count=TOTALUPLOADNUM=TOTALUPLOADSIZE=UPLOADIDX=0;calcTotalSize();displayTotalSizeAndNum();b=getDialogDocument();try{b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML="",b.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width="0%",b.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=
""}catch(c){}UPLOADTOP.G_CURRUPLOADER.xhr_onprogress&&UPLOADTOP.G_CURRUPLOADER.xhr_onprogress.abort();try{"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&intervalObj&&clearInterval(intervalObj)}catch(e){}},start:function(b,a){var c=this,e=function(a){"html4"==b.userRunTimeMode&&"0"==b.uploadMethodHtml4&&(new SWFUpload({swfupload_preload_handler:dext5upload_swf_preLoad},a.ID,G_StrCustomHeaderColor,G_StrCustomFooterColor),delete SWFUpload.instances["SWFUpload_"+
a.ID]);""!=b.customHeaderColor&&(G_StrCustomHeaderColor=" background:"+b.customHeaderColor,G_StrCustomHeaderColor2=" border-bottom-color:"+b.customHeaderColor);""!=b.customFooterColor&&(G_StrCustomFooterColor=" color:"+b.customFooterColor);""!=b.customProgressBarColor&&(G_StrCustomProgressBarColor=" background:"+b.customProgressBarColor);G_StrCustomProgressBarColor2=" border-color:"+b.customProgressBarColor;""!=b.customTextColor&&(G_StrCustomTextColor=" color:"+b.customTextColor);var e=getDialogDocument(),
f=e.getElementById("DEXT_fiVe_UP_ly_wrapper");f&&f.parentNode.removeChild(f);(f=e.getElementById("DEXT_fiVe_UP_Popup_Mode"))&&f.parentNode.removeChild(f);(e=e.getElementById("dext5upload_context_menu_"+a.ID))&&e.parentNode.removeChild(e);c.____.p=b.productKey;var e=parseInt(b.height),g=getHeaderBarHeight(b),f=Math.floor((g-12)/2),g=g-f,f=f-4+"px",g=g+4+"px";_7_=b.licenseKeyEx;-1<b.height.indexOf("%")&&(e=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+
a.ID)),e=e.bottom-e.top);setFileListHeight(e,b,a);c.____.l=_L__.DE(_7_);try{c.____.l=c.____.l.split("|"),_0_=c.____.l,_1_=c.____.p}catch(k){}e="";-1<b.userRunTimeMode.indexOf("html5")&&1>b.imageQuality.quality&&(e+='<canvas id="imageCanvas" style="display:none;"></canvas>');var l=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc(),e=e+('<iframe id="download_frame" name="download_frame" frameborder="0" style="display:none;" src="'+l+'" title="DEXT5Upload Download"></iframe>')+('<div id="DEXT_fiVe_UP_wrapper" class="DEXT_fiVe_UP_file_upload_'+
b.skinName+'" style="width:100%">');if("top"==b.buttonBarPosition){var n=b.userMessage.edit,p=b.userMessage.view;if("upload"!=b.mode){n=b.showButtonBarView.length;l="";0==n&&(l+="display: none;");e+='<div id="DEXT_fiVe_UP_uploadbox_btm" style="'+l+G_StrCustomHeaderColor+'" class="DEXT_fiVe_UP_uploadbox_btm2">';e="left"==b.showViewAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==b.showViewAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_right">':e+"<ul>";e+='<li class="fbutton">';for(l=0;l<n;l++)e+=
getHtmlForBtnNameView(b.showButtonBarView[l]);e+="</li>";e+="</ul>";"left"==b.showViewAlign&&""!=b.userMessage.view&&(e+='<div class="DEXT_fiVe_UP_user_message_rt" id="DEXT_fiVe_UP_usermessage" title="'+p+'">'+p+"</div>");"right"==b.showViewAlign&&""!=b.userMessage.view&&(e+='<div class="DEXT_fiVe_UP_user_message_lt" id="DEXT_fiVe_UP_usermessage" title="'+p+'">'+p+"</div>")}else{p=b.showButtonBarEdit.length;l="";0==p&&(-1<b.userRunTimeMode.indexOf("html5")||"ieplugin"==b.userRunTimeMode)&&(l+="display:none;");
e+='<div id="DEXT_fiVe_UP_uploadbox_btm" style="'+l+G_StrCustomHeaderColor+'" class="DEXT_fiVe_UP_uploadbox_btm2">';e="left"==b.showEditAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==b.showEditAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_right">':e+"<ul>";e+='<li class="fbutton">';for(l=0;l<p;l++)e+=getHtmlForBtnNameEdit(b.showButtonBarEdit[l]);0==p&&"html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4&&(e+='<div id="files_container" class="input_file_box">',e+="<div>",e+='<form id="form_'+
a.TagID+'" method="post" enctype="multipart/form-data" encoding="multipart/form-data">',e+='<span class="input_image_add" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_add+"</span>",e+='<input id="file_'+a.TagID+'" name="file_'+a.TagID+'" type="file" onchange="fileHandler_html4(this, this.value);" onblur="focusHandler(this);"/>',e+='<input id="tab_'+a.TagID+'" name="tab_'+a.TagID+'" type="hidden" value="0"/></form>',e+="</div>",e+="</div> ");e+="</li>";e+="</ul>";
"left"==b.showEditAlign&&""!=b.userMessage.edit&&(e+='<div class="DEXT_fiVe_UP_user_message_rt" id="DEXT_fiVe_UP_usermessage" title="'+n+'">'+n+"</div>");"right"==b.showEditAlign&&""!=b.userMessage.edit&&(e+='<div class="DEXT_fiVe_UP_user_message_lt" id="DEXT_fiVe_UP_usermessage" title="'+n+'">'+n+"</div>")}e+="</div>"}e+='<div id="DEXT_fiVe_UP_content">';e+='<div class="DEXT_fiVe_UP_uploadbox">';e+='<div class="DEXT_fiVe_UP_uploadbox_tit"';"0"==b.showHeaderBar&&(e+=' style="display:none;" ');e+=
">";e+='<ul style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">';l="";l="ieplugin"!=b.userRunTimeMode||"0"!=b.showCheckBox&&"1"!=b.showCheckBox&&"3"!=b.showCheckBox?l+"display:block;":l+"display:none;";e+='<li style="height:'+g+"; padding-top:"+f+";"+l+'" class="input_chk"';l="";"ieplugin"==b.userRunTimeMode||"1"!=b.showCheckBox&&"3"!=b.showCheckBox||(l="visibility:hidden;");var e=e+('><input style="'+l+'" type="checkbox" id="chk_all_box" name="" title="checkbox" /><em></em></li>'),n=
"",n="ieplugin"!=b.userRunTimeMode||"0"!=b.showCheckBox&&"1"!=b.showCheckBox&&"3"!=b.showCheckBox?n+"margin-left:0;padding-left:35px":n+"margin-left:10px;",p="",p="0"==b.sizeColumnWidth?p+"display:none;":p+"display:block;",r="",r="0"==b.sort_ascdesc?r+"icon_up":r+"icon_down",t="";"1"==b.use_file_sort&&(t="cursor:pointer");"1"==b.folderDetailHeader&&(b.headerBarItem.splice(0,0,Dext5Upload_Lang.folder_count),b.headerBarItem.splice(0,0,Dext5Upload_Lang.folder_file_count),b.headerBarItemWidth.splice(0,
0,"70px"),b.headerBarItemWidth.splice(0,0,"70px"));for(var q=b.headerBarItem.length,u="0",l=0;l<q;l++)""==b.headerBarItemWidth[l]?b.headerBarItemWidth[l]="80px":b.headerBarItemWidth[l]||b.headerBarItemWidth.push("80px"),u+=parseInt(b.headerBarItemWidth[l]),u++;l="95%";""!=b.headerBarItem&&(l=parseInt(b.width),-1<b.width.indexOf("%")&&(l=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+a.ID)),l=l.right-l.left),l=l-u-125+"px");e+='<li id="header_bar_file_name" style="'+
t+"; height:"+g+"; padding-top:"+f+";"+n+'" class="fname"';e+=">"+Dext5Upload_Lang.file_name+'<span style="display:none;width:'+l+'" class="btn_sort"><em class='+r+"></em></span></li>";e+='<li id="header_bar_file_size" style="'+t+";right:"+u+"px; height:"+g+"; padding-top:"+f+";"+p+'" class="fsize">'+Dext5Upload_Lang.file_size+'<span style="display:none;" class="btn_sort"><em class='+r+"></em></span></li>";if(b.headerBarItem&&0<b.headerBarItem.length){e+='<li class="user_header_box" style="'+t+'">';
for(l=0;l<q;l++)b.headerBarItemWidth[l]&&(e+='<span id="user_header_bar_'+l+'" class="user_header_bar_lst" style="width: '+b.headerBarItemWidth[l]+";height:"+g+"; padding-top:"+f+';">'+b.headerBarItem[l]+'<span style="display:none;width:'+b.headerBarItemWidth[l]+'" class="btn_sort btn_sort2"><em class='+r+"></em></span></span>");e+="</li>"}else e+='<li class="user_header_box" style="display:none"></li>';e+='<li class="header_rt_type">&nbsp;</li>';e+="</ul>";e+="</div>";f="";b.initVisible||(f=' style="overflow:visible;"');
e+='<div class="DEXT_fiVe_UP_uploadbox_sec" id="DEXT_fiVe_UP_PL_holder"'+f+">";f=a.fileListHeight;e="ieplugin"==b.userRunTimeMode?e+('<div id="DEXT_fiVe_UP_Load" class="DEXT_fiVe_UP_file_load_box" style="min-height:'+f+'px; display:none;">'):e+'<div id="DEXT_fiVe_UP_Load" class="DEXT_fiVe_UP_file_load_box" style="display:none;">';e+='<div class="DEXT_fiVe_UP_file_load">';e+="</div>";e+='<span class="DEXT_fiVe_UP_file_loadty"><img src="'+b.webPath.image+'loading.gif" alt="\ub85c\ub529 \uc774\ubbf8\uc9c0" /></span>';
e+="</div>";1>b.imageQuality.quality&&(e+='<div id="DEXT_fiVe_UP_Image_processing" class="DEXT_fiVe_UP_file_load_box" style="display:none;">',e+='<div class="DEXT_fiVe_UP_file_load">',e+="</div>",e+='<span class="DEXT_fiVe_UP_file_text">Processing image...(<span id="quality_processing">0%</span>)</span>',e+="</div>");if("ieplugin"==b.userRunTimeMode)e+='<div id="DEXT_fiVe_UP_PL_Object_holder" style="width:0px; height:0px;">',e+=c._dextPluginObjectHtml(c);else{f="";f="html4"==b.userRunTimeMode&&"1"!=
b.hideListInfo||1==UPLOADTOP.DEXT5UPLOAD.browser.mobile?Dext5Upload_Lang.message_insert_file:"1"==b.hideListInfo?"":"1"==b.listViewDbclick&&"1"==b.listViewDragAndDrop?Dext5Upload_Lang.message_drag_dbclick:"1"==b.listViewDbclick?Dext5Upload_Lang.message_dbclick:"1"==b.listViewDragAndDrop?Dext5Upload_Lang.message_drag_drop:Dext5Upload_Lang.message_insert_file;g="";if("upload"!=b.mode||"1"==b.hideListInfo)g=' style="display:none;';l="";l=90>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a.fileListHeight)?""!=
g?l+'background-image:none;margin-top:-3px;" ':'style="background-image:none;margin-top:-3px;" ':"";g+=l;""==l&&(g+='"');e+='<p id="file_temp" class="txt"'+g+">"+f+"</p>";e+='<div id="DEXT_fiVe_UP_file_temp" class="DEXT_fiVe_UP_uploadbox_txt" style="position:relative; overflow-y:scroll; height:'+a.fileListHeight+'px">';e="thumbs"==b.views?e+'<ol id="file_list" class="lst_line_none olthumblst_sec"></ol>':e+('<ol id="file_list" class="lst_line_'+b.uploadBorderStyle+'"></ol>')}e+="</div>";e+="</div>";
e+='<div class="DEXT_fiVe_UP_info_area"';"0"==b.showStatusBar&&(e+=' style="display:none;" ');e+=">";e+="<ul>";f="";"0"==b.statusBarShowLimit&&(f+="display: none;");e+='<li id="basic_file_info" class="basic_file_info" style="'+f+'">';if("upload"==b.mode){if("html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4)0<b.maxTotalFileCount&&(e+=Dext5Upload_Lang.file_maximum+" ");else if(0<b.maxTotalFileCount||0<b.maxTotalFileSize)e+=Dext5Upload_Lang.file_maximum+" ";0<b.maxTotalFileCount&&(e+="<span>"+b.maxTotalFileCount+
"</span> "+Dext5Upload_Lang.file_unit+" ");if("html4"!=b.userRunTimeMode||"1"!=b.uploadMethodHtml4)0<b.maxTotalFileSize?(f=Dext5Upload_Lang.file_maximum_limit,f=f.replace("{0}","<span>"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(b.maxTotalFileSize).toString+"</span>"),e+=f):e+=Dext5Upload_Lang.file_no_limit}e+="</li>";f="";"0"==b.statusBarShowStatus&&(f+="display: none;");e+='<li class="contents_file_info" id="current_file_info" style="'+f+'">';e+='<span id="total_num">0</span> '+Dext5Upload_Lang.file_unit;
if("html4"!=b.userRunTimeMode||"1"!=b.uploadMethodHtml4||"upload"!=b.mode)e+=', <span id="total_size">0 byte</span> ';e+=' <span id="file_inserted_text">';"upload"==b.mode&&(e+=Dext5Upload_Lang.file_inserted);e+="</span>";e+="</li>";e+="</ul>";e+="</div>";e+="</div>";e+="</div>";f="";""!=b.extension.mimeAccept&&(f='accept="'+b.extension.mimeAccept+'"');g="";-1<b.userRunTimeMode.indexOf("html5")&&(b.multiFileSelect&&(g='multiple="true"'),e+='<div style="position:absolute; top:-1000px; left:-1000px; display:block;"><input type="file" id="file_'+
a.TagID+'" onchange="fileHandler(this.files, this.value);" '+f+" "+g+' tabIndex="-1" title="file" /></div>');if("top"!=b.buttonBarPosition){n=b.userMessage.edit;p=b.userMessage.view;if("upload"!=b.mode){n=b.showButtonBarView.length;l="";0==n&&(l+="display: none;");e+='<div id="DEXT_fiVe_UP_uploadbox_btm" style="'+l+G_StrCustomHeaderColor+'" class="DEXT_fiVe_UP_uploadbox_btm">';e="left"==b.showViewAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==b.showViewAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_right">':
e+"<ul>";e+='<li class="fbutton">';for(l=0;l<n;l++)e+=getHtmlForBtnNameView(b.showButtonBarView[l]);e+="</li>";e+="</ul>";"left"==b.showViewAlign&&""!=b.userMessage.view&&(e+='<div class="DEXT_fiVe_UP_user_message_rb" id="DEXT_fiVe_UP_usermessage" title="'+p+'">'+p+"</div>");"right"==b.showViewAlign&&""!=b.userMessage.view&&(e+='<div class="DEXT_fiVe_UP_user_message_lb" id="DEXT_fiVe_UP_usermessage" title="'+p+'">'+p+"</div>")}else{p=b.showButtonBarEdit.length;l="";0==p&&(-1<b.userRunTimeMode.indexOf("html5")||
"ieplugin"==b.userRunTimeMode||"form"==b.subMode)&&(l+="display:none;");e+='<div id="DEXT_fiVe_UP_uploadbox_btm" style="'+l+G_StrCustomHeaderColor+'" class="DEXT_fiVe_UP_uploadbox_btm">';e="left"==b.showEditAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==b.showEditAlign?e+'<ul class="DEXT_fiVe_UP_uploadbox_right">':e+"<ul>";e+='<li class="fbutton">';for(l=0;l<p;l++)e+=getHtmlForBtnNameEdit(b.showButtonBarEdit[l]);0==p&&"html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4&&(e+='<div id="files_container" class="input_file_box">',
e+="<div>",e+='<form id="form_'+a.TagID+'" method="post" enctype="multipart/form-data" encoding="multipart/form-data">',e+='<span class="input_image_add" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_add+"</span>",e+='<input id="file_'+a.TagID+'" name="file_'+a.TagID+'" type="file" onchange="fileHandler_html4(this, this.value);" onblur="focusHandler(this);"/>',e+='<input id="tab_'+a.TagID+'" name="tab_'+a.TagID+'" type="hidden" value="0"/></form>',e+="</div>",
e+="</div> ");e+="</li>";e+="</ul>";"left"==b.showEditAlign&&""!=b.userMessage.edit&&(e+='<div class="DEXT_fiVe_UP_user_message_rb" id="DEXT_fiVe_UP_usermessage" title="'+n+'">'+n+"</div>");"right"==b.showEditAlign&&""!=b.userMessage.edit&&(e+='<div class="DEXT_fiVe_UP_user_message_lb" id="DEXT_fiVe_UP_usermessage" title="'+n+'">'+n+"</div>")}e+="</div>"}e+="</div>";e+=makeTransferHTML(a);e+=makeImgPreViewHTML(a);document.body.innerHTML=e;var z=document.getElementById("DEXT_fiVe_UP_uploadbox_btm"),
w=z.getElementsByTagName("button"),y=10;setTimeout(function(){for(var b=0;b<w.length;b++)y=y+parseInt(w[b].offsetWidth,10)+8;(b=document.getElementById("files_container"))&&(y=y+parseInt(b.offsetWidth,10)+8);if(b=document.getElementById("DEXT_fiVe_UP_usermessage")){var c;-1<a._config.width.indexOf("%")?(c=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(z),c=c.right-c.left):c=parseInt(a._config.width,10);try{y=c-y,b.style.width=y+"px"}catch(e){}}},1);if("ieplugin"==a._config.userRunTimeMode)if((e=document.getElementById("dext5PL"))&&
(e.nAtMode||void 0!=e.nAtMode))document.getElementById("DEXT_fiVe_UP_PL_Object_holder").style.width="",document.getElementById("DEXT_fiVe_UP_PL_Object_holder").style.height="",document.getElementById("DEXT_fiVe_UP_Load").style.display="none",a._config.initVisible&&(e.style.display="block");else if(document.getElementById("DEXT_fiVe_UP_Load").style.display="block",e&&(e.style.display="none"),"1"==b.pluginInstallType||"2"==b.pluginInstallType)"0"==b.usePluginInstallGuide?requestPluginInstall(window,
document):(e=640,UPLOADTOP.DEXT5UPLOAD.browser.ie&&(e-=4),a.pluginInstallGuidePopup=Dext5_PopupCenter(b.webPath.pluginInstallGuide,"DEXT5_Plugin_Install_Guide",e,400,void 0,void 0,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no"));"html4"==b.userRunTimeMode&&"0"==b.uploadMethodHtml4&&(e="*.*",f=Dext5Upload_Lang.file_type_all_description,"1"==b.extension.allowOrLimit&&""!=b.extension.extToString&&(e=UPLOADTOP.DEXT5UPLOAD.util.getMimeFilterEx(b.extension.extToString),
f=Dext5Upload_Lang.file_type_description),l=g="",UPLOADTOP.DEXT5UPLOAD.isRelease?(g=b.webPath.js+"swfupload/swfupload.swf",l=b.webPath.js+"swfupload/swfupload_FP9.swf"):(g=b.webPath.jsDev+"swfupload/swfupload.swf",l=b.webPath.jsDev+"swfupload/swfupload_FP9.swf"),n=G_StrCustomFooterColor,""==G_StrCustomFooterColor&&(n="color: #464646"),new SWFUpload({upload_url:b.handlerUrl,post_params:{},file_types:e,file_types_description:f,file_upload_limit:0,file_size_limit:"0",swfupload_loaded_handler:dext5upload_swf_Loaded,
file_dialog_start_handler:dext5upload_swf_fileDialogStart,file_queued_handler:dext5upload_swf_fileQueued,file_dialog_complete_handler:dext5upload_swf_fileDialogComplete,file_queue_error_handler:dext5upload_swf_fileQueueError,upload_start_handler:dext5upload_swf_uploadStart,upload_progress_handler:dext5upload_swf_uploadProgress,upload_success_handler:dext5upload_swf_uploadSuccess,upload_complete_handler:dext5upload_swf_uploadComplete,upload_error_handler:dext5upload_swf_uploadError,button_placeholder_id:"button_add",
button_width:76,button_height:23,button_text:'<button type="button" id="button_add" value="'+Dext5Upload_Lang.btn_add+'">'+Dext5Upload_Lang.btn_add+"</button>",button_text_style:"button {"+n+";font-family:dotum,\ub3cb\uc6c0,tahoma,sans-serif}",button_text_top_padding:4,button_text_left_padding:12,button_action:0==b.multiFileSelect?SWFUpload.BUTTON_ACTION.SELECT_FILE:SWFUpload.BUTTON_ACTION.SELECT_FILES,flash_url:g,flash9_url:l,custom_settings:{},debug:!1},a.ID,G_StrCustomHeaderColor,G_StrCustomFooterColor));
setPage(a);""!=b.licenseKeyEx&&setTimeout(_fn_0,2E3)};if("1"!=b.hybridDownload)e(a);else{var f;f=""+('<div id="DEXT_fiVe_UP_Load" class="DEXT_fiVe_UP_file_load_box" style="width:'+b.width+"; height:"+b.height+';">');f=f+'<div class="DEXT_fiVe_UP_file_load"></div>'+('<span class="DEXT_fiVe_UP_file_loadty2"><img src="'+b.webPath.image+'loading2.gif" alt="\ub85c\ub529 \uc774\ubbf8\uc9c0" /></span>');f+="</div>";f+='<iframe id="download_frame" name="download_frame" style="display:none;" title="DEXT5Upload Download"></iframe>';
UPLOADTOP.DEXT5UPLOAD.browser.ie&&9>=UPLOADTOP.DEXT5UPLOAD.browser.ieVersion&&(f+='<iframe id="hybrid_response_1" name="hybrid_response_1" style="display:none;" title="DEXT5Upload Hybrid"></iframe>',f+='<iframe id="hybrid_response_2" name="hybrid_response_2" style="display:none;" title="DEXT5Upload Hybrid"></iframe>');document.body.innerHTML=f;b.hybridParam=createHybridParamInit(b);var g=setHybridParam(b.hybridParam,[["p00","IT"]]),g=makeEncryptHybridParam(g);try{UPLOADTOP.window.localStorage.d5_hybrid_port&&
5==UPLOADTOP.window.localStorage.d5_hybrid_port.length&&(a.hybridPort=UPLOADTOP.window.localStorage.d5_hybrid_port)}catch(k){}var n=0,p=null,l=function(){if(0==n)if(n++,"1"==a._config.useInstallGuide){var b=640;UPLOADTOP.DEXT5UPLOAD.browser.ie&&(b-=4);p=Dext5_PopupCenter(a._config.webPath.installGuide,"DEXT5_Hybrid_Install_Guide",b,400,void 0,void 0,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no")}else requestHybridInstall(window,document)};
if(UPLOADTOP.DEXT5UPLOAD.browser.ie&&9>=UPLOADTOP.DEXT5UPLOAD.browser.ieVersion){var q=!1,r=!1,u={length:0};f=a.hybridUrl;-1<UPLOADTOP.location.host.indexOf("127.0.0.1")&&(f="http://localhost:");""!=a.hybridPort?(u[a.hybridPort]=[f+a.hybridPort,"hybrid_response_1"],u[0]=a.hybridPort,u.length=1):(u["47307"]=[f+"47307","hybrid_response_1"],u[0]="47307",u["47129"]=[f+"47129","hybrid_response_2"],u[1]="47129",u.length=2);var w,y;1<=u.length&&(w=document.getElementById("hybrid_response_1"),UPLOADTOP.DEXT5UPLOAD.util.addEvent(w,
"load",function(){try{w.contentWindow.postMessage("check","*")}catch(b){}}));2==u.length&&(y=document.getElementById("hybrid_response_2"),UPLOADTOP.DEXT5UPLOAD.util.addEvent(y,"load",function(){try{y.contentWindow.postMessage("check","*")}catch(b){}}));var t=!1,z=null,B=null,x=null,A=!1,D=function(b){b=UPLOADTOP.DEXT5UPLOAD.util.trim(b.data);t=!0;var c=b.split("|");try{c[2]=c[2].split("<")[0]}catch(f){}if(0==c[0].indexOf("100")||0==c[0].indexOf("700"))if("1000"==c[0]){A=!0;a.hybridPort=c[1];try{UPLOADTOP.window.localStorage.d5_hybrid_port=
a.hybridPort}catch(g){}UPLOADTOP.DEXT5UPLOAD.util.removeEvent(window,"message",D);var k=setInterval(function(){1==q&&1==r&&(clearInterval(k),1<=u.length&&w.parentNode.removeChild(w),2==u.length&&y.parentNode.removeChild(y),"1"==a._config.useInstallGuide&&null!=p&&p.close(),createEditorPluginObject(),e(a))},1E3)}else 0==b.indexOf("7000")?(l(),setTimeout(function(){var b=setInterval(function(){0==A?UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u[c[1]][0],u[c[1]][1],C):clearInterval(b)},1E3)},1E3)):
setTimeout(function(){UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u[c[1]][0],u[c[1]][1],C)},1E3)};UPLOADTOP.DEXT5UPLOAD.util.addEvent(window,"message",D);var C=[["d00",g]];1<=u.length&&(B=setInterval(function(){try{w.contentWindow.document.getElementById("checkDiv")}catch(b){q=!0}},500));2==u.length?x=setInterval(function(){try{y.contentWindow.document.getElementById("checkDiv")}catch(b){r=!0}},500):r=!0;var v=0,z=setInterval(function(){1==q&&1==r&&0==t?(v++,3==v&&l(),q=!1,2==u.length&&(r=!1),
setTimeout(function(){H()},1E3)):1==q&&1==r&&1==t&&(clearInterval(B),clearInterval(x),clearInterval(z))},1E3),H=function(){for(var b=0;b<u.length;b++)UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u[u[b]][0],u[u[b]][1],C)};H()}else{var E=0,F=!1,G=[],J=0;""!=a.hybridPort?G.push([UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest(),a.hybridPort]):(G.push([UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest(),"47307"]),G.push([UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest(),"47129"]));var I=function(b,
c){b.open("POST",a.hybridUrl+c,!0);b.setRequestHeader("Content-type","application/x-www-form-urlencoded");b.onreadystatechange=function(){if(4==b.readyState)if(200==b.status){var f=b.responseText,g=f.split("|");try{g[2]=g[2].split("<")[0]}catch(k){}if(0==g[0].indexOf("100")||0==g[0].indexOf("700"))if(0==f.indexOf("1000")){a.hybridPort=c;try{UPLOADTOP.window.localStorage.d5_hybrid_port=a.hybridPort}catch(n){}"1"==a._config.useInstallGuide&&null!=p&&p.close();createEditorPluginObject();e(a)}else 0==
f.indexOf("7000")?l():0==f.indexOf("1001")&&(F=!0),setTimeout(function(){I(b,c)},1E3)}else if(J++,J==G.length)for(J=0,E++,1==E&&0==F&&l(),f=0;f<G.length;f++)I(G[f][0],G[f][1])};b.send(g)};for(f=0;f<G.length;f++)I(G[f][0],G[f][1])}}},_dextPluginObjectHtml:function(b){var a=b._config,c=a.licenseKey;"1"==a.licenseKeySE&&(c=UPLOADTOP.DEXT5UPLOAD.util.base64_encode(c));var e="dext5.cab";-1<navigator.userAgent.search("x64")&&(e="dext5_x64.cab");var f="",e=a.webPath.plugin+e;!a.pluginInstallType||"1"!=a.pluginInstallType.toString()&&
"2"!=a.pluginInstallType.toString()||(e="");var g="",g=a.initVisible?'WIDTH="100%" HEIGHT="'+b.fileListHeight+'px" style="margin:0;"':'WIDTH="0px" HEIGHT="'+b.fileListHeight+'px" style="margin:0; display:none;"',f="1"==a.hybridDownload?f+('<object id="dext5PL" name="dext5PL" classid="CLSID:F7FEF85C-B9A4-421C-BD01-099E534ABFB0" border="0" '+g+">"):f+('<object id="dext5PL" name="dext5PL" classid="CLSID:F7FEF85C-B9A4-421C-BD01-099E534ABFB0" codebase="'+e+"#version="+a.plugin_version+'" border="0" '+
g+">"),e="",e="upload"==a.mode?"0":"view"==a.mode?"1":"open"==a.mode?"2":"3",f=f+('<param name="nAtMode" value="'+e+'">'),e="1";"thumbs"==a.views&&(e="2",f+='<param name="nThumbSize" value="120">');f=f+('<param name="nViewMode" value="'+e+'">')+('<param name="strSkinName" value="'+a.skinName+'">');f+='<param name="nMaxTotalCount" value="'+a.maxTotalFileCount+'">';f+='<param name="nMaxTotalSize" value="'+a.maxTotalFileSize+'">';f+='<param name="nMaxOneFileSize" value="'+a.maxOneFileSize+'">';e="";
e=1==a.multiFileSelect?"1":"0";f+='<param name="nMultiFileSelect" value="'+e+'">';f+='<param name="sPostURL" value="'+a.handlerUrl+'">';f+='<param name="strProductKey" value="'+a.productKey+'">';f+='<param name="strLicenseKey" value="'+c+'">';f+='<param name="nS" value="'+a.licenseKeySE+'">';f+='<param name="strLang" value="'+a.lang+'">';f+='<param name="nCanSelectFileDbClick" value="'+a.listViewDbclick+'">';f+='<param name="nCanSelectFileDragDrop" value="'+a.listViewDragAndDrop+'">';f+='<param name="nChunkSize" value="'+
a.partialSize+'">';f+='<param name="strFileNameRule" value="'+a.fileNameRule+'">';f+='<param name="strFileNameRuleEx" value="'+a.fileNameRuleEx+'">';f+='<param name="strFolderNameRule" value="'+a.folderNameRule+'">';f+='<param name="strFileFilter" value="'+a.extension.extToString+'">';f+='<param name="nAllowOrLimit" value="'+a.extension.allowOrLimit+'">';e=c="";"1"==a.useLogoImage.use&&(c=a.useLogoImage.logoPath,e=a.useLogoImage.logoVer,f+='<param name="strLogoURL" value="'+c+"|"+e+'">');f+='<param name="nHideListInfo" value="'+
a.hideListInfo+'">';c="1";if(""==a.uploadBorderStyle||"none"==a.uploadBorderStyle)c="0";f+='<param name="nShowGridLine" value="'+c+'">';f+='<param name="strWebFileColor" value="'+a.customWebFileColor+'">';f+='<param name="strSkinColor" value="'+a.customHeaderColor+","+a.customFooterColor+","+a.customProgressBarColor+","+a.customTextColor+'">';f+='<param name="nResumeUp" value="'+a.resumeUpload+'">';f+='<param name="nResumeDown" value="'+a.resumeDownload+'">';f+='<param name="nUseFolderTransfer" value="'+
a.folderTransfer+'">';"1"==a.folderDetailHeader&&(f+='<param name="nFolderDetailHeader" value="1">');f+='<param name="nUseServerFileSize" value="'+a.useServerFileSize+'">';f+='<param name="nUseAddFileBeforeEvent" value="'+a.useAddEvent+'">';f+='<param name="nUseDeleteEvent" value="'+a.useDeleteEvent+'">';f+='<param name="nUseViewOrOpenEvent" value="'+a.useViewOrOpenEvent+'">';f+='<param name="nUseDownloadBeforeEvent" value="'+a.useDownloadEvent+'">';f+='<param name="nTimeout" value="'+a.timeout+'">';
f+='<param name="nAutomaticConnection" value="'+a.automaticConnection+'">';f+='<param name="nShowFolderView" value="'+a.showFolderView+'">';f+='<param name="nShowHeader" value="0">';f+='<param name="strMessageTitle" value="'+a.messageTitle+'">';f+='<param name="nMsgSendToBroswer" value="'+a.useScriptEventControl+'">';"0"!=a.showCheckBox&&"3"!=a.showCheckBox&&(f+='<param name="nShowCheckBox" value="3">');f+='<param name="nHideContextMenu" value="'+a.hideContextMenu+'">';f+='<param name="nSizeColumnWidth" value="'+
a.sizeColumnWidth+'">';f+='<param name="strRemoveContextList" value="'+a.removeContextItem+'">';f+='<param name="nDisableDeleteMessage" value="'+a.disableAlertMessage.disableDeleteConfirm+'">';f+='<param name="strNTLMAuth" value="'+a.NTLM+'">';f+='<param name="HugeBaseSize" value="'+a.largeFiles.markSize+'">';0!=a.largeFiles.markBaseTotalSize&&(f+='<param name="HugeBaseTotalSize" value="'+a.largeFiles.markBaseTotalSize+'">');f+='<param name="HugeText" value="'+(""==a.largeFiles.text?Dext5Upload_Lang.large_files:
a.largeFiles.text)+'">';f+='<param name="HugeColor" value="'+a.largeFiles.color+'">';f+='<param name="HugeMaxFileSize" value="'+a.largeFiles.maxTotalSize+'">';f+='<param name="HugeMaxCount" value="'+a.largeFiles.maxCount+'">';"1"==a.use_file_sort&&(f+='<param name="strSortOptValue" value="0'+a.sort_field+a.sort_ascdesc+a.auto_sort+'">');f+='<param name="nUseMoveMenu" value="'+a.fileMoveContextMenu+'">';"1"==a.imgPreView&&(f+='<param name="strThumbWndWH" value="'+a.imgPreViewWidth+","+a.imgPreViewHeight+
'">');f+='<param name="nUseSingleSelect" value="'+a.useSingleSelect+'">';f+='<param name="nAllowedZeroFileSize" value="'+a.allowedZeroFileSize+'">';f+='<param name="nSilentUpload" value="'+a.silentUpload+'">';"1"==a.security.encryptParam&&(f+='<param name="nPe" value="1">');"1"==a.security.fileExtensionDetector&&(f+='<param name="nSP1" value="1">');"1"==a.security.fileIntegrity&&(f+='<param name="nSP2" value="1">');"1"==a.security.fileEncrypt&&(f+='<param name="nSP3" value="1">');"1"==a.highSpeed&&
(f+='<param name="nSP4" value="1">');f+='<param name="strGroupID" value="'+a.groupId+'">';c=a.headerBarItem.length;e="";for(g=0;g<c;g++){var k=a.headerBarItemAlign[g];"left"==k?k="0":"right"==k?k="1":"center"==k&&(k="2");e+=a.headerBarItemWidth[g]+" "+a.headerBarItem[g]+" "+k;c-1>g&&(e+="|")}""!=e&&(f+='<param name="strAddListHeader" value="'+e+'">');"none"==a.developLang.toLowerCase()&&""!=a.licenseKeyEx&&(f+='<param name="strLicenseKeyEx" value="'+a.licenseKeyEx+'">');""!=a.defaultDownloadPath&&
(f+='<param name="strDefaultSavePath" value="'+a.defaultDownloadPath+'">');f="standard"!=a.uploadTransferWindow.view?f+'<param name="nTransViewType" value="1">':f+'<param name="nTransViewType" value="0">';f="1"==a.transferOpenFile?f+'<param name="nNotAllowIfOpen" value="0">':f+'<param name="nNotAllowIfOpen" value="1">';""!=a.savePathSetting&&(f+='<param name="strSavePathOpt" value="'+a.savePathSetting+'">');f+='<param name="nSelectByClicked" value="'+a.selectByClicked+'">';f+='<param name="strUploadID" value="'+
b.ID+'">';""!=a.fileFilterEx&&(f+='<param name="strFileFilterEx" value="'+a.fileFilterEx+'">');"1"==a.useZipDownload&&(f+='<param name="nUseZipDownload" value="'+a.useZipDownload+'">');f+='<param name="strKeepLang" value="'+a.keepLang+'">';f+='<param name="nUseHybrid" value="'+a.hybridDownload+'">';1==a.hybridMethod&&(f+='<param name="nHybridMethod" value="1">');f+='<param name="nAllowedRealTimeAdd" value="'+a.allowedRealtimeDownloadAdd+'">';f+='<param name="nShowTransItemCount" value="'+a.hybridShowItemCount+
'">';f+='<param name="nModalWindow" value="'+a.hybridWindowMode+'">';0<a.downloadPartialSize&&(f+='<param name="nPartialSize" value="'+a.downloadPartialSize+'">');f+='<param name="nSkipSentFile" value="'+a.skipSentFile+'">';f+='<param name="strKeepVersion" value="'+a.pluginKeepVersion+'">';f+='<param name="nForceOverwrite" value="'+a.forceOverwrite+'">';return f+="</object>"},_dextCommands:function(b,a,c,e){G_LAST_CMD=a;switch(a){case "add_file":selectFile();break;case "remove_current_file":deleteCurrentFile(e);
break;case "remove_selected_file":deleteSelectedFile();break;case "remove_all_file":deleteAllFile();break;case "open_seleceted_file":openFile();break;case "open_current_file":openCurrFile(e);break;case "download_seleceted_file":downloadFile();break;case "download_current_file":downloadCurrFile(e);break;case "download_all_file":downloadAllFile();break;case "move_first":moveFirstFile();break;case "move_forward":moveForwardFile();break;case "move_backward":moveBackwardFile();break;case "move_end":moveEndFile();
break;case "setting":setting();break;case "save_and_open":saveAndOpenEx();break;case "download_zipfile":downloadZipfile();break;case "download_all_zipfile":downloadAllZipfile()}},_setLazyLoadFrame:function(b){"0"==UPLOADTOP.G_CURRUPLOADER._config.hideContextMenu&&DEXT5UPLOAD_CONTEXT._create(b)}};K__+="FA";R[0]="h";SIL[1]="u";K__+="BG";function dext_frame_loaded_event(b,a,c){_dext5_uploader=new Dext5Upload_Uploader(b,a,c);_dext5_uploader.start(a,_dext5_uploader)}
function setPage(b){var a=document.getElementById("DEXT_fiVe_UP_wrapper");UPLOADTOP.DEXT5UPLOAD.util.addEvent(window,"unload",_uploadiframe_unload);"1"==UPLOADTOP.G_CURRUPLOADER._config.listViewDragAndDrop&&-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")&&!UPLOADTOP.DEXT5UPLOAD.browser.mobile&&(UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"dragenter",dragenter),UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"dragover",dragover),UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"drop",drop));if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDropzone&&
-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")&&!UPLOADTOP.DEXT5UPLOAD.browser.mobile)for(var c=UPLOADTOP.document.getElementsByTagName("*"),e=c.length,f=0;f<e;f++)0==c[f].className.indexOf("dext5dropzone")&&null==c[f].getAttribute("dext_event")&&(UPLOADTOP.DEXT5UPLOAD.util.addEvent(c[f],"dragenter",dragenterDropZone),UPLOADTOP.DEXT5UPLOAD.util.addEvent(c[f],"dragleave",dragleaveDropZone),UPLOADTOP.DEXT5UPLOAD.util.addEvent(c[f],"dragover",dragoverDropZone),UPLOADTOP.DEXT5UPLOAD.util.addEvent(c[f],
"drop",dropDropZone),c[f].setAttribute("dext_event","1"));UPLOADTOP.DEXT5UPLOAD.util.addEvent(a.parentNode,"keydown",_layer_Keydown);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a.parentNode,"keyup",_layer_Keyup);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a.parentNode,"click",_layer_filebtn);UPLOADTOP.DEXT5UPLOAD.util.addEvent(UPLOADTOP,"keydown",_top_Keydown);UPLOADTOP.DEXT5UPLOAD.util.addEvent(UPLOADTOP,"click",_top_filebtn);-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")&&!UPLOADTOP.DEXT5UPLOAD.browser.mobile&&
UPLOADTOP.DEXT5UPLOAD.util.addEvent(document.body,"paste",_layer_PasteEvent);if("1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort){var g=document.getElementById("header_bar_file_name"),k=document.getElementById("header_bar_file_size");UPLOADTOP.DEXT5UPLOAD.util.addEvent(g,"click",function(){fileListSort("0",g)});UPLOADTOP.DEXT5UPLOAD.util.addEvent(k,"click",function(){fileListSort("1",k)});if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(a=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,
f=0;f<a;f++)c=document.getElementById("user_header_bar_"+f),UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"click",function(b,a){return function(){fileListSort(b,a)}}((2+f).toString(),c))}f=document.getElementById("chk_all_box");UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"click",checkAll);f=document.getElementById("DEXT_fiVe_UP_close_btn");null!=f&&UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"click",function(){uploadCancel()});f=document.getElementById("DEXT_fiVe_UP_close_btn2");null!=document.getElementById("DEXT_fiVe_UP_Popup_Mode")&&
UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"click",function(){closeBtnPreViewMode()});f=document.getElementById("DEXT_fiVe_UP_upload_cancel");null!=f&&UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"click",function(){uploadCancel()});if("1"==b._config.listViewDbclick&&-1<b._config.userRunTimeMode.indexOf("html5")&&!UPLOADTOP.DEXT5UPLOAD.browser.mobile){f=document.getElementById("file_temp").parentNode;if(null!=f){UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"dblclick",function(a){var c=UPLOADTOP.DEXT5UPLOAD.util.getParentbyTagName(a.target?
a.target:a.srcElement,"ol");"upload"==b._config.mode&&null==c&&"block"!=b.frameWin.document.getElementById("DEXT_fiVe_UP_Load").style.display&&selectFile(a)});var n=function(b){var a=b.target?b.target:b.srcElement;try{UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b)}catch(c){}if(null==UPLOADTOP.DEXT5UPLOAD.util.getParentbyTagName(a,"ol")){b=document.getElementById("file_list").getElementsByTagName("input");for(var a=b.length,e=0;e<a;e++)b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")&&
fileListNoneSelection(b[e])}}}UPLOADTOP.DEXT5UPLOAD.util.addEvent(f,"click",n)}"ieplugin"!=b._config.userRunTimeMode&&"0"==b._config.hideContextMenu&&(n=document.getElementById("DEXT_fiVe_UP_file_temp").parentNode,UPLOADTOP.DEXT5UPLOAD.util.addEvent(n,"contextmenu",uploadContextMenu));setTabOrder();if("ieplugin"!=b._config.userRunTimeMode){var p=document.getElementById("download_frame");UPLOADTOP.DEXT5UPLOAD.util.addEvent(p,"load",function(){p.contentWindow.postMessage("check","*");try{var a=p.contentWindow.document.body.innerHTML;
if(a&&""!=UPLOADTOP.DEXT5UPLOAD.util.trim(a)&&"[FAIL]"==a.substring(0,6)){var a=a.substring(6),a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(a):Dext5Base64.makeDecryptReponseMessage(a),c=a.split("|");if("error"==c[0])try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+c[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,c[1],c[2]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,c[1],eval("Dext5Upload_Lang.error_info.error_code_"+
c[1]))}catch(e){}}}catch(f){}},!0);window.postMessage&&UPLOADTOP.DEXT5UPLOAD.util.addEvent(window,"message",function(a){var c=UPLOADTOP.DEXT5UPLOAD.util.trim(a.data);if(0==c.indexOf("[OK]"))c=c.substring(4),c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),"d"==c&&(clearInterval(G_zipInterval),displayDownloadReady(!1),download_request(G_zipIntervalData.downloadUrl,G_zipIntervalData.downloadLIST,
G_zipIntervalData.zipGuid),G_zipIntervalData.downloadUrl="",G_zipIntervalData.zipGuid="",G_zipIntervalData.downloadLIST=[]);else if(0==c.indexOf("[FAIL]")){c=c.substring(6);c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c);a=[];a=c.split("|");"error"==a[0]&&(null!=G_zipInterval&&clearInterval(G_zipInterval),displayDownloadReady(!1));try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+
a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],a[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]),f)}catch(e){}}else if((c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c))&&""!=c)if(a=c.split("|"),"error"==a[0]){var f=getUploadedFileListObj();try{if("019"==a[1])"0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.fileExtensionDetect?
alert(Dext5Upload_Lang.message_file_ext_detect_html4):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],a[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]),f);else if("026"==a[1]){var g=eval("Dext5Upload_Lang.error_info.error_code_"+a[1]),g=g.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize).toString);alert(g)}else void 0==eval("Dext5Upload_Lang.error_info.error_code_"+
a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],a[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(b.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]),f)}catch(k){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();var n=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==n.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(n.G_TagID,n.G_LocalFileObject),n.G_LocalFileObject=
[],n.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID));closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else if("size"==a[0])html4GetSize(c);else try{var n=parseInt(a[3],10),f={size:n,numberOfChunks:1},p,B,x=a[1],A=x.indexOf("::");-1<A?(p=x.substring(0,A),B=x.substring(A+2)):(p=RESULTFILELIST[uploaders[UPLOADIDX]].originName,B=resultDataAry[0]);RESULTFILELIST[uploaders[UPLOADIDX]].fileSize=n;RESULTFILELIST[uploaders[UPLOADIDX]].uploadPath=B;RESULTFILELIST[uploaders[UPLOADIDX]].uploadName=
a[2];RESULTFILELIST[uploaders[UPLOADIDX]].status="complete";RESULTFILELIST[uploaders[UPLOADIDX]].logicalPath="";RESULTFILELIST[uploaders[UPLOADIDX]].originName=p;4==a.length?RESULTFILELIST[uploaders[UPLOADIDX]].responseCustomValue="":5==a.length?RESULTFILELIST[uploaders[UPLOADIDX]].responseCustomValue=a[4]:6==a.length&&(RESULTFILELIST[uploaders[UPLOADIDX]].responseCustomValue=a[4],RESULTFILELIST[uploaders[UPLOADIDX]].groupId=a[5],UPLOADTOP.G_CURRUPLOADER._config.groupId=a[5]);""==RESULTFILELIST[uploaders[UPLOADIDX]].groupId&&
""!=UPLOADTOP.G_CURRUPLOADER._config.groupId&&(RESULTFILELIST[uploaders[UPLOADIDX]].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId);clearInterval(intervalObj);uploadProgress(n,1,f,uploaders[UPLOADIDX]);UPLOADIDX>=uploaders.length?totalUploadComplete():UploadFormSingle()}catch(D){}})}n=getDialogDocument();"ieplugin"!=b._config.userRunTimeMode&&0==!!n.getElementById("DEXT_fiVe_UP_ly_wrapper")&&(f=document.getElementById("DEXT_fiVe_UP_ly_wrapper"),n.body.appendChild(f));"ieplugin"!=b._config.userRunTimeMode&&
0==!!n.getElementById("DEXT_fiVe_UP_Popup_Mode")&&(f=document.getElementById("DEXT_fiVe_UP_Popup_Mode"),n.body.appendChild(f));setTimeout(function(){try{if("ieplugin"==b._config.userRunTimeMode)if(dext5PL_init(),Dext5PL&&(Dext5PL.nAtMode||void 0!=Dext5PL.nAtMode)){document.getElementById("DEXT_fiVe_UP_Load").style.display="none";if("1"==b._config.usePluginInstallGuide&&null!=b.pluginInstallGuidePopup)try{b.pluginInstallGuidePopup.close()}catch(a){}UPLOADTOP.G_CURRUPLOADER._config.initVisible&&(Dext5PL.style.display=
"block");try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnCreationComplete(b.ID)}catch(c){}}else var e=setInterval(function(){if(Dext5PL&&(Dext5PL.nAtMode||void 0!=Dext5PL.nAtMode)){document.getElementById("DEXT_fiVe_UP_PL_Object_holder").style.width="";document.getElementById("DEXT_fiVe_UP_PL_Object_holder").style.height="";document.getElementById("DEXT_fiVe_UP_Load").style.display="none";if("1"==UPLOADTOP.G_CURRUPLOADER._config.usePluginInstallGuide&&null!=UPLOADTOP.G_CURRUPLOADER.pluginInstallGuidePopup)try{UPLOADTOP.G_CURRUPLOADER.pluginInstallGuidePopup.close()}catch(a){}UPLOADTOP.G_CURRUPLOADER._config.initVisible&&
(Dext5PL.style.display="block");try{dext5PL_init(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnCreationComplete(b.ID)}catch(c){}clearInterval(e)}else if("1"==b._config.pluginInstallType||"2"==b._config.pluginInstallType){var f=document.getElementById("DEXT_fiVe_UP_PL_Object_holder"),g=document.getElementById("dext5PL"),k=b._dextPluginObjectHtml(b);g&&(Dext5PL=null,f.removeChild(g));f.innerHTML=k;document.getElementById("dext5PL")&&(Dext5PL=document.getElementById("dext5PL"))}},200);else if(Dext5Upload_Uploader.prototype._setLazyLoadFrame(b.ID),
"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)var f=setInterval(function(){swfLoadedFlag&&(clearInterval(f),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnCreationComplete(b.ID))},200);else UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnCreationComplete(b.ID)}catch(g){}},100)}
function _uploadiframe_unload(b){try{var a=getUploaderFromUnloadEvent(b);if(null==a||void 0==a)a=UPLOADTOP.G_CURRUPLOADER;"1"==a._config.autoDestroy&&UPLOADTOP.DEXT5UPLOAD.Destroy(a.ID)}catch(c){}}K__+=R[0];R[1]="t";function uploadContextMenu(b){b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));setContextMenu(b);UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b)}
function _layer_Keydown(b){b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));var a=UPLOADTOP.G_CURRUPLOADER,c="which"in b?b.which:b.keyCode,e=b.target?b.target:b.srcElement;13==c&&e.id=="file_"+UPLOADTOP.G_CURRUPLOADER.TagID&&UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b);var f=b.ctrlKey;"ieplugin"==a._config.userRunTimeMode&&"upload"==a._config.mode&&1==f&&86==c&&(UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b),a.frameWin.Dext5PL.DoKeyDown(1,0,"v"));32==c&&e.id=="file_"+UPLOADTOP.G_CURRUPLOADER.TagID&&
(G_FileHandlerControl=1);if(1==f)if(b.altKey&&48==c){e="- DEXT5 Upload Information -\n  Client Ver. "+UPLOADTOP.DEXT5UPLOAD.ReleaseVer;e+="\n  Server Ver. "+UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer;if("ieplugin"==a._config.userRunTimeMode)try{e+="\n  Client Cab Ver. "+a.frameWin.Dext5PL.GetCabFileVersion()}catch(g){}alert(e)}else b.altKey||86!=c||_layer_PasteEventMake(b);"ieplugin"==a._config.userRunTimeMode||"upload"!=a._config.mode||"form"==a._config.subMode||f||b.altKey||46!=c||deleteSelectedFile(null,
!0)}K__+="i";
function _layer_Keyup(b){b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));var a="which"in b?b.which:b.keyCode;b=b.target?b.target:b.srcElement;if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&b.id=="file_"+UPLOADTOP.G_CURRUPLOADER.TagID&&9==a)if("0"==document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value)document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value="1",
document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundImage="url("+UPLOADTOP.G_CURRUPLOADER._config.webPath.image+"img_bt_line.png)",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundRepeat="no-repeat",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionY="2px",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionX="3px";
else if("1"==document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value){document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value="0";a=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit.length;if(1<a)for(b=0;b<a;b++)"add"==UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[b]&&b+1<a&&document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[b+1]).focus();document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundImage=
"";document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundRepeat="";document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionY="";document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionX="";G_FileHandlerControl=0}}
function _top_Keydown(b){try{116==("which"in b?b.which:b.keyCode)&&0<uploaders.length&&(UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b),UPLOADTOP.G_CURRUPLOADER.frameWin.uploadCancel())}catch(a){}}Z[0]="h";K__+="j";
function selectFile(b){b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&0!=isExecuteApi())if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.OpenFileDialog()}catch(a){}else{b=getTotalFileCount();var c=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount;(0==c||b<c)&&UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).click()}}
function checkAll(b){var a=document.getElementById("chk_all_box");if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)1==a.checked?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetCheckAllItem(1):UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetCheckAllItem(0);else{b=document.getElementById("file_list").getElementsByTagName("input");for(var c=b.length,a=a.checked,e=0;e<c;e++)"checkbox"==b[e].type&&(b[e].checked=a,fileListNoneSelection(b[e]))}}R[2]="t";
function dragenter(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID,c=getUploaderFromEvent(b);"upload"==c._config.mode&&a!=c.ID&&(UPLOADTOP.G_CURRUPLOADER=c);b.stopPropagation();b.preventDefault()}}function dragover(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID,c=getUploaderFromEvent(b);"upload"==c._config.mode&&a!=c.ID&&(UPLOADTOP.G_CURRUPLOADER=c);b.stopPropagation();b.preventDefault()}}K__+="k";R[3]="p";
function drop(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID,c=getUploaderFromEvent(b);if("upload"==c._config.mode){a!=c.ID&&(UPLOADTOP.G_CURRUPLOADER=c);if("block"==UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("DEXT_fiVe_UP_Load").style.display)return;a=b.dataTransfer;if(a.items&&a.items[0].webkitGetAsEntry)for(var c=[],e=a.items.length,f=0;f<e;f++)a.items[f].webkitGetAsEntry().isFile&&c.push(a.items[f].getAsFile());else c=a.files;fileHandler(c,a.value)}b.stopPropagation();b.preventDefault()}}
function dragenterDropZone(b){b&&(b.target.className+="_over",b.stopPropagation(),b.preventDefault())}function dragleaveDropZone(b){if(b){var a=b.target.className;b.target.className=a.substring(0,a.length-5);b.stopPropagation();b.preventDefault()}}function dragoverDropZone(b){b&&(b.stopPropagation(),b.preventDefault())}
function dropDropZone(b){if(b){var a=b.target.className;b.target.className=a.substring(0,a.length-5);var a=b.dataTransfer,c;if(a.items&&a.items[0].webkitGetAsEntry){c=[];for(var e=a.items.length,f=0;f<e;f++)a.items[f].webkitGetAsEntry().isFile&&c.push(a.items[f].getAsFile())}else c=a.files;try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_DropZoneAddItem(c,a.value,b.target.id)}catch(g){}b.stopPropagation();b.preventDefault()}}
function startUpload(b){"1"==UPLOADTOP.G_CURRUPLOADER._config.imgPreView&&closeBtnPreViewMode();if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID;b=getUploaderFromEvent(b);a!=b.ID&&(UPLOADTOP.G_CURRUPLOADER=b)}if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&0!=isExecuteApi())if(UPLOADTOP.G_CURRUPLOADER.transferCompleteEvt=!1,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.StartFileTransfer()}catch(c){}else if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||
"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"1"!=UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||!G_IntervalGetSize)if(UPLOADTOP.G_CURRUPLOADER._config.uploadCancel=!1,UPLOADTOP.G_CURRUPLOADER._config.handlerUrlCheck)if(0<RESULTFILELIST.length){for(var e=RESULTFILELIST.length,a=0;a<e;a++)RESULTFILELIST[a].file&&(b=!1,"1"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&"complete"==RESULTFILELIST[a].status&&(b=!0),b||"form"!=UPLOADTOP.G_CURRUPLOADER._config.subMode||uploadForms.push(RESULTFILELIST[a].file.parentNode),
b||uploaders.push(a));e=!0;try{e=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Start(UPLOADTOP.G_CURRUPLOADER.ID),void 0==e&&(e=!0)}catch(f){e=!0}if(0==e)UPLOADTOP.G_CURRUPLOADER.init(UPLOADTOP.G_CURRUPLOADER.ID),UPLOADTOP.G_CURRUPLOADER.isUploadComplete=!0;else if(0<uploaders.length){var g=function(){showUploadingFileList();if("html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)soketUploadStart();else if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4){var a=
getDialogDocument();a.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width="0%";a.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML="0%";a.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=UPLOADIDX+"/"+TOTALUPLOADNUM;"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&(a.getElementById("DEXT_fiVe_UP_upload_size_title").innerHTML="&nbsp;",a.getElementById("DEXT_fiVe_UP_upload_size").innerHTML="&nbsp;");UploadFormSingle()}else"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?(upload_complete_count=0,UploadSWFSingle()):0==UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported&&0==UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported?"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity||"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileEncrypt?setTimeout(function(){uploadWithoutWorker()},10):uploadWithoutWorker():"1"==UPLOADTOP.G_CURRUPLOADER._config.multiUpload?sliceAndUploadMulti():UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported?
(upload_complete_count=0,startAjaxOnprogressUpload()):sliceAndUploadSingle()};if(1>UPLOADTOP.G_CURRUPLOADER._config.imageQuality.quality){G_ChnageImageArr=[];for(j=0;j<uploaders.length;j++)0==RESULTFILELIST[uploaders[j]].file.type.indexOf("image")&&0==RESULTFILELIST[uploaders[j]].completeImgQualityPrc&&G_ChnageImageArr.push(uploaders[j]);0<G_ChnageImageArr.length?(G_CompleteImageQuality=!1,displayImageProcessing(!0),setTimeout(function(){UPLOADTOP.DEXT5UPLOAD.browser.imageProcessWorkerSupported?changeImageQuality():
changeImageQualityWithoutWorker()},10)):G_CompleteImageQuality=!0;var k=setInterval(function(){G_CompleteImageQuality&&(clearInterval(k),displayImageProcessing(!1),g())},300)}else g()}else totalUploadComplete()}else{a=UPLOADTOP.G_CURRUPLOADER.ID;try{e=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==e.G_UseAddLocalFileObject&&"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(e.G_TagID,e.G_LocalFileObject),e.G_LocalFileObject=
[],e.G_TagID=[]);UPLOADTOP.G_CURRUPLOADER.isUploadComplete=!0;"function"==typeof UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete?UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete(a):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Complete(a);try{"1"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset()}catch(n){}}catch(p){}"0"==UPLOADTOP.G_CURRUPLOADER._config.completeEventResetUse&&(fileListReset(),"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUploadEx(a,
!1))}else{e=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,e)}catch(l){}fileListReset();e=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==e.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(e.G_TagID,e.G_LocalFileObject),e.G_LocalFileObject=[],e.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}}
K__+="l";function fileListReset(){if(0<RESULTFILELIST.length)for(var b=RESULTFILELIST.length,a=0;a<b;a++)"y"!=RESULTFILELIST[a].isWebFile&&(RESULTFILELIST[a].guid="",RESULTFILELIST[a].uploadName="",RESULTFILELIST[a].uploadPath="",RESULTFILELIST[a].logicalPath="",RESULTFILELIST[a].status="ready",RESULTFILELIST[a].responseCustomValue="",RESULTFILELIST[a].groupId="")}K__+="m";
function inputFileObjectChange(b,a){for(var c=b.length,e=0;e<c;e++){var f=UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("form_"+b[e]),g=UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("file_"+b[e]),k=g.id,n=g.name;g.id=a[e].id;g.name=a[e].name;a[e].id=k;a[e].name=n;k=findPreviousObject(a[e]);n=findPreviousObject(g);a[e].parentNode.insertBefore(g,k);f.insertBefore(a[e],n)}}function findPreviousObject(b){do b=b.previousSibling;while(b&&1!=b.nodeType);return b}Z[1]="t";K__+="n";
R[4]=":";
function addFileList(b,a,c){var e=document.getElementById("file_temp");""==e.style.display&&(e.style.display="none");var f=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(b.fileSize),g=getExtension(b.originName);b.fileExt=g;var k=getIconImagePath(g),n=UPLOADTOP.G_CURRUPLOADER._config.uploadBorderStyle,p=e="";"y"==b.isWebFile&&"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(p=' style="background:'+UPLOADTOP.G_CURRUPLOADER._config.customWebFileColor+';"');var l="";"0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth&&(l=
"margin-right:0");var q="";""!=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color&&(q="color:"+UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color+";border-color:"+UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color+"");var r="",r=""==UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text?Dext5Upload_Lang.large_files:UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text,u="",w=0;if(UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth)for(var y=UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth.length,t=0;t<y;t++)w+=
parseInt(UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t],10);isLargeFiles(b.fileSize)&&(u="0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth?u+('<span class="larger_fsize" style="right:'+(w+5)+"px;"+q+'" title="'+r+'">'+r+"</span>"):u+('<span class="larger_fsize" style="right:'+(w+109)+"px;"+q+'" title="'+r+'">'+r+"</span>"));t=UPLOADTOP.document.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID);-1<UPLOADTOP.G_CURRUPLOADER._config.width.indexOf("%")?(t=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(t),
q=t.right-t.left):q=parseInt(UPLOADTOP.G_CURRUPLOADER._config.width,10);t="";t="0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth?q-w-42:q-w-142;""==b.originName?(w="",w="display:none"):w="";e="thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views?e+('<ul class="border_none"'+p+" dext5customdata_fileitem>"):e+('<ul class="border_'+n+'"'+p+" dext5customdata_fileitem>");"undefined"===typeof c&&(c=RESULTFILELIST.length-1);e+='<li class="input_chk"><input style="" type="checkbox" listvalue="'+c+'" title="checkbox" /></li>';
c="";b.originNameColor&&""!=b.originNameColor&&(c=' style="color:'+b.originNameColor+';"');n=b.originName;b.originNameFormat&&""!=b.originNameFormat&&(-1<b.originNameFormat.indexOf("B")&&(n="<strong>"+n+"</strong>"),-1<b.originNameFormat.indexOf("U")&&(n="<u>"+n+"</u>"),-1<b.originNameFormat.indexOf("S")&&(n="<strike>"+n+"</strike>"),-1<b.originNameFormat.indexOf("I")&&(n="<i>"+n+"</i>"));e="thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views?e+('<li class="fname" style="'+l+'"><span title="'+b.originName+
'" class="imglst"><canvas></canvas></span><span title="'+b.originName+'"'+c+">"+n+"</span>"+u+"</li>"):e+('<li class="fname" style="width:'+t+"px;"+l+'"><img src="'+k+'" alt="'+g+'" style="'+w+'"><span title="'+b.originName+'"'+c+">"+n+"</span>"+u+"</li>");g=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length;l=UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth.length;k="0";for(t=0;t<l;t++)k+=parseInt(UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t]),k++;l="";l="0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth?
l+('style="display:none;right:'+k+'px"'):l+('style="display:block;right:'+k+'px"');""==f.size.toString()?e+='<li class="fsize" '+l+'><span name="DEXT_fiVe_UP_fsize" title="N/A">N/A</span></li>':(f=f.toString,"form"==UPLOADTOP.G_CURRUPLOADER._config.subMode&&"N/A"==f&&(f=""),e+='<li class="fsize" '+l+'><span name="DEXT_fiVe_UP_fsize" title="'+f+'">'+f+"</span></li>");if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem&&"list"==UPLOADTOP.G_CURRUPLOADER._config.views){e+='<li class="user_header_box">';
for(t=0;t<g;t++)""==b.headerEx?e+='<span style="width: '+UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t]+';"></span>':(k=b.headerEx,f="",k&&(f=k.split("|")),k=UPLOADTOP.G_CURRUPLOADER._config.headerBarItemAlign[t],"button_add"==f[t]?(f=UPLOADTOP.DEXT5UPLOAD.util.makeGuidTagName(),e+="",e+='<span style="width:'+UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t]+';" class="DEXT_fiVe_UP_button_box">',e+='<form id="form_'+f+'" method="post" enctype="multipart/form-data" encoding="multipart/form-data">',
e+='<em class="DEXT_fiVe_UP_button_value">'+Dext5Upload_Lang.btn_add+"</em>",e+='<input id="file_'+f+'" name="file_'+f+'" type="file" onchange="fileHandler_formAdd(this, this.value);" /></form>',e+="</span>"):"button_cancel"==f[t]?(e+='<span style="width:'+UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t]+';" class="DEXT_fiVe_UP_button_box">',e+='<em class="DEXT_fiVe_UP_button_value">'+Dext5Upload_Lang.btn_remove+"</em>",e+='<input type="button" onclick="fileHandler_formCancel(this)" />',e+=
"</span>"):(k=null==k||""==k?"text-align:left":"text-align:"+k,e+='<span style="width:'+UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[t]+";"+k+';" title="'+f[t]+'">&nbsp;'+f[t]+"&nbsp;</span>"));e+="</li>"}else e+='<li class="user_header_box" style="display:none"></li>';var e=e+"</ul>",z=document.createElement("li");"thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views&&(z.className="thumblst");z.innerHTML=e;UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[1],"click",listClickEvent);
UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[2],"click",listClickEvent);UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[3],"click",listClickEvent);UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("input")[0],"click",function(a){var b=document.getElementById("file_list").getElementsByTagName("input");a=b.length;for(var c=[],e=0;e<a;e++)"checkbox"==b[e].type&&c.push(b[e]);a=c.length;b=z.getElementsByTagName("input")[0];if(1==b.checked)for(b.value="true",
fileListSelection(b),e=0;e<a;e++)b!=c[e]&&(c[e].value="false",fileListNoneSelection(c[e]));else fileListNoneSelection(b);for(var b=document.getElementById("chk_all_box"),f=!0,e=0;e<a;e++)if(0==c[e].checked){f=!1;break}b.checked=f});var B=document.getElementById("file_list"),e=function(a){return function(b){if("download"!=UPLOADTOP.G_CURRUPLOADER._config.mode){b=[];for(var c=0;c<B.childNodes.length;c++)if(a==B.childNodes[c]){var e=a.getElementsByTagName("input")[0].getAttribute("listvalue");if(!(null==
e||void 0==e||""==RESULTFILELIST[e].originName&&"form"==UPLOADTOP.G_CURRUPLOADER._config.subMode||"y"!=RESULTFILELIST[e].isWebFile||"n"!=RESULTFILELIST[e].isDelete||""==RESULTFILELIST[e].webPath&&""==RESULTFILELIST[e].customValue)){b.push(c);break}}if(0<b.length)if(""==UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension)openSubmit(b[0]);else if(""!=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension){for(var e=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension.split(","),f=e.length,c=0;c<f;c++)if(e[c]==
RESULTFILELIST[b[0]].fileExt){openSubmit(b[0]);return}"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload([b[0]]):downloadSubmit([b[0]])}}}}(z);UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[1],"dblclick",e);UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[2],"dblclick",e);UPLOADTOP.DEXT5UPLOAD.util.addEvent(z.getElementsByTagName("li")[3],"dblclick",e);"thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views&&(a?thumbsViewWithCanvas(b,z,a):UPLOADTOP.G_CURRUPLOADER.checkThumbsArr.push({fileObj:b,
oneList:z}));B.appendChild(z)}
function listClickEvent(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID,c=getUploaderFromEvent(b);a!=c.ID&&(UPLOADTOP.G_CURRUPLOADER=c)}var e=UPLOADTOP.DEXT5UPLOAD.util.getParentbyTagName(b.target?b.target:b.srcElement,"ul");if(e){for(var f=document.getElementById("file_list").getElementsByTagName("input"),a=f.length,c=[],g=0;g<a;g++)"checkbox"==f[g].type&&c.push(f[g]);a=c.length;e=e.getElementsByTagName("input")[0];for(g=0;g<a;g++)if(c[g]==e){fileListSelection(c[g]);"1"==UPLOADTOP.G_CURRUPLOADER._config.selectByClicked&&
0==c[g].checked&&c[g].click();f=c[g].getAttribute("listvalue");try{RESULTFILELIST[f]&&UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_SelectItem(UPLOADTOP.G_CURRUPLOADER.ID,{isWebFile:RESULTFILELIST[f].isWebFile,localPath:RESULTFILELIST[f].localPath,webPath:RESULTFILELIST[f].webPath,fileSize:RESULTFILELIST[f].fileSize,originalName:RESULTFILELIST[f].originName,extension:RESULTFILELIST[f].fileExt,order:f+1,uniqkey:RESULTFILELIST[f].fileIdx,customValue:RESULTFILELIST[f].customValue})}catch(k){}"1"==UPLOADTOP.G_CURRUPLOADER._config.imgPreView&&
(f=RESULTFILELIST[f],"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?showImgPreViewPopup(b,f,"html4"):showImgPreViewPopup(b,f,"html5"))}else fileListNoneSelection(c[g])}}R[5]="/";function uploadPause(){}function uploadResume(){}R[6]="/";
function uploadCancel(b){if(b){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useUploadingCancelEvent){b=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_UploadingCancel(UPLOADTOP.G_CURRUPLOADER.ID,b)}catch(a){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();b=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==b.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(b.G_TagID,
b.G_LocalFileObject),b.G_LocalFileObject=[],b.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}UPLOADTOP.G_CURRUPLOADER._config.uploadCancel=!0;"html5"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported&&removeWorker();"html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&removeSocket();"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(b=SWFUpload.instances["SWFUpload_"+
UPLOADTOP.G_CURRUPLOADER.ID],b.cancelUpload(RESULTFILELIST[upload_complete_count].file.id),b.requeueUpload(RESULTFILELIST[upload_complete_count].file.id));closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else if(1==confirm(Dext5Upload_Lang.message_upload_cancel)){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useUploadingCancelEvent){b=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_UploadingCancel(UPLOADTOP.G_CURRUPLOADER.ID,b)}catch(c){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&
fileListReset();b=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==b.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(b.G_TagID,b.G_LocalFileObject),b.G_LocalFileObject=[],b.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}UPLOADTOP.G_CURRUPLOADER._config.uploadCancel=!0;"html5"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported&&
removeWorker();"html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&removeSocket();"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(b=SWFUpload.instances["SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID],b.cancelUpload(RESULTFILELIST[upload_complete_count].file.id),b.requeueUpload(RESULTFILELIST[upload_complete_count].file.id));closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}}SIL[2]="o";
function getUploadedFileListObj(){for(var b=[],a=RESULTFILELIST.length,c=1,e=0;e<a;e++)"complete"==RESULTFILELIST[e].status&&b.push({guid:RESULTFILELIST[e].guid,originName:RESULTFILELIST[e].originName,fileSize:RESULTFILELIST[e].fileSize,uploadName:RESULTFILELIST[e].uploadName,uploadPath:RESULTFILELIST[e].uploadPath,logicalPath:RESULTFILELIST[e].logicalPath,order:c,status:RESULTFILELIST[e].status,mark:RESULTFILELIST[e].mark,responseCustomValue:RESULTFILELIST[e].responseCustomValue}),"n"==RESULTFILELIST[e].isDelete&&
c++;return b}Z[2]="t";
function deleteSelectedFile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&0!=isExecuteApi()){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.deleteUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.DoCancelFile()}catch(f){}else{for(var c=document.getElementById("file_list"),
g=c.getElementsByTagName("input"),k=g.length,n=[],e=0;e<k;e++)"checkbox"==g[e].type&&n.push(g[e]);n=c.getElementsByTagName("input");k=n.length;g=-1;for(e=0;e<k;e++)n[e].getAttribute("dext_select")&&"1"==n[e].getAttribute("dext_select")&&0==n[e].checked&&(g=e,n[e].checked=!0);for(var p=!1,e=0;e<k;e++)if(n[e].checked){p=!0;break}if(p)if(e=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.disableDeleteConfirm,p=document.getElementById("chk_all_box").checked,"1"==e||"2"==e&&!p||"3"==e&&p||!p&&confirm(Dext5Upload_Lang.message_selected_remove)||
p&&confirm(Dext5Upload_Lang.message_all_remove)){"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&G_IntervalGetSize&&(clearInterval(G_IntervalGetSize),G_IntervalGetSize=null);p=0;if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)for(g=0;g<RESULTFILELIST.length;g++)"n"==RESULTFILELIST[g].isDelete&&"y"==RESULTFILELIST[g].isWebFile&&
p++;for(var l=[],e=k-1;0<=e;e--)if(n[e].checked){for(var k=e,q=RESULTFILELIST.length,r=-1,g=0;g<q;g++)if("y"!=RESULTFILELIST[g].isDelete&&r++,r==e){k=g;break}if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEvent){q="0";"y"==RESULTFILELIST[k].isWebFile.toLowerCase()&&(q="1");try{if(0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeDeleteItem(UPLOADTOP.G_CURRUPLOADER.ID,q,RESULTFILELIST[k].fileIdx,RESULTFILELIST[k].webPath,e+1))continue;else"1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent&&G_DeleteItemCount++}catch(u){}}"html4"==
UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"n"==RESULTFILELIST[k].isWebFile&&(k=document.getElementById("files_container"))&&(q=k.getElementsByTagName("form")[e-p])&&k.removeChild(q.parentNode);c.removeChild(c.childNodes[e]);"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"0"!=RESULTFILELIST[g].fileSize&&RESULTFILELIST[g].file&&""!=RESULTFILELIST[g].file.id&&SWFUpload.instances["SWFUpload_"+
UPLOADTOP.G_CURRUPLOADER.ID].cancelUpload(RESULTFILELIST[g].file.id);for(g=0;g<RESULTFILELIST.length;g++)"n"==RESULTFILELIST[g].isDelete&&l.push(RESULTFILELIST[g]);l[e].isDelete="y"}0==c.hasChildNodes()&&("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(document.getElementById("file_temp").style.display=""),document.getElementById("chk_all_box").checked=!1);for(e=RESULTFILELIST.length-1;0<=e;e--)if("y"==RESULTFILELIST[e].isDelete&&"n"==RESULTFILELIST[e].isWebFile)for(RESULTFILELIST.splice(e,1),n=
c.getElementsByTagName("input"),p=n.length,g=0;g<p;g++)l=n[g].getAttribute("listvalue"),null!=l&&void 0!=l&&(l=parseInt(l,10),l>e&&n[g].setAttribute("listvalue",l-1));calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setLargeFileAllList();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_DeleteItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID,G_DeleteItemCount),G_DeleteItemCount=0}catch(w){}}else-1<g&&(n[g].checked=!1)}setTabOrder()}}R[7]="w";
function deleteCurrentFile(b,a){var c=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.disableDeleteConfirm,e=document.getElementById("chk_all_box").checked;if("1"==c||"2"==c&&!e||"3"==c&&e||!e&&confirm(Dext5Upload_Lang.message_selected_remove)||e&&confirm(Dext5Upload_Lang.message_all_remove)){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEvent){for(var e=RESULTFILELIST.length,c=b,f=-1,g=0;g<e;g++)if("y"!=RESULTFILELIST[g].isDelete&&f++,f==b){c=g;break}e="0";"y"==RESULTFILELIST[c].isWebFile.toLowerCase()&&
(e="1");try{if(0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeDeleteItem(UPLOADTOP.G_CURRUPLOADER.ID,e,RESULTFILELIST[c].fileIdx,RESULTFILELIST[c].webPath,b+1))return!1;"1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent&&G_DeleteItemCount++}catch(k){}}var c=b,n=0;if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)for(g=0;g<RESULTFILELIST.length;g++)"n"==RESULTFILELIST[g].isDelete&&"y"==RESULTFILELIST[g].isWebFile&&n++;e=document.getElementById("file_list");
f=[];if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"n"==RESULTFILELIST[c].isWebFile){var p=document.getElementById("files_container"),n=p.getElementsByTagName("form")[c-n];p.removeChild(n.parentNode)}e.removeChild(e.childNodes[c]);"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"0"!=RESULTFILELIST[g].fileSize&&SWFUpload.instances["SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID].cancelUpload(RESULTFILELIST[g].file.id);
for(g=0;g<RESULTFILELIST.length;g++)"n"==RESULTFILELIST[g].isDelete&&f.push(RESULTFILELIST[g]);f[c].isDelete="y";0==e.hasChildNodes()&&("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(document.getElementById("file_temp").style.display=""),document.getElementById("chk_all_box").checked=!1);for(c=RESULTFILELIST.length-1;0<=c;c--)if("y"==RESULTFILELIST[c].isDelete&&"n"==RESULTFILELIST[c].isWebFile)for(RESULTFILELIST.splice(c,1),f=e.getElementsByTagName("input"),p=f.length,g=0;g<p;g++)n=f[g].getAttribute("listvalue"),
null!=n&&void 0!=n&&(n=parseInt(n,10),n>c&&f[g].setAttribute("listvalue",n-1));calcTotalSize();displayTotalSizeAndNum();setFileListBorder();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_DeleteItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID,G_DeleteItemCount),G_DeleteItemCount=0}catch(l){}}}R[8]="w";
function deleteAllFile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&0!=isExecuteApi())if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.deleteUnchosen&&-1==getSelectedFileCount())alert(Dext5Upload_Lang.message_file_notexist);else{if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.DoCancelFileAll()}catch(f){}else document.getElementById("file_list").hasChildNodes()&&
(document.getElementById("chk_all_box").checked=!0,checkAll(),void 0!=a?deleteSelectedFile(null,a):deleteSelectedFile());setTabOrder()}}R[9]="w";function calcTotalSize(){for(var b=RESULTFILELIST.length,a=0,c=0,e=0;e<b;e++)"n"==RESULTFILELIST[e].isDelete&&(""!=RESULTFILELIST[e].fileSize&&(a+=RESULTFILELIST[e].fileSize),c++);TOTALFILELISTSIZE=a;TOTALFILELISTNUM=c}R[10]=".";
function displayTotalSizeAndNum(b){var a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALFILELISTSIZE),c=document.getElementById("current_file_info"),e="",e=b&&""!=b?'<span id="total_num">'+b+"</span>":'<span id="total_num">'+TOTALFILELISTNUM+"</span> "+Dext5Upload_Lang.file_unit;if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode)e+=', <span id="total_size">'+a.toString+"</span>";e+=' <span id="file_inserted_text">';
"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(e+=Dext5Upload_Lang.file_inserted);c.innerHTML=e+"</span>";setAutoHeight(b)}T[0]="Li";function displayTotalUploadSizeAndNum(){var b=getDialogDocument(),a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALUPLOADSIZE);b.getElementById("DEXT_fiVe_UP_upload_count").innerHTML="0/"+TOTALUPLOADNUM;b.getElementById("DEXT_fiVe_UP_upload_size").innerHTML="0/"+a.toString}R[11]="d";
function createTransferDiv(){var b=UPLOADTOP.G_CURRUPLOADER._config,a="",c=document.createElement("div");"standard"!=b.uploadTransferWindow.view||UPLOADTOP.DEXT5UPLOAD.browser.mobile?(c.id="DEXT_fiVe_UP_ly_wrapper",c.className="DEXT_fiVe_UP_ly_wrapper DEXT_fiVe_UP_ly_wrapper_standard",c.style.height="170px",c.style.display="none","light"==b.uploadTransferWindow.view?c.style.width=b.uploadTransferWindow.viewWidth:null==navigator.userAgent.match(/iPad/)&&null!=navigator.userAgent.match(/iPhone|Mobile|UP.Browser|Android|BlackBerry|Windows CE|Nokia|webOS|Opera Mini|SonyEricsson|opera mobi|Windows Phone|IEMobile|POLARIS/)?
c.style.width="320px":c.style.width=b.uploadTransferWindow.viewWidth):(c.id="DEXT_fiVe_UP_ly_wrapper",c.className="DEXT_fiVe_UP_ly_wrapper",c.style.width="470px",c.style.height="260px",c.style.display="none");a+=createTransferDivHtml(UPLOADTOP.G_CURRUPLOADER);c.innerHTML=a;b=getDialogDocument();b.body.appendChild(c);c=b.getElementById("DEXT_fiVe_UP_close_btn");UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"click",function(){uploadCancel()});c=b.getElementById("DEXT_fiVe_UP_upload_cancel");UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,
"click",function(){uploadCancel()})}
function showUploadingFileList(){var b="1"==UPLOADTOP.G_CURRUPLOADER._config.silentUpload&&"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode?"display:none;":"",a=getDialogWindow(),c=getDialogDocument();null==c.getElementById("DEXT_fiVe_UP_ly_wrapper")&&createTransferDiv();var e=c.createElement("div");e.setAttribute("id","upload_background");var b='<div style="'+b+UPLOADTOP.G_CURRUPLOADER._config.transferBackgroundStyle+' z-index:1000; position:fixed; _position:absolute; left:0px; top:0px;overflow:hidden;width:100%;height:100%;"></div>',f=
UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();e.innerHTML=b+('<iframe src="'+f+'" frameborder="0" scrolling="no" style="filter:alpha(opacity=0); opacity:0; -moz-opacity:0; -ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=0); -khtml-opacity: 0; left: 0px; top: 0px; width: 100%; height: 100%; display: block; position: absolute; z-index: 999;" title="Transfer Background"></iframe>');c.body.appendChild(e);b={};b=getWindowClientSize(a);f={};f=getWindowScrollPos(a);e.getElementsByTagName("iframe")[0].style.height=
b[1]+f[1]+"px";a=c.getElementById("DEXT_fiVe_UP_upload_cancel");a.style.display=a&&"2"==UPLOADTOP.G_CURRUPLOADER._config.silentUpload?"none":"";a=c.getElementById("DEXT_fiVe_UP_ly_wrapper");c.getElementById("DEXT_fiVe_UP_ly_content").className="DEXT_fiVe_UP_upload_popup_"+UPLOADTOP.G_CURRUPLOADER._config.skinName;a.style.zIndex="2000";a.style.display="";"1"==UPLOADTOP.G_CURRUPLOADER._config.silentUpload&&"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(a.style.display="none");var e=parseInt(a.style.width,
10),g=parseInt(a.style.height,10);a.style.left=f[0]+b[0]/2-e/2+"px";a.style.top=f[1]+b[1]/2-g/2+"px";makeDragPopup(a);b=c.getElementById("DEXT_fiVe_UP_uploading_file_list");b.innerHTML="";f=uploaders.length;for(a=0;a<f;a++)if(e=RESULTFILELIST[uploaders[a]],e.file){var g=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(e.fileSize),k=e.fileExt,n=getIconImagePath(k),p=c.createElement("li"),l="",l=l+('<span id="DEXT_fiVe_UP_uploadFileName_'+uploaders[a]+'" class="fname">'),l=l+'<span class="ficon">',l=l+('<img src="'+
n+'" alt="'+k+'" />'),l=l+"</span> ",l=l+e.originName,l=l+"</span>",l=l+'<span class="fprogress">',l="html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4?l+('<span id="DEXT_fiVe_UP_uploadFileSize_'+uploaders[a]+'" class="st">&nbsp;</span>'):l+('<span id="DEXT_fiVe_UP_uploadFileSize_'+uploaders[a]+'" class="st">'+g.toString+"</span>"),l=l+'<span class="pbar">',l=l+('<span id="DEXT_fiVe_UP_uploadFileProgress_'+
uploaders[a]+'" style="width: 0%;'+G_StrCustomProgressBarColor+'"class="pbar_value"></span>'),l=l+"</span>",l=l+('<span id="DEXT_fiVe_UP_uploadFileStatus_'+uploaders[a]+'" class="transfer_status">'+Dext5Upload_Lang.upload_status.wait+"</span>"),l=l+"</span>";p.innerHTML=l;b.appendChild(p);TOTALUPLOADSIZE+=e.fileSize;TOTALUPLOADNUM++}displayTotalUploadSizeAndNum();c.getElementById("DEXT_fiVe_UP_upload_cancel").focus()}T[1]="c";
function closeSendDialog(){var b=getDialogDocument(),a=b.getElementById("upload_background");a&&(b.body.removeChild(a),b.getElementById("DEXT_fiVe_UP_ly_wrapper").style.display="none")}_R_O_+=R[11];
function closeBtnPreViewMode(){try{if("ieplugin"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var b=getDialogDocument().getElementById("DEXT_fiVe_UP_Popup_Mode");b.style.display="none";if(-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")||"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4){var a=b.getElementsByTagName("img")[0];a&&(a.style.display="none")}}}catch(c){}}R[12]="e";
function fileHandler(b,a,c){if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode){for(var e=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize,f=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount,g=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize,k=!0,n=b.length,p=[],l=0;l<n;l++)p.push({file:b[l],name:b[l].name,size:b[l].size,type:b[l].type,lastModifiedDate:b[l].lastModifiedDate.toString()});"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"2"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("0"==UPLOADTOP.G_CURRUPLOADER._config.sort_field?
"0"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc?p.sort(function(a,b){return a.name<b.name?-1:a.name>b.name?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc&&p.sort(function(a,b){return a.name>b.name?-1:a.name<b.name?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&("0"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc?p.sort(function(a,b){return a.size<b.size?-1:a.size>b.size?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc&&p.sort(function(a,b){return a.size>b.size?-1:a.size<
b.size?1:0})));var q=p.length;if(0!=q){var r=UPLOADTOP.G_CURRUPLOADER.frameWin.getTotalFileCount(),u=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.duplication;b=[];for(var n=0,w=[],y=0,t=0,l=0;l<q;l++)if(sameFileCheck(p[l]))"0"==u&&alert(p[l].name+" "+Dext5Upload_Lang.message_duplication_file);else{calcTotalSize();var z=!1;"0"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==p[l].size?y++:0!=g&&p[l].size>g?t++:z=!0;var B=!1,x=UPLOADTOP.G_CURRUPLOADER._config.extension,A=x.extArr.length;
if(0<A){var D=x.allowOrLimit;if("1"==D){for(var C=getExtension(p[l].name),v=0;v<A;v++)if(x.extArr[v]==C){B=!0;break}B||w.push(C)}else for(C=getExtension(p[l].name),v=0;v<A;v++)if(x.extArr[v]==C){B=!1;w.push(C);break}else B=!0}else B=!0;v=TOTALFILELISTNUM;if(0!=f&&v+1>f){alert(Dext5Upload_Lang.file_maximum+" "+f+Dext5Upload_Lang.message_limit_num);break}v=TOTALFILELISTSIZE;if(0!=e&&v+p[l].size>e){alert(Dext5Upload_Lang.file_maximum+" "+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(e).toString+Dext5Upload_Lang.message_limit_size);
break}x=!0;A=UPLOADTOP.G_CURRUPLOADER._config.largeFiles;if(isLargeFiles(p[l].size)){for(var H=RESULTFILELIST.length,E=C=0,v=0;v<H;v++)RESULTFILELIST[v].fileSize>=A.markSize&&"y"!=RESULTFILELIST[v].isDelete&&(C+=RESULTFILELIST[v].fileSize,E++);0<A.maxCount&&E+1>A.maxCount&&(v=Dext5Upload_Lang.message_large_files_count,x=""==A.text?Dext5Upload_Lang.large_files:A.text,v=v.replace("{0}",x),v=v.replace("{1}",A.maxCount),alert(v),x=!1);x&&0<A.maxTotalSize&&B&&z&&C+p[l].size>A.maxTotalSize&&(v=Dext5Upload_Lang.message_large_files_size,
x=""==A.text?Dext5Upload_Lang.large_files:A.text,v=v.replace("{0}",x),v=v.replace("{1}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(A.maxTotalSize).toString),alert(v),x=!1)}null==a&&(a="");null==c&&(c="");v=a.split(",");A=v[l];if("undefined"==typeof A||null==A)A="undefined"==typeof v[0]||null==v[0]?"":v[0].substring(0,v[0].lastIndexOf("\\")+1)+p[l].name;if(B&&z&&x){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent)try{k=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,p[l].name,
p[l].size,r+n+1,A)}catch(F){}if(k){z={file:p[l].file,guid:"",fileIdx:"",webPath:"",originName:p[l].name,fileSize:p[l].size,uploadName:"",uploadPath:"",logicalPath:"",status:"ready",fileExt:"",isDelete:"n",isWebFile:"n",localPath:A,mark:c,responseCustomValue:"",headerEx:"",groupId:"",completeImgQualityPrc:!1};RESULTFILELIST.push(z);b.push(RESULTFILELIST.length-1);addFileList(z);if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent)try{calcTotalSize(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(UPLOADTOP.G_CURRUPLOADER.ID,
p[l].name,p[l].size,r+n+1)}catch(G){}n++}}}1==y?alert(Dext5Upload_Lang.message_size_zero):1<y&&(l=Dext5Upload_Lang.message_size_zeros,l=l.replace("{0}",y),alert(l));1==t?(l=Dext5Upload_Lang.message_limit_one_size,l=l.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString),alert(l)):1<t&&(l=Dext5Upload_Lang.message_limit_one_sizes,l=l.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString),l=l.replace("{1}",t),alert(l));g=w.length;0<g&&(v="",l=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr,
"1"==D?1<g?(v=Dext5Upload_Lang.message_not_allow_exts,v=v.replace("{0}",g),v=v.replace("{1}",l)):(v=w[0]+" "+Dext5Upload_Lang.message_not_allow_ext,v=v.replace("{0}",l)):1<g?(v=Dext5Upload_Lang.message_not_limit_exts,v=v.replace("{0}",g),v=v.replace("{1}",l)):(v=w[0]+" "+Dext5Upload_Lang.message_not_limit_ext,v=v.replace("{0}",l)),alert(v));"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,
UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc);fileListSortIconReset();calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder();setListvalue();setLargeFileAllList();D=document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID);D.value="";UPLOADTOP.DEXT5UPLOAD.browser.ie&&10==UPLOADTOP.DEXT5UPLOAD.browser.ieVersion&&(D.type="text",D.type="file");if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&"0"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&
0<n)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(J){}"thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views&&thumbsViewWithCanvas();if(-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")&&"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector)for(var I=b.length,K=0,L=0,l=0;l<I;l++)D=function(a){return function(b,c,e){L++;if(0==e)for(b=document.getElementById("file_list").getElementsByTagName("input"),c=b.length,e=0;e<c;e++)if(b[e].getAttribute("listvalue")&&
b[e].getAttribute("listvalue").toString()==a.toString()){b[e].checked=!0;K++;break}if(L==I&&(0<K&&(b=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.disableDeleteConfirm,UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.disableDeleteConfirm="1",deleteSelectedFile(null,!1),UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.disableDeleteConfirm=b,"0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.fileExtensionDetect&&setTimeout(function(){alert(Dext5Upload_Lang.message_file_ext_detect.replace("{0}",
K))},0)),setLargeFileAllList(),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&K<I))try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(f){}}}(b[l]),D5FileDetector(RESULTFILELIST[b[l]].file,RESULTFILELIST[b[l]].fileExt,D)}}}Z[3]="p";_R_O_+="g";
function fileHandler_html4(b,a,c){if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&G_IntervalGetSize)b.value="";else{var e=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount,f=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.duplication,g=0;if(sameFileCheck_html4(b.value))"0"==f&&alert(getFileName(b.value,!1)+" "+Dext5Upload_Lang.message_duplication_file);
else{calcTotalSize();f=TOTALFILELISTNUM;if(0!=e&&f+1>e){alert(Dext5Upload_Lang.file_maximum+" "+e+Dext5Upload_Lang.message_limit_num);return}var f=!1,k=UPLOADTOP.G_CURRUPLOADER._config.extension,n=k.extArr.length,e=getFileName(b.value,!1);if(0<n)if("1"==k.allowOrLimit){for(var p=getExtension(e),l=0;l<n;l++)if(k.extArr[l]==p){f=!0;break}f||(k=Dext5Upload_Lang.message_not_allow_ext,n=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr,k=k.replace("{0}",n),alert(p+" "+k))}else for(p=getExtension(e),l=
0;l<n;l++)if(k.extArr[l]==p){f=!1;k=Dext5Upload_Lang.message_not_limit_ext;n=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr;k=k.replace("{0}",n);alert(p+" "+k);break}else f=!0;else f=!0;if(f){f=UPLOADTOP.G_CURRUPLOADER.frameWin.getTotalFileCount();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4)try{if(0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,e,null,f+1,a))return}catch(q){}var r="";document.getElementById("tab_"+
UPLOADTOP.G_CURRUPLOADER.TagID)&&(r=document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value);0==!!a&&(a="");0==!!c&&(c="");a={file:b,guid:"",fileIdx:"",webPath:"",originName:e,fileSize:"",uploadName:"",uploadPath:"",logicalPath:"",status:"ready",fileExt:"",isDelete:"n",isWebFile:"n",localPath:a,mark:c,responseCustomValue:"",headerEx:"",groupId:""};RESULTFILELIST.push(a);addFileList(a);if("1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4){a=b.parentNode;c=document.createElement("form");
c.method="post";document.body.appendChild(c);c.style.position="absolute";c.style.top="-500px";c.style.left="-500px";c.setAttribute("enctype","multipart/form-data");c.setAttribute("encoding","multipart/form-data");c.appendChild(b);var u=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,u=-1<u.indexOf("?")?u+"&":u+"?",w=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();c.action=u+"dext5CMD=uh4fc&g="+w;c.target="download_frame";var y=!1,t="";try{c.submit()}catch(z){y=!0,t=z.message}a.insertBefore(b,a.getElementsByTagName("input")[0]);
c.parentNode.removeChild(c);b=document.getElementById("file_list").getElementsByTagName("li");b=b[b.length-1].getElementsByTagName("span");b[b.length-1].innerHTML=Dext5Upload_Lang.get_size;G_IntervalGetSize=setInterval(function(){if(y){clearInterval(G_IntervalGetSize);G_IntervalGetSize=null;deleteFileAfterGetSize();var a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"error",t,a)}catch(b){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();
a=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==a.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(a.G_TagID,a.G_LocalFileObject),a.G_LocalFileObject=[],a.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}else try{var a="",c=u+"dext5CMD=uh4fc&g="+w+"&s="+w;UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain?UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,c+"&cd=1","download_frame"):
(a=UPLOADTOP.DEXT5UPLOAD.ajax.postData(c,""),a=UPLOADTOP.DEXT5UPLOAD.util.trim(a),a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(a):Dext5Base64.makeDecryptReponseMessage(a),html4GetSize(a))}catch(e){clearInterval(G_IntervalGetSize),G_IntervalGetSize=null}},500)}g++;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent)try{calcTotalSize(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(UPLOADTOP.G_CURRUPLOADER.ID,e,null,
f+1)}catch(B){}b=document.getElementById("files_container");UPLOADTOP.G_CURRUPLOADER.TagID=UPLOADTOP.DEXT5UPLOAD.util.makeGuidTagName();e=document.createElement("form");e.setAttribute("id","form_"+UPLOADTOP.G_CURRUPLOADER.TagID);e.setAttribute("method","post");e.setAttribute("enctype","multipart/form-data");e.setAttribute("encoding","multipart/form-data");a=document.createElement("span");a.className="input_image_add";a.innerHTML=Dext5Upload_Lang.btn_add;""!=UPLOADTOP.G_CURRUPLOADER._config.customHeaderColor&&
(a.style.background=UPLOADTOP.G_CURRUPLOADER._config.customHeaderColor);""!=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor&&(a.style.color=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor);var x=document.createElement("input");x.setAttribute("id","file_"+UPLOADTOP.G_CURRUPLOADER.TagID);x.setAttribute("name","file_"+UPLOADTOP.G_CURRUPLOADER.TagID);x.setAttribute("type","file");UPLOADTOP.DEXT5UPLOAD.util.addEvent(x,"change",function(){fileHandler_html4(x,x.value)});UPLOADTOP.DEXT5UPLOAD.util.addEvent(x,
"blur",function(){focusHandler(x)});c=document.createElement("input");c.setAttribute("id","tab_"+UPLOADTOP.G_CURRUPLOADER.TagID);c.setAttribute("name","tab_"+UPLOADTOP.G_CURRUPLOADER.TagID);c.setAttribute("type","hidden");c.setAttribute("value","0");e.appendChild(a);e.appendChild(x);e.appendChild(c);a=document.createElement("div");a.appendChild(e);b.appendChild(a);a.previousSibling&&(a.previousSibling.style.display="none",a.previousSibling.childNodes[0].childNodes[1].tabIndex="-1")}}"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc));fileListSortIconReset();calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder();setListvalue();1==r&&(document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).focus(),document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundImage=
"url("+UPLOADTOP.G_CURRUPLOADER._config.webPath.image+"img_bt_line.png)",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundRepeat="no-repeat",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionY="2px",document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundPositionX="3px",document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID).value="1",G_FileHandlerControl=0);
"thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views&&thumbsViewWithCanvas();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&0<g)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(A){}}}function fileHandler_html4_swf(b){filesListSWF.push({file:b,name:b.name,size:b.size,type:b.type,lastModifiedDate:b.modificationdate.toString()})}
function fileHandlerComplete_html4_swf(b,a){if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode){var c=SWFUpload.instances["SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID],e=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize,f=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount,g=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize,k=!0;"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"2"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("0"==UPLOADTOP.G_CURRUPLOADER._config.sort_field?"0"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc?
filesListSWF.sort(function(a,b){return a.name<b.name?-1:a.name>b.name?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc&&filesListSWF.sort(function(a,b){return a.name>b.name?-1:a.name<b.name?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&("0"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc?filesListSWF.sort(function(a,b){return a.size<b.size?-1:a.size>b.size?1:0}):"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc&&filesListSWF.sort(function(a,b){return a.size>b.size?-1:a.size<b.size?
1:0})));var n=filesListSWF.length;if(0!=n){for(var p=UPLOADTOP.G_CURRUPLOADER.frameWin.getTotalFileCount(),l=UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.duplication,q=[],r=0,u=[],w=0,y=0,t=0;t<n;t++)if(sameFileCheck(filesListSWF[t]))"0"==l&&(alert(filesListSWF[t].name+" "+Dext5Upload_Lang.message_duplication_file),"0"!=filesListSWF[t].size&&c.cancelUpload(filesListSWF[t].file.id));else{calcTotalSize();var z=!1;"0"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==filesListSWF[t].size?
w++:0!=g&&filesListSWF[t].size>g?y++:z=!0;var B=!1,x=UPLOADTOP.G_CURRUPLOADER._config.extension,A=x.extArr.length;if(0<A){var D=x.allowOrLimit;if("1"==D){for(var C=getExtension(filesListSWF[t].name),v=0;v<A;v++)if(x.extArr[v]==C){B=!0;break}B||u.push(C)}else for(C=getExtension(filesListSWF[t].name),v=0;v<A;v++)if(x.extArr[v]==C){B=!1;u.push(C);break}else B=!0}else B=!0;v=TOTALFILELISTNUM;if(0!=f&&v+1>f){alert(Dext5Upload_Lang.file_maximum+" "+f+Dext5Upload_Lang.message_limit_num);"0"!=filesListSWF[t].size&&
c.cancelUpload(filesListSWF[t].file.id);break}v=TOTALFILELISTSIZE;if(0!=e&&v+filesListSWF[t].size>e){alert(Dext5Upload_Lang.file_maximum+" "+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(e).toString+Dext5Upload_Lang.message_limit_size);"0"!=filesListSWF[t].size&&c.cancelUpload(filesListSWF[t].file.id);break}C=!0;x=UPLOADTOP.G_CURRUPLOADER._config.largeFiles;if(isLargeFiles(filesListSWF[t].size)){for(var H=RESULTFILELIST.length,E=A=0,v=0;v<H;v++)RESULTFILELIST[v].fileSize>=x.markSize&&"y"!=RESULTFILELIST[v].isDelete&&
(A+=RESULTFILELIST[v].fileSize,E++);0<x.maxCount&&E+1>x.maxCount&&(v=Dext5Upload_Lang.message_large_files_count,C=""==x.text?Dext5Upload_Lang.large_files:x.text,v=v.replace("{0}",C),v=v.replace("{1}",x.maxCount),alert(v),C=!1);C&&0<x.maxTotalSize&&B&&z&&A+filesListSWF[t].size>x.maxTotalSize&&(v=Dext5Upload_Lang.message_large_files_size,C=""==x.text?Dext5Upload_Lang.large_files:x.text,v=v.replace("{0}",C),v=v.replace("{1}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(x.maxTotalSize).toString),alert(v),C=
!1)}if(B&&z&&C){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent)try{k=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,filesListSWF[t].name,filesListSWF[t].size,p+r+1,"")}catch(F){}if(k){z={file:filesListSWF[t].file,guid:"",fileIdx:"",webPath:"",originName:filesListSWF[t].name,fileSize:filesListSWF[t].size,uploadName:"",uploadPath:"",logicalPath:"",status:"ready",fileExt:"",isDelete:"n",isWebFile:"n",localPath:"",mark:"",responseCustomValue:"",headerEx:"",groupId:""};
RESULTFILELIST.push(z);q.push(RESULTFILELIST.length-1);addFileList(z);if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent)try{calcTotalSize(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(UPLOADTOP.G_CURRUPLOADER.ID,filesListSWF[t].name,filesListSWF[t].size,p+r+1)}catch(G){}r++}else"0"!=filesListSWF[t].size&&c.cancelUpload(filesListSWF[t].file.id)}else"0"!=filesListSWF[t].size&&c.cancelUpload(filesListSWF[t].file.id)}1==w?alert(Dext5Upload_Lang.message_size_zero):1<w&&(c=Dext5Upload_Lang.message_size_zeros,
c=c.replace("{0}",w),alert(c));1==y?(c=Dext5Upload_Lang.message_limit_one_size,c=c.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString),alert(c)):1<y&&(c=Dext5Upload_Lang.message_limit_one_sizes,c=c.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString),c=c.replace("{1}",y),alert(c));g=u.length;0<g&&(v="",w=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr,"1"==D?1<g?(v=Dext5Upload_Lang.message_not_allow_exts,v=v.replace("{0}",g),v=v.replace("{1}",w)):(v=u[0]+" "+Dext5Upload_Lang.message_not_allow_ext,
v=v.replace("{0}",w)):1<g?(v=Dext5Upload_Lang.message_not_limit_exts,v=v.replace("{0}",g),v=v.replace("{1}",w)):(v=u[0]+" "+Dext5Upload_Lang.message_not_limit_ext,v=v.replace("{0}",w)),alert(v));"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc);fileListSortIconReset();calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder();
setListvalue();setLargeFileAllList();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&"0"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&0<r)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(J){}}}}
function fileHandler_formCancel(b){for(var a=document.getElementById("file_list").getElementsByTagName("input"),c=a.length,e=[],f=0;f<c;f++)"checkbox"==a[f].type&&e.push(a[f]);c=e.length;b=b.parentNode.parentNode.parentNode.parentNode.getElementsByTagName("input")[0];for(f=0;f<c;f++)if(e[f]==b&&""!=RESULTFILELIST[f].originName){RESULTFILELIST[f].fileTag?(c=document.createElement("input"),c.type="file",c.id=RESULTFILELIST[f].fileTag.id,c.name=RESULTFILELIST[f].fileTag.name,c.style.margin=RESULTFILELIST[f].fileTag.style.margin,
c.onchange=RESULTFILELIST[f].fileTag.onchange,RESULTFILELIST[f].fileTag.parentNode.replaceChild(c,RESULTFILELIST[f].fileTag),RESULTFILELIST[f].fileTag=null):RESULTFILELISTCLON[f].isDelete="y";RESULTFILELIST[f].file=null;RESULTFILELIST[f].guid="";RESULTFILELIST[f].fileIdx=RESULTFILELISTCLON[f].fileIdx;RESULTFILELIST[f].webPath="";RESULTFILELIST[f].originName="";RESULTFILELIST[f].fileSize="";RESULTFILELIST[f].uploadName="";RESULTFILELIST[f].uploadPath="";RESULTFILELIST[f].logicalPath="";RESULTFILELIST[f].status=
"";RESULTFILELIST[f].fileExt="";RESULTFILELIST[f].isDelete="n";RESULTFILELIST[f].isWebFile="y";RESULTFILELIST[f].localPath="";RESULTFILELIST[f].mark="";RESULTFILELIST[f].responseCustomValue="";RESULTFILELIST[f].headerEx=RESULTFILELISTCLON[f].headerEx;RESULTFILELIST[f].groupId="";f=b.parentNode.parentNode.getElementsByTagName("li");c=f[1];f[2].innerHTML='<span name="DEXT_fiVe_UP_fsize"></span>';c.innerHTML='<img src="" alt="" style="display:none;"><span title=""> </span>';calcTotalSize();displayTotalSizeAndNum();
setListvalue();break}}
function fileHandler_formAdd(b,a,c){if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"0"!=UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize||0!=b.files[0].size){var e=0,f=!1,g=UPLOADTOP.G_CURRUPLOADER._config.extension,k=g.extArr.length,n=getFileName(b.value,!1);if(0<k)if("1"==g.allowOrLimit){for(var p=getExtension(n),l=0;l<k;l++)if(g.extArr[l]==p){f=!0;break}f||(g=Dext5Upload_Lang.message_not_allow_ext,
k=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr,g=g.replace("{0}",k),alert(p+" "+g))}else for(p=getExtension(n),l=0;l<k;l++)if(g.extArr[l]==p){f=!1;g=Dext5Upload_Lang.message_not_limit_ext;k=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr;g=g.replace("{0}",k);alert(p+" "+g);break}else f=!0;else f=!0;if(f){document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID)&&document.getElementById("tab_"+UPLOADTOP.G_CURRUPLOADER.TagID);0==!!a&&(a="");0==!!c&&(c="");p=document.getElementById("file_list").getElementsByTagName("input");
f=p.length;g=[];for(k=0;k<f;k++)"checkbox"==p[k].type&&g.push(p[k]);for(var f=g.length,l=b.parentNode.parentNode.parentNode.parentNode.getElementsByTagName("input")[0],q,k=0;k<f;k++)if(g[k]==l){q=k;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4)try{if(0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,n,null,q,a))return}catch(r){}"y"==RESULTFILELIST[k].isWebFile&&""!=RESULTFILELIST[k].originName&&(RESULTFILELISTCLON[k].isDelete=
"y");RESULTFILELIST[k].file="html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?b:b.files[0];RESULTFILELIST[k].guid="";RESULTFILELIST[k].fileIdx="";RESULTFILELIST[k].webPath="";RESULTFILELIST[k].originName=n;RESULTFILELIST[k].fileSize="";RESULTFILELIST[k].uploadName="";RESULTFILELIST[k].uploadPath="";RESULTFILELIST[k].logicalPath="";RESULTFILELIST[k].status="ready";RESULTFILELIST[k].fileExt="";RESULTFILELIST[k].isDelete="n";RESULTFILELIST[k].isWebFile=
"n";RESULTFILELIST[k].localPath=a;RESULTFILELIST[k].mark=c;RESULTFILELIST[k].responseCustomValue="";RESULTFILELIST[k].headerEx="";RESULTFILELIST[k].groupId="";RESULTFILELIST[k].fileTag=b;var p=l.parentNode.parentNode.getElementsByTagName("li"),u=p[1],p=p[2],w=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(RESULTFILELIST[k].file.size),y="";"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?y+='<span name="DEXT_fiVe_UP_fsize">N/A</span>':(RESULTFILELIST[k].fileSize=RESULTFILELIST[k].file.size,y+='<span name="DEXT_fiVe_UP_fsize">'+
w.toString+"</span>");p.innerHTML=y;p=getExtension(n);RESULTFILELIST[k].fileExt=p;w=getIconImagePath(p);""==n?(y="",y="display:none"):y="";u.innerHTML='<img src="'+w+'" alt="'+p+'" style="'+y+'"><span title="'+n+'">'+n+"</span>"}e++;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent)try{calcTotalSize(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(UPLOADTOP.G_CURRUPLOADER.ID,n,null,q)}catch(t){}}calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder();setListvalue();if("1"==
UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&0<e)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(z){}}else alert(Dext5Upload_Lang.message_size_zero)}R[13]="x";
function sortTotalFileList(b,a,c){if(!c){var e=document.getElementById("header_bar_file_name");c=document.getElementById("header_bar_file_size");e=e.getElementsByTagName("span")[0];c.getElementsByTagName("span")[0].style.display="none";e.style.display="none";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(e=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,c=0;c<e;c++)document.getElementById("user_header_bar_"+c).getElementsByTagName("span")[0].style.display="none"}document.getElementById("file_list");
e=RESULTFILELIST.length;for(c=1;c<e;c++)if("y"!=RESULTFILELIST[0].isDelete){var f=c,g="";if("0"==b)g=RESULTFILELIST[c].originName;else if("1"==b)g=RESULTFILELIST[c].fileSize;else if("2"<=b){var k=RESULTFILELIST[c].headerEx;null!=k&&""!=k&&(k=k.split("|"),g=k[parseInt(b,10)-2])}for(var n=c-1;0<=n;n--)if("y"!=RESULTFILELIST[n].isDelete){var p="";"0"==b?p=RESULTFILELIST[n].originName:"1"==b?p=RESULTFILELIST[n].fileSize:"2"<=b&&(k=RESULTFILELIST[n].headerEx,null!=k&&""!=k&&(k=k.split("|"),p=k[parseInt(b,
10)-2]));if("0"==a)if(g<p)f=sortSwapFile(c,n,f,file_list);else break;else if("1"==a)if(g>p)f=sortSwapFile(c,n,f,file_list);else break}}}T[2]="e";_R_O_+="f";
function sortSwapFile(b,a,c,e){for(var f=0,g=0;g<b;g++)"y"==RESULTFILELIST[g].isDelete&&f++;for(g=b=0;g<a;g++)"y"==RESULTFILELIST[g].isDelete&&b++;var g=e.childNodes[c-f],k=e.childNodes[a-b];e.removeChild(e.childNodes[a-b]);e.insertBefore(g,e.childNodes[a-b]);e.insertBefore(k,e.childNodes[c-f-1].nextSibling);if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"n"==RESULTFILELIST[c].isWebFile&&"n"==RESULTFILELIST[a].isWebFile){for(g=
e=0;g<c;g++)"n"==RESULTFILELIST[g].isDelete&&"n"==RESULTFILELIST[g].isWebFile&&e++;for(g=f=0;g<a;g++)"n"==RESULTFILELIST[g].isDelete&&"n"==RESULTFILELIST[g].isWebFile&&f++;g=document.getElementById("files_container");b=g.childNodes[e];k=g.childNodes[f];g.removeChild(g.childNodes[f]);g.insertBefore(b,g.childNodes[f]);g.insertBefore(k,g.childNodes[e-1].nextSibling)}e=RESULTFILELIST[c];RESULTFILELIST[c]=RESULTFILELIST[a];RESULTFILELIST[a]=e;return a}R[14]="t";
function html4GetSize(b){try{if(b){clearInterval(G_IntervalGetSize);G_IntervalGetSize=null;var a=b.split("|");if("size"==a[0]){var c=document.getElementById("file_list").getElementsByTagName("li"),e=c[c.length-1].getElementsByTagName("span"),f=e[e.length-1],g=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize,k=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize,n=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a[1]);b=!1;if(0==n)alert(Dext5Upload_Lang.message_size_zero);else if(0!=k&&n>k){var p=Dext5Upload_Lang.message_limit_one_size,
p=p.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(k).toString);alert(p)}else b=!0;k=!1;p=TOTALFILELISTSIZE;0!=g&&p+n>g?alert(Dext5Upload_Lang.file_maximum+" "+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString+Dext5Upload_Lang.message_limit_size):k=!0;var g=!0,l=UPLOADTOP.G_CURRUPLOADER._config.largeFiles;if(0<l.markSize&&l.markSize<n){for(var q=RESULTFILELIST.length,c=a=p=0;c<q;c++)RESULTFILELIST[c].fileSize>=l.markSize&&(p+=RESULTFILELIST[c].fileSize,a++);if(0<l.maxCount&&a+1>l.maxCount){var r=
Dext5Upload_Lang.message_large_files_count,u=""==l.text?Dext5Upload_Lang.large_files:l.text,r=r.replace("{0}",u),r=r.replace("{1}",l.maxCount);alert(r);g=!1}g&&0<l.maxTotalSize&&b&&k&&p+n>l.maxTotalSize&&(r=Dext5Upload_Lang.message_large_files_size,u=""==l.text?Dext5Upload_Lang.large_files:l.text,r=r.replace("{0}",u),r=r.replace("{1}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(l.maxTotalSize).toString),alert(r),g=!1)}if(b&&k&&g){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent&&"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4)try{if(0==
UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,RESULTFILELIST[RESULTFILELIST.length-1].originName,n,UPLOADTOP.G_CURRUPLOADER.frameWin.getTotalFileCount())){deleteFileAfterGetSize();return}}catch(w){}var y=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(n);f.innerHTML=y.toString;var t=document.getElementById("file_list").getElementsByTagName("ul"),z=t[t.length-1].getElementsByTagName("li")[1],q=l=f="";""!=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color&&(l="color:"+UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color+
";border-color:"+UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color+"");q=""==UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text?Dext5Upload_Lang.large_files:UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text;"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&0<UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markSize&&n>UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markSize&&(f="0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth?
f+('<span class="larger_fsize" style="right:5px;'+l+'" title="'+q+'">'+q+"</span>"):f+('<span class="larger_fsize" style="right:88px;'+l+'" title="'+q+'">'+q+"</span>"),z.innerHTML+=f);RESULTFILELIST[RESULTFILELIST.length-1].fileSize=n;calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder()}else deleteFileAfterGetSize()}else if("error"==a[0]){deleteFileAfterGetSize();var B=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+
a[1]),B)}catch(x){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();var A=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==A.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(A.G_TagID,A.G_LocalFileObject),A.G_LocalFileObject=[],A.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}}}catch(D){clearInterval(G_IntervalGetSize),G_IntervalGetSize=null}}
_R_O_+="e";T[3]="n";
function deleteFileAfterGetSize(){var b=document.getElementById("files_container"),a=b.getElementsByTagName("form");b.removeChild(a[a.length-2].parentNode);b=document.getElementById("file_list");b.removeChild(b.childNodes[b.childNodes.length-1]);0==b.hasChildNodes()&&("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(document.getElementById("file_temp").style.display=""),document.getElementById("chk_all_box").checked=!1);RESULTFILELIST.splice(RESULTFILELIST.length-1,1);calcTotalSize();displayTotalSizeAndNum();
setFileListBorder();setTabOrder()}R[15]="5";function fileHandler_plugin(b,a,c,e){b=b.Dext5PL.GetFileInfoList(b.Dext5PL.GetFileCount()-1);""!=b&&(b=b.split(G_formfeed),G_LocalFileObjectLocalPath[b[4]]=c,G_LocalFileObjectMark[b[4]]=e)}_R_O_+="a";function getFileName(b,a){for(var c;-1!==b.indexOf("\\");)b=b.replace("\\","/");c=b.split("/").pop();a&&(c=c.slice(0,c.lastIndexOf(".")));return c}R[16]=".";
function sameFileCheck(b){for(var a=!1,c=RESULTFILELIST.length,e=0;e<c;e++)if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4){if(RESULTFILELIST[e].file&&b.name==RESULTFILELIST[e].file.name&&b.size==RESULTFILELIST[e].file.size&&b.lastModifiedDate==RESULTFILELIST[e].file.modificationdate&&b.type==RESULTFILELIST[e].file.type){a=!0;break}}else if(RESULTFILELIST[e].file&&b.name==RESULTFILELIST[e].file.name&&b.size==RESULTFILELIST[e].file.size&&
b.lastModifiedDate==RESULTFILELIST[e].file.lastModifiedDate&&b.type==RESULTFILELIST[e].file.type){a=!0;break}return a}T[4]="se";_R_O_+="b";function sameFileCheck_html4(b){var a=!1,c=RESULTFILELIST.length;b=getFileName(b,!1);for(var e=0;e<c;e++){var f=RESULTFILELIST[e].originName;if(RESULTFILELIST[e].file&&b==f){a=!0;break}}return a}R[17]="c";T[5]="k";function getExtension(b){b=b.split(".");var a="";1<b.length&&(a=b[b.length-1]);return a.toLowerCase()}_R_O_+="H";T[6]="e";
function getIconImagePath(b){b=b.toLowerCase();var a=UPLOADTOP.G_CURRUPLOADER._config.webPath.image,c="";if(""!=b){switch(b){case "docx":b="doc";break;case "xlsx":b="xls";break;case "pptx":b="ppt";break;case "jpeg":b="jpg";break;case "tiff":b="tif"}for(var e=basicExtIcon.length,f=0;f<e;f++){var g=basicExtIcon[f];if(g==b){c=a+b+".png";break}}if(""==c)for(e=UPLOADTOP.G_CURRUPLOADER._config.addExtIcon.length,f=0;f<e;f++)if(g=UPLOADTOP.G_CURRUPLOADER._config.addExtIcon[f],g==b){c=a+b+".png";break}""==
c&&(c=a+"etc.png")}else c=a+"etc.png";return c}SIL[3]="b";
function uploadProgress(b,a,c,e){var f=getDialogDocument();"html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(G_oneFileUploadedSize+=b);f.getElementById("DEXT_fiVe_UP_uploadFileSize_"+e).innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(c.size).toString;1==a&&(UPLOADIDX++,f.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=upload_complete_count+1+"/"+TOTALUPLOADNUM);var g=0,g="html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?0==b?100:parseInt(100*G_oneFileUploadedSize/
c.size,10):parseInt(100*a/c.numberOfChunks,10);f.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+e).style.width=g+"%";100==g&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?f.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+e).innerHTML=Dext5Upload_Lang.upload_status.uploaded:f.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+e).innerHTML=Dext5Upload_Lang.upload_status.merge);PREVIOUSUPLOADEDSIZE+=b;b=0;b="html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?parseInt(1*UPLOADIDX/TOTALUPLOADNUM*100,10):"1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==TOTALUPLOADSIZE?100:parseInt(1*PREVIOUSUPLOADEDSIZE/TOTALUPLOADSIZE*100,10);f.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=b+"%";f.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width=b+"%";"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?f.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString:f.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString+"/"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALUPLOADSIZE).toString;-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")&&(b=(new Date).getTime()/1E3-G_UploadStartTime,0==b?(a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString,f.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=
a+"/"+Dext5Upload_Lang.upload_timeunit.sec,f.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML="-"):(a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE/b).toString,f.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=a+"/"+Dext5Upload_Lang.upload_timeunit.sec,b=b/PREVIOUSUPLOADEDSIZE*(TOTALUPLOADSIZE-PREVIOUSUPLOADEDSIZE),b=Math.ceil(b),b=viewTime(b),f.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML=b))}T[7]="y";
function uploadProgress_html4(b,a,c){var e=getDialogDocument();1==b&&(e.getElementById("DEXT_fiVe_UP_upload_size_title").innerHTML=Dext5Upload_Lang.upload_size+" :");adjustUploadFileListScroll(b-1,b,a);e.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]).style.width="0%";e.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b]).innerHTML="&nbsp;";e.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[b]).innerHTML=Dext5Upload_Lang.upload_status.uploading;intervalObj=setInterval(function(){f()},
400);var f=function(){var f;f=0;if(e.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b])){f=e.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]).style.width.split("%");f=Math.round(1*f[0]*c/100);f++;if(f==parseInt(3*c/5,10)+1)g("1");else if(f==parseInt(4*c/5,10)+1)g("2");else if(f==parseInt(9*c/10,10)+1)g("3");else if(f==parseInt(24*c/25,10)+1){g("4");return}if(e.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b])){e.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b]).innerHTML=
"&nbsp;";if(1==f)if(e.getElementById("DEXT_fiVe_UP_upload_count"))e.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=b+1+"/"+a;else return;var n=parseInt(1/c*100*f,10);if(e.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b])){e.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]).style.width=n+"%";for(var p=n=0;p<uploaders[b];p++)RESULTFILELIST[p].file&&(n+=RESULTFILELIST[p].fileSize);f=parseInt(1*b/a*100,10)+parseInt(parseInt(1/c*100*f,10)/a,10);e.getElementById("DEXT_fiVe_UP_total_upload_percent")&&
(e.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=f+"%",e.getElementById("DEXT_fiVe_UP_total_upload_progress_bar")&&(e.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width=f+"%",e.getElementById("DEXT_fiVe_UP_upload_size")&&0!=n&&(e.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(n).toString)))}}}},g=function(a){"1"==a?(clearInterval(intervalObj),intervalObj=setInterval(function(){f()},800)):"2"==a?(clearInterval(intervalObj),
intervalObj=setInterval(function(){f()},1E3)):"3"==a?(clearInterval(intervalObj),intervalObj=setInterval(function(){f()},1200)):"4"==a&&clearInterval(intervalObj)}}G_AP2[1]="a";G_AP2[3]="n";
function uploadProgress_html4_2(b,a){var c=getDialogDocument();1==b&&(c.getElementById("DEXT_fiVe_UP_upload_size_title").innerHTML=Dext5Upload_Lang.upload_size+" :");adjustUploadFileListScroll(b,b,a);var e=0,f;"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&(e=RESULTFILELIST[uploaders[b]].fileSize,f=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(e));c.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]).style.width="0%";"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4?c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+
uploaders[b]).innerHTML=f.toString:c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b]).innerHTML="&nbsp;";c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[b]).innerHTML=Dext5Upload_Lang.upload_status.uploading;var g=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,g=-1<g.indexOf("?")?g+"&":g+"?",g=g+("dext5CMD=uh4s&g="+RESULTFILELIST[uploaders[b]].guid);intervalObj=setInterval(function(){k()},500);var k=function(){var f=UPLOADTOP.DEXT5UPLOAD.ajax.postData(g+"&rg="+UPLOADTOP.DEXT5UPLOAD.util.makeGuid(),
""),k="",k=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(f):Dext5Base64.makeDecryptReponseMessage(f),f=k.split("|");if("uploading"==f[0]){if(c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b])){if("1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4){var l=f[1]*RESULTFILELIST[uploaders[b]].fileSize/100;displayUploadInfo(l,e,f[1],b)}else c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[b]).innerHTML=
"&nbsp;";if(c.getElementById("DEXT_fiVe_UP_upload_count")){c.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=b+1+"/"+a;if("0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4)if(c.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]))c.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[b]).style.width=f[1]+"%";else return;"100"==f[1]&&c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[b])&&(c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[b]).innerHTML=
Dext5Upload_Lang.upload_status.merge)}}}else if("success"==f[0]){clearInterval(intervalObj);try{var l=parseInt(f[3],10),k={size:l,numberOfChunks:1},q,r,u=f[1],w=u.indexOf("::");-1<w?(q=u.substring(0,w),r=u.substring(w+2)):(q=RESULTFILELIST[uploaders[UPLOADIDX]].originName,r=resultDataAry[0]);RESULTFILELIST[uploaders[UPLOADIDX]].fileSize=l;RESULTFILELIST[uploaders[UPLOADIDX]].uploadPath=r;RESULTFILELIST[uploaders[UPLOADIDX]].uploadName=f[2];RESULTFILELIST[uploaders[UPLOADIDX]].status="complete";RESULTFILELIST[uploaders[UPLOADIDX]].logicalPath=
"";RESULTFILELIST[uploaders[UPLOADIDX]].originName=q;clearInterval(intervalObj);"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4?(displayUploadInfo(l,l,"100",b),c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b).innerHTML=Dext5Upload_Lang.upload_status.uploaded,PREVIOUSUPLOADEDSIZE+=l,UPLOADIDX++):uploadProgress(l,1,k,uploaders[UPLOADIDX]);UPLOADIDX>=uploaders.length?totalUploadComplete():UploadFormSingle()}catch(y){}}else if("error"==f[0]){clearInterval(intervalObj);l=getUploadedFileListObj();
try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+f[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,f[1],f[2],l):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,f[1],eval("Dext5Upload_Lang.error_info.error_code_"+f[1]),l)}catch(t){}fileListReset();l=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==l.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(l.G_TagID,
l.G_LocalFileObject),l.G_LocalFileObject=[],l.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID));closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}}}_R_O_+="M";G_AP2[4]="w";
function displayUploadInfo(b,a,c,e){var f=getDialogDocument(),g=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize;f.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[e]).innerHTML=g(a).toString;f.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[e])&&(f.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[e]).style.width=c+"%");a=0;a=parseInt(1*(PREVIOUSUPLOADEDSIZE+b)/TOTALUPLOADSIZE*100,10);f.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=a+"%";f.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width=
a+"%";f.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=g(PREVIOUSUPLOADEDSIZE+b).toString+"/"+g(TOTALUPLOADSIZE).toString;g=(new Date).getTime()/1E3-G_UploadStartTime;0==g?(a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE+b).toString,f.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=a+"/"+Dext5Upload_Lang.upload_timeunit.sec,f.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML="-"):(a=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize((PREVIOUSUPLOADEDSIZE+b)/g).toString,f.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=
a+"/"+Dext5Upload_Lang.upload_timeunit.sec,b=g/(PREVIOUSUPLOADEDSIZE+b)*(TOTALUPLOADSIZE-(PREVIOUSUPLOADEDSIZE+b)),b=Math.ceil(b),b=viewTime(b),f.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML=b)}
function totalUploadComplete(){setTimeout(function(){"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.aftertUpload.setFileSize&&setFileSizeAfterUpload();"html5"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported&&removeWorker();"html5plus"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&removeSocket();closeSendDialog();var b=UPLOADTOP.G_CURRUPLOADER.ID;try{UPLOADTOP.G_CURRUPLOADER.init(b);setTabOrder();
var a=UPLOADTOP.G_CURRUPLOADER.ID;if(0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel){var c=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==c.G_UseAddLocalFileObject&&"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(c.G_TagID,c.G_LocalFileObject),c.G_LocalFileObject=[],c.G_TagID=[]);UPLOADTOP.G_CURRUPLOADER.isUploadComplete=!0;if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.html4LimitFileSize){for(var c=0,e=RESULTFILELIST.length,f=0;f<e;f++)"n"==RESULTFILELIST[f].isDelete&&""!=RESULTFILELIST[f].fileSize&&(c+=RESULTFILELIST[f].fileSize);var g=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize;if(0!=g&&c>g)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"026",Dext5Upload_Lang.file_maximum+" "+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(g).toString+Dext5Upload_Lang.message_limit_size)}catch(k){}else{"function"==typeof UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete?
UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete(a):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Complete(a);try{"1"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset()}catch(n){}}}else{"function"==typeof UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete?UPLOADTOP.G_CURRUPLOADER._config.event.transferComplete(a):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Complete(a);try{"1"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset()}catch(p){}}}"0"==UPLOADTOP.G_CURRUPLOADER._config.completeEventResetUse&&
(fileListReset(),"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUploadEx(a,!1))}catch(l){UPLOADTOP.G_CURRUPLOADER.init(b),setTabOrder()}},100)}R[18]="o";T[8]=" i";
function setCurrFileStatus(){var b=RESULTFILELIST.length,a=UPLOADTOP.G_CURRUPLOADER;if("ieplugin"==a._config.userRunTimeMode){a.newOriginalName=[];a.newUploadName=[];a.newUploadPath=[];a.newLogicalPath=[];a.newSize=[];a.newGuid=[];a.newStatus=[];a.newOrder=[];a.newLocalPath=[];a.newMark=[];a.newResponseCustomValue=[];a.newLargeFiles=[];a.newSelectFlag=[];a.newGroupId=[];a.delUniqKey=[];a.delOriginalName=[];a.delSize=[];a.delLargeFiles=[];a.nonDelUniqKey=[];a.nonDelOriginalName=[];a.nonDelUploadPath=
[];a.nonDelSize=[];a.nonDelOrder=[];a.nonDelLargeFiles=[];a.nonDelSelectFlag=[];a.nonDelCustomValue=[];a.nonDelLogicalPath=[];for(var b=a.frameWin.Dext5PL.GetFileInfoList(-1).split(G_vertical),c=b.length,e=G_UploadedFileListObj.length,f=1,g=0;g<c;g++)if(""!=b[g]){var k=b[g].split(G_formfeed);if("0"==k[0]){for(var n=!1,p,l=0;l<e;l++)if(G_UploadedFileListObj[l].order==f){n=!0;p=G_UploadedFileListObj[l];break}n?(a.newOriginalName.push(p.originName),a.newUploadName.push(p.uploadName),a.newUploadPath.push(p.uploadPath),
a.newSize.push(p.fileSize),a.newGuid.push(p.guid),a.newLogicalPath.push(p.logicalPath),a.newStatus.push(p.status),a.newResponseCustomValue.push(p.responseCustomValue),isLargeFiles(p.fileSize)||k[13]&&"1"==k[13]?a.newLargeFiles.push("1"):a.newLargeFiles.push("0"),a.newGroupId.push(p.responseGroupId)):(a.newOriginalName.push(k[2]),a.newUploadName.push(""),a.newUploadPath.push(""),n=k[3],9E18<=n&&(n=""),a.newSize.push(n),a.newGuid.push(""),a.newLogicalPath.push(k[12]),a.newStatus.push("ready"),a.newResponseCustomValue.push(""),
isLargeFiles(k[3])||k[13]&&"1"==k[13]?a.newLargeFiles.push("1"):a.newLargeFiles.push("0"),a.newGroupId.push(""));a.newOrder.push(g+1);G_LocalFileObjectLocalPath[k[4]]?(a.newLocalPath.push(G_LocalFileObjectLocalPath[k[4]]),a.newMark.push(G_LocalFileObjectMark[k[4]])):(a.newLocalPath.push(k[5]),a.newMark.push(""));"1"==k[10]||"1"==k[11]?a.newSelectFlag.push("1"):a.newSelectFlag.push("0");f++}else if("1"==k[0]){a.nonDelUniqKey.push(k[9]);a.nonDelOriginalName.push(k[2]);var n=k[5],l=k[5].indexOf("&fileVirtualPath="),
q=k[5].indexOf("&fileOrgName=");-1<k[5].indexOf("&fileVirtualPath=")&&(n=k[5].substring(l+17,q),n=decodeURIComponent(n));a.nonDelUploadPath.push(n);n=k[3];9E18<=n&&(n="");a.nonDelSize.push(n);isLargeFiles(k[3])||k[13]&&"1"==k[13]?a.nonDelLargeFiles.push("1"):a.nonDelLargeFiles.push("0");a.nonDelOrder.push(g+1);"1"==k[10]||"1"==k[11]?a.nonDelSelectFlag.push("1"):a.nonDelSelectFlag.push("0");n="";l=k[5].indexOf("&customValue=");-1<l&&(n=k[5].substring(l+13));a.nonDelCustomValue.push(decodeURIComponent(n));
a.nonDelLogicalPath.push(k[12])}}c=G_DeletedFileListObj.length;if(0==c){if(g=a.frameWin.Dext5PL.GetDeleteFileList())for(c=g.split(G_vertical),e=c.length,g=0;g<e;g++)p=c[g].split(G_formfeed),a.delUniqKey.push(p[0]),b=p[1],-1<b.lastIndexOf("\\")&&(b=b.substring(b.lastIndexOf("\\")+1,b.length)),a.delOriginalName.push(b),a.delSize.push(p[2]),isLargeFiles(p[2])||p[3]&&"1"==p[3]?a.delLargeFiles.push("1"):a.delLargeFiles.push("0")}else for(g=0;g<c;g++)p=G_DeletedFileListObj[g],a.delUniqKey.push(p.uniqKey),
b=p.originName,-1<b.lastIndexOf("\\")&&(b=b.substring(b.lastIndexOf("\\")+1,b.length)),a.delOriginalName.push(b),a.delSize.push(p.fileSize),a.delLargeFiles.push(p.largeFiles)}else for(a.newOriginalName=[],a.newUploadName=[],a.newUploadPath=[],a.newLogicalPath=[],a.newSize=[],a.newGuid=[],a.newStatus=[],a.newOrder=[],a.newLocalPath=[],a.newMark=[],a.newResponseCustomValue=[],a.newLargeFiles=[],a.newSelectFlag=[],a.newGroupId=[],a.delUniqKey=[],a.delOriginalName=[],a.delSize=[],a.delLargeFiles=[],a.nonDelUniqKey=
[],a.nonDelOriginalName=[],a.nonDelUploadPath=[],a.nonDelSize=[],a.nonDelOrder=[],a.nonDelLargeFiles=[],a.nonDelSelectFlag=[],a.nonDelCustomValue=[],a.nonDelLogicalPath=[],p=1,c=0,e=document.getElementById("file_list").getElementsByTagName("input"),g=0;g<b;g++)"form"==a._config.subMode&&"y"==RESULTFILELISTCLON[g].isDelete&&(a.delUniqKey.push(RESULTFILELISTCLON[g].fileIdx),a.delOriginalName.push(RESULTFILELISTCLON[g].originName),a.delSize.push(RESULTFILELISTCLON[g].fileSize),isLargeFiles(RESULTFILELISTCLON[g].fileSize)||
RESULTFILELISTCLON[g].largeFile&&"1"==RESULTFILELISTCLON[g].largeFile?a.delLargeFiles.push("1"):a.delLargeFiles.push("0")),RESULTFILELIST[g].file?"y"!=RESULTFILELIST[g].isDelete&&(a.newOriginalName.push(RESULTFILELIST[g].originName),a.newSize.push(RESULTFILELIST[g].fileSize),a.newUploadName.push(RESULTFILELIST[g].uploadName),a.newUploadPath.push(RESULTFILELIST[g].uploadPath),a.newLogicalPath.push(""),a.newGuid.push(RESULTFILELIST[g].guid),a.newStatus.push(RESULTFILELIST[g].status),a.newLocalPath.push(RESULTFILELIST[g].localPath),
a.newMark.push(RESULTFILELIST[g].mark),a.newResponseCustomValue.push(RESULTFILELIST[g].responseCustomValue),isLargeFiles(RESULTFILELIST[g].fileSize)||RESULTFILELIST[g].largeFile&&"1"==RESULTFILELIST[g].largeFile?a.newLargeFiles.push("1"):a.newLargeFiles.push("0"),e[c].getAttribute("dext_select")&&"1"==e[c].getAttribute("dext_select")||e[c].checked?a.newSelectFlag.push("1"):a.newSelectFlag.push("0"),a.newGroupId.push(RESULTFILELIST[g].groupId),c++):"y"==RESULTFILELIST[g].isDelete?(a.delUniqKey.push(RESULTFILELIST[g].fileIdx),
a.delOriginalName.push(RESULTFILELIST[g].originName),a.delSize.push(RESULTFILELIST[g].fileSize),isLargeFiles(RESULTFILELIST[g].fileSize)||RESULTFILELIST[g].largeFile&&"1"==RESULTFILELIST[g].largeFile?a.delLargeFiles.push("1"):a.delLargeFiles.push("0")):(a.nonDelUniqKey.push(RESULTFILELIST[g].fileIdx),a.nonDelOriginalName.push(RESULTFILELIST[g].originName),a.nonDelUploadPath.push(RESULTFILELIST[g].webPath),a.nonDelSize.push(RESULTFILELIST[g].fileSize),isLargeFiles(RESULTFILELIST[g].fileSize)||RESULTFILELIST[g].largeFile&&
"1"==RESULTFILELIST[g].largeFile?a.nonDelLargeFiles.push("1"):a.nonDelLargeFiles.push("0"),e[c].getAttribute("dext_select")&&"1"==e[c].getAttribute("dext_select")||e[c].checked?a.nonDelSelectFlag.push("1"):a.nonDelSelectFlag.push("0"),a.nonDelCustomValue.push(RESULTFILELIST[g].customValue),a.nonDelLogicalPath.push(""),c++),"n"==RESULTFILELIST[g].isDelete&&(RESULTFILELIST[g].file?a.newOrder.push(p):a.nonDelOrder.push(p),p++)}T[9]="s ";_R_O_+="N";
function removeWorker(){for(var b=0;b<UPLOADTOP.G_CURRUPLOADER.frameWin.fileworkers.length;b++)UPLOADTOP.G_CURRUPLOADER.frameWin.fileworkers[b]&&UPLOADTOP.G_CURRUPLOADER.frameWin.fileworkers[b].terminate();UPLOADTOP.G_CURRUPLOADER.frameWin.fileworkers=[];for(b=0;b<UPLOADTOP.G_CURRUPLOADER.frameWin.uploadworkers.length;b++)UPLOADTOP.G_CURRUPLOADER.frameWin.uploadworkers[b]&&UPLOADTOP.G_CURRUPLOADER.frameWin.uploadworkers[b].terminate();UPLOADTOP.G_CURRUPLOADER.frameWin.uploadworkers=[]}
function removeSocket(){G_oneFileUploadedSize=0;if(UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported){G_Complete_Socket_worker=0;G_SocketUploadWorkArray=[];for(var b=G_SocketWorkerPool.length,a=0;a<b;a++)G_SocketWorkerPool[a]&&G_SocketWorkerPool[a].postMessage({type:"close",workerIdx:a})}else{for(a=0;a<G_socketArr.length;a++)if(G_socketArr[a])G_socketArr[a].onClose();G_socketArr=[]}}
function setFileListBorder(){var b=document.getElementById("file_list"),a=b.childNodes.length;b.className=0<a?UPLOADTOP.G_CURRUPLOADER.fileListHeight>22*a?"lst_line_"+UPLOADTOP.G_CURRUPLOADER._config.uploadBorderStyle:"":"lst_line_"+UPLOADTOP.G_CURRUPLOADER._config.uploadBorderStyle}R[19]="m";T[10]="i";
function getTotalFileCount(){var b=null;if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{b=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileCount()}catch(a){}else b=TOTALFILELISTNUM;return b}function getTotalFileSize(){var b=null;if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{b=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileSize()}catch(a){}else b=TOTALFILELISTSIZE;return b}
function getWindowClientSize(b){var a=0,c=0;"number"==typeof b.innerWidth?(a=b.innerWidth,c=b.innerHeight):b.document.documentElement&&(b.document.documentElement.clientWidth||b.document.documentElement.clientHeight)?(a=b.document.documentElement.clientWidth,c=b.document.documentElement.clientHeight):b.document.body&&(b.document.body.clientWidth||b.document.body.clientHeight)&&(a=b.document.body.clientWidth,c=b.document.body.clientHeight);return[a,c]}T[11]="n";R[20]="/";
function getWindowScrollPos(b){return"number"===typeof b.pageYOffset?[b.pageXOffset,b.pageYOffset]:b.document.body&&(b.document.body.scrollLeft||b.document.body.scrollTop)?[b.document.body.scrollLeft,b.document.body.scrollTop]:b.document.documentElement&&(b.document.documentElement.scrollLeft||b.document.documentElement.scrollTop)?[b.document.documentElement.scrollLeft,b.document.documentElement.scrollTop]:[0,0]}
function setLayerbgResize(){var b=getDialogDocument(),a=b.getElementById("upload_background"),c="CSS1Compat"==b.compatMode?b.documentElement.scrollHeight:b.body.scrollHeight;a.firstChild.style.width=("CSS1Compat"==b.compatMode?b.documentElement.scrollWidth:b.body.scrollWidth)+"px";a.firstChild.style.height=c+"px"}Z[4]=":";T[12]="c";var dragPopup=null,currentScrollPos;
function startDrag(b){var a=getDialogWindow(),c=getDialogDocument();b||(b=a.event);offsetX=b.clientX;offsetY=b.clientY;dragPopup.style.left||(dragPopup.style.left="0px");dragPopup.style.top||(dragPopup.style.top="0px");coordX=parseInt(dragPopup.style.left);coordY=parseInt(dragPopup.style.top);drag=!0;UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"mousemove",dragDiv);UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"mouseup",stopDrag);UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b)}
function dragDiv(b){if(drag){var a=getDialogWindow();b||(b=a.event);var c=getWindowClientSize(a),e=[parseInt(dragPopup.style.width,10)||0,parseInt(dragPopup.style.height,10)||0];currentScrollPos=getWindowScrollPos(a);var a=coordX+b.clientX-offsetX,f=coordY+b.clientY-offsetY;0>=a-currentScrollPos[0]&&(a=currentScrollPos[0]);0>=f-currentScrollPos[1]&&(f=currentScrollPos[1]);a+e[0]>=c[0]+currentScrollPos[0]&&(a=c[0]-e[0]+currentScrollPos[0]);f+e[1]>=c[1]+currentScrollPos[1]&&(f=c[1]-e[1]+currentScrollPos[1]);
dragPopup.style.left=a+"px";dragPopup.style.top=f+"px";UPLOADTOP.DEXT5UPLOAD.util.stopEvent(b);UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b)}}R[21]="r";function stopDrag(b){b=getDialogDocument();G_PreviewDialogPosition.top=dragPopup.style.top;G_PreviewDialogPosition.left=dragPopup.style.left;drag=!1;dragPopup.style.cursor="";UPLOADTOP.DEXT5UPLOAD.util.removeEvent(b,"mousemove",dragDiv);UPLOADTOP.DEXT5UPLOAD.util.removeEvent(b,"mouseup",stopDrag)}
function makeDragPopup(b,a){drag=!1;var c=null,c="imgPreview"==a?b.getElementsByTagName("strong")[0]:b.getElementsByTagName("div")[2];UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"mousedown",startDrag);UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"mouseover",function(){b.style.cursor="move"});UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"mouseout",function(){b.style.cursor="default"});dragPopup=b}
function openFile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&0!=isExecuteApi()){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.DoOpenFile()}catch(f){}else{for(var g=
document.getElementById("file_list").getElementsByTagName("input"),k=g.length,n=!1,p=!1,l=-1,q=-1,c=-1,e=0;e<k;e++){var r=g[e].getAttribute("dext_select");if(r&&"1"==r){n=!0;l=e;break}else g[e].checked&&(p=!0,-1==q&&(q=e))}if(n){for(e=0;e<k;e++)l==e&&(c=g[e].getAttribute("listvalue"),null!=c&&void 0!=c&&"n"==RESULTFILELIST[c].isWebFile||"y"==RESULTFILELIST[c].isDelete)&&(c=-1);if(-1!=c&&(""!=RESULTFILELIST[c].originName||"form"!=UPLOADTOP.G_CURRUPLOADER._config.subMode))if(""==UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension)openSubmit(c);
else if(""!=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension){g=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension.split(",");k=g.length;for(e=0;e<k;e++)if(g[e]==RESULTFILELIST[c].fileExt){openSubmit(c);return}"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload([c]):downloadSubmit([c])}}else if(p){for(e=0;e<k;e++)if(g[e].checked){if(c=g[e].getAttribute("listvalue"),null!=c&&void 0!=c&&"n"==RESULTFILELIST[c].isWebFile||"y"==RESULTFILELIST[c].isDelete)g[e].checked=!1,c=-1}else g[e].checked=
!1;if(-1!=c&&(""!=RESULTFILELIST[c].originName||"form"!=UPLOADTOP.G_CURRUPLOADER._config.subMode))if(""==UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension)openSubmit(c);else if(""!=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension){g=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension.split(",");k=g.length;for(e=0;e<k;e++)if(g[e]==RESULTFILELIST[c].fileExt){openSubmit(c);return}"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload([c]):downloadSubmit([c])}}}}}T[13]="o";
function openSubmit(b){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useViewOrOpenEvent){var a="0",c="",e="",f="";"y"==RESULTFILELIST[b].isWebFile&&(a="1");var c=RESULTFILELIST[b].fileIdx,e=RESULTFILELIST[b].originName,f=RESULTFILELIST[b].webPath,g=!0,k="0";isLargeFiles(RESULTFILELIST[b].fileSize)&&(k="1");try{g=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeFileViewOrOpen(UPLOADTOP.G_CURRUPLOADER.ID,a,c,e,f,k)}catch(n){}if(1!=g)return}if("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload)excuteHybridDownloadOrOpen("U03",
[RESULTFILELIST[b]]);else{var a="downloadRequest",c="bmp,gif,jpeg,jpg,png,pdf,swf,txt,xml,html,htm",p=getExtension(RESULTFILELIST[b].originName),p=p.toLowerCase(),l=40;UPLOADTOP.DEXT5UPLOAD.browser.ie&&UPLOADTOP.DEXT5UPLOAD.browser.edge?l=70:UPLOADTOP.DEXT5UPLOAD.browser.ie?(c+=",doc,docx,xls,xlsx,ppt,pptx,hwp,gul,tif,tiff",l=40):UPLOADTOP.DEXT5UPLOAD.browser.chrome?(c+=",mp4",l=70):UPLOADTOP.DEXT5UPLOAD.browser.gecko?l=40:UPLOADTOP.DEXT5UPLOAD.browser.opera?(c+=",mp4",l=80):UPLOADTOP.DEXT5UPLOAD.browser.safari&&
(c+=",tif,tiff",l=100);-1<c.split(",").indexOf(p)&&(a="openRequest");var q=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl;""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(q=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl);if("openRequest"==a)if(g="",a=q,1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain){var r=document.createElement("div"),c=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();r.innerHTML='<iframe name="openFileCheck" id="openFileCheck" style="display:none;" src="'+c+'"></iframe>';
r.style.display="none";document.body.appendChild(r);var u=document.getElementById("openFileCheck");UPLOADTOP.DEXT5UPLOAD.util.addEvent(u,"load",function(){u.contentWindow.postMessage("check","*")},!0);if(window.postMessage){var w=function(a){document.body.removeChild(r);g=a.data;a=!0;if(null!=g&&""!=g&&""!=UPLOADTOP.DEXT5UPLOAD.util.trim(g)&&(g=UPLOADTOP.DEXT5UPLOAD.util.trim(g),"[FAIL]"==g.substring(0,6))){a=!1;g=g.substring(6);g=Dext5Base64.makeDecryptReponseMessage(g);var c=g.split("|");if("error"==
c[0])try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+c[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(_uploader.ID,c[1],c[2]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(_uploader.ID,c[1],eval("Dext5Upload_Lang.error_info.error_code_"+c[1]))}catch(e){}}if(a){var f;a=winHeight=0;-1<"pdf,swf,txt,xml,html,htm".indexOf(p)?(a=1E3,winHeight=screen.availHeight,f=0):"mp4"==p?(a=600,winHeight=400):a=winHeight=200;var c=screen.availWidth,k=screen.availHeight;c<=a&&(a=c-40);k<=winHeight&&(winHeight=k-l);
var c=UPLOADTOP.DEXT5UPLOAD.util.makeGuidTagName("openPopUpWin_"),k=UPLOADTOP.G_CURRUPLOADER._config.viewerUrl,k=0>k.indexOf("?")?k+"?":k+"&",n="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var u;u=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"openRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);u+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;
u+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].webPath+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d31"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.skinName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d41"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
UPLOADTOP.DEXT5UPLOAD.isRelease+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d42"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+q+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d43"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+p+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u+="d44"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"true"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;var C="";UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)&&
(C=document.domain);u+="d30"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+C+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;u=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(u);n=n+("d00="+u)+("&customValue="+encodeURI(encodeURIComponent(RESULTFILELIST[b].customValue)))}else n+="dext5CMD=openRequest",n+="&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx,"2.7.1057770.1856.01">UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?(n+="&fileVirtualPath="+encodeURIComponent(RESULTFILELIST[b].webPath),
n+="&fileOrgName="+encodeURIComponent(RESULTFILELIST[b].originName),n+="&customValue="+encodeURIComponent(RESULTFILELIST[b].customValue)):(n+="&fileVirtualPath="+encodeURI(encodeURIComponent(RESULTFILELIST[b].webPath)),n+="&fileOrgName="+encodeURI(encodeURIComponent(RESULTFILELIST[b].originName)),n+="&customValue="+encodeURI(encodeURIComponent(RESULTFILELIST[b].customValue))),n+="&skinName="+UPLOADTOP.G_CURRUPLOADER._config.skinName,n+="&dext5Release="+UPLOADTOP.DEXT5UPLOAD.isRelease,n+="&downloadUrl="+
encodeURI(q),n+="&fileExt="+p,n+="&PopupInit=true",C="",UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)&&(C=document.domain),n+="&documentDomain="+C;Dext5_PopupCenter(k+n,c,a,winHeight,f,void 0,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no",p)}UPLOADTOP.DEXT5UPLOAD.util.removeEvent(window,"message",w)};UPLOADTOP.DEXT5UPLOAD.util.addEvent(window,"message",w)}"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(c=""+("d01"+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"ofc"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),c+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].webPath+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(c),c=[["d00",c],["customValue",encodeURIComponent(RESULTFILELIST[b].customValue)]]):c=[["dext5CMD","ofc"],["cd","1"],["fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx],["fileVirtualPath",encodeURIComponent(RESULTFILELIST[b].webPath)],
["fileOrgName",encodeURIComponent(RESULTFILELIST[b].originName)],["customValue",encodeURIComponent(RESULTFILELIST[b].customValue)]];UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,a,"download_frame",c)}else c="","1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(c=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"ofc"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),c+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].webPath+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(c),c="d00="+c):(c+="dext5CMD=ofc&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx,c+=
"&fileVirtualPath="+encodeURI(encodeURIComponent(RESULTFILELIST[b].webPath)),c+="&fileOrgName="+encodeURI(encodeURIComponent(RESULTFILELIST[b].originName))),c+="&customValue="+encodeURI(encodeURIComponent(RESULTFILELIST[b].customValue)),UPLOADTOP.DEXT5UPLOAD.ajax.postData(a,c,function(a){var c=!0;if(null!=a&&""!=a&&""!=UPLOADTOP.DEXT5UPLOAD.util.trim(a)&&(a=UPLOADTOP.DEXT5UPLOAD.util.trim(a),"[FAIL]"==a.substring(0,6)&&(c=!1,a=a.substring(6),a=Dext5Base64.makeDecryptReponseMessage(a),a=a.split("|"),
"error"==a[0])))try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.DEXT5UPLOAD.ID,a[1],a[2]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.DEXT5UPLOAD.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]))}catch(e){}if(c){var f,c=winHeight=0;-1<"pdf,swf,txt,xml,html,htm".indexOf(p)?(c=1E3,winHeight=screen.availHeight,f=0):"mp4"==p?(c=600,winHeight=400):c=winHeight=200;a=screen.availWidth;var g=screen.availHeight;a<=c&&(c=a-
40);g<=winHeight&&(winHeight=g-l);a=UPLOADTOP.DEXT5UPLOAD.util.makeGuidTagName("openPopUpWin_");var g=UPLOADTOP.G_CURRUPLOADER._config.viewerUrl,g=0>g.indexOf("?")?g+"?":g+"&",k="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var n;n=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"openRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);n+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].webPath+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[b].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d31"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.skinName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;
n+="d41"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.DEXT5UPLOAD.isRelease+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d42"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+q+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d43"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+p+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n+="d44"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"true"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;
var r="";UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)&&(r=document.domain);n+="d30"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+r+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(n);k=k+("d00="+n)+("&customValue="+encodeURI(encodeURIComponent(RESULTFILELIST[b].customValue)))}else k+="dext5CMD=openRequest",k+="&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx,"2.7.1057770.1856.01">UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?
(k+="&fileVirtualPath="+encodeURIComponent(RESULTFILELIST[b].webPath),k+="&fileOrgName="+encodeURIComponent(RESULTFILELIST[b].originName),k+="&customValue="+encodeURIComponent(RESULTFILELIST[b].customValue)):(k+="&fileVirtualPath="+encodeURI(encodeURIComponent(RESULTFILELIST[b].webPath)),k+="&fileOrgName="+encodeURI(encodeURIComponent(RESULTFILELIST[b].originName)),k+="&customValue="+encodeURI(encodeURIComponent(RESULTFILELIST[b].customValue))),k+="&skinName="+UPLOADTOP.G_CURRUPLOADER._config.skinName,
k+="&dext5Release="+UPLOADTOP.DEXT5UPLOAD.isRelease,k+="&downloadUrl="+encodeURI(q),k+="&fileExt="+p,k+="&PopupInit=true",r="",UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)&&(r=document.domain),k+="&documentDomain="+r;Dext5_PopupCenter(g+k,a,c,winHeight,f,void 0,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no",p)}});else a=[],a.push(b),downloadSubmit(a)}}
function Dext5_PopupCenter(b,a,c,e,f,g,k,n){f=void 0!=f?f:screen.height/2-e/2-40;g=void 0!=g?g:screen.width/2-c/2;-1<G_ImageFileExt.indexOf(n)&&(f=100);b=window.open(b,a,k+", width="+c+", height="+e+", top="+f+", left="+g);window.focus&&b&&b.focus();return b}_R_O_+="O";R[22]="a";
function downloadFile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&0!=isExecuteApi()){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SaveFile()}catch(f){}else{for(var e=
document.getElementById("file_list"),c=document.getElementById("chk_all_box"),e=e.getElementsByTagName("input"),g=e.length,k=[],n=[],p=0;p<g;p++){var l=e[p].getAttribute("dext_select");l&&"1"==l?k.push(p):e[p].checked&&k.push(p)}l=k.length;for(p=0;p<g;p++)for(var q=0;q<l;q++)if(p==k[q]){var r=e[k[q]].getAttribute("listvalue");null!=r&&void 0!=r&&"y"==RESULTFILELIST[r].isWebFile&&"n"==RESULTFILELIST[r].isDelete&&""!=RESULTFILELIST[r].originName?(n.push(r),e[k[q]].checked=!0,fileListNoneSelection(e[k[q]])):
(e[p].checked=!1,fileListNoneSelection(e[p]),c.checked=!1)}0<n.length&&("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload?downloadSubmit(n):"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload(n):"0"==UPLOADTOP.G_CURRUPLOADER._config.downloadMulti?downloadSubmit(n):downloadMultiIndividual(n))}}}
function downloadCurrFile(b){if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SaveFile()}catch(a){}else{var c=[];b=document.getElementById("file_list").getElementsByTagName("input")[b].getAttribute("listvalue");c.push(b);0<c.length&&("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload?downloadSubmit(c):"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload(c):downloadSubmit(c))}}
function downloadAllFile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&0!=isExecuteApi())if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen&&-1==getSelectedFileCount())alert(Dext5Upload_Lang.message_file_notexist);else if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SaveAllFiles()}catch(f){}else if(e=
document.getElementById("file_list"),e.hasChildNodes()){c=document.getElementById("chk_all_box");c.checked=!0;checkAll();for(var e=e.getElementsByTagName("input"),g=e.length,k=[],n=0;n<g;n++){if(e[n].checked){var p=e[n].getAttribute("listvalue");null!=p&&void 0!=p&&"y"==RESULTFILELIST[p].isWebFile&&"n"==RESULTFILELIST[p].isDelete&&""!=RESULTFILELIST[p].originName?k.push(p):(e[n].checked=!1,c.checked=!1)}e[n].getAttribute("dext_select")&&"1"==e[n].getAttribute("dext_select")&&fileListNoneSelection(e[n])}0<
k.length&&("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload?downloadSubmit(k):"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload(k):"0"==UPLOADTOP.G_CURRUPLOADER._config.downloadMulti?downloadSubmit(k):downloadMultiIndividual(k))}}Z[5]="/";
function downloadMultiIndividual(b){G_DownloadCnt=0;var a=[b[G_DownloadCnt]];downloadSubmit(a);var c=b.length;if(1<c){G_DownloadCnt++;var e=setInterval(function(){a=[b[G_DownloadCnt]];c>G_DownloadCnt?(downloadSubmit(a),G_DownloadCnt++):(clearInterval(e),G_DownloadCnt=0)},1E3)}}
function reOrganizeDownloadWebPathUrl(b){for(var a=[],c=b.length,e=location.protocol+"//"+document.domain+(""==location.port?"":":"+location.port),f,g=0;g<c;g++)f=RESULTFILELIST[b[g]],"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload||0!=f.webPath.indexOf(e)||(f.webPath=f.webPath.replace(e,"")),a.push(f);return a}Z[6]="/";_R_O_+="P";R[23]="o";
function downloadSubmit(b,a){var c=reOrganizeDownloadWebPathUrl(b),e=c.length;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDownloadEvent)for(var f="",g="",k="",n="0",p=!0,l=0;l<e;l++){var f=c[l].fileIdx,g=c[l].originName,k=c[l].webPath,n=isLargeFiles(c[l].fileSize)?"1":"0",q="0";l==e-1&&(q="1");try{p=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeFileDownload(UPLOADTOP.G_CURRUPLOADER.ID,"1",f,g,k,n,q)}catch(r){}if(1!=p)return}if(UPLOADTOP.DEXT5UPLOAD.browser.mobile){var u=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl;
""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(u=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl);l=0>u.indexOf("?")?u+"?":u+"&";"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(e=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"downloadRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,
e+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c[0].webPath+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,e+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c[0].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,e=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e),l+="d00="+e):(l=l+"dext5CMD=downloadRequest"+("&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx),l+="&fileVirtualPath="+encodeURI(encodeURIComponent(c[0].webPath)),
l+="&fileOrgName="+encodeURI(encodeURIComponent(c[0].originName)));l+="&customValue="+encodeURIComponent(c[0].customValue);window.open(l)}else if(u=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(u=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl),"1"==UPLOADTOP.G_CURRUPLOADER._config.downloadMulti&&(u+=(-1<u.indexOf("?")?"&":"?")+"md="+UPLOADTOP.DEXT5UPLOAD.util.makeGuid()),"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload)excuteHybridDownloadOrOpen("U01",
c,a);else if(1<e)if("0"==UPLOADTOP.G_CURRUPLOADER._config.resumeDownload&&"0"==UPLOADTOP.G_CURRUPLOADER._config.downloadMulti){var w=[];if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){g=fileOrgNameList="";for(l=0;l<e;l++)k=[],k.push("customValue"),k.push(encodeURIComponent(c[l].customValue)),w.push(k),g+=c[l].webPath+"|",fileOrgNameList+=c[l].originName+"|";e=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mdr"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);
e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+fileOrgNameList+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain&&(e+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);e=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e);l=[];l.push("d00");l.push(e);w.push(l)}else for(w=[["dext5CMD","mdr"],["fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx]],1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain&&(g=[],g.push("cd"),g.push("1"),w.push(g)),l=0;l<e;l++)g=[],g.push("fileVirtualPath"),g.push(encodeURIComponent(c[l].webPath)),w.push(g),g=[],g.push("fileOrgName"),g.push(encodeURIComponent(c[l].originName)),
w.push(g),k=[],k.push("customValue"),k.push(encodeURIComponent(c[l].customValue)),w.push(k);UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u,"download_frame",w)}else{for(l=f=0;l<e;l++)f+=c[l].fileSize;var f=getLoadingImageInterval(f),y=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain){if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){g=
fileOrgNameList="";for(l=0;l<e;l++)k=[],k.push("customValue"),k.push(encodeURIComponent(c[l].customValue)),w.push(k),g+=c[l].webPath+"|",fileOrgNameList+=c[l].originName+"|";e=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mzf"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);e+="d28"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mz"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
fileOrgNameList+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e);l=[];l.push("d00");l.push(e);w.push(l)}else for(w=[["dext5CMD","mzf"],["c2","mz"],["fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx],["cd","1"],["zfn",y]],l=0;l<e;l++)g=[],g.push("fileVirtualPath"),g.push(encodeURIComponent(c[l].webPath)),w.push(g),g=[],g.push("fileOrgName"),g.push(encodeURIComponent(c[l].originName)),w.push(g),k=[],k.push("customValue"),k.push(encodeURIComponent(c[l].customValue)),
w.push(k);UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u,"download_frame",w);G_zipIntervalData.downloadUrl=u;G_zipIntervalData.zipGuid=y;G_zipIntervalData.downloadLIST=c;displayDownloadReady(!0);G_zipInterval=setInterval(function(){if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var a;a=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"zs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);a+="d28"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
"mz"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;a+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;a+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;a=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(a);w=[["d00",a]]}else w=[["dext5CMD","zs"],["c2","mz"],["cd","1"],["zfn",y]];UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,u,"download_frame",
w)},f)}else{var t=UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest();t.open("POST",u,!0);k="";if("undefined"==typeof FormData){if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){g=fileOrgNameList="";for(l=0;l<e;l++)k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","customValue",encodeURIComponent(c[l].customValue)),g+=c[l].webPath+"|",fileOrgNameList+=c[l].originName+"|";e=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mzf"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);
e+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+fileOrgNameList+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e);k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","d00",e,!0)}else{for(l=0;l<e;l++)k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","fileVirtualPath",encodeURIComponent(c[l].webPath)),k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","fileOrgName",encodeURIComponent(c[l].originName)),k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg",
"customValue",encodeURIComponent(c[l].customValue));k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","dext5CMD","mzf");k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","zfn",y);k+=UPLOADTOP.DEXT5UPLOAD.util.buildFormData("----12345678wertysdfg","fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx,!0)}t.setRequestHeader("Content-Type","multipart/form-data; boundary=----12345678wertysdfg");t.setRequestHeader("Content-Length",k.length)}else if(k=new FormData,
"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){g=fileOrgNameList="";for(l=0;l<e;l++)k.append("customValue",encodeURIComponent(c[l].customValue)),g+=c[l].webPath+"|",fileOrgNameList+=c[l].originName+"|";e=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mzf"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);e+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+fileOrgNameList+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e);k.append("d00",e)}else{for(l=0;l<e;l++)k.append("fileVirtualPath",encodeURIComponent(c[l].webPath)),
k.append("fileOrgName",encodeURIComponent(c[l].originName)),k.append("customValue",encodeURIComponent(c[l].customValue));k.append("dext5CMD","mzf");k.append("zfn",y);k.append("fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx)}t.onreadystatechange=function(){if(4==t.readyState&&200<=t.status){var a=t.responseText;if("[FAIL]"==a.substring(0,6)){a=a.substring(6);a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(a):
Dext5Base64.makeDecryptReponseMessage(a);a=a.split("|");if("error"==a[0])try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],a[2]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]))}catch(b){}null!=G_zipInterval&&clearInterval(G_zipInterval);displayDownloadReady(!1)}else if((a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=
UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(a):Dext5Base64.makeDecryptReponseMessage(a))&&""!=a&&0==a.indexOf("error|")){a=a.split("|");try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],a[2]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]))}catch(c){}null!=G_zipInterval&&clearInterval(G_zipInterval);displayDownloadReady(!1)}}};
t.send(k);displayDownloadReady(!0);G_zipInterval=setInterval(function(){var a="";"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(a=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"zs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),a+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(a),a="d00="+a):a="dext5CMD=zs&zfn="+y;var b=UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest();
b.open("POST",u,!1);b.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");b.onreadystatechange=function(){if(4==b.readyState)if(200==b.status){var a=b.responseText;if("[OK]"==a.substring(0,4)){var e="",e=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(a.substring(4)):Dext5Base64.makeDecryptReponseMessage(a.substring(4));"d"==e&&(clearInterval(G_zipInterval),displayDownloadReady(!1),download_request(u,
c,y))}}else if(400==b.status||415==b.status||413==b.status||500==b.status){clearInterval(G_zipInterval);displayDownloadReady(!1);a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200012",Dext5Upload_Lang.error_info.error_code_200012,a)}catch(f){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();a=UPLOADTOP.G_CURRUPLOADER.frameWin;"1"==a.G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&
(inputFileObjectChange(a.G_TagID,a.G_LocalFileObject),a.G_LocalFileObject=[],a.G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID))}};b.send(a)},f)}}else download_request(u,c,y)}T[14]="r";Z[7]="w";
function download_request(b,a,c){var e=document.createElement("form");e.target="download_frame";e.action=b;e.method="1"==UPLOADTOP.G_CURRUPLOADER._config.resumeDownload?"get":"post";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var f;f=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"downloadRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);"1"==UPLOADTOP.G_CURRUPLOADER._config.resumeDownload&&(f+="d27"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);1==a.length?(f+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,f+="d25"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a[0].webPath+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,f+="d26"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a[0].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,
4<b.length&&"http"==b.substring(0,4).toLowerCase()&&b.match(/:\/\/(.[^\/]+)/)[1]!=window.location.host&&(f+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),c=document.createElement("input"),c.type="hidden",c.name="customValue",c.value=encodeURIComponent(a[0].customValue),e.appendChild(c)):1<a.length&&(f+="d24"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(c+".zip")+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);
f=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(f);b=document.createElement("input");b.type="hidden";b.name="d00";b.value=f;e.appendChild(b)}else f=document.createElement("input"),f.type="hidden",f.name="dext5CMD",f.value="downloadRequest",e.appendChild(f),"1"==UPLOADTOP.G_CURRUPLOADER._config.resumeDownload&&(f=document.createElement("input"),f.type="hidden",f.name="resumeMode",f.value="1",e.appendChild(f)),1==a.length?(c=document.createElement("input"),c.type="hidden",c.name="fileNameRuleEx",c.value=
UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx,e.appendChild(c),c=document.createElement("input"),c.type="hidden",c.name="fileVirtualPath",c.value=encodeURIComponent(a[0].webPath),e.appendChild(c),c=document.createElement("input"),c.type="hidden",c.name="fileOrgName",c.value=encodeURIComponent(a[0].originName),e.appendChild(c),c=document.createElement("input"),c.type="hidden",c.name="customValue",c.value=encodeURIComponent(a[0].customValue),e.appendChild(c),4<b.length&&"http"==b.substring(0,4).toLowerCase()&&
b.match(/:\/\/(.[^\/]+)/)[1]!=window.location.host&&(b=document.createElement("input"),b.type="hidden",b.name="cd",b.value="1",e.appendChild(b))):1<a.length&&(b=document.createElement("input"),b.type="hidden",b.name="zfn",b.value=c+".zip",e.appendChild(b));document.body.appendChild(e);e.submit();document.body.removeChild(e)}
function displayDownloadReady(b){var a=document.getElementById("DEXT_fiVe_UP_Load");b?(a.style.display="block",a.firstChild.className="DEXT_fiVe_UP_file_load2",a.getElementsByTagName("img")[0].src=UPLOADTOP.G_CURRUPLOADER._config.webPath.image+"loading.gif",UPLOADTOP.G_CURRUPLOADER.isDownloadReady=!0):(a.style.display="none",UPLOADTOP.G_CURRUPLOADER.isDownloadReady=!1)}
function displayCommonReady(b,a){var c=document.getElementById("DEXT_fiVe_UP_Load");if(b){if(c.style.display="block","ieplugin"==a._config.userRunTimeMode&&(c.style.position="absolute",c.style.width="100%",c.style.height="100%"),c.firstChild.className="DEXT_fiVe_UP_file_load2",c.getElementsByTagName("img")[0].src=a._config.webPath.image+"loading.gif",c.getElementsByTagName("img")[0].draggable=!1,"ieplugin"==a._config.userRunTimeMode&&0==c.getElementsByTagName("iframe").length){var e=document.createElement("iframe"),
f=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();e.src=f;e.style.width="100%";e.style.height="100%";c.appendChild(e)}}else c.style.display="none"}function displayImageProcessing(b){var a=document.getElementById("DEXT_fiVe_UP_Image_processing");b?(a.style.display="block",a.firstChild.className="DEXT_fiVe_UP_file_load2"):a.style.display="none"}T[15]="r";
function getHtmlForBtnNameEdit(b){var a="";if("form"==UPLOADTOP.G_CURRUPLOADER._config.subMode&&0>b.indexOf("custom_"))return a;switch(b){case "add":"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?(a='<div id="files_container" class="input_file_box"><div>'+('<form id="form_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" method="post" enctype="multipart/form-data" encoding="multipart/form-data">'),a+='<span class="input_image_add" style="'+G_StrCustomHeaderColor+
";"+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_add+"</span>",a+='<input id="file_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" name="file_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" type="file" onchange="fileHandler_html4(this, this.value);" onblur="focusHandler(this);"/>',a+='<input id="tab_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" name="tab_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" type="hidden" value="0"/></form>',a+="</div>",a+="</div> ",0==UPLOADTOP.DEXT5UPLOAD.browser.ie&&(a+="&nbsp;")):(a="","ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
"2"==UPLOADTOP.G_CURRUPLOADER._config.folderTransfer&&(a="display:none; "),a='<button type="button" id="button_add" value="'+Dext5Upload_Lang.btn_add+'" class="input_image" onclick="selectFile(event)" style="'+a+""+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_add+"</button> ");break;case "add_folder":"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a="","0"==UPLOADTOP.G_CURRUPLOADER._config.folderTransfer&&(a="display:none; "),a=0==UPLOADTOP.G_CURRUPLOADER._config.lang.indexOf("ja")?
'<button type="button" id="button_add_folder" value="'+Dext5Upload_Lang.btn_add_folder+'" class="input_image" onclick="Dext5PL.DoSelectFolderOnly();" style="'+a+""+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'"><span style="letter-spacing:-2px;">'+Dext5Upload_Lang.btn_add_folder+"</span></button> ":'<button type="button" id="button_add_folder" value="'+Dext5Upload_Lang.btn_add_folder+'" class="input_image" onclick="Dext5PL.DoSelectFolderOnly();" style="'+a+""+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+
'">'+Dext5Upload_Lang.btn_add_folder+"</button> ");break;case "send":a='<button type="button" id="button_send" value="'+Dext5Upload_Lang.btn_send+'" class="input_image" onclick="startUpload(event)" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_send+"</button> ";break;case "remove":a='<button type="button" id="button_remove" value="'+Dext5Upload_Lang.btn_remove+'" class="input_image" onclick="deleteSelectedFile(event)" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+
'">'+Dext5Upload_Lang.btn_remove+"</button> ";break;case "remove_all":a='<button type="button" id="button_remove_all" value="'+Dext5Upload_Lang.btn_remove_all+'" class="input_image" onclick="deleteAllFile(event)" style="width:105px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_remove_All+"</button> ";break;case "move_first":a='<button type="button" id="button_top" value="'+Dext5Upload_Lang.btn_move_first+'" class="input_image" onclick="moveFirstFile(event)" style="'+
G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_move_first+"</button> ";break;case "move_forward":a='<button type="button" id="button_up" value="'+Dext5Upload_Lang.btn_move_forward+'" class="input_image" onclick="moveForwardFile(event)" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_move_forward+"</button> ";break;case "move_backward":a='<button type="button" id="button_down" value="'+Dext5Upload_Lang.btn_move_backward+'" class="input_image" onclick="moveBackwardFile(event)" style="'+
G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_move_backward+"</button> ";break;case "move_end":a='<button type="button" id="button_bottom" value="'+Dext5Upload_Lang.btn_move_end+'" class="input_image" onclick="moveEndFile(event)" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_move_end+"</button> "}if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)switch(b){case "open":if("download"!=UPLOADTOP.G_CURRUPLOADER._config.mode||
"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload)a='<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="openFile(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_open+"</button> ";break;case "saveandopen":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_saveandopen" value="'+Dext5Upload_Lang.btn_saveandopen+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+
"; "+G_StrCustomFooterColor+'" onclick="saveAndOpenEx(event, true);">'+Dext5Upload_Lang.btn_saveandopen+"</button> ");break;case "saveandfolderopen":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_saveandfolderopen" value="'+Dext5Upload_Lang.btn_saveandfolderopen+'" class="input_image" style="width:130px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="saveAndFolderOpenEx(event, true);">'+Dext5Upload_Lang.btn_saveandfolderopen+"</button> ");break;
case "download":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download" value="'+Dext5Upload_Lang.btn_download+'" class="input_image" onclick="downloadFile(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_download+"</button> ");break;case "download_all":UPLOADTOP.DEXT5UPLOAD.browser.mobile||"open"==UPLOADTOP.G_CURRUPLOADER._config.mode||(a='<button type="button" id="button_download_all" value="'+Dext5Upload_Lang.btn_download_All+
'" class="input_image" style="width:125px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllFile(event, true);">'+Dext5Upload_Lang.btn_download_All+"</button> ");break;case "print":"download"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_print" value="'+Dext5Upload_Lang.btn_print+'" class="input_image" onclick="printFileEx(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_print+"</button> ");
break;case "download_zipfile":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_zipfile" value="'+Dext5Upload_Lang.btn_download_zipfile+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadZipfile(event, true);">'+Dext5Upload_Lang.btn_download_zipfile+"</button> ");break;case "download_all_zipfile":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_all_zipfile" value="'+
Dext5Upload_Lang.btn_download_all_zipfile+'" class="input_image" style="width:135px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllZipfile(event, true);">'+Dext5Upload_Lang.btn_download_all_zipfile+"</button> ")}else switch(b){case "open":var c=!1;UPLOADTOP.DEXT5UPLOAD.browser.mobile?UPLOADTOP.DEXT5UPLOAD.browser.iOS&&(c=!0):c=!0;if("download"==UPLOADTOP.G_CURRUPLOADER._config.mode||"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload)c=!1;c&&(a=UPLOADTOP.DEXT5UPLOAD.browser.iOS?
'<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="downloadFile(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_open+"</button> ":'<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="openFile(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_open+"</button> ");break;case "download":0==UPLOADTOP.DEXT5UPLOAD.browser.iOS&&
"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download" value="'+Dext5Upload_Lang.btn_download+'" class="input_image" onclick="downloadFile(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_download+"</button> ");break;case "download_all":UPLOADTOP.DEXT5UPLOAD.browser.mobile||"open"==UPLOADTOP.G_CURRUPLOADER._config.mode||(a='<button type="button" id="button_download_all" value="'+Dext5Upload_Lang.btn_download_All+
'" class="input_image" style="width:105px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllFile(event, true);">'+Dext5Upload_Lang.btn_download_All+"</button> ");break;case "saveandopen":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_saveandopen" value="'+Dext5Upload_Lang.btn_saveandopen+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+
'" onclick="saveAndOpenEx(event, true);">'+Dext5Upload_Lang.btn_saveandopen+"</button> ");break;case "print":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"download"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_print" value="'+Dext5Upload_Lang.btn_print+'" class="input_image" onclick="printFileEx(event, true);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_print+"</button> ");break;case "download_zipfile":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&
"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_zipfile" value="'+Dext5Upload_Lang.btn_download_zipfile+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadZipfile(event, true);">'+Dext5Upload_Lang.btn_download_zipfile+"</button> ");break;case "download_all_zipfile":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_all_zipfile" value="'+
Dext5Upload_Lang.btn_download_all_zipfile+'" class="input_image" style="width:135px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllZipfile(event, true);">'+Dext5Upload_Lang.btn_download_all_zipfile+"</button> ")}-1<b.indexOf("custom_")&&(a=b.split("|")[0],(b=b.split("|")[1])||(b=a),a='<button type="button" id="button_'+a+'" value="'+b+'" class="input_image" onclick="command_custom_upload(\''+a.toString()+'\')" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+
'">'+b+"</button> ");return a}G_AP2[2]="o";
function getHtmlForBtnNameView(b){var a="";if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)switch(b){case "open":if("download"!=UPLOADTOP.G_CURRUPLOADER._config.mode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload)a='<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="openFile(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_open+"</button> ";break;case "saveandopen":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&
(a='<button type="button" id="button_saveandopen" value="'+Dext5Upload_Lang.btn_saveandopen+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="saveAndOpenEx(event);">'+Dext5Upload_Lang.btn_saveandopen+"</button> ");break;case "saveandfolderopen":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_saveandfolderopen" value="'+Dext5Upload_Lang.btn_saveandfolderopen+'" class="input_image" style="width:130px;'+G_StrCustomHeaderColor+
"; "+G_StrCustomFooterColor+'" onclick="saveAndFolderOpenEx(event);">'+Dext5Upload_Lang.btn_saveandfolderopen+"</button> ");break;case "download":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download" value="'+Dext5Upload_Lang.btn_download+'" class="input_image" onclick="downloadFile(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_download+"</button> ");break;case "download_all":UPLOADTOP.DEXT5UPLOAD.browser.mobile||
"open"==UPLOADTOP.G_CURRUPLOADER._config.mode||(a='<button type="button" id="button_download_all" value="'+Dext5Upload_Lang.btn_download_All+'" class="input_image" style="width:125px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllFile(event);">'+Dext5Upload_Lang.btn_download_All+"</button> ");break;case "print":"download"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_print" value="'+Dext5Upload_Lang.btn_print+'" class="input_image" onclick="printFileEx(event);" style="'+
G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_print+"</button> ");break;case "download_zipfile":"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_zipfile" value="'+Dext5Upload_Lang.btn_download_zipfile+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadZipfile(event);">'+Dext5Upload_Lang.btn_download_zipfile+"</button> ");break;case "download_all_zipfile":"open"!=
UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_all_zipfile" value="'+Dext5Upload_Lang.btn_download_all_zipfile+'" class="input_image" style="width:135px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllZipfile(event);">'+Dext5Upload_Lang.btn_download_all_zipfile+"</button> ")}else switch(b){case "open":var c=!1;UPLOADTOP.DEXT5UPLOAD.browser.mobile?UPLOADTOP.DEXT5UPLOAD.browser.iOS&&(c=!0):c=!0;if("download"==UPLOADTOP.G_CURRUPLOADER._config.mode||
"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload)c=!1;c&&(a=UPLOADTOP.DEXT5UPLOAD.browser.iOS?'<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="downloadFile(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_open+"</button> ":'<button type="button" id="button_open" value="'+Dext5Upload_Lang.btn_open+'" class="input_image" onclick="openFile(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+
'">'+Dext5Upload_Lang.btn_open+"</button> ");break;case "download":0==UPLOADTOP.DEXT5UPLOAD.browser.iOS&&"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download" value="'+Dext5Upload_Lang.btn_download+'" class="input_image" onclick="downloadFile(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_download+"</button> ");break;case "download_all":UPLOADTOP.DEXT5UPLOAD.browser.mobile||"open"==UPLOADTOP.G_CURRUPLOADER._config.mode||
(a='<button type="button" id="button_download_all" value="'+Dext5Upload_Lang.btn_download_All+'" class="input_image" style="width:105px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllFile(event);">'+Dext5Upload_Lang.btn_download_All+"</button> ");break;case "saveandopen":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_saveandopen" value="'+Dext5Upload_Lang.btn_saveandopen+'" class="input_image" style="width:110px;'+
G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="saveAndOpenEx(event);">'+Dext5Upload_Lang.btn_saveandopen+"</button> ");break;case "print":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"download"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_print" value="'+Dext5Upload_Lang.btn_print+'" class="input_image" onclick="printFileEx(event);" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_print+"</button> ");break;
case "download_zipfile":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_zipfile" value="'+Dext5Upload_Lang.btn_download_zipfile+'" class="input_image" style="width:110px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadZipfile(event);">'+Dext5Upload_Lang.btn_download_zipfile+"</button> ");break;case "download_all_zipfile":"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&
"open"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&(a='<button type="button" id="button_download_all_zipfile" value="'+Dext5Upload_Lang.btn_download_all_zipfile+'" class="input_image" style="width:135px;'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'" onclick="downloadAllZipfile(event);">'+Dext5Upload_Lang.btn_download_all_zipfile+"</button> ")}-1<b.indexOf("custom_")&&(a=b.split("|")[0],(b=b.split("|")[1])||(b=a),a='<button type="button" id="button_'+a+'" value="'+b+'" class="input_image" onclick="command_custom_upload(\''+
a.toString()+'\')" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+b+"</button> ");return a}Z[8]="w";_R_O_+="Q";T[16]="e";Z[9]="w";function command_custom_upload(b){UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_CustomAction(UPLOADTOP.G_CURRUPLOADER.ID,b)}
function uploadShow(){try{var b=UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID);if(b){var a=document.getElementById("DEXT_fiVe_UP_PL_holder");a&&(a.style.overflow="hidden");b.style.width=UPLOADTOP.G_CURRUPLOADER._config.width;b.style.height=UPLOADTOP.G_CURRUPLOADER._config.height;b.style.display="";if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var c=document.getElementById("dext5PL");c.style.display="block";c.width="100%"}}}catch(e){}}
R[24]="n";T[17]="c";function uploadHidden(){try{var b=UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID);if(b){var a=document.getElementById("DEXT_fiVe_UP_PL_holder");a&&(a.style.overflow="visible");b.style.height="0px";b.style.display="none";if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var c=document.getElementById("dext5PL");c.style.display="none";c.width="0px"}}}catch(e){}}Z[10]=".";
function getUploaderFromEvent(b){var a=null;try{if(null!=b){var c=b.target?b.target:b.srcElement;if(c){b=null;b=9!=c.nodeType?c.ownerDocument:c;try{b=b.defaultView?b.defaultView:b.parentWindow,a=UPLOADTOP.DEXT5UPLOAD.DEXTMULTIPLE[b.frameElement.id]}catch(e){a=UPLOADTOP.G_CURRUPLOAD}}else a=UPLOADTOP.G_CURRUPLOAD}else a=UPLOADTOP.G_CURRUPLOAD}catch(f){a=UPLOADTOP.G_CURRUPLOAD}return a}
function getUploaderFromUnloadEvent(b){var a=null;try{if(null!=b){var c=b.target?b.target:b.srcElement;if(c)try{b="",c.frameElement&&(b=c.frameElement.id),a=UPLOADTOP.DEXT5UPLOAD.DEXTMULTIPLE[b]}catch(e){a=UPLOADTOP.G_CURRUPLOAD}else a=UPLOADTOP.G_CURRUPLOAD}else a=UPLOADTOP.G_CURRUPLOAD}catch(f){a=UPLOADTOP.G_CURRUPLOAD}return a}
function setUploadMode(b){if("ieplugin"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){UPLOADTOP.G_CURRUPLOADER._config.mode="upload"==b?"upload":"view"==b?"view":"open"==b?"open":"download";var a=document.getElementById("file_list");"upload"!=b?document.getElementById("file_temp").style.display="none":0==a.hasChildNodes()&&(document.getElementById("file_temp").style.display="");if(a&&0<a.childNodes.length)for(var c=a.childNodes.length,e=0;e<c;e++){var f=a.childNodes[e].getElementsByTagName("input")[0],
g,k=f.getAttribute("listvalue");null!=k&&void 0!=k&&(g=f.getAttribute("listvalue"));f=!1;null!=g&&void 0!=g&&"y"==RESULTFILELIST[g].isWebFile&&(f=!0);a.childNodes[e].getElementsByTagName("ul")[0].style.background="upload"==UPLOADTOP.G_CURRUPLOADER._config.mode?f?UPLOADTOP.G_CURRUPLOADER._config.customWebFileColor:"":""}}else UPLOADTOP.G_CURRUPLOADER._config.mode="upload"==b?"upload":"view"==b?"view":"open"==b?"open":"download";a="b";"top"==UPLOADTOP.G_CURRUPLOADER._config.buttonBarPosition&&(a="t");
k=UPLOADTOP.G_CURRUPLOADER._config.userMessage.edit;g=UPLOADTOP.G_CURRUPLOADER._config.userMessage.view;c=document.getElementById("DEXT_fiVe_UP_uploadbox_btm");if("upload"!=b){if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(k=document.getElementById("files_container"))){f=document.createElement("div");f.style.display="none";k=k.getElementsByTagName("form");e=k.length;if(1<e){for(var n=document.createElement("div"),e=e-2;0<=e;e--)n.appendChild(k[e]);
for(e=n.childNodes.length-1;0<=e;e--)f.appendChild(n.childNodes[e]);n=null}UPLOADTOP.G_CURRUPLOADER.uploadModeFormTags=f}k=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView.length;c.style.display=0==k?"none":"";f="";f="left"==UPLOADTOP.G_CURRUPLOADER._config.showViewAlign?f+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==UPLOADTOP.G_CURRUPLOADER._config.showViewAlign?f+'<ul class="DEXT_fiVe_UP_uploadbox_right">':f+"<ul>";f+='<li class="fbutton">';for(e=0;e<k;e++)f+=getHtmlForBtnNameView(UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[e]);
f+="</li></ul>";"left"==UPLOADTOP.G_CURRUPLOADER._config.showViewAlign&&""!=UPLOADTOP.G_CURRUPLOADER._config.userMessage.view&&(f+='<div class="DEXT_fiVe_UP_user_message_r'+a+'" id="DEXT_fiVe_UP_usermessage" title="'+g+'">'+g+"</div>");"right"==UPLOADTOP.G_CURRUPLOADER._config.showViewAlign&&""!=UPLOADTOP.G_CURRUPLOADER._config.userMessage.view&&(f+='<div class="DEXT_fiVe_UP_user_message_l'+a+'" id="DEXT_fiVe_UP_usermessage" title="'+g+'">'+g+"</div>");c.innerHTML=f}else{g=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit.length;
0==g&&(-1<UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode.indexOf("html5")||"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)?c.style.display="none":c.style.display="";f="";f="left"==UPLOADTOP.G_CURRUPLOADER._config.showEditAlign?f+'<ul class="DEXT_fiVe_UP_uploadbox_left">':"right"==UPLOADTOP.G_CURRUPLOADER._config.showEditAlign?f+'<ul class="DEXT_fiVe_UP_uploadbox_right">':f+"<ul>";f+='<li class="fbutton">';for(e=0;e<g;e++)f+=getHtmlForBtnNameEdit(UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[e]);
0==g&&"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(f+='<div id="files_container" class="input_file_box">',f+="<div>",f+='<form id="form_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" method="post" enctype="multipart/form-data" encoding="multipart/form-data">',f+='<span class="input_image_add" style="'+G_StrCustomHeaderColor+"; "+G_StrCustomFooterColor+'">'+Dext5Upload_Lang.btn_add+"</span>",f+='<input id="file_'+UPLOADTOP.G_CURRUPLOADER.TagID+
'" name="file_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" type="file" onchange="fileHandler_html4(this, this.value);" onblur="focusHandler(this);"/>',f+='<input id="tab_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" name="tab_'+UPLOADTOP.G_CURRUPLOADER.TagID+'" type="hidden" value="0"/></form>',f+="</div>",f+="</div> ");f+="</li>";f+="</ul>";"left"==UPLOADTOP.G_CURRUPLOADER._config.showEditAlign&&""!=UPLOADTOP.G_CURRUPLOADER._config.userMessage.edit&&(f+='<div class="DEXT_fiVe_UP_user_message_r'+a+'" id="DEXT_fiVe_UP_usermessage" title="'+
k+'">'+k+"</div>");"right"==UPLOADTOP.G_CURRUPLOADER._config.showEditAlign&&""!=UPLOADTOP.G_CURRUPLOADER._config.userMessage.edit&&(f+='<div class="DEXT_fiVe_UP_user_message_l'+a+'" id="DEXT_fiVe_UP_usermessage" title="'+k+'">'+k+"</div>");c.innerHTML=f;if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&UPLOADTOP.G_CURRUPLOADER.uploadModeFormTags){k=document.getElementById("files_container");a=UPLOADTOP.G_CURRUPLOADER.uploadModeFormTags.childNodes;
for(e=a.length-1;0<=e;e--)c=document.createElement("div"),c.style.display="none",c.appendChild(a[e]),k.insertBefore(c,k.firstChild);UPLOADTOP.G_CURRUPLOADER.uploadModeFormTags=null}"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(e="*.*",a=Dext5Upload_Lang.file_type_all_description,"1"==UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit&&""!=UPLOADTOP.G_CURRUPLOADER._config.extension.extToString&&(e=UPLOADTOP.DEXT5UPLOAD.util.getMimeFilterEx(UPLOADTOP.G_CURRUPLOADER._config.extension.extToString),
a=Dext5Upload_Lang.file_type_description),g=c="",UPLOADTOP.DEXT5UPLOAD.isRelease?(c=UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"swfupload/swfupload.swf",g=UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"swfupload/swfupload_FP9.swf"):(c=UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"swfupload/swfupload.swf",g=UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"swfupload/swfupload_FP9.swf"),f=G_StrCustomFooterColor,""==G_StrCustomFooterColor&&(f="color: #464646"),new SWFUpload({upload_url:UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,
post_params:{},file_types:e,file_types_description:a,file_upload_limit:0,file_size_limit:"0",swfupload_loaded_handler:dext5upload_swf_Loaded,file_dialog_start_handler:dext5upload_swf_fileDialogStart,file_queued_handler:dext5upload_swf_fileQueued,file_dialog_complete_handler:dext5upload_swf_fileDialogComplete,file_queue_error_handler:dext5upload_swf_fileQueueError,upload_start_handler:dext5upload_swf_uploadStart,upload_progress_handler:dext5upload_swf_uploadProgress,upload_success_handler:dext5upload_swf_uploadSuccess,
upload_complete_handler:dext5upload_swf_uploadComplete,upload_error_handler:dext5upload_swf_uploadError,button_placeholder_id:"button_add",button_width:76,button_height:23,button_text:'<button type="button" id="button_add" value="'+Dext5Upload_Lang.btn_add+'">'+Dext5Upload_Lang.btn_add+"</button>",button_text_style:"button {"+f+";font-family:dotum,\ub3cb\uc6c0,tahoma,sans-serif}",button_text_top_padding:4,button_text_left_padding:12,button_action:0==UPLOADTOP.G_CURRUPLOADER._config.multiFileSelect?
SWFUpload.BUTTON_ACTION.SELECT_FILE:SWFUpload.BUTTON_ACTION.SELECT_FILES,flash_url:c,flash9_url:g,custom_settings:{},debug:!1},UPLOADTOP.G_CURRUPLOADER.ID,G_StrCustomHeaderColor,G_StrCustomFooterColor))}var p=document.getElementById("DEXT_fiVe_UP_uploadbox_btm").getElementsByTagName("button"),l=10;setTimeout(function(){for(var a=0;a<p.length;a++)l=l+parseInt(p[a].offsetWidth,10)+8;(a=document.getElementById("files_container"))&&(l=l+parseInt(a.offsetWidth,10)+8);if(a=document.getElementById("DEXT_fiVe_UP_usermessage")){var b=
0;-1<UPLOADTOP.G_CURRUPLOADER._config.width.indexOf("%")?(b=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID)),b=b.right-b.left):b=parseInt(UPLOADTOP.G_CURRUPLOADER._config.width,10);l=b-l;a.style.width=l+"px"}},1);e=parseInt(UPLOADTOP.G_CURRUPLOADER._config.height);-1<UPLOADTOP.G_CURRUPLOADER._config.height.indexOf("%")&&(e=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+
UPLOADTOP.G_CURRUPLOADER.ID)),e=e.bottom-e.top,0==e&&(e=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID)),e=e.bottom-e.top));setFileListHeight(e,UPLOADTOP.G_CURRUPLOADER._config,UPLOADTOP.G_CURRUPLOADER);"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?document.getElementById("dext5PL").style.height=UPLOADTOP.G_CURRUPLOADER.fileListHeight+"px":document.getElementById("DEXT_fiVe_UP_file_temp").style.height=
UPLOADTOP.G_CURRUPLOADER.fileListHeight+"px";e=document.getElementById("basic_file_info");a=document.getElementById("file_inserted_text");if("upload"!=b)e.innerHTML="",a.innerHTML="";else{f="";if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)0<UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount&&(f+=Dext5Upload_Lang.file_maximum+" ");else if(0<UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount||0<UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize)f+=
Dext5Upload_Lang.file_maximum+" ";0<UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount&&(f+="<span>"+UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount+"</span> "+Dext5Upload_Lang.file_unit+" ");if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)0<UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize?(b=Dext5Upload_Lang.file_maximum_limit,b=b.replace("{0}","<span>"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize).toString+
"</span>"),f+=b):f+=Dext5Upload_Lang.file_no_limit;e.innerHTML=f;f=""+Dext5Upload_Lang.file_inserted;a.innerHTML=f}displayTotalSizeAndNum();b=document.getElementById("total_num").innerHTML;setAutoHeight(b);if("ieplugin"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){b=document.getElementById("file_list");a=b.childNodes.length;for(e=0;e<a;e++)c=b.childNodes[e],UPLOADTOP.DEXT5UPLOAD.util.addEvent(c.getElementsByTagName("li")[1],"click",listClickEvent),UPLOADTOP.DEXT5UPLOAD.util.addEvent(c.getElementsByTagName("li")[2],
"click",listClickEvent);setTabOrder()}}Z[11]="d";_R_O_+="I";
function setTabOrder(){var b=1;if(0<getTotalFileCount())if(document.getElementById("chk_all_box").tabIndex=b,b++,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)document.getElementById("dext5PL")&&(document.getElementById("dext5PL").tabIndex=b,b++);else{if(document.getElementById("DEXT_fiVe_UP_file_temp")&&(document.getElementById("DEXT_fiVe_UP_file_temp").tabIndex="-1"),document.getElementById("file_list"))for(var a=document.getElementById("file_list").childNodes,c=0;c<a.length;c++)try{a[c].childNodes[0]&&
a[c].childNodes[0].childNodes[0]&&a[c].childNodes[0].childNodes[0].childNodes[0]&&(a[c].childNodes[0].childNodes[0].childNodes[0].tabIndex=b,b++)}catch(e){}}else document.getElementById("chk_all_box")&&(document.getElementById("chk_all_box").tabIndex="-1"),"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?document.getElementById("dext5PL")&&(document.getElementById("dext5PL").tabIndex=b,b++):document.getElementById("DEXT_fiVe_UP_file_temp")&&(document.getElementById("DEXT_fiVe_UP_file_temp").tabIndex=
b,b++);if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode){if(a=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView.length,0<a)for(c=0;c<a;c++)document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[c].split("|")[0])&&(document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[c].split("|")[0]).tabIndex=b,b++)}else if(a=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit.length,0<a)for(c=0;c<a;c++)"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"add"==UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[c]?document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID)&&(document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).tabIndex=b,b++):"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"add"==UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[c]?document.getElementById("SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID)&&
(document.getElementById("SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID).tabIndex=b,b++):document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[c].split("|")[0])&&(document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[c].split("|")[0]).tabIndex=b,b++);else"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID)&&(document.getElementById("file_"+
UPLOADTOP.G_CURRUPLOADER.TagID).tabIndex=b)}function setListvalue(){for(var b=[],a=document.getElementById("file_list").getElementsByTagName("input"),c=a.length,e=0;e<c;e++)"checkbox"==a[e].type&&b.push(a[e]);a=0;c=RESULTFILELIST.length;for(e=0;e<c;e++)"n"==RESULTFILELIST[e].isDelete&&(b[a].setAttribute("listvalue",e),a++)}
function viewTime(b){var a="",c=m=h=d=-1;b=parseInt(b);if(!b)return 0+Dext5Upload_Lang.upload_timeunit.sec;if(60>b)return b+Dext5Upload_Lang.upload_timeunit.sec;c=b%60;60<=b&&(m=parseInt(b/60),60<=m&&(h=parseInt(m/60),m%=60,24<=h&&(d=parseInt(h/24),h%=24)));0<c&&(a=c+Dext5Upload_Lang.upload_timeunit.sec);0<m&&(a=m+Dext5Upload_Lang.upload_timeunit.min+" "+a);0<h&&(a=h+Dext5Upload_Lang.upload_timeunit.hour+" "+a);0<d&&(a=d+Dext5Upload_Lang.upload_timeunit.day+" "+a);return a}T[18]="t";_R_O_+="J";
R[25]=".";function dextWriteLog(b,a){void 0==a&&(a=!1);if(0==UPLOADTOP.DEXT5UPLOAD.isRelease){try{a?parent.parent.document.getElementById("dextWriteLog").value+=b:parent.parent.document.getElementById("dextWriteLog").value=b}catch(c){}try{dext_console.log("[DEXT5 Upload] "+b)}catch(e){}}}
function _fn_0(){var b=T.toString(),b=b.replace(/,/gi,""),a,c,e,f,g,k;a=c=e=f=g=k="";try{k=_0_[3],a=_0_[4],c=_0_[5],e=_0_[7],g=_0_[8],f=_1_}catch(n){alert(b);return}try{if(0==f.length||f!=e){alert(b);return}}catch(p){alert(b);return}try{if(0==c.length){alert(b);return}if("0"!=c){var l=new Date;c=c.toString();c=c.split("-");var q=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c[0],10),r=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c[1],10),r=r-1,u=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c[2],10);if(l>new Date(q,
r,u,23,59,59)){alert(b);return}}}catch(w){alert(b);return}try{if(0==a.length){alert(b);return}a=a.split(",");c=!1;a.push("localhost");a.push("127.0.0.1");""==UPLOADTOP.DEXT5UPLOAD.util.trim(___)&&(___=UPLOADTOP.UPLOADWIN.location.href);-1<___.indexOf("file://")&&(___=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl);for(e=0;e<a.length;e++){var y=a[e].replace("*.","");if(12==y.length&&3==y.split("-").length){c=!0;break}if(-1!=___.toLowerCase().search(y.toLowerCase())){c=!0;break}else{var t=y.replace("www.",
"");if(t!=y&&(t="/"+t,-1!=___.toLowerCase().search(t.toLowerCase()))){c=!0;break}}}if(0==c){alert(b);return}}catch(z){alert(b);return}a=!1;try{if(0==k.length){alert(b);return}"I"==k?a=!0:"P"==k?a=!0:"A"==k?a=!0:"M"==k&&(a=!0)}catch(B){alert(b);return}try{if(1==a&&-1==g.indexOf("U")){alert(b);return}}catch(x){alert(b);return}_1_=!0}
function focusHandler(b){0==G_FileHandlerControl&&(b.parentNode.childNodes[2].value="0",b.parentNode.childNodes[0].style.backgroundImage="",b.parentNode.childNodes[0].style.backgroundRepeat="",b.parentNode.childNodes[0].style.backgroundPositionY="",b.parentNode.childNodes[0].style.backgroundPositionX="")}Z[12]="e";
function _layer_filebtn(b){try{b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));var a=UPLOADTOP.G_CURRUPLOADER;"upload"==a._config.mode&&"html4"==a._config.userRunTimeMode&&"1"==a._config.uploadMethodHtml4&&1==G_FileHandlerControl&&(G_FileHandlerControl=0,a.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[2].value="0",a.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundImage="",a.frameWin.document.getElementById("file_"+
UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundRepeat="",a.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundPositionY="",a.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundPositionX="")}catch(c){}}
function _top_filebtn(){try{var b=UPLOADTOP.G_CURRUPLOADER;"upload"==b._config.mode&&"html4"==b._config.userRunTimeMode&&"1"==b._config.uploadMethodHtml4&&1==G_FileHandlerControl&&(G_FileHandlerControl=0,b.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[2].value="0",b.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundImage="",b.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundRepeat=
"",b.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundPositionY="",b.frameWin.document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID).parentNode.childNodes[0].style.backgroundPositionX="")}catch(a){}}T[19]=". P";
function moveFirstFile(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID;b=getUploaderFromEvent(b);a!=b.ID&&(UPLOADTOP.G_CURRUPLOADER=b)}if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if(a=UPLOADTOP.G_CURRUPLOADER.frameWin,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)a.Dext5PL.DoMoveItemToBegin();else if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"1"!=UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||!G_IntervalGetSize){b=
document.getElementById("file_list").getElementsByTagName("input");for(var c=b.length,a=-1,e=0;e<c;e++)if(b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")){a=e;break}if(!(0>=a)){c=[];b=[];for(var f=RESULTFILELIST.length,g=0,e=0;e<f;e++)"n"==RESULTFILELIST[e].isDelete&&(c.push(e),"n"==RESULTFILELIST[e].isWebFile?(b.push(g),g++):b.push(-1));e=file_list.childNodes[a];file_list.removeChild(file_list.childNodes[a]);file_list.insertBefore(e,file_list.childNodes[0]);e=c[a];c=RESULTFILELIST[e];
RESULTFILELIST.splice(e,1);RESULTFILELIST.unshift(c);"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(e=document.getElementById("files_container"),a=b[a],-1!=a&&(b=e.childNodes[a],e.removeChild(e.childNodes[a]),e.insertBefore(b,e.childNodes[0])));moveFileScroll();setTabOrder();setListvalue()}}}T[20]="l";_R_O_+="K";
function moveForwardFile(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID;b=getUploaderFromEvent(b);a!=b.ID&&(UPLOADTOP.G_CURRUPLOADER=b)}if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if(a=UPLOADTOP.G_CURRUPLOADER.frameWin,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)a.Dext5PL.DoMoveItemToBofore();else if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"1"!=UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||!G_IntervalGetSize){b=
document.getElementById("file_list").getElementsByTagName("input");for(var c=b.length,a=-1,e=0;e<c;e++)if(b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")){a=e;break}if(!(0>=a)){c=[];b=[];for(var f=RESULTFILELIST.length,g=0,e=0;e<f;e++)"n"==RESULTFILELIST[e].isDelete&&(c.push(e),"n"==RESULTFILELIST[e].isWebFile?(b.push(g),g++):b.push(-1));e=file_list.childNodes[a];file_list.removeChild(file_list.childNodes[a]);file_list.insertBefore(e,file_list.childNodes[a-1]);e=c[a];c=c[a-
1];"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"n"==RESULTFILELIST[e].isWebFile&&"n"==RESULTFILELIST[c].isWebFile&&(f=document.getElementById("files_container"),a=b[a],b=f.childNodes[a],f.removeChild(f.childNodes[a]),f.insertBefore(b,f.childNodes[a-1]));a=RESULTFILELIST[e];RESULTFILELIST[e]=RESULTFILELIST[c];RESULTFILELIST[c]=a;moveFileScroll();setTabOrder();setListvalue()}}}Z[13]="x";
function moveBackwardFile(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID;b=getUploaderFromEvent(b);a!=b.ID&&(UPLOADTOP.G_CURRUPLOADER=b)}if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if(a=UPLOADTOP.G_CURRUPLOADER.frameWin,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)a.Dext5PL.DoMoveItemToNext();else if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"1"!=UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||!G_IntervalGetSize){b=
document.getElementById("file_list").getElementsByTagName("input");for(var c=b.length,a=-1,e=0;e<c;e++)if(b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")){a=e;break}if(!(0>a||a==c-1)){c=[];b=[];for(var f=RESULTFILELIST.length,g=0,e=0;e<f;e++)"n"==RESULTFILELIST[e].isDelete&&(c.push(e),"n"==RESULTFILELIST[e].isWebFile?(b.push(g),g++):b.push(-1));e=file_list.childNodes[a];file_list.removeChild(file_list.childNodes[a]);file_list.insertBefore(e,file_list.childNodes[a].nextSibling);
e=c[a];c=c[a+1];"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"n"==RESULTFILELIST[e].isWebFile&&"n"==RESULTFILELIST[c].isWebFile&&(f=document.getElementById("files_container"),a=b[a],b=f.childNodes[a],f.removeChild(f.childNodes[a]),f.insertBefore(b,f.childNodes[a+1]));a=RESULTFILELIST[e];RESULTFILELIST[e]=RESULTFILELIST[c];RESULTFILELIST[c]=a;moveFileScroll();setTabOrder();setListvalue()}}}R[26]="a";
function moveEndFile(b){if(b){var a=UPLOADTOP.G_CURRUPLOADER.ID;b=getUploaderFromEvent(b);a!=b.ID&&(UPLOADTOP.G_CURRUPLOADER=b)}if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode)if(a=UPLOADTOP.G_CURRUPLOADER.frameWin,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)a.Dext5PL.DoMoveItemToEnd();else if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode||"1"!=UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4||"1"!=UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||!G_IntervalGetSize){b=
document.getElementById("file_list").getElementsByTagName("input");for(var c=b.length,a=-1,e=0;e<c;e++)if(b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")){a=e;break}if(!(0>a||a==c-1)){c=[];b=[];for(var f=RESULTFILELIST.length,g=0,e=0;e<f;e++)"n"==RESULTFILELIST[e].isDelete&&(c.push(e),"n"==RESULTFILELIST[e].isWebFile?(b.push(g),g++):b.push(-1));e=file_list.childNodes[a];file_list.removeChild(file_list.childNodes[a]);file_list.insertBefore(e,file_list.childNodes[file_list.childNodes.length-
1].nextSibling);e=c[a];c=RESULTFILELIST[e];RESULTFILELIST.splice(e,1);RESULTFILELIST.push(c);"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(e=document.getElementById("files_container"),a=b[a],-1!=a&&(b=e.childNodes[a],e.removeChild(e.childNodes[a]),e.insertBefore(b,e.childNodes[e.childNodes.length-1])));moveFileScroll();setTabOrder();setListvalue()}}}Z[14]="t";
function moveFileScroll(){for(var b=document.getElementById("file_list").getElementsByTagName("input"),a=b.length,c=-1,e=0;e<a;e++)if(b[e].getAttribute("dext_select")&&"1"==b[e].getAttribute("dext_select")){c=e;break}-1<c&&(b=document.getElementById("DEXT_fiVe_UP_file_temp"),a=parseInt(b.style.height.replace("px",""),10),b&&(b.scrollTop>22*c?b.scrollTop=22*c:b.scrollTop+a<22*(c+1)&&(b.scrollTop=22*(c+1)-a)))}T[21]=T[2]+"a";
function adjustUploadFileListScroll(b,a,c){try{var e=getDialogDocument();0>b&&(b=0);var f="DEXT_fiVe_UP_uploadfile_lst";UPLOADTOP.DEXT5UPLOAD.browser.ie&&8>UPLOADTOP.DEXT5UPLOAD.browser.ieVersion&&(f="DEXT_fiVe_UP_uploading_file_list");var g=e.getElementById(f),k=36*parseInt(b/2,10);g.scrollTop=a==c-1?g.scrollHeight:k}catch(n){}}Z[15]="5";_R_O_+="L";
function openCurrFile(b){if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.DoOpenFile()}catch(a){}else{var c=!1;b=document.getElementById("file_list").getElementsByTagName("input")[b].getAttribute("listvalue");"n"==RESULTFILELIST[b].isWebFile&&(c=!0);if(!c&&null!=b&&void 0!=b)if(""==UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension)openSubmit(b);else if(""!=UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension){for(var c=
UPLOADTOP.G_CURRUPLOADER._config.allowOpenExtension.split(","),e=c.length,f=0;f<e;f++)if(c[f]==RESULTFILELIST[b].fileExt){openSubmit(b);return}"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload?directDownload([b]):downloadSubmit([b])}}}T[22]=T[4];
function fileListSelection(b){var a=b.parentNode.parentNode;if("thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views)a.parentNode.className="thumblst_chk",a.style.backgroundImage='url("../images/img_thumblst_box.png")';else{var c=a.getElementsByTagName("span")[0];a.getElementsByTagName("li");var e=a.getElementsByTagName("li")[2],a=a.getElementsByTagName("li")[3];c.style.backgroundColor="#3399ff";e.style.backgroundColor="#3399ff";c.style.color="#ffffff";e.style.color="#ffffff";e.style.borderLeftWidth="0px";
a.style.color="#ffffff";a.style.backgroundColor="#3399ff";"none"==UPLOADTOP.G_CURRUPLOADER._config.uploadBorderStyle&&(a.style.paddingLeft="3px")}b.setAttribute("dext_select","1")}Z[16]=".";_R_O_+="R";
function fileListNoneSelection(b){var a=b.parentNode.parentNode;if("thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views)a.parentNode.className="thumblst",a.style.backgroundImage="";else{var c=RESULTFILELIST[b.getAttribute("listvalue")],e="#323231";c.originNameColor&&(e=c.originNameColor);a=b.parentNode.parentNode;c=a.getElementsByTagName("span")[0];a.getElementsByTagName("li");var f=a.getElementsByTagName("li")[2],a=a.getElementsByTagName("li")[3];c.style.backgroundColor="";f.style.backgroundColor="";
c.style.color=e;f.style.color="#323231";f.style.borderLeftWidth="1px";a.style.color="#323231";a.style.backgroundColor=""}b.removeAttribute("dext_select")}Z[17]="c";___=1==UPLOADTOP.IsCrossDomain?___.href:top.location.href;
function setUploadSize(b,a,c){var e=UPLOADTOP.G_CURRUPLOADER,f=e._config,g=b;0>b.indexOf("px")&&0>b.indexOf("%")&&(g=b+"px");b=0;0>a.indexOf("%")&&(b=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a),b<f.minHeight&&(b=f.minHeight));var k=UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+e.ID);k.style.width=g;f.width=g;0>a.indexOf("%")?k.style.height=b+"px":k.style.height=a;UPLOADTOP.DEXT5UPLOAD.browser.quirks&&(g=UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_frame_"+e.ID),0>a.indexOf("%")&&(g.style.height=
b+"px"));0>a.indexOf("%")?f.height=b+"px":f.height=a;c&&(0>a.indexOf("%")?f.originHeight=b+"px":f.originHeight=a);-1<a.indexOf("%")&&(a=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID)),b=a.bottom-a.top);setFileListHeight(b,f,e);"ieplugin"!=f.userRunTimeMode?(document.getElementById("DEXT_fiVe_UP_file_temp").style.height=e.fileListHeight+"px",setFileListBorder(),90>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.fileListHeight)?
(document.getElementById("file_temp").style.backgroundImage="none",document.getElementById("file_temp").style.marginTop="-3px"):(document.getElementById("file_temp").style.backgroundImage="",document.getElementById("file_temp").style.marginTop="")):document.getElementById("dext5PL").style.height=e.fileListHeight+"px"}R[27]="s";T[23]=",";G_AP2[5]="i";
function setFileListHeight(b,a,c){var e=getHeaderBarHeight(a),f=3;"1"==a.showHeaderBar&&(f+=e);"1"==a.showStatusBar&&(f+=a.statusBarHeight);if("upload"!=a.mode)0<a.showButtonBarView.length&&(f+=a.buttonBarHeight);else if(0<a.showButtonBarEdit.length||"html4"==a.userRunTimeMode&&"1"==a.uploadMethodHtml4)f+=a.buttonBarHeight;c.fileListHeight=b-f}___=1==UPLOADTOP.IsCrossDomain?UPLOADTOP.UPLOADDOC.referrer:___;Z[18]="o";
function getHeaderBarHeight(b){var a=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(b.customHeaderHeight);a<b.minHeaderBarHeight&&(a=b.minHeaderBarHeight);return a}Z[19]="m";K__+=Z[18];_R_O_+="S";T[24]=" c";
function setAutoHeight(b){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAutoHeight){var a=0;if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var a=19,c=1;void 0!=b&&""!=b?(b=b.split(" "),c+=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(b[0])):c+=getTotalFileCount();a*=c}else a=document.getElementById("file_list").scrollHeight,UPLOADTOP.DEXT5UPLOAD.browser.ie&&(8>UPLOADTOP.DEXT5UPLOAD.browser.ieVersion||UPLOADTOP.DEXT5UPLOAD.browser.quirks)&&--a,"none"==UPLOADTOP.G_CURRUPLOADER._config.uploadBorderStyle&&
(UPLOADTOP.DEXT5UPLOAD.browser.ie&&(8>UPLOADTOP.DEXT5UPLOAD.browser.ieVersion||UPLOADTOP.DEXT5UPLOAD.browser.quirks)?a+=1:--a);a+=2;"1"==UPLOADTOP.G_CURRUPLOADER._config.showStatusBar&&(a+=UPLOADTOP.G_CURRUPLOADER._config.statusBarHeight);if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode){if(0<UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit.length||"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)a+=UPLOADTOP.G_CURRUPLOADER._config.buttonBarHeight}else 0<
UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView.length&&(a+=UPLOADTOP.G_CURRUPLOADER._config.buttonBarHeight);"1"==UPLOADTOP.G_CURRUPLOADER._config.showHeaderBar&&(a+=getHeaderBarHeight(UPLOADTOP.G_CURRUPLOADER._config));a<UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(UPLOADTOP.G_CURRUPLOADER._config.originHeight)&&(a=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(UPLOADTOP.G_CURRUPLOADER._config.originHeight));setUploadSize(UPLOADTOP.G_CURRUPLOADER._config.width,a+"px",!1)}}_R_O_+="T";K__+="p";
function isLargeFiles(b){var a=!1;if(b){var c=UPLOADTOP.G_CURRUPLOADER._config.largeFiles;0<c.markSize&&c.markSize<b&&(a=!0)}return a}T[25]=T[13];K__+="XYZ";_R_O_+="x";
function thumbsViewWithCanvas(b,a,c){try{var e,f;c?(e=b,f=a):(e=UPLOADTOP.G_CURRUPLOADER.checkThumbsArr[UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx].fileObj,f=UPLOADTOP.G_CURRUPLOADER.checkThumbsArr[UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx].oneList);if("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4)thumbsViewWithImage(e,f,c);else{var g=UPLOADTOP.G_CURRUPLOADER.commonToolCanvas,k=g.getContext("2d"),n=UPLOADTOP.G_CURRUPLOADER._config.canvasWidth,
p=UPLOADTOP.G_CURRUPLOADER._config.canvasHeight,l=new Image;l.onload=function(){if(0!=l.width&&0!=l.height){var a=0,b,e,r=Math.min(n/l.width,p/l.height);1>r?(b=Math.round(l.width*r),e=Math.round(l.height*r)):(b=l.width,e=l.height);g.width=b;g.height=e;e<p&&(a=(p-e)/2);var u;c?(r=l,r.style.width=b+"px",r.style.height=e+"px",0<a&&(r.style.marginTop=a+"px")):(k.drawImage(l,0,0,b,e),u=g.toDataURL("image/jpeg",.5),l.setAttribute("dext_thumbs","true"),l.src="",r=new Image,r.style.width=b+"px",r.style.height=
e+"px",0<a&&(r.style.marginTop=a+"px"),r.src=u);a=f.getElementsByTagName("canvas")[0];a.parentNode.insertBefore(r,a);a.parentNode.removeChild(a);0==!!c&&setNextThumnail()}};l.onerror=function(a){null==a.target.getAttribute("dext_thumbs")&&(thumbsViewWithText(e,f),0==!!c&&setNextThumnail())};if("y"==e.isWebFile){b=!1;var q=UPLOADTOP.G_CURRUPLOADER.thumbsImgExtArr,r=q.length;for(a=0;a<r;a++)if(e.fileExt==q[a]){b=!0;break}b?l.onerror=function(a){null==a.target.getAttribute("dext_thumbs")&&(thumbsViewWithText(e,
f),0==!!c&&setNextThumnail())}:(l.onerror=function(){},thumbsViewWithText(e,f),0==!!c&&setNextThumnail());l.src=e.webPath}else if(-1<e.file.type.indexOf("image")){var u=(window.URL||window.webkitURL||window).createObjectURL(e.file);l.src=u}else thumbsViewWithText(e,f),0==!!c&&setNextThumnail()}}catch(w){}}_R_O_+="y";
function thumbsViewWithImage(b,a,c){var e=new Image,f=!1;e.onload=function(){var g=UPLOADTOP.G_CURRUPLOADER._config.canvasWidth,k=UPLOADTOP.G_CURRUPLOADER._config.canvasHeight;if(0==e.width||0==e.height)thumbsViewWithText(b,a),0==!!c&&setNextThumnail(),f=!0;else{var g=Math.min(g/e.width,k/e.height),n=0;1>g?(newWidth=Math.round(e.width*g),newHeight=Math.round(e.height*g)):(newWidth=e.width,newHeight=e.height);newHeight<k&&(n=(k-newHeight)/2);e.width=newWidth;e.height=newHeight;e.style.width=newWidth+
"px";e.style.height=newHeight+"px";0<n&&(e.style.marginTop=n+"px");k=a.getElementsByTagName("canvas")[0];k.parentNode.insertBefore(e,k);k.parentNode.removeChild(k);0==!!c&&setNextThumnail()}};if("y"==b.isWebFile)if(e.src=b.webPath,0==e.src.indexOf("file"))thumbsViewWithText(b,a),e.onerror=function(){};else{for(var g=!1,k=UPLOADTOP.G_CURRUPLOADER.thumbsImgExtArr,n=k.length,p=0;p<n;p++)if(b.fileExt==k[p]){g=!0;break}g?e.onerror=function(){thumbsViewWithText(b,a)}:(e.onerror=function(){},thumbsViewWithText(b,
a))}else{g=!1;k=UPLOADTOP.G_CURRUPLOADER.thumbsImgExtArr;n=k.length;for(p=0;p<n;p++)if(b.fileExt==k[p]){g=!0;break}g?-1<b.localPath.indexOf(":\\fakepath")?(thumbsViewWithText(b,a),0==!!c&&setNextThumnail()):(e.onerror=function(e){0==f&&(thumbsViewWithText(b,a),0==!!c&&setNextThumnail())},e.src=b.localPath):(thumbsViewWithText(b,a),0==!!c&&setNextThumnail())}}Z[20]="/";
function thumbsViewWithText(b,a){var c=a.getElementsByTagName("canvas")[0];c.parentNode.className="icontxt";var e=b.fileExt,f=getIconImagePath(e),g=new Image;g.src=f;g.alt=e;c.parentNode.insertBefore(g,c);c.parentNode.removeChild(c);""==b.originName&&(g.style.display="none")}
function setNextThumnail(){UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx++;UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx<UPLOADTOP.G_CURRUPLOADER.checkThumbsArr.length?thumbsViewWithCanvas():(UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx=0,UPLOADTOP.G_CURRUPLOADER.checkThumbsArr=[]);0==UPLOADTOP.G_CURRUPLOADER.checkThumbsArrIdx%10&&insertAndRemoveDummySpan()}
function insertAndRemoveDummySpan(){for(var b=document.createElement("div"),a=0;100>a;a++)document.createElement("span").innerHTML="";document.body.appendChild(b);document.body.removeChild(b)}T[26]="n";K__+="qrstU";function isExecuteApi(){var b=!0;UPLOADTOP.G_CURRUPLOADER.isDownloadReady&&(b=!1);return b}Z[21]="z";K__+="VW";function getLoadingImageInterval(b){return 1073741824>=b?1E3:1073741824<b&&2147483648>=b?2E3:3E3}K__+="67890";T[27]=T[18];
function fileListSortIconReset(){var b=UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("header_bar_file_name"),a=UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("header_bar_file_size"),b=b.getElementsByTagName("span")[0],a=a.getElementsByTagName("span")[0];b.style.display="none";a.style.display="none";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(a=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,b=0;b<a;b++)UPLOADTOP.G_CURRUPLOADER.frameWin.document.getElementById("user_header_bar_"+
b).getElementsByTagName("span")[0].style.display="none"}T[28]="a";
function fileListSort(b,a){var c=document.getElementById("header_bar_file_name"),e=document.getElementById("header_bar_file_size"),f=c.getElementsByTagName("em")[0],g=e.getElementsByTagName("em")[0],c=c.getElementsByTagName("span")[0],k=e.getElementsByTagName("span")[0];k.style.display="none";c.style.display="none";if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)Dext5PL.SetSortCommand(b,"0");else{e="0";if("0"==b){c.style.display="block";k.style.display="none";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(c=
UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,k=0;k<c;k++){var n=document.getElementById("user_header_bar_"+k),n=n.getElementsByTagName("span")[0];n.style.display="none"}"icon_up"==f.className?(e="1",f.className="icon_down"):f.className="icon_up"}else if("1"==b){c.style.display="none";k.style.display="block";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(c=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,k=0;k<c;k++)n=document.getElementById("user_header_bar_"+k),n=n.getElementsByTagName("span")[0],
n.style.display="none";"icon_up"==g.className?(e="1",g.className="icon_down"):g.className="icon_up"}else if("2"<=b){c.style.display="none";k.style.display="none";n=document.getElementById("user_header_bar_"+(parseInt(b,10)-2));f=n.getElementsByTagName("em")[0];n=n.getElementsByTagName("span")[0];n.style.display="block";c=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length;for(k=0;k<c;k++)parseInt(b,10)-2!=k&&(n=document.getElementById("user_header_bar_"+k),n=n.getElementsByTagName("span")[0],n.style.display=
"none");"icon_up"==f.className?(e="1",f.className="icon_down"):f.className="icon_up"}sortTotalFileList(b,e,!0);setListvalue()}}
function setSelectItem(b,a){var c=document.getElementById("chk_all_box");if(-1==b)1==a?c.checked=!0:0==a&&(c.checked=!1),checkAll();else if(!(getTotalFileCount()<b+1)){0==a&&(c.checked=!1,checkAll());var c=document.getElementById("file_list"),e=c.getElementsByTagName("input");e[b].click();0==e[b].checked&&e[b].click();0==e[b].checked&&(e[b].checked=!0,fileListSelection(e[b]));for(var f=c.getElementsByTagName("input").length,c=document.getElementById("chk_all_box"),g=!0,k=0;k<f;k++)if(0==e[k].checked){g=
!1;break}c.checked=g}}Z[25]=".";T[29]="c";
function setSelectItemEx(b,a){var c=document.getElementById("chk_all_box");if(""==b)1==a?c.checked=!0:0==a&&(c.checked=!1),checkAll();else{var e=RESULTFILELIST.length;if(0<e){for(var f=0,g=[],k=0;k<e;k++)"y"!=RESULTFILELIST[k].isDelete&&("y"==RESULTFILELIST[k].isWebFile&&RESULTFILELIST[k].fileIdx==b&&g.push(f),f++);f=g.length;if(0!=f){0==a&&(c.checked=!1,checkAll());c=document.getElementById("file_list");e=c.getElementsByTagName("input");for(k=0;k<f;k++)e[g[k]].click(),0==e[g[k]].checked&&e[g[k]].click(),
0==e[g[k]].checked&&(e[g[k]].checked=!0,fileListSelection(e[g[k]]));g=c.getElementsByTagName("input").length;c=document.getElementById("chk_all_box");f=!0;for(k=0;k<g;k++)if(0==e[k].checked){f=!1;break}c.checked=f}}}}K__+="123";
function createImgPreViewPopup(){var b=UPLOADTOP.G_CURRUPLOADER._config,a="",c=document.createElement("div");parseInt(b.imgPreViewHeight);c.id="DEXT_fiVe_UP_Popup_Mode";c.style.width=b.imgPreViewWidth;c.style.height=b.imgPreViewHeight;c.style.display="none";a+=createImgPreviewDivHtml(UPLOADTOP.G_CURRUPLOADER);c.innerHTML=a;b=getDialogDocument();b.body.appendChild(c);c=b.getElementById("DEXT_fiVe_UP_close_btn2");UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"click",function(){closeBtnPreViewMode()})}
function showImgPreViewPopup(b,a,c){b=getDialogDocument();var e=b.getElementById("DEXT_fiVe_UP_Popup_Mode");null==e&&(createImgPreViewPopup(),e=b.getElementById("DEXT_fiVe_UP_Popup_Mode"));var f=parseInt(UPLOADTOP.G_CURRUPLOADER._config.imgPreViewWidth)-6,g=parseInt(UPLOADTOP.G_CURRUPLOADER._config.imgPreViewHeight)-6-16;if("html4"==c){var k=e.getElementsByTagName("span")[0];k.innerHTML="";e.getElementsByTagName("em");c=a.fileExt;b=!1;for(var n=UPLOADTOP.G_CURRUPLOADER.thumbsImgExtArr,p=n.length,
l=0;l<p;l++)if(c==n[l]){b=!0;break}var q=new Image;q.onload=function(){if(0!=q.width&&0!=q.height){var a=Math.min(f/q.width,g/q.height),b=0;1>a?(newWidth=Math.round(q.width*a),newHeight=Math.round(q.height*a)):(newWidth=q.width,newHeight=q.height);newHeight<g&&(b=(g-newHeight)/2);q.width=newWidth;q.height=newHeight;q.style.width=newWidth+"px";q.style.height=newHeight+"px";0<b&&(q.style.marginTop=b+"px");setPositionPreviewPopup(e)}};q.onerror=function(){closeBtnPreViewMode()};b?(q.src=a.localPath,
k.appendChild(q)):closeBtnPreViewMode()}else if("html5"==c){k=e.getElementsByTagName("span")[0];k.innerHTML="";e.getElementsByTagName("em");var r=UPLOADTOP.G_CURRUPLOADER.commonToolCanvas,u=r.getContext("2d"),q=new Image;q.onload=function(){if(0!=q.width&&0!=q.height){var a=0,b,c;c=Math.min(f/q.width,g/q.height);1>c?(b=Math.round(q.width*c),c=Math.round(q.height*c)):(b=q.width,c=q.height);r.width=b;r.height=c;c<g&&(a=(g-c)/2);var l,n;u.drawImage(q,0,0,b,c);n=r.toDataURL("image/jpeg",.7);q.setAttribute("dext_thumbs",
"true");q.src="";l=new Image;l.style.width=b+"px";l.style.height=c+"px";0<a&&(l.style.marginTop=a+"px");k.appendChild(l);l.src=n;insertAndRemoveDummySpan();setPositionPreviewPopup(e)}};q.onerror=function(a){null==a.target.getAttribute("dext_thumbs")&&closeBtnPreViewMode()};"y"==a.isWebFile?closeBtnPreViewMode():-1<a.file.type.indexOf("image")?(a=(window.URL||window.webkitURL||window).createObjectURL(a.file),q.src=a):closeBtnPreViewMode()}}T[30]="t ";
function setPositionPreviewPopup(b){var a=getDialogWindow();getDialogDocument();var c=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID));b.style.zIndex="900";b.style.display="";makeDragPopup(b,"imgPreview");var e=0,f=0;if(null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow)for(var g=window.parent;g!=a;)var k=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(g.frameElement),e=e+k.left,f=f+k.top,g=g.parent;if(-1==G_PreviewDialogPosition.top&&
-1==G_PreviewDialogPosition.left){var g=parseInt(b.style.width),k=parseInt(b.style.height),n={},n=getWindowClientSize(a),p={},p=getWindowScrollPos(a),l=a=0;k>=c.top+f&&(a=k-c.top+10);parseInt(c.right-c.left)+e>=n[0]&&(l=g+c.left+10);b.style.left=p[0]+(parseInt(c.right)-g)-l+e+"px";b.style.top=p[1]+a+(c.top-k)+1+f+"px"}else b.style.left=parseInt(G_PreviewDialogPosition.left,10)-5,b.style.top=parseInt(G_PreviewDialogPosition.top)-5}Z[26]="a";K__+="45";SIL[4]="a";
function setFileMark(b,a){for(var c=RESULTFILELIST.length,e=0,f=0,g=0;g<c;g++)if("y"!=RESULTFILELIST[g].isDelete){if(-1!=b&&e==b){RESULTFILELIST[g].mark=a;break}e++}else f++;-1==b&&(RESULTFILELIST[e-1+f].mark=a)}Z[27]="s";T[31]="u";
function setAllowOrLimitExtension(b,a){if("0"==b||"1"==b){UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit=b;if(0<a.length)if(-1<a.indexOf("|")){var c=UPLOADTOP.DEXT5UPLOAD.util.getExtStringFromExtEx(a);UPLOADTOP.G_CURRUPLOADER._config.extension.extArr=c.split(",")}else UPLOADTOP.G_CURRUPLOADER._config.extension.extArr=a.toLowerCase().split(",");else UPLOADTOP.G_CURRUPLOADER._config.extension.extArr=[];c=document.getElementById("file_"+UPLOADTOP.G_CURRUPLOADER.TagID);if("1"==b){var e=UPLOADTOP.DEXT5UPLOAD.util.getMimeFilter(UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.toString());
UPLOADTOP.G_CURRUPLOADER._config.extension.mimeAccept=e;c&&(c.accept=e)}else c&&(c.accept="")}}G_AP1[0]=8;R[28]="p";G_AP1[1]=6;function getFileObjectList(){for(var b=[],a=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileInfoList(-1).split(G_vertical),c=a.length,e=0;e<c;e++)if(""!=a[e]){var f=a[e].split(G_formfeed);"0"==f[0]&&b.push({file:f[5]})}return b}function setFileObjectList(b){for(var a=UPLOADTOP.G_CURRUPLOADER.frameWin,c=b.length,e=0;e<c;e++)a.Dext5PL.AddLocalFileDirectly(b[e].file)}
Z[28]="p";K__+="+";T[32]="s.";K__+="/";
function directDownload(b){var a=b.length,c=reOrganizeDownloadWebPathUrl(b),e=c.length;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDownloadEvent){var f="",g="";b="";for(var k="0",n=!0,p=0;p<e;p++){f=c[p].fileIdx;g=c[p].originName;b=c[p].webPath;var k=isLargeFiles(c[p].fileSize)?"1":"0",l="0";p==e-1&&(l="1");try{n=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeFileDownload(UPLOADTOP.G_CURRUPLOADER.ID,"1",f,g,b,k,l)}catch(q){}if(1!=n)return}}if(0<c.length&&(G_DownloadCnt=0,b=c[G_DownloadCnt].webPath,e=document.createElement("form"),
e.target="download_frame",e.action=b,document.body.appendChild(e),e.submit(),document.body.removeChild(e),e=c.length,1<e)){G_DownloadCnt++;var r=setInterval(function(){if(a>G_DownloadCnt){var b=c[G_DownloadCnt].webPath,e=document.createElement("form");e.target="download_frame";e.action=b;document.body.appendChild(e);e.submit();document.body.removeChild(e);G_DownloadCnt++}else clearInterval(r),G_DownloadCnt=0},1E3)}}
function uploadProgress_ajax(b,a){var c=getDialogDocument();c.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=upload_complete_count+1+"/"+TOTALUPLOADNUM;var e=parseInt(b/a*100,10);"1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==RESULTFILELIST[uploaders[upload_complete_count]].fileSize&&(e=100);c.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[upload_complete_count]).style.width=e+"%";c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[upload_complete_count]).innerHTML=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(a).toString;100==e&&(c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count]).innerHTML=Dext5Upload_Lang.upload_status.merge);PREVIOUSUPLOADEDSIZE=b==a?G_PreUploadedSizeForOnprogress+=b:G_PreUploadedSizeForOnprogress+b;e=0;e="1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==TOTALUPLOADSIZE?100:parseInt(1*PREVIOUSUPLOADEDSIZE/TOTALUPLOADSIZE*100,10);c.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=e+"%";c.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width=
e+"%";c.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString+"/"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALUPLOADSIZE).toString;e=(new Date).getTime()/1E3-G_UploadStartTime;if(0==e){var f=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString;c.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=f+"/"+Dext5Upload_Lang.upload_timeunit.sec;c.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML="-"}else f=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE/e).toString,c.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=f+"/"+Dext5Upload_Lang.upload_timeunit.sec,e=e/PREVIOUSUPLOADEDSIZE*(TOTALUPLOADSIZE-PREVIOUSUPLOADEDSIZE),e=Math.ceil(e),e=viewTime(e),c.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML=e}
function uploadProgress_swf(b,a){var c=getDialogDocument();c.getElementById("DEXT_fiVe_UP_upload_count").innerHTML=upload_complete_count+1+"/"+TOTALUPLOADNUM;var e=parseInt(b/a*100,10);"1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==RESULTFILELIST[uploaders[upload_complete_count]].fileSize&&(e=100);c.getElementById("DEXT_fiVe_UP_uploadFileProgress_"+uploaders[upload_complete_count]).style.width=e+"%";c.getElementById("DEXT_fiVe_UP_uploadFileSize_"+uploaders[upload_complete_count]).innerHTML=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(a).toString;100==e&&(c.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count]).innerHTML=Dext5Upload_Lang.upload_status.merge);PREVIOUSUPLOADEDSIZE=b==a?G_PreUploadedSizeForOnprogress+=b:G_PreUploadedSizeForOnprogress+b;e=0;e="1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==TOTALUPLOADSIZE?100:parseInt(1*PREVIOUSUPLOADEDSIZE/TOTALUPLOADSIZE*100,10);c.getElementById("DEXT_fiVe_UP_total_upload_percent").innerHTML=e+"%";c.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.width=
e+"%";c.getElementById("DEXT_fiVe_UP_upload_size").innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString+"/"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALUPLOADSIZE).toString;e=(new Date).getTime()/1E3-G_UploadStartTime;if(0==e){var f=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE).toString;c.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=f+"/"+Dext5Upload_Lang.upload_timeunit.sec;c.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML="-"}else f=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(PREVIOUSUPLOADEDSIZE/e).toString,c.getElementById("DEXT_fiVe_UP_upload_speed").innerHTML=f+"/"+Dext5Upload_Lang.upload_timeunit.sec,e=e/PREVIOUSUPLOADEDSIZE*(TOTALUPLOADSIZE-PREVIOUSUPLOADEDSIZE),e=Math.ceil(e),e=viewTime(e),c.getElementById("DEXT_fiVe_UP_upload_remainingtime").innerHTML=e}
var _L__={_K:_R_O_+K__,DE:function(b){var a="",c,e,f,g,k,n=0;for(b=b.replace(/[^A-Za-z0-9\+\/\=]/g,"");n<b.length;)c=this._K.indexOf(b.charAt(n++)),e=this._K.indexOf(b.charAt(n++)),g=this._K.indexOf(b.charAt(n++)),k=this._K.indexOf(b.charAt(n++)),c=c<<2|e>>4,e=(e&15)<<4|g>>2,f=(g&3)<<6|k,a+=String.fromCharCode(c),64!=g&&(a+=String.fromCharCode(e)),64!=k&&(a+=String.fromCharCode(f));return a=_L__._utf8_d(a)},_utf8_d:function(b){for(var a="",c=0,e=c1=c2=0;c<b.length;)e=b.charCodeAt(c),128>e?(a+=String.fromCharCode(e),
c++):191<e&&224>e?(c2=b.charCodeAt(c+1),a+=String.fromCharCode((e&31)<<6|c2&63),c+=2):(c2=b.charCodeAt(c+1),c3=b.charCodeAt(c+2),a+=String.fromCharCode((e&15)<<12|(c2&63)<<6|c3&63),c+=3);return a}};
function createSocketWorker(){var b="",b=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(b=(new Date).getTime());b=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"dext5upload.uploadplus.min.js?ver="+b:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.uploadplus.js?ver="+b;return new Worker(b)}
function setSocketEvent(b,a,c){var e=getDialogDocument();b.onmessage=function(b){b=b.data;switch(b.type){case "complete":G_Complete_Socket_worker++;if(G_Complete_Socket_worker==c.totalWorkerLength){G_SocketUploadWorkArray=[];var g=RESULTFILELIST[uploaders[upload_complete_count]];socketUploadMergeAndAfterEvent(b.id,g.originName,g.guid,{numberOfChunks:c.totalWorkerLength},UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,!0,a)}else if(0<G_SocketUploadWorkArray.length)try{var g=b.workerIdx,k=G_SocketWorkerPool[g],
n=G_SocketUploadWorkArray.pop();k.postMessage({type:"start",guid:RESULTFILELIST[uploaders[upload_complete_count]].guid,file:RESULTFILELIST[uploaders[upload_complete_count]].file,id:uploaders[upload_complete_count],asyncstate:UPLOADTOP.G_CURRUPLOADER._config.asyncUpload,bytesperchunk:UPLOADTOP.G_CURRUPLOADER._config.wsChunkSize,configValue:UPLOADTOP.G_CURRUPLOADER._config,formdataex:G_FormData,beforeEventValue:a,currWorkInfo:n,socketWorkerJobSize:c.socketWorkerJobSize,workerIdx:g})}catch(p){}break;
case "progress":e.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b.id).innerHTML=Dext5Upload_Lang.upload_status.uploading;uploadProgress(b.uploadedSize,b.uploadedChunks,b.chunkInfo,b.id,b.blockSize);break;case "error":removeSocket();g=getUploadedFileListObj();try{900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(b.code)&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(b.code)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,b.code,b.message,g):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+
b.code)?"017"==b.code?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,b.code,b.message,g):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,g):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,b.code,eval("Dext5Upload_Lang.error_info.error_code_"+b.code),g)}catch(l){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(G_TagID,G_LocalFileObject),G_LocalFileObject=[],G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID));closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel();break;case "closeComplete":g=b.workerIdx;G_SocketWorkerPool[g].terminate();G_SocketWorkerPool[g]=null;G_SocketWorkerCloseCount++;G_SocketWorkerCloseCount==G_SocketWorkerPool.length&&(G_SocketWorkerPool=[],G_SocketWorkerCloseCount=0);break;case "log":dextWriteLog(b.message+
"\r\n",!0)}}}
function socketUploadMergeAndAfterEvent(b,a,c,e,f,g,k){var n=getDialogDocument(),p=XHRFactory.getInstance();p.open("POST",f,g);p.onreadystatechange=function(a){if(4==p.readyState)if(200==p.status)try{var c=p.responseText,c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|");if("complete"==e[0]){var f=e[1],g=f.indexOf("::");-1<g?(orgfilename=f.substring(0,g),uploadfilepath=
f.substring(g+2)):(orgfilename=RESULTFILELIST[b].originName,uploadfilepath=e[1]);n.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b).innerHTML=Dext5Upload_Lang.upload_status.uploaded;RESULTFILELIST[b].originName=orgfilename;RESULTFILELIST[b].uploadName=e[2];RESULTFILELIST[b].uploadPath=uploadfilepath;RESULTFILELIST[b].status="complete";RESULTFILELIST[b].logicalPath="";4==e.length?-1<e[3].indexOf("\b")?(RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=e[3].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=
RESULTFILELIST[b].groupId):(RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId):5==e.length&&(-1<e[4].indexOf("\b")?(RESULTFILELIST[b].responseCustomValue=e[4].split("\b")[0],RESULTFILELIST[b].groupId=e[4].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[b].groupId):(RESULTFILELIST[b].responseCustomValue=e[4],RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId));upload_complete_count++;G_Complete_Socket_worker=
0;G_SocketUploadWorkArray=[];G_oneFileUploadedSize=0;null!=G_curr_socket&&(G_curr_socket.onClose(),G_curr_socket=null);if(upload_complete_count==TOTALUPLOADNUM)totalUploadComplete();else{var k=uploaders.indexOf(b);adjustUploadFileListScroll(k,upload_complete_count,TOTALUPLOADNUM);soketUploadStart();XHRFactory.release(p)}}else{var l=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],e[2],l)}catch(q){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&
fileListReset();"1"==G_UseAddLocalFileObject&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(inputFileObjectChange(G_TagID,G_LocalFileObject),G_LocalFileObject=[],G_TagID=[]),UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID));XHRFactory.release(p);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}}catch(A){}else{l=getUploadedFileListObj();try{c=p.responseText,c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=
UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|")}catch(D){UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,l)}try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+e[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],e[2],l):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],eval("Dext5Upload_Lang.error_info.error_code_"+
e[1]),l)}catch(C){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(p);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};c=k.tempPath+"|"+c+"|"+getExtension(a)+"|";c+=k.newFileLocation+"|"+k.responseFileServerPath+"|"+k.responseFileName;c=Dext5Base64.encode(c);c=c.replace(/[+]/g,"%2B");"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?
(k=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"h5pae"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),k+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d19"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+e.numberOfChunks+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d34"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,
k+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c.fileIdx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(k),a="d00="+k):(a="dext5CMD=h5pae&info="+c+"&fileName="+encodeURIComponent(a)+"&numberOfChunks="+e.numberOfChunks,a+="&gpid="+k.groupId+"&fidx="+k.fileIdx);
p.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");try{0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&p.send(a)}catch(l){XHRFactory.release(p);a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,a)}catch(q){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)}}
function reLoadStatusBar(b){var a=document.getElementById("basic_file_info").parentNode,c;c="";"0"==b.statusBarShowLimit&&(c+="display: none;");c=""+('<li id="basic_file_info" class="basic_file_info" style="'+c+'">');if("upload"==b.mode){if("html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4)0<b.maxTotalFileCount&&(c+=Dext5Upload_Lang.file_maximum+" ");else if(0<b.maxTotalFileCount||0<b.maxTotalFileSize)c+=Dext5Upload_Lang.file_maximum+" ";0<b.maxTotalFileCount&&(c+="<span>"+b.maxTotalFileCount+
"</span> "+Dext5Upload_Lang.file_unit+" ");if("html4"!=b.userRunTimeMode||"1"!=b.uploadMethodHtml4)if(0<b.maxTotalFileSize){var e=Dext5Upload_Lang.file_maximum_limit,e=e.replace("{0}","<span>"+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(b.maxTotalFileSize).toString+"</span>");c+=e}else c+=Dext5Upload_Lang.file_no_limit}e="";"0"==b.statusBarShowStatus&&(e+="display: none;");c=c+"</li>"+('<li class="contents_file_info" id="current_file_info" style="'+e+'">');c+='<span id="total_num">'+getTotalFileCount()+
"</span> "+Dext5Upload_Lang.file_unit;if("html4"!=b.userRunTimeMode||"1"!=b.uploadMethodHtml4||"upload"!=b.mode)c+=', <span id="total_size">'+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(getTotalFileSize()).toString+"</span>";c+=' <span id="file_inserted_text">';"upload"==b.mode&&(c+=Dext5Upload_Lang.file_inserted);c+="</span>";c+="</li>";a.innerHTML=c}
function getSelectedFileCount(){var b=0;if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var a=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileInfoList(-1);if(null==a||""==a)b=-1;else for(var c=a.split(G_vertical),e=c.length,a=0;a<e;a++)if(""!=c[a]){var f=c[a].split(G_formfeed);"1"!=f[10]&&"1"!=f[11]||b++}}else if(c=document.getElementById("file_list").getElementsByTagName("input"),e=c.length,0==e)b=-1;else for(a=0;a<e;a++)(c[a].getAttribute("dext_select")&&"1"==c[a].getAttribute("dext_select")||
c[a].checked)&&b++;return b}
function saveAndOpenEx(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}UPLOADTOP.DEXT5UPLOAD.DoSaveAndOpenEx(UPLOADTOP.G_CURRUPLOADER.ID)}else if("1"==
UPLOADTOP.G_CURRUPLOADER._config.hybridDownload){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}for(var c=document.getElementById("file_list").getElementsByTagName("input"),e=c.length,f=!1,g=!1,k=-1,n=-1,p=-1,l=0;l<e;l++){var q=c[l].getAttribute("dext_select");if(q&&"1"==q){f=!0;k=l;break}else c[l].checked&&(g=!0,-1==n&&
(n=l))}if(f)for(l=0;l<e;l++)k==l&&(p=c[l].getAttribute("listvalue"),null!=p&&void 0!=p&&"n"==RESULTFILELIST[p].isWebFile||"y"==RESULTFILELIST[p].isDelete)&&(p=-1);else if(g)for(l=0;l<e;l++)if(c[l].checked){if(p=c[l].getAttribute("listvalue"),null!=p&&void 0!=p&&"n"==RESULTFILELIST[p].isWebFile||"y"==RESULTFILELIST[p].isDelete)c[l].checked=!1,p=-1}else c[l].checked=!1;-1!=p&&excuteHybridDownloadOrOpen("U02",[RESULTFILELIST[p]])}}
function saveAndFolderOpenEx(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if(("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}UPLOADTOP.DEXT5UPLOAD.DoSaveAndFolderOpenEx(UPLOADTOP.G_CURRUPLOADER.ID)}}
function printFileEx(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}UPLOADTOP.DEXT5UPLOAD.DoPrintFileEx(UPLOADTOP.G_CURRUPLOADER.ID)}else if("1"==
UPLOADTOP.G_CURRUPLOADER._config.hybridDownload){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.openUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}for(var c=document.getElementById("file_list").getElementsByTagName("input"),e=c.length,f=!1,g=!1,k=-1,n=-1,p=-1,l=0;l<e;l++){var q=c[l].getAttribute("dext_select");if(q&&"1"==q){f=!0;k=l;break}else c[l].checked&&(g=!0,-1==n&&
(n=l))}if(f)for(l=0;l<e;l++)k==l&&(p=c[l].getAttribute("listvalue"),null!=p&&void 0!=p&&"n"==RESULTFILELIST[p].isWebFile||"y"==RESULTFILELIST[p].isDelete)&&(p=-1);else if(g)for(l=0;l<e;l++)if(c[l].checked){if(p=c[l].getAttribute("listvalue"),null!=p&&void 0!=p&&"n"==RESULTFILELIST[p].isWebFile||"y"==RESULTFILELIST[p].isDelete)c[l].checked=!1,p=-1}else c[l].checked=!1;-1!=p&&excuteHybridDownloadOrOpen("U04",[RESULTFILELIST[p]])}}
function downloadZipfile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}UPLOADTOP.DEXT5UPLOAD.DoSaveFileEx("",
UPLOADTOP.G_CURRUPLOADER.ID)}else if("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen){c=getSelectedFileCount();if(-1==c){alert(Dext5Upload_Lang.message_file_notexist);return}if(0==c){alert(Dext5Upload_Lang.message_file_unchosen);return}}for(var e=document.getElementById("file_list"),c=document.getElementById("chk_all_box"),e=e.getElementsByTagName("input"),f=e.length,g=[],k=[],n=0;n<f;n++){var p=e[n].getAttribute("dext_select");
p&&"1"==p?g.push(n):e[n].checked&&g.push(n)}p=g.length;for(n=0;n<f;n++)for(var l=0;l<p;l++)if(n==g[l]){var q=e[g[l]].getAttribute("listvalue");null!=q&&void 0!=q&&"y"==RESULTFILELIST[q].isWebFile&&"n"==RESULTFILELIST[q].isDelete?(k.push(q),e[g[l]].checked=!0,fileListNoneSelection(e[g[l]])):(e[n].checked=!1,fileListNoneSelection(e[n]),c.checked=!1)}0<k.length&&downloadSubmit(k,!0)}}G_AP1[3]=7;
function downloadAllZipfile(b,a){if(b){var c=UPLOADTOP.G_CURRUPLOADER.ID,e=getUploaderFromEvent(b);c!=e.ID&&(UPLOADTOP.G_CURRUPLOADER=e)}if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode||0!=!!a)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen&&(c=getSelectedFileCount(),-1==c)){alert(Dext5Upload_Lang.message_file_notexist);return}UPLOADTOP.DEXT5UPLOAD.DoSaveAllFilesEx("",UPLOADTOP.G_CURRUPLOADER.ID)}else if("1"==
UPLOADTOP.G_CURRUPLOADER._config.hybridDownload){if("0"==UPLOADTOP.G_CURRUPLOADER._config.disableAlertMessage.downloadUnchosen&&(c=getSelectedFileCount(),-1==c)){alert(Dext5Upload_Lang.message_file_notexist);return}e=document.getElementById("file_list");if(e.hasChildNodes()){c=document.getElementById("chk_all_box");c.checked=!0;checkAll();for(var e=e.getElementsByTagName("input"),f=e.length,g=[],k=0;k<f;k++){if(e[k].checked){var n=e[k].getAttribute("listvalue");null!=n&&void 0!=n&&"y"==RESULTFILELIST[n].isWebFile&&
"n"==RESULTFILELIST[n].isDelete?g.push(n):(e[k].checked=!1,c.checked=!1)}e[k].getAttribute("dext_select")&&"1"==e[k].getAttribute("dext_select")&&fileListNoneSelection(e[k])}0<g.length&&downloadSubmit(g,!0)}}}
function setFileSizeAfterUpload(){for(var b=[],a=document.getElementById("file_list").getElementsByTagName("span"),c=a.length,e=/(^|\s)DEXT_fiVe_UP_fsize(\s|$)/,f=g=0;g<c;g++)e.test(a[g].getAttribute("name"))&&(b[f]=a[g],f++);for(var a=RESULTFILELIST.length,g=0,f=0;g<a;g++)"n"==RESULTFILELIST[g].isDelete&&""!=RESULTFILELIST[g].fileSize&&(c=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(RESULTFILELIST[g].fileSize),b[f].innerHTML="<span>"+c.toString+"</span>",f++);calcTotalSize();b=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(TOTALFILELISTSIZE);
g=document.getElementById("current_file_info");f="";f='<span id="total_num">'+TOTALFILELISTNUM+"</span> "+Dext5Upload_Lang.file_unit;f+=', <span id="total_size">'+b.toString+"</span>";f+=' <span id="file_inserted_text">';"upload"==UPLOADTOP.G_CURRUPLOADER._config.mode&&(f+=Dext5Upload_Lang.file_inserted);g.innerHTML=f+"</span>"}
function setLargeFileAllList(){if("html4"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&0<UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markBaseTotalSize){var b=0,a=UPLOADTOP.DEXT5UPLOAD.GetAllFileMergeListForJson();if(a)for(var c=a.size.length,e=0;e<c;e++){var f=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a.size[e]),b=b+f;b>=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markBaseTotalSize?setLargeFile(e,!0):0<UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markSize?isLargeFiles(f)?setLargeFile(e,!0):setLargeFile(e,
!1):setLargeFile(e,!1)}}}
function setLargeFile(b,a){var c=document.getElementById("file_list").getElementsByTagName("input"),e=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c[b].getAttribute("listvalue")),c=c[b].parentNode.nextSibling;if(a){var f=c.getElementsByTagName("span");if(1==f.length){f=document.createElement("span");f.className="larger_fsize";""!=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color&&(f.style.color=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color,f.style.borderColor=UPLOADTOP.G_CURRUPLOADER._config.largeFiles.color);var g=
"",g=""==UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text?Dext5Upload_Lang.large_files:UPLOADTOP.G_CURRUPLOADER._config.largeFiles.text;f.title=g;f.innerHTML=g;g=0;if(UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth)for(var k=UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth.length,n=0;n<k;n++)g+=parseInt(UPLOADTOP.G_CURRUPLOADER._config.headerBarItemWidth[n],10);f.style.right="0"==UPLOADTOP.G_CURRUPLOADER._config.sizeColumnWidth?g+5+"px":g+109+"px";c.appendChild(f)}RESULTFILELIST[e].largeFile=
"1"}else f=c.getElementsByTagName("span"),2==f.length&&c.removeChild(f[1]),RESULTFILELIST[e].largeFile="0"}function isSettingLargFile(){var b=!1;if(0<UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markSize||0<UPLOADTOP.G_CURRUPLOADER._config.largeFiles.markBaseTotalSize||"1"==UPLOADTOP.G_CURRUPLOADER._config.largeFiles.customMode)b=!0;return b}
function changeImageQuality(){var b=G_ChnageImageArr.length;G_changeCount=0;var a="",a=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(a=(new Date).getTime());for(var a=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"dext5upload.image.process.min.js?ver="+a:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.image.process.js?ver="+a,c=UPLOADTOP.G_CURRUPLOADER._config.imageQuality.quality,e=UPLOADTOP.G_CURRUPLOADER._config.imageQuality.workerCount,
f=b>=e?e:b,g=0;g<f;g++){var k=new Worker(a);k.onmessage=function(a){a=a.data;switch(a.type){case "complete":var c=a.sendData;a=c.workerIdx;var f=c.fileIdx,c=c.newBlob;RESULTFILELIST[f].file=c;var g=RESULTFILELIST[f].originName;if("jpg"!=getExtension(g)&&"jpeg"!=getExtension(g)){var k=g.lastIndexOf("."),g=g.substring(0,k)+".jpg";RESULTFILELIST[f].originName=g}RESULTFILELIST[f].file.name=g;RESULTFILELIST[f].fileSize=c.size;RESULTFILELIST[f].completeImgQualityPrc=!0;c=document.getElementById("file_list").getElementsByTagName("input");
g=c.length;for(j=0;j<g;j++)if(c[j].getAttribute("listvalue")&&c[j].getAttribute("listvalue")==f.toString()){c[j].parentNode.parentNode.childNodes[2].firstChild.innerHTML=UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(RESULTFILELIST[f].fileSize).toString;break}calcTotalSize();displayTotalSizeAndNum();G_changeCount++;document.getElementById("quality_processing")&&(f=parseInt(G_changeCount/b*100,10),document.getElementById("quality_processing").innerHTML=f+"%");G_changeCount==b?changeImageQualityComplete():
(f=G_changeCount+e,G_ChnageImageArr[f-1]&&l(a,G_ChnageImageArr[f-1]))}};G_ImageWorkerArr.push(k)}for(var n=document.getElementById("imageCanvas"),p=n.getContext("2d"),l=function(a,f){return function(a,f){var g=RESULTFILELIST[f].file,k=new Image;k.onload=function(){try{n.width=k.width;n.height=k.height;p.drawImage(k,0,0);var b=n.toDataURL("image/jpeg",c);k.setAttribute("dext_thumbs","true");delete k;try{p.clearRect(0,0,n.width,n.height),p.beginPath()}catch(e){}var l=65636;l>g.size&&(l=g.size);var r;
r=g.mozSlice?g.mozSlice(0,l):g.webkitSlice?g.webkitSlice(0,l):g.slice(0,l);var q=new FileReader;q.onload=function(c){r=null;c=new Uint8Array(c.target.result);c=[].slice.call(c);G_ImageWorkerArr[a].postMessage({type:"start",sendData:{workerIdx:a,fildIdx:f,listObj:RESULTFILELIST[f],imageOriginalData:c,imageNewData:b}})};q.readAsArrayBuffer(r)}catch(z){}};k.onerror=function(c){null==c.target.getAttribute("dext_thumbs")&&(G_changeCount++,RESULTFILELIST[f].completeImgQualityPrc=!0,G_changeCount==b?changeImageQualityComplete():
(c=G_changeCount+e,G_ChnageImageArr[c-1]&&l(a,G_ChnageImageArr[c-1])))};try{var r=(window.URL||window.webkitURL||window).createObjectURL(RESULTFILELIST[f].file);k.src=r}catch(q){G_changeCount++,RESULTFILELIST[f].completeImgQualityPrc=!0,G_changeCount==b?changeImageQualityComplete():(r=G_changeCount+e,G_ChnageImageArr[r-1]&&l(a,G_ChnageImageArr[r-1]))}}}(),g=0;g<f;g++)l(g,G_ChnageImageArr[g])}
function changeImageQualityWithoutWorker(){var b=G_ChnageImageArr.length;G_changeCount=0;var a=UPLOADTOP.G_CURRUPLOADER._config.imageQuality.quality,c=document.getElementById("imageCanvas"),e=c.getContext("2d"),f=0,g=function(k){return function(k){var p=RESULTFILELIST[k].file,l=new Image;l.onload=function(){c.width=l.width;c.height=l.height;e.drawImage(l,0,0);var r=c.toDataURL("image/jpeg",a);l.setAttribute("dext_thumbs","true");delete l;var q=65636;q>p.size&&(q=p.size);var y;y=p.mozSlice?p.mozSlice(0,
q):p.webkitSlice?p.webkitSlice(0,q):p.slice(0,q);q=new FileReader;q.onload=function(a){y=null;a=new Uint8Array(a.target.result);a=[].slice.call(a);r=ExifRestorer.restore2(a,r);0>r.indexOf("data:image/jpeg;base64,")&&(r="data:image/jpeg;base64,"+r);a=UPLOADTOP.DEXT5UPLOAD.util.dataURItoBlob(r);RESULTFILELIST[k].file=a;var c=RESULTFILELIST[k].originName;if("jpg"!=getExtension(c)&&"jpeg"!=getExtension(c)){var e=c.lastIndexOf("."),c=c.substring(0,e)+".jpg";RESULTFILELIST[k].originName=c}RESULTFILELIST[k].file.name=
c;RESULTFILELIST[k].file.name=RESULTFILELIST[k].originName;RESULTFILELIST[k].fileSize=a.size;RESULTFILELIST[k].completeImgQualityPrc=!0;G_changeCount++;document.getElementById("quality_processing")&&(a=parseInt(G_changeCount/b*100,10),document.getElementById("quality_processing").innerHTML=a+"%");a=document.getElementById("file_list").getElementsByTagName("input");c=a.length;for(j=0;j<c;j++)if(a[j].getAttribute("listvalue")&&a[j].getAttribute("listvalue")==k.toString()){a[j].parentNode.parentNode.childNodes[2].firstChild.innerHTML=
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(RESULTFILELIST[k].fileSize).toString;break}G_changeCount==b?changeImageQualityComplete():(f++,g(G_ChnageImageArr[f]))};q.readAsArrayBuffer(y)};l.onerror=function(a){null==a.target.getAttribute("dext_thumbs")&&(G_changeCount++,RESULTFILELIST[k].completeImgQualityPrc=!0,G_changeCount==b&&changeImageQualityComplete())};try{var q=(window.URL||window.webkitURL||window).createObjectURL(RESULTFILELIST[k].file);l.src=q}catch(r){G_changeCount++,RESULTFILELIST[k].completeImgQualityPrc=
!0,G_changeCount==b&&changeImageQualityComplete()}}}(G_ChnageImageArr[f]);g(G_ChnageImageArr[f])}function changeImageQualityComplete(){calcTotalSize();displayTotalSizeAndNum();for(var b=0;b<G_ImageWorkerArr.length;b++)G_ImageWorkerArr[b]&&G_ImageWorkerArr[b].terminate();G_changeCount=0;G_ImageWorkerArr=[];G_CompleteImageQuality=!0}
function setting(){var b=UPLOADTOP.G_CURRUPLOADER._config,a=setHybridParam(UPLOADTOP.G_CURRUPLOADER._config.hybridParam,[["p00","U99"]]),a=makeEncryptHybridParam(a);UPLOADTOP.DEXT5UPLOAD.browser.ie&&"html4"==b.userRunTimeMode?(G_RetryHybridCmdCount=0,xmlHttpSendForHybrid_submit(a,!1)):xmlHttpSendForHybrid(a,!1)}
function xmlHttpSendForHybrid(b,a){var c=0,e=function(){var f=UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest();f.open("POST",UPLOADTOP.G_CURRUPLOADER.hybridUrl+UPLOADTOP.G_CURRUPLOADER.hybridPort,!1);f.setRequestHeader("Content-type","application/x-www-form-urlencoded");f.onreadystatechange=function(){if(4==f.readyState&&200==f.status){var b=f.responseText.split("|");b[2]=b[2].split("<")[0];G_TestResult=b[0];0==b[0].indexOf("2000")?(c++,1==c&&e()):0==b[0].indexOf("1000")&&a&&("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?
downloadEventCheck():setTimeout(downloadEventCheck,0))}};f.send(b)};e()}var G_StopEventCheck=!1,G_EventCheckInterval=null;
function downloadEventCheck(b){var a="",c,e;if(b)a="8000";else try{c=UPLOADTOP.G_CURRUPLOADER.downEventkeyArr[0].split("|"),a=c[0],e=c[1]}catch(f){return}b=UPLOADTOP.G_CURRUPLOADER._config.hybridParam;b=setHybridParam(UPLOADTOP.G_CURRUPLOADER._config.hybridParam,[["p00","U90"],["p29",a]]);b=makeEncryptHybridParam(b);var g=UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest();g.open("POST",UPLOADTOP.G_CURRUPLOADER.hybridUrl+UPLOADTOP.G_CURRUPLOADER.hybridPort,!0);g.setRequestHeader("Content-type","application/x-www-form-urlencoded");
g.onreadystatechange=function(){if(4==g.readyState&&200==g.status){var a=g.responseText,b=a.split("|");if(0==b[0].indexOf("800"))if("8001"==b[0])UPLOADTOP.G_CURRUPLOADER.downEventkeyArr=[];else if("8002"==b[0])UPLOADTOP.G_CURRUPLOADER.downEventkeyArr.splice(0,1),UPLOADTOP.G_CURRUPLOADER.checkDownloadFail=!0,UPLOADTOP.G_CURRUPLOADER.downEventkeyArr[0]?"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?downloadEventCheck():setTimeout(downloadEventCheck,700):("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?
downloadEventCheck(!0):setTimeout(function(){downloadEventCheck(!0)},700),UPLOADTOP.G_CURRUPLOADER.checkDownloadFail=!1);else{if("8000"==b[0]){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useScriptEventControl)try{3!=b[2].length&&(b[3]=b[2],b[2]="899"),void 0==eval("Dext5Upload_Lang.error_info.error_code_"+b[2])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,b[2],b[3]):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,b[2],eval("Dext5Upload_Lang.error_info.error_code_"+
b[2]))}catch(c){}UPLOADTOP.G_CURRUPLOADER.downEventkeyArr=[]}}else if("1999"==b[0]){UPLOADTOP.G_CURRUPLOADER.downEventkeyArr.splice(0,1);var f=!0;if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterDownloadEvent){try{var q=decodeURIComponent(b[2]),f=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterDownload(UPLOADTOP.G_CURRUPLOADER.ID,q,b[3])}catch(r){}"U01"!=e&&0!=f&&openCmdSendAfterDownloadEvent(a)}UPLOADTOP.G_CURRUPLOADER.downEventkeyArr[0]?"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?downloadEventCheck():
setTimeout(downloadEventCheck,700):UPLOADTOP.G_CURRUPLOADER.checkDownloadFail&&("1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?downloadEventCheck(!0):setTimeout(function(){downloadEventCheck(!0)},0),UPLOADTOP.G_CURRUPLOADER.checkDownloadFail=!1)}else"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridWindowMode?downloadEventCheck():setTimeout(downloadEventCheck,700)}};g.send(b)}
function excuteHybridDownloadOrOpen(b,a,c){var e=UPLOADTOP.G_CURRUPLOADER._config,f=a.length,g=e.handlerUrl;""!=e.downloadHandlerUrl&&(g=e.downloadHandlerUrl);for(var k=[],n=0;n<f;n++){var p=a[n].webPath;"1"!=e.directDownload?(p="&fileVirtualPath="+encodeURIComponent(p)+"&fileOrgName="+encodeURIComponent(a[n].originName),a[n].customValue&&""!=a[n].customValue&&(p=p+"&customValue="+encodeURIComponent(a[n].customValue)),p=0>g.indexOf("?")?g+"?dext5CMD=downloadRequest"+p:g+"&dext5CMD=downloadRequest"+
p):p=encodeURIComponent(p);var l;l=isNaN(Number(a[n].fileSize))||0>Number(a[n].fileSize)||""==a[n].fileSize?9E18:parseInt(a[n].fileSize,10);var q=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();1==c?0==UPLOADTOP.G_CURRUPLOADER.downEventkeyArr&&UPLOADTOP.G_CURRUPLOADER.downEventkeyArr.push(q+"|"+b):UPLOADTOP.G_CURRUPLOADER.downEventkeyArr.push(q+"|"+b);p={key:q,fname:encodeURIComponent(a[n].originName),hurl:p,size:l,guid:q};k.push(p)}a={use:1==c?1:0,name:""};c=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.useAfterDownloadEvent);
f=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.security.encryptParam);"1"==e.directDownload&&1==f&&(f=0);b=[["p00",b],["p11",c],["p30",1],["p31","1"==UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.resumeDownload)?2:0],["p32",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.automaticConnection)],["p33","standard"==e.uploadTransferWindow.view?0:1],["p34",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.showFolderView)],["p35",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.timeout)],["p36",a],["p37",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.useScriptEventControl)],
["p38",encodeURIComponent(e.defaultDownloadPath)],["p39",encodeURIComponent(e.savePathSetting)],["p40",f],["p41",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.security.fileExtensionDetector)],["p42","1"==e.security.fileIntegrity?e.security.keyValue:""],["p43","1"==e.security.fileEncrypt?e.security.keyValue:""],["p44",UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e.highSpeed)],["p45",e.fileNameRuleEx],["p70",k]];1==e.hybridMethod&&b.push(["p12",1]);b=setHybridParam(UPLOADTOP.G_CURRUPLOADER._config.hybridParam,
b);b=makeEncryptHybridParam(b);UPLOADTOP.DEXT5UPLOAD.browser.ie&&"html4"==e.userRunTimeMode?(G_RetryHybridCmdCount=0,xmlHttpSendForHybrid_submit(b)):xmlHttpSendForHybrid(b,"1"==UPLOADTOP.G_CURRUPLOADER._config.useScriptEventControl||"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterDownloadEvent?!0:!1)}
function xmlHttpSendForHybrid_submit(b){G_LastHybridCmd=b;UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,UPLOADTOP.G_CURRUPLOADER.hybridUrl+UPLOADTOP.G_CURRUPLOADER.hybridPort,"download_frame",[["d00",b]])}
function downloadEventCheck_submit(b){var a;b?b="8000":(a=UPLOADTOP.G_CURRUPLOADER.downEventkeyArr[0].split("|"),b=a[0],UPLOADTOP.G_CURRUPLOADER.hybridLastCmd=a[1]);a=UPLOADTOP.G_CURRUPLOADER._config.hybridParam;a=setHybridParam(UPLOADTOP.G_CURRUPLOADER._config.hybridParam,[["p00","U90"],["p29",b]]);a=makeEncryptHybridParam(a);UPLOADTOP.DEXT5UPLOAD.util.postFormData(document,UPLOADTOP.G_CURRUPLOADER.hybridUrl+UPLOADTOP.G_CURRUPLOADER.hybridPort,"download_frame",[["d00",a]])}
function openCmdSendAfterDownloadEvent(b){var a=UPLOADTOP.DEXT5UPLOAD.ajax.createXMLHttpRequest();a.open("POST",UPLOADTOP.G_CURRUPLOADER.hybridUrl+UPLOADTOP.G_CURRUPLOADER.hybridPort,!0);a.setRequestHeader("Content-type","application/x-www-form-urlencoded");var c=UPLOADTOP.G_CURRUPLOADER._config.hybridParam,c=setHybridParam(UPLOADTOP.G_CURRUPLOADER._config.hybridParam,[["p00","U90"],["p29",b]]),c=makeEncryptHybridParam(c);a.send(c)}
function createHybridParamInit(b){var a;a='{"p00": "",'+('"p01": '+UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(b.hybridWindowMode)+",");a+='"p02": "'+b.plugin_version+'",';a+='"p03": "'+b.webPath.hybrid+'",';a+='"p04": { "browser": "'+UPLOADTOP.DEXT5UPLOAD.UserAgent.browser.name+'", "version": "'+UPLOADTOP.DEXT5UPLOAD.UserAgent.browser.major+'" },';a+='"p05": "'+b.handlerUrl+'",';a+='"p06": "'+b.productKey+'",';a+='"p07": "'+b.licenseKey+'",';a+='"p08": "'+encodeURIComponent(b.messageTitle)+'",';a+='"p09": "'+
b.lang+'",';var c=0,e=b.customHeaderColor,f=b.customFooterColor,g=b.customProgressBarColor,k=b.customTextColor;if(""!=e||""!=f||""!=g||""!=k)c=1,e=e.replace("#",""),f=f.replace("#",""),g=g.replace("#",""),k=k.replace("#","");a+='"p10": { "usercolor": '+c+', "name" : "'+b.skinName+'", "color1" : "'+e+'", "color2" : "'+f+'", "color3" : "'+g+'", "color4" : "'+k+'" },';a+='"p13": '+b.allowedRealtimeDownloadAdd+",";a+='"p14": '+b.hybridShowItemCount+",";0<b.downloadPartialSize&&(a+='"p15": '+b.downloadPartialSize+
",");a+='"p29": "",';c="";try{"1"==UPLOADTOP.G_CURRUPLOADER._config.useSetCookie&&(c=UPLOADTOP.location.protocol+"//"+UPLOADTOP.location.host)}catch(n){}"1"==b.useLogoImage.use&&(a+='"p47": "'+encodeURIComponent(b.useLogoImage.logoPath+"|"+b.useLogoImage.logoVer)+'",');a+='"p99": "'+c+'"';a+="}";""!=b.NTLM&&(b={keyvalue:encodeURIComponent("Authorization="+b.NTLM)},UPLOADTOP.G_CURRUPLOADER.httpHeaderArr.push(b));return a=setHybridParam(a,[["p46",UPLOADTOP.G_CURRUPLOADER.httpHeaderArr]])}
function setHybridParam(b,a){for(var c=b,c=JSON.parse(b),e=a.length,f=0;f<e;f++)c[a[f][0]]=a[f][1];return c=JSON.stringify(c)}function makeEncryptHybridParam(b){b=UPLOADTOP.DEXT5UPLOAD.util.base64_encode(b);b=b.insertAt(G_AP1[0],G_AP2[0]);b=b.insertAt(G_AP1[1],G_AP2[1]);b=b.insertAt(G_AP1[2],G_AP2[2]);b=b.insertAt(G_AP1[3],G_AP2[3]);b=b.insertAt(G_AP1[0],G_AP2[4]);b=b.insertAt(G_AP1[1],G_AP2[5]);b=b.insertAt(G_AP1[2],G_AP2[6]);b=b.replace(/[+]/g,"%2B");return b+="\x0B"}
function requestPluginInstall(b,a){var c=a.createElement("iframe");c.name="download_frame";c.id="download_frame";c.style.display="none";a.body.appendChild(c);c=a.createElement("form");c.target="download_frame";var e="",e=""!=b.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl?b.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl:b.UPLOADTOP.G_CURRUPLOADER._config.webPath.plugin+b.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallFileName;c.action=e;a.body.appendChild(c);c.submit();a.body.removeChild(c)}
function requestHybridInstall(b,a){var c=a.createElement("iframe");c.name="download_frame";c.id="download_frame";c.style.display="none";a.body.appendChild(c);c=a.createElement("form");c.target="download_frame";c.action=b.UPLOADTOP.G_CURRUPLOADER._config.webPath.hybrid+b.UPLOADTOP.G_CURRUPLOADER._config.installFileName;a.body.appendChild(c);c.submit();a.body.removeChild(c)}
function getDialogWindow(){var b=UPLOADTOP.window;null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow&&(b=UPLOADTOP.G_CURRUPLOADER.dialogWindow);return b}function getDialogDocument(){var b=UPLOADTOP.window.document;null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow&&(b=UPLOADTOP.G_CURRUPLOADER.dialogWindow.document);return b}
function _layer_PasteEventMake(b){try{b&&(UPLOADTOP.G_CURRUPLOADER=getUploaderFromEvent(b));var a=UPLOADTOP.G_CURRUPLOADER;if("upload"==a._config.mode&&"form"!=a._config.subMode)if(UPLOADTOP.DEXT5UPLOAD.browser.ie&&11<=UPLOADTOP.DEXT5UPLOAD.browser.ieVersion){var c=document.createElement("div");c.id="pasteDiv";c.innerHTML=unescape("%uFEFF");c.style.position="absolute";c.style.filter="alpha(opacity=0)";c.style.overflow="hidden";c.style.left="-1000px";c.style.top="-1000px";c.style.width="0px";c.style.height=
"0px";c.contentEditable=!0;document.body.appendChild(c);c.focus()}else if(UPLOADTOP.DEXT5UPLOAD.browser.gecko){var e=document.createElement("iframe");e.id="pasteIframe";e.frameBorder=0;e.style.width="0px";e.style.height="0px";e.style.position="absolute";e.style.left="0px";e.style.top="0px";if(UPLOADTOP.DEXT5UPLOAD.browser.mobile||UPLOADTOP.DEXT5UPLOAD.browser.iOS)e.style.overflow="hidden",e.style.display="inline-block";var f="document.open();"+(UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)?
'document.domain="'+document.domain+'";':"")+" document.close();",f=UPLOADTOP.DEXT5UPLOAD.browser.ie?"javascript:void(function(){"+encodeURIComponent(f)+"}())":"";e.src=f;document.body.appendChild(e);var g=UPLOADTOP.DEXT5UPLOAD.util.getIframeDocument(e);g.open("text/html","replace");UPLOADTOP.DEXT5UPLOAD.browser.isCustomDomain(document)&&(g.domain=document.domain);g.write('<body contenteditable="true">'+unescape("%uFEFF")+"</body>");g.close();g.body.contentEditable=!0;g.body.onpaste=_layer_PasteEvent;
g.body.focus()}}catch(k){}}
function _layer_PasteEvent(b){try{var a=UPLOADTOP.G_CURRUPLOADER;if("upload"==a._config.mode&&"form"!=a._config.subMode){var c=document.createElement("canvas"),e=c.getContext("2d"),f=function(a){var b=new Image;b.onload=function(){c.width=b.width;c.height=b.height;e.drawImage(b,0,0);var a=c.toDataURL("image/jpeg",1),a=UPLOADTOP.DEXT5UPLOAD.util.dataURItoBlob(a);b=c=null;fileHandlerCopyPasteImage(a,"jpg")};b.src=a};if(UPLOADTOP.DEXT5UPLOAD.browser.ie||UPLOADTOP.DEXT5UPLOAD.browser.gecko)setTimeout(function(){var a=null,
b=null;if(UPLOADTOP.DEXT5UPLOAD.browser.ie)b=a=document.getElementById("pasteDiv");else if(UPLOADTOP.DEXT5UPLOAD.browser.gecko)try{var c=document.getElementById("pasteIframe");c&&(a=UPLOADTOP.DEXT5UPLOAD.util.getIframeDocument(c).body,b=c)}catch(e){a=null}if(a){a.blur();a=a.getElementsByTagName("img");c=a.length;if(0<c)for(var g=0;g<c;g++)f(a[g].src);b.parentNode.removeChild(b)}},0);else if(UPLOADTOP.DEXT5UPLOAD.browser.chrome||UPLOADTOP.DEXT5UPLOAD.browser.opera){var g=b.clipboardData.items,k=g.length;
for(b=0;b<k;++b){var n=g[b].getAsFile(),p=new FileReader;p.onload=function(a){f(a.target.result)};p.readAsDataURL(n)}}}}catch(l){}}
function fileHandlerCopyPasteImage(b,a){if("upload"==UPLOADTOP.G_CURRUPLOADER._config.mode||"form"==uploader._config.subMode){var c=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileSize,e=UPLOADTOP.G_CURRUPLOADER._config.maxTotalFileCount,f=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize,g=!0;if(null!=b&&0!=b.size){var k=UPLOADTOP.G_CURRUPLOADER.frameWin.getTotalFileCount(),n=0,p=[],l=0;calcTotalSize();var q=!1;0!=f&&b.size>f?l++:q=!0;var r=!1,u=UPLOADTOP.G_CURRUPLOADER._config.extension,w=u.extArr.length;
if(0<w){var y=u.allowOrLimit;if("1"==y){for(var t=0;t<w;t++)if(u.extArr[t]==a){r=!0;break}r||p.push(a)}else for(t=0;t<w;t++)if(u.extArr[t]==a){r=!1;p.push(a);break}else r=!0}else r=!0;t=TOTALFILELISTNUM;if(0!=e&&t+1>e)alert(Dext5Upload_Lang.file_maximum+" "+e+Dext5Upload_Lang.message_limit_num);else if(t=TOTALFILELISTSIZE,0!=c&&t+b.size>c)alert(Dext5Upload_Lang.file_maximum+" "+UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(c).toString+Dext5Upload_Lang.message_limit_size);else{c=!0;e=UPLOADTOP.G_CURRUPLOADER._config.largeFiles;
if(isLargeFiles(b.size)){for(var w=RESULTFILELIST.length,z=u=0,t=0;t<w;t++)RESULTFILELIST[t].fileSize>=e.markSize&&"y"!=RESULTFILELIST[t].isDelete&&(u+=RESULTFILELIST[t].fileSize,z++);0<e.maxCount&&z+1>e.maxCount&&(c=Dext5Upload_Lang.message_large_files_count,t=""==e.text?Dext5Upload_Lang.large_files:e.text,c=c.replace("{0}",t),c=c.replace("{1}",e.maxCount),alert(c),c=!1);c&&0<e.maxTotalSize&&r&&q&&u+b.size>e.maxTotalSize&&(c=Dext5Upload_Lang.message_large_files_size,t=""==e.text?Dext5Upload_Lang.large_files:
e.text,c=c.replace("{0}",t),c=c.replace("{1}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(e.maxTotalSize).toString),alert(c),c=!1)}t="image."+a;e=1;u=!1;w=RESULTFILELIST.length;do{u=!1;for(z=0;z<w;z++)if(RESULTFILELIST[z].originName==t){u=!0;break}u&&(t="image("+e+")."+a,e++)}while(u);if(r&&q&&c){if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent)try{g=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(UPLOADTOP.G_CURRUPLOADER.ID,t,b.size,k+n+1,"")}catch(B){}if(g){g={file:b,guid:"",fileIdx:"",webPath:"",
originName:t,fileSize:b.size,uploadName:"",uploadPath:"",logicalPath:"",status:"ready",fileExt:"",isDelete:"n",isWebFile:"n",localPath:"",mark:"",responseCustomValue:"",headerEx:"",groupId:"",completeImgQualityPrc:!1};RESULTFILELIST.push(g);addFileList(g);if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent)try{calcTotalSize(),UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(UPLOADTOP.G_CURRUPLOADER.ID,t,b.size,k+n+1)}catch(x){}n++}}1==l?(k=Dext5Upload_Lang.message_limit_one_size,k=k.replace("{0}",
UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(f).toString),alert(k)):1<l&&(k=Dext5Upload_Lang.message_limit_one_sizes,k=k.replace("{0}",UPLOADTOP.DEXT5UPLOAD.util.bytesToSize(f).toString),k=k.replace("{1}",l),alert(k));f=p.length;0<f&&(l=UPLOADTOP.G_CURRUPLOADER._config.extension.extArr,"1"==y?1<f?(c=Dext5Upload_Lang.message_not_allow_exts,c=c.replace("{0}",f),c=c.replace("{1}",l)):(c=p[0]+" "+Dext5Upload_Lang.message_not_allow_ext,c=c.replace("{0}",l)):1<f?(c=Dext5Upload_Lang.message_not_limit_exts,c=c.replace("{0}",
f),c=c.replace("{1}",l)):(c=p[0]+" "+Dext5Upload_Lang.message_not_limit_ext,c=c.replace("{0}",l)),alert(c));"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc);fileListSortIconReset();calcTotalSize();displayTotalSizeAndNum();setFileListBorder();setTabOrder();setListvalue();setLargeFileAllList();"thumbs"==UPLOADTOP.G_CURRUPLOADER._config.views&&
thumbsViewWithCanvas();if("1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&"0"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&0<n)try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(UPLOADTOP.G_CURRUPLOADER.ID)}catch(A){}}}}}
function createEditorPluginObject(){try{if(UPLOADTOP.DEXT5&&"3.5.1122996"<=UPLOADTOP.DEXT5.ReleaseVer)for(var b=UPLOADTOP.DEXT5.DEXTMULTIPLEID,a=b.length,c=0;c<a;c++){var e=UPLOADTOP.DEXT5.DEXTMULTIPLE["dext_frame_"+b[c]];e._config&&"ieplugin"==e._config.runtimes&&e._FRAMEWIN.fn_createPluginObject(e._config)}}catch(f){}}
function createTransferDivHtml(b){b=b._config;var a="",c="";"2"==b.silentUpload&&(c="display:none;");if("standard"!=b.uploadTransferWindow.view||UPLOADTOP.DEXT5UPLOAD.browser.mobile){a+='<div id="DEXT_fiVe_UP_ly_content" class="DEXT_fiVe_UP_upload_popup_'+b.skinName+'" style="'+G_StrCustomProgressBarColor2+'">';a=a+'<div class="DEXT_fiVe_UP_ly_uploadbox">'+('<div class="popup_header" style="'+G_StrCustomProgressBarColor+"; "+G_StrCustomTextColor+'">');a+='<span class="icon_total_upload"></span>';
a+='<span id="DEXT_fiVe_UP_total_upload_percent" class="total_upload_percent_new"></span><span class="bar_line">-</span>';a+='<span id="DEXT_fiVe_UP_current_upload_file_name"></span>';a+='<div class="DEXT_fiVe_UP_btn_cls" style="'+c+'"><button id="DEXT_fiVe_UP_close_btn" type="button" value="'+Dext5Upload_Lang.upload_close+'">'+Dext5Upload_Lang.upload_close+"</button></div>";a+="</div>";a+='<div class="tbl_status">';a+='<table width="100%" summary="upload transfer window list">';a+="<caption>upload transfer window</caption>";
a+="<colgroup>";a+='<col width="" />';a+='<col width="" />';a+='<col width="" />';a+='<col width="" />';a+="</colgroup>";a+="<tbody>";a+="<tr>";a+='<th scope="row">'+Dext5Upload_Lang.upload_information+" &nbsp;:</th>";a+='<td id="DEXT_fiVe_UP_upload_count">0/0</td>';a="html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4?a+'<th scope="row" id="DEXT_fiVe_UP_upload_size_title" class="type" style="display:none"></th>':a+'<th scope="row" class="type" style="display:none"></th>';a+='<td id="DEXT_fiVe_UP_upload_size" class="type">0/0</td>';
a+="</tr>";if(-1<b.userRunTimeMode.indexOf("html5")||"html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4&&"1"==b.displayFileSizeHtml4)a+='<tr  style="display:none">',a+='<th scope="row">'+Dext5Upload_Lang.upload_speed+" :</th>",a+='<td id="DEXT_fiVe_UP_upload_speed">-</td>',a+='<th scope="row">'+Dext5Upload_Lang.upload_remainingtime+" :</th>",a+='<td id="DEXT_fiVe_UP_upload_remainingtime">-</td>',a+="</tr>";a+="</tbody>";a+="</table>";a+="</div>";a+='<span class="line" style="'+G_StrCustomHeaderColor2+
'"><em class="blind"></em></span>';a+='<div class="DEXT_fiVe_UP_ly_statusbox">';a+='<p class="progressbar">';a+='<span class="tit">'+Dext5Upload_Lang.upload_progress+"</span>";a+='<span id="DEXT_fiVe_UP_total_upload_percent" class="total" style="display:none"></span>';a+='<span class="pbar"><span id="DEXT_fiVe_UP_total_upload_progress_bar" style="width: 0%;'+G_StrCustomProgressBarColor+'" class="pbar_value"></span></span>';a+="</p>";a+="</div>";a+='<div class="ly_uploadfile" style="display:none">';
a+='<div class="uploadfile_h">';a+='<span class="fname">'+Dext5Upload_Lang.file_name+"</span>";a+='<span class="fprogress">'+Dext5Upload_Lang.upload_size+"</span>";a+='<span class="transfer_status">'+Dext5Upload_Lang.upload_status.status+"</span>";a+="</div>";a+='<div id="DEXT_fiVe_UP_uploadfile_lst" class="uploadfile_lst">';a+='<ul id="DEXT_fiVe_UP_uploading_file_list"></ul>';a+="</div>";a+="</div>";a+='<div class="upload_security">';"1"==b.security.fileExtensionDetector&&(a+='<span class="type01"></span>');
"1"==b.security.fileIntegrity&&(a+='<span class="type02"></span>');"1"==b.security.fileEncrypt&&(a+='<span class="type03"></span>');"https:"==location.protocol&&(a+='<span class="type04"></span>');a+="</div>";a+='<div class="DEXT_fiVe_UP_uploadbox_btn" id="DEXT_fiVe_UP_uploadbox_btn">';"1"==b.useLogoImage.use&&(logoVer=b.useLogoImage.logoVer,logoUrl=b.useLogoImage.logoPath+"?version="+logoVer,a+='<span class="logo_area" style="background:url('+logoUrl+') no-repeat;"></span>');a+='<button id="DEXT_fiVe_UP_upload_cancel" type="button" value="cancel" class="input_standard" style="'+
G_StrCustomProgressBarColor+";"+G_StrCustomTextColor+'">'+Dext5Upload_Lang.upload_cancel+"</button>"}else{a+='<div id="DEXT_fiVe_UP_ly_content" class="DEXT_fiVe_UP_upload_popup_'+b.skinName+'" style="'+G_StrCustomProgressBarColor2+'">';a+='<div class="DEXT_fiVe_UP_ly_uploadbox">';a+='<div class="popup_header" style="'+G_StrCustomProgressBarColor+"; "+G_StrCustomTextColor+'">';a+='<span class="icon_total_upload"></span>';a+='<span id="DEXT_fiVe_UP_total_upload_percent" class="total_upload_percent_new"></span><span class="bar_line">-</span>';
a+='<span id="DEXT_fiVe_UP_current_upload_file_name"></span>';a+='<div class="DEXT_fiVe_UP_btn_cls" style="'+c+'"><button id="DEXT_fiVe_UP_close_btn" type="button" value="'+Dext5Upload_Lang.upload_close+'">'+Dext5Upload_Lang.upload_close+"</button></div>";a+="</div>";a+='<div class="DEXT_fiVe_UP_ly_statusbox">';a+='<p class="progressbar">';a+='<span class="tit">'+Dext5Upload_Lang.total_upload+"</span>";a+='<span class="pbar"><span id="DEXT_fiVe_UP_total_upload_progress_bar" style="width: 0%;'+G_StrCustomProgressBarColor+
'" class="pbar_value"></span></span>';a+="</p>";a+="</div>";a+='<span class="line" style="'+G_StrCustomHeaderColor2+'"><em class="blind"></em></span>';a+='<div class="tbl_status">';a+='<table width="100%" summary="upload transfer window list">';a+="<caption>upload transfer window</caption>";a+="<colgroup>";a+='<col width="" />';a+='<col width="" />';a+='<col width="" />';a+='<col width="" />';a+="</colgroup>";a+="<tbody>";a+="<tr>";a+='<th scope="row">'+Dext5Upload_Lang.upload_file+" :</th>";a+='<td id="DEXT_fiVe_UP_upload_count">0/0</td>';
a="html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4?a+('<th scope="row" id="DEXT_fiVe_UP_upload_size_title">'+Dext5Upload_Lang.upload_size+" :</th>"):a+('<th scope="row">'+Dext5Upload_Lang.upload_size+" :</th>");a+='<td id="DEXT_fiVe_UP_upload_size">0/0</td>';a+="</tr>";if(-1<b.userRunTimeMode.indexOf("html5")||"html4"==b.userRunTimeMode&&"1"==b.displayFileSizeHtml4||"html4"==b.userRunTimeMode&&"0"==b.uploadMethodHtml4)a+="<tr>",a+='<th scope="row">'+Dext5Upload_Lang.upload_speed+" :</th>",a+=
'<td id="DEXT_fiVe_UP_upload_speed">-</td>',a+='<th scope="row">'+Dext5Upload_Lang.upload_remainingtime+" :</th>",a+='<td id="DEXT_fiVe_UP_upload_remainingtime">-</td>',a+="</tr>";a+="</tbody>";a+="</table>";a+="</div>";a+='<div class="ly_uploadfile">';a+='<div class="uploadfile_h">';a+='<span class="fname">'+Dext5Upload_Lang.file_name+"</span>";a+='<span class="fprogress">'+Dext5Upload_Lang.upload_size+"</span>";a+='<span class="transfer_status">'+Dext5Upload_Lang.upload_status.status+"</span>";
a+="</div>";a+='<div id="DEXT_fiVe_UP_uploadfile_lst" class="uploadfile_lst">';a+='<ul id="DEXT_fiVe_UP_uploading_file_list"></ul>';a+="</div>";a+="</div>";a+='<div class="upload_security">';"1"==b.security.fileExtensionDetector&&(a+='<span class="type01"></span>');"1"==b.security.fileIntegrity&&(a+='<span class="type02"></span>');"1"==b.security.fileEncrypt&&(a+='<span class="type03"></span>');"https:"==location.protocol&&(a+='<span class="type04"></span>');a+="</div>";a+='<div class="DEXT_fiVe_UP_uploadbox_btn" id="DEXT_fiVe_UP_uploadbox_btn">';
"1"==b.useLogoImage.use&&(logoVer=b.useLogoImage.logoVer,logoUrl=b.useLogoImage.logoPath+"?version="+logoVer,a+='<span class="logo_area" style="background:url('+logoUrl+') no-repeat;"></span>');a+='<button id="DEXT_fiVe_UP_upload_cancel" type="button" value="cancel" class="input_image2" style="'+G_StrCustomProgressBarColor+";"+G_StrCustomTextColor+'">'+Dext5Upload_Lang.upload_cancel+"</button>"}a+="</div>";a+="</div>";return a+="</div>"}
function createImgPreviewDivHtml(b){b=b._config;var a=parseInt(b.imgPreViewHeight)-2+"px",a=""+('<div class="DEXT_fiVe_UP_upload_popup_'+b.skinName+'" style="height:'+a+'">'),a=a+'<strong class="tit_mode">Image Preview</strong>';if(-1<b.userRunTimeMode.indexOf("html5")||"html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4)a+='<span class="img_box"><em></em></span>';a+='<span class="btn_cls" id="DEXT_fiVe_UP_close_btn2">\ub2eb\uae30</span></div>';b=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();
return a+='<iframe frameborder="0" scrolling="no" style="position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1" src="'+b+'" title="Image Preview"></iframe>'}
function makeTransferHTML(b){var a="",c=b._config,a="standard"!=c.uploadTransferWindow.view||UPLOADTOP.DEXT5UPLOAD.browser.mobile?"light"==c.uploadTransferWindow.view?a+('<div id="DEXT_fiVe_UP_ly_wrapper" class="DEXT_fiVe_UP_ly_wrapper DEXT_fiVe_UP_ly_wrapper_standard" style="width:'+c.uploadTransferWindow.viewWidth+'; height:170px; display:none;">'):null==navigator.userAgent.match(/iPad/)&&null!=navigator.userAgent.match(/iPhone|Mobile|UP.Browser|Android|BlackBerry|Windows CE|Nokia|webOS|Opera Mini|SonyEricsson|opera mobi|Windows Phone|IEMobile|POLARIS/)?
a+'<div id="DEXT_fiVe_UP_ly_wrapper" class="DEXT_fiVe_UP_ly_wrapper DEXT_fiVe_UP_ly_wrapper_standard" style="width:320px; height:170px; display:none;">':a+('<div id="DEXT_fiVe_UP_ly_wrapper" class="DEXT_fiVe_UP_ly_wrapper DEXT_fiVe_UP_ly_wrapper_standard" style="width:'+c.uploadTransferWindow.viewWidth+'; height:170px; display:none;">'):a+'<div id="DEXT_fiVe_UP_ly_wrapper" class="DEXT_fiVe_UP_ly_wrapper" style="width:470px; height:260px; display:none;">',a=a+createTransferDivHtml(b);return a+"</div>"}
function makeImgPreViewHTML(b){var a="",c=b._config;parseInt(c.imgPreViewHeight);a+='<div id="DEXT_fiVe_UP_Popup_Mode" style="width:'+c.imgPreViewWidth+";height:"+c.imgPreViewHeight+';display:none">';a+=createImgPreviewDivHtml(b);return a+"</div>"};var G_vertical="\x0B",G_formfeed="\f";function createEvent(b,a,c){var e=document.createElement("script");e.setAttribute("for",b);e.event=a;e.appendChild(document.createTextNode(c));document.body.appendChild(e)}
function dext5PL_init(){var b=document.getElementById("dext5PL");b&&(Dext5PL=b);try{11<=UPLOADTOP.DEXT5UPLOAD.browser.ieVersion?(createEvent(b.id,"d5_event_OnFileChanged(fileCount, fileTotalSize, itemInfo)","dext5_OnFileChanged(fileCount, fileTotalSize, itemInfo)"),createEvent(b.id,"d5_event_OnFileTransCompleted(strNew, strDelete)","dext5_OnFileTransCompleted(strNew, strDelete)"),createEvent(b.id,"d5_event_OnError(nErrorCode, strErrorMessage)","dext5_OnError(nErrorCode, strErrorMessage)"),createEvent(b.id,
"d5_event_BeforeStartFileTransfer()","dext5_BeforeStartFileTransfer()"),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"thumbs"!=UPLOADTOP.G_CURRUPLOADER._config.views&&createEvent(b.id,"d5_event_OnSortArrowState(nArrowState, nColumn)","dext5_OnSortArrowState(nArrowState, nColumn)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent&&createEvent(b.id,"d5_event_BeforeAddFile(nWebFile, strItemKey, strFullPathOrUrl, strFileSize, strFileName)","dext5_BeforeAddFile(nWebFile, strItemKey, strFullPathOrUrl, strFileSize, strFileName)"),
"1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEvent&&createEvent(b.id,"d5_event_BeforeDeleteItem(nWebFile, strItemKey, strItemUrlOrPath, strReserved)","dext5_BeforeDeleteItem(nWebFile, strItemKey, strItemUrlOrPath, strReserved)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useViewOrOpenEvent&&createEvent(b.id,"d5_event_BeforeFileViewOrOpen(nWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strReserved)","dext5_BeforeFileViewOrOpen(nWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strReserved)"),
"1"==UPLOADTOP.G_CURRUPLOADER._config.useUploadingCancelEvent&&createEvent(b.id,"d5_event_UploadingCancel(strUploadedInfo, strReserved)","dext5_UploadingCancel(strUploadedInfo, strReserved)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useDownloadEvent&&createEvent(b.id,"d5_event_BeforeDownload(nWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strReserved)","dext5_BeforeFileDownload(nWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strReserved)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent&&
createEvent(b.id,"d5_event_AfterAddFile(nWebFile, strFileID, strFullPathOrUrl, strFileSize, strFileName)","dext5_AfterAddFile(nWebFile, strFileID, strFullPathOrUrl, strFileSize, strFileName)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&createEvent(b.id,"d5_event_AfterAddFileEndTime(sReserved)","dext5_AfterAddFileEndTime(sReserved)"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterDownloadEvent&&createEvent(b.id,"d5_event_AfterFileDownloaded(strDownloadPath, nViewOrSave)","dext5_AfterDownload(strDownloadPath, nViewOrSave)"),
"1"==UPLOADTOP.G_CURRUPLOADER._config.useFinishDownloadedEvent&&createEvent(b.id,"d5_event_FinishFileDownloaded(nFileCount, strReserved)","dext5_FinishFileDownloaded(nFileCount, strReserved)"),createEvent(b.id,"d5_event_CheckChanged(nAllItemChecked)","dext5_CheckChanged(nAllItemChecked)"),createEvent(b.id,"d5_event_OnClickListControl(nReserved)","dext5_OnMouseClickOnList(nReserved)")):(b.attachEvent("d5_event_OnFileChanged",dext5_OnFileChanged),b.attachEvent("d5_event_OnFileTransCompleted",dext5_OnFileTransCompleted),
b.attachEvent("d5_event_OnError",dext5_OnError),b.attachEvent("d5_event_BeforeStartFileTransfer",dext5_BeforeStartFileTransfer),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"thumbs"!=UPLOADTOP.G_CURRUPLOADER._config.views&&b.attachEvent("d5_event_OnSortArrowState",dext5_OnSortArrowState),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAddEvent&&b.attachEvent("d5_event_BeforeAddFile",dext5_BeforeAddFile),"1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEvent&&b.attachEvent("d5_event_BeforeDeleteItem",
dext5_BeforeDeleteItem),"1"==UPLOADTOP.G_CURRUPLOADER._config.useViewOrOpenEvent&&b.attachEvent("d5_event_BeforeFileViewOrOpen",dext5_BeforeFileViewOrOpen),"1"==UPLOADTOP.G_CURRUPLOADER._config.useUploadingCancelEvent&&b.attachEvent("d5_event_UploadingCancel",dext5_UploadingCancel),"1"==UPLOADTOP.G_CURRUPLOADER._config.useDownloadEvent&&b.attachEvent("d5_event_BeforeDownload",dext5_BeforeFileDownload),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEvent&&b.attachEvent("d5_event_AfterAddFile",dext5_AfterAddFile),
"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterAddEndTimeEvent&&b.attachEvent("d5_event_AfterAddFileEndTime",dext5_AfterAddFileEndTime),"1"==UPLOADTOP.G_CURRUPLOADER._config.useAfterDownloadEvent&&b.attachEvent("d5_event_AfterFileDownloaded",dext5_AfterDownload),"1"==UPLOADTOP.G_CURRUPLOADER._config.useFinishDownloadedEvent&&b.attachEvent("d5_event_FinishFileDownloaded",dext5_FinishFileDownloaded),b.attachEvent("d5_event_CheckChanged",dext5_CheckChanged),b.attachEvent("d5_event_OnClickListControl",
dext5_OnMouseClickOnList))}catch(a){}}function dext5_OnFileChanged(b,a,c){TOTALFILELISTSIZE=a;TOTALFILELISTNUM=b;displayTotalSizeAndNum(c);G_FolderCount=""==c?b:UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c)}
function dext5_OnFileTransCompleted(b,a){var c=UPLOADTOP.G_CURRUPLOADER;G_UploadedFileListObj=[];G_DeletedFileListObj=[];var e="";if(b)for(var f=b.split(G_vertical),g=f.length,k=0,n="",p=0;p<g;p++){var l=f[p].split(G_formfeed),q=l[0],r="",e=l[1],r=-1<e.indexOf("*")?e.split("*")[0]:-1<e.indexOf("|")?e.split("|")[0]:-1<e.indexOf("\b")?e.split("\b")[0]:l[1],u="",e=l[2],u=-1<e.indexOf("*")?e.split("*")[0]:-1<e.indexOf("|")?e.split("|")[0]:-1<e.indexOf("\b")?e.split("\b")[0]:l[2],w=l[3],y=l[4],t="",t=
l[6]&&void 0!=l[6]?l[6]:"";if(""!=t){var z=t.substring(0,t.indexOf("\\"));""!=n&&n==z&&k++;n=z}var z="",z=l[5]&&void 0!=l[5]?parseInt(l[5],10)+1+k:"",B="",B=G_LocalFileObjectLocalPath[l[4]]?G_LocalFileObjectMark[l[4]]:"",x=l="";-1<e.indexOf("|")?(e=e.split("|")[1],-1<e.indexOf("\b")?(l=e.split("\b")[0],x=e.split("\b")[1],c._config.groupId=x):(l=e,x=c._config.groupId)):-1<e.indexOf("\b")?(x=e.split("\b")[1],c._config.groupId=x):x=c._config.groupId;q={guid:y,originName:q,fileSize:w,uploadName:r,uploadPath:u,
logicalPath:t,order:z,status:"complete",mark:B,responseCustomValue:l,responseGroupId:x};G_UploadedFileListObj.push(q)}if(a)for(f=a.split(G_vertical),g=f.length,p=0;p<g;p++)l=f[p].split(G_formfeed),k=l[0],q=l[1],w=l[2],n="",n=isLargeFiles(l[2])||l[3]&&"1"==l[3]?"1":"0",q={uniqKey:k,originName:q,fileSize:w,largeFiles:n},G_DeletedFileListObj.push(q);try{c.isUploadComplete=!0,0==c.transferCompleteEvt&&("function"==typeof c._config.event.transferComplete?c._config.event.transferComplete(Dext5PL.strUploadID):
UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Complete(Dext5PL.strUploadID),c.transferCompleteEvt=!0)}catch(A){}G_UploadedFileListObj=[];G_DeletedFileListObj=[];"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&"0"==UPLOADTOP.G_CURRUPLOADER._config.completeEventResetUse&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(Dext5PL.strUploadID)}
function dext5_OnError(b,a){setTimeout(function(){try{if("0"==UPLOADTOP.G_CURRUPLOADER._config.useScriptEventControl)"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(Dext5PL.strUploadID);else{var c="",e=b.indexOf(G_formfeed),f=[];if(-1<e){var c=b.substring(0,e),g=b.substring(e+1,b.length);if(g)for(var k=g.split(G_vertical),n=k.length,e=0,g="",p=0;p<n;p++){var l=k[p].split(G_formfeed);if(null!=l&&""!=l){var q=l[0],r="";tempWebFileInfo=l[1];var r=-1<
tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[0]:l[1],u="";tempWebFileInfo=l[2];var u=-1<tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[0]:l[2],w=l[3],y=l[4],t="",t=l[6]&&void 0!=l[6]?l[6]:"";if(""!=t){var z=t.substring(0,t.indexOf("\\"));""!=g&&g==z&&e++;g=z}var B="",B=l[5]&&void 0!=l[5]?parseInt(l[5],10)+1+e:"",x="",x=G_LocalFileObjectLocalPath[l[4]]?G_LocalFileObjectMark[l[4]]:"",A="",A=-1<tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[1]:"";f.push({guid:y,originName:q,
fileSize:w,uploadName:r,uploadPath:u,logicalPath:t,order:B,status:"complete",mark:x,responseCustomValue:A})}}}else c=b;"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(Dext5PL.strUploadID);void 0==eval("Dext5Upload_Lang.error_info.error_code_"+c)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(Dext5PL.strUploadID,c,a,f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(Dext5PL.strUploadID,c,eval("Dext5Upload_Lang.error_info.error_code_"+c),f)}}catch(D){}},1)}
function dext5_BeforeStartFileTransfer(){var b=!0;try{b=UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnTransfer_Start(Dext5PL.strUploadID)}catch(a){b=!0}0==b?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("FAIL"):UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("OK")}
function dext5_OnMouseClickOnList(b){try{if(-1!=b&&UPLOADTOP.G_CURRUPLOADER){var a=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileInfoList(b).split(G_formfeed);b={};var c="";if(1==a[0])if(b.isWebFile="y",b.localPath="","1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam)RegExp("d00=(.*)&customValue","gi").exec(a[5]),c=RegExp.$1,c=Dext5Base64.makeDecryptReponseMessage(c),c=c.split(UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter)[3].split(UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter)[1];
else{var e=a[5].indexOf("&fileVirtualPath="),f=a[5].indexOf("&fileOrgName=");-1<a[5].indexOf("&fileVirtualPath=")&&(c=a[5].substring(e+17,f),c=decodeURIComponent(c))}else b.isWebFile="n",b.localPath=a[5];b.webPath=c;var g=a[3];9E18<=g&&(g="");b.fileSize=g;b.originalName=a[2];b.extension=a[8];b.order=parseInt(a[7],10)+1;b.uniqkey=a[9];var e="",k=a[5].indexOf("&customValue=");-1<k&&(e=a[5].substring(k+13));b.customValue=decodeURIComponent(e);UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_SelectItem(UPLOADTOP.G_CURRUPLOADER.ID,
b)}}catch(n){}}function dext5_getFileNameMessage(b){var a="";b&&0<b.length&&(a=b,b=b.split("\\"),0<b.length&&(a=b[b.length-1]));return a}
function dext5_BeforeAddFile(b,a,c,e,f){try{0==b&&(0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeAddItem(Dext5PL.strUploadID,f,UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e),UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileCount()+1,c)?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("FAIL"):UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("OK"))}catch(g){}}
function dext5_BeforeDeleteItem(b,a,c,e){try{var f=c.indexOf("&fileVirtualPath="),g=c.indexOf("&fileOrgName=");-1<c.indexOf("&fileVirtualPath=")&&(c=c.substring(f+17,g),c=decodeURIComponent(c));g=f="";if(-1<e.indexOf("^"))var k=e.split("^"),f=k[0],g=k[1];else f=e;0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeDeleteItem(Dext5PL.strUploadID,b+"",a,c,UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(f))?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("FAIL"):(UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("OK"),
"1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent&&""!=g&&G_DeleteItemCount++);if("1"==UPLOADTOP.G_CURRUPLOADER._config.useDeleteEndTimeEvent&&""!=g&&(0==G_SelectedItemCount&&(G_SelectedItemCount=getSelectedFileCount()),UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(g)==G_SelectedItemCount))try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_DeleteItemEndTime(Dext5PL.strUploadID,G_DeleteItemCount),G_SelectedItemCount=G_DeleteItemCount=0}catch(n){}}catch(p){}}
function dext5_BeforeFileViewOrOpen(b,a,c,e,f){try{var g=e.indexOf("&fileVirtualPath="),k=e.indexOf("&fileOrgName=");-1<e.indexOf("&fileVirtualPath=")&&(e=e.substring(g+17,k),e=decodeURIComponent(e));var n=UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileInfoList(f).split(G_formfeed)[3];9E18<=n&&(n="");f="0";isLargeFiles(n)&&(f="1");0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeFileViewOrOpen(Dext5PL.strUploadID,b+"",a,c,e,f)?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("FAIL"):UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("OK")}catch(p){}}
function dext5_BeforeFileDownload(b,a,c,e,f){try{var g=e.indexOf("&fileVirtualPath="),k=e.indexOf("&fileOrgName=");-1<e.indexOf("&fileVirtualPath=")&&(e=e.substring(g+17,k),e=decodeURIComponent(e));g="0";-1<f.indexOf("TRUE")&&(g="1");k="0";-1<f.indexOf("^")&&(k="1");0==UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_BeforeFileDownload(Dext5PL.strUploadID,b+"",a,c,e,g,k)?UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("FAIL"):UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.SetD5EventResult("OK")}catch(n){}}
function dext5_AfterAddFile(b,a,c,e,f){try{0==b&&UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItem(Dext5PL.strUploadID,f,UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(e),UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5PL.GetFileCount())}catch(g){}}function dext5_AfterAddFileEndTime(b){try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterAddItemEndTime(Dext5PL.strUploadID)}catch(a){}}function dext5_AfterDownload(b,a){setTimeout(function(){try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_AfterDownload(Dext5PL.strUploadID,b,a)}catch(c){}},1)}
function dext5_FinishFileDownloaded(b,a){setTimeout(function(){try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_FinishDownloaded(Dext5PL.strUploadID,b)}catch(a){}},1)}
function dext5_UploadingCancel(b,a){try{var c=[];if(b)for(var e=b.split(G_vertical),f=e.length,g=0,k="",n=0;n<f;n++){var p=e[n].split(G_formfeed);if(null!=p&&""!=p){var l=p[0],q="";tempWebFileInfo=p[1];var q=-1<tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[0]:p[1],r="";tempWebFileInfo=p[2];var r=-1<tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[0]:p[2],u=p[3],w=p[4],y="",y=p[6]&&void 0!=p[6]?p[6]:"";if(""!=y){var t=y.substring(0,y.indexOf("\\"));""!=k&&k==t&&g++;k=t}var z="",z=
p[5]&&void 0!=p[5]?parseInt(p[5],10)+1+g:"",B="",B=G_LocalFileObjectLocalPath[p[4]]?G_LocalFileObjectMark[p[4]]:"",x="",x=-1<tempWebFileInfo.indexOf("|")?tempWebFileInfo.split("|")[1]:"";c.push({guid:w,originName:l,fileSize:u,uploadName:q,uploadPath:r,logicalPath:y,order:z,status:"complete",mark:B,responseCustomValue:x})}}UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_UploadingCancel(Dext5PL.strUploadID,c);"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(Dext5PL.strUploadID)}catch(A){}}
function dext5_CheckChanged(b){var a=document.getElementById("chk_all_box");a&&(a.checked="0"==b?!1:!0)}
function dext5_OnSortArrowState(b,a){if("0"==b&&"0"==a)fileListSortIconReset();else{var c=document.getElementById("header_bar_file_name"),e=document.getElementById("header_bar_file_size"),f=c.getElementsByTagName("em")[0],g=e.getElementsByTagName("em")[0],c=c.getElementsByTagName("span")[0],e=e.getElementsByTagName("span")[0];if("0"==a){c.style.display="block";e.style.display="none";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(e=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,c=
0;c<e;c++){var k=document.getElementById("user_header_bar_"+c),k=k.getElementsByTagName("span")[0];k.style.display="none"}"1"==b?f.className="icon_up":"2"==b&&(f.className="icon_down")}else if("1"==a){c.style.display="none";e.style.display="block";if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(e=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,c=0;c<e;c++)k=document.getElementById("user_header_bar_"+c),k=k.getElementsByTagName("span")[0],k.style.display="none";"1"==b?g.className="icon_up":
"2"==b&&(g.className="icon_down")}else if("2"<=a){c.style.display="none";e.style.display="none";k=document.getElementById("user_header_bar_"+(parseInt(a,10)-2));f=k.getElementsByTagName("em")[0];k=k.getElementsByTagName("span")[0];k.style.display="block";e=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length;for(c=0;c<e;c++)parseInt(a,10)-2!=c&&(k=document.getElementById("user_header_bar_"+c),k=k.getElementsByTagName("span")[0],k.style.display="none");"1"==b?f.className="icon_up":"2"==b&&(f.className=
"icon_down")}}};function UploadFormSingle(){var b=getDialogDocument();0==UPLOADIDX&&(G_UploadStartTime=(new Date).getTime()/1E3);var a=(0<uploadForms.length?uploadForms:document.getElementById("files_container").getElementsByTagName("form"))[UPLOADIDX],c=document.charset;document.charset="utf-8";var e=UPLOADIDX.toString();e==(uploaders.length-1).toString()&&(e+="z");var f=a.getElementsByTagName("input")[0];if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var g="d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
"uploadHtml4Request"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,g=g+("d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.DEXT5UPLOAD.util.makeGuid()+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[uploaders[UPLOADIDX]].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+e+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),
g=g+("d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d36"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),g=g+("d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&(g+="d39"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&(g+="d15"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);var k=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize;"1"==
UPLOADTOP.G_CURRUPLOADER._config.html4LimitFileSize&&0!=k&&(g+="d48"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+k+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);var g=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(g),n=document.createElement("input");n.type="hidden";n.name="d00";n.value=g;a.insertBefore(n,f)}else{var p=document.createElement("input");p.type="hidden";p.name="dext5CMD";p.value="uploadHtml4Request";var l=document.createElement("input");l.type="hidden";l.name="fileName";
l.value=RESULTFILELIST[uploaders[UPLOADIDX]].originName;var q=document.createElement("input");q.type="hidden";q.name="GUID";RESULTFILELIST[uploaders[UPLOADIDX]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();q.value=RESULTFILELIST[uploaders[UPLOADIDX]].guid;var r=document.createElement("input");r.type="hidden";r.name="gpid";r.value=UPLOADTOP.G_CURRUPLOADER._config.groupId;var u=document.createElement("input");u.type="hidden";u.name="fidx";u.value=e;var w=document.createElement("input");w.type="hidden";
w.name="folderNameRule";w.value=UPLOADTOP.G_CURRUPLOADER._config.folderNameRule;var y=document.createElement("input");y.type="hidden";y.name="fileNameRule";y.value=UPLOADTOP.G_CURRUPLOADER._config.fileNameRule;var t=document.createElement("input");t.type="hidden";t.name="fileNameRuleEx";t.value=UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx;var z=document.createElement("input");z.type="hidden";z.name="allowedZeroFileSize";z.value=UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize;var B=document.createElement("input");
B.type="hidden";B.name="cfe";B.value=UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(",");if("1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4){var x=document.createElement("input");x.type="hidden";x.name="ds";x.value="1"}if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector){var A=document.createElement("input");A.type="hidden";A.name="fed";A.value="1"}k=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize;if("1"==
UPLOADTOP.G_CURRUPLOADER._config.html4LimitFileSize&&0!=k){var D=document.createElement("input");D.type="hidden";D.name="lofs";D.value=k}a.insertBefore(p,f);a.insertBefore(l,f);a.insertBefore(q,f);a.insertBefore(r,f);a.insertBefore(u,f);a.insertBefore(w,f);a.insertBefore(y,f);a.insertBefore(t,f);a.insertBefore(z,f);a.insertBefore(B,f);"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&a.insertBefore(x,f);"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&a.insertBefore(A,
f);"1"==UPLOADTOP.G_CURRUPLOADER._config.html4LimitFileSize&&0!=k&&a.insertBefore(D,f)}g=document.createElement("input");g.type="hidden";g.name="mark";g.value=RESULTFILELIST[uploaders[UPLOADIDX]].mark;a.insertBefore(g,f);for(var e=G_FormData.length,C=[],k=0;k<e;k++){var v=document.createElement("input");v.type="hidden";v.name=G_FormData[k].form_name;v.value=G_FormData[k].form_value;a.insertBefore(v,f);C.push(v)}a.setAttribute("target","download_frame");a.setAttribute("action",UPLOADTOP.G_CURRUPLOADER._config.handlerUrl);
b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=" "+RESULTFILELIST[uploaders[UPLOADIDX]].originName;a.submit();document.charset=c;"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?a.removeChild(n):(a.removeChild(p),a.removeChild(l),a.removeChild(q),a.removeChild(r),a.removeChild(u),a.removeChild(w),a.removeChild(y),a.removeChild(t),a.removeChild(z),a.removeChild(B),"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4&&a.removeChild(x),"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&
a.removeChild(A),k=UPLOADTOP.G_CURRUPLOADER._config.maxOneFileSize,"1"==UPLOADTOP.G_CURRUPLOADER._config.html4LimitFileSize&&0!=k&&a.removeChild(D));a.removeChild(g);for(k=e-1;0<=k;k--)a.removeChild(C[k]);k=UPLOADIDX;b=TOTALUPLOADNUM;"1"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4?uploadProgress_html4_2(k,b):uploadProgress_html4(k,b,50)}
function UploadSWFSingle(){var b=getDialogDocument();0==upload_complete_count&&(adjustUploadFileListScroll(0,upload_complete_count,TOTALUPLOADNUM),G_UploadStartTime=(new Date).getTime()/1E3);if("1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&"0"==RESULTFILELIST[uploaders[upload_complete_count]].fileSize){RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();var a=upload_complete_count.toString();a==(uploaders.length-1).toString()&&(a+="z");var b=UPLOADTOP.G_CURRUPLOADER._config,
c=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl;uploadProgress(0,1,{currentNumber:1,numberOfChunks:1,size:0},uploaders[upload_complete_count]);uploadZeroFileWithoutWorker(uploaders[upload_complete_count],RESULTFILELIST[uploaders[upload_complete_count]].originName,RESULTFILELIST[uploaders[upload_complete_count]].guid,RESULTFILELIST[uploaders[upload_complete_count]].mark,UPLOADTOP.G_CURRUPLOADER._config.groupId,a,c,!0,RESULTFILELIST[uploaders[upload_complete_count]].file,b)}else{c=SWFUpload.instances["SWFUpload_"+
UPLOADTOP.G_CURRUPLOADER.ID];G_PreUploadedSizeForOnprogress=PREVIOUSUPLOADEDSIZE;a=upload_complete_count.toString();a==(uploaders.length-1).toString()&&(a+="z");if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var e="d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"uploadHtml4SWFRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();e+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
RESULTFILELIST[uploaders[upload_complete_count]].guid+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[uploaders[upload_complete_count]].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;e+="d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&(e+="d15"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);e=
UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(e);c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"d00",e)}else c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"dext5CMD","uploadHtml4SWFRequest"),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"fileNameEx",RESULTFILELIST[uploaders[upload_complete_count]].originName),RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid(),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,
"GUID",RESULTFILELIST[uploaders[upload_complete_count]].guid),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"gpid",UPLOADTOP.G_CURRUPLOADER._config.groupId),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"fidx",a),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"folderNameRule",UPLOADTOP.G_CURRUPLOADER._config.folderNameRule),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"fileNameRule",UPLOADTOP.G_CURRUPLOADER._config.fileNameRule),
c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"fileNameRuleEx",UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx),c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"cfe",UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(",")),"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileExtensionDetector&&c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,"fed","1");c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,
"mark",RESULTFILELIST[uploaders[upload_complete_count]].mark);a=G_FormData.length;for(e=0;e<a;e++)c.addFileParam(RESULTFILELIST[uploaders[upload_complete_count]].file.id,G_FormData[e].form_name,G_FormData[e].form_value);0<c.getStats().files_queued&&(b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=" "+RESULTFILELIST[uploaders[upload_complete_count]].originName,c.startUpload(RESULTFILELIST[uploaders[upload_complete_count]].file.id))}}
function sliceAndUploadSingle(){var b=getDialogDocument(),a=UPLOADTOP.G_CURRUPLOADER._config;RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();if(null==fileworkers[0]||void 0==fileworkers[0]){0==upload_complete_count&&(G_UploadStartTime=(new Date).getTime()/1E3);var c="",c=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(c=(new Date).getTime());c=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+
"dext5upload.uploadchunk.min.js?ver="+c:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.uploadchunk.js?ver="+c;c=new Worker(c);c.onmessage=function(a){a=a.data;switch(a.type){case "complete":b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+a.id).innerHTML=Dext5Upload_Lang.upload_status.uploaded;RESULTFILELIST[a.id].uploadName=a.uploadFileName;RESULTFILELIST[a.id].uploadPath=a.uploadFilePath;RESULTFILELIST[a.id].status="complete";RESULTFILELIST[a.id].logicalPath="";RESULTFILELIST[a.id].responseCustomValue=
a.uploadCustomValue;RESULTFILELIST[a.id].originName=a.originalFileName;a.uploadGroupId&&""!=a.uploadGroupId&&(RESULTFILELIST[a.id].groupId=a.uploadGroupId,UPLOADTOP.G_CURRUPLOADER._config.groupId=a.uploadGroupId);""==RESULTFILELIST[a.id].groupId&&""!=UPLOADTOP.G_CURRUPLOADER._config.groupId&&(RESULTFILELIST[a.id].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId);upload_complete_count++;upload_complete_count==TOTALUPLOADNUM?totalUploadComplete():(a=uploaders.indexOf(a.id),adjustUploadFileListScroll(a,
upload_complete_count,TOTALUPLOADNUM),UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported?startAjaxOnprogressUpload():sliceAndUploadSingle());break;case "progress":b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+a.id).innerHTML=Dext5Upload_Lang.upload_status.uploading;uploadProgress(a.uploadedSize,a.uploadedChunks,a.chunkInfo,a.id);break;case "reupload":uploadworkers[0].postMessage({type:"upload",chunk:a.chunk,filename:a.filename,handlerurl:a.configValues.handlerUrl,chunkInfo:a.chunkInfo,asyncstate:a.asyncstate,
id:a.id,guid:a.guid,configValues:a.configValues,mark:a.mark,gpid:a.gpid,fidx:a.fidx,formDataEx:G_FormData});break;case "error":removeWorker();var c=getUploadedFileListObj();try{900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a.code)&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(a.code)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a.code,a.message,c):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a.code)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
"200004",Dext5Upload_Lang.error_info.error_code_200004,c):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a.code,eval("Dext5Upload_Lang.error_info.error_code_"+a.code),c)}catch(e){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel();break;case "log":dextWriteLog(a.message+"\r\n",!0)}};
c.onerror=function(a){removeWorker();a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200005",Dext5Upload_Lang.error_info.error_code_200005,a)}catch(b){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)};uploadworkers.push(c);c="";c=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&
(c=(new Date).getTime());c=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"dext5upload.processfile.min.js?ver="+c:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.processfile.js?ver="+c;c=new Worker(c);c.onmessage=function(c){c=c.data;switch(c.type){case "upload":var e={handlerUrl:a.handlerUrl,uploadChunkSize:a.chunkSize,folderNameRule:a.folderNameRule,fileNameRule:a.fileNameRule,fileNameRuleEx:a.fileNameRuleEx,encryptParam:a.security.encryptParam,integrity:a.security.fileIntegrity,
encryptFile:a.security.fileEncrypt,checkFileExtension:UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","),FDIData:c.FDIData,ServerReleaseVer:UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer};uploadworkers[0].postMessage({type:"upload",chunk:c.blob,filename:c.filename,handlerurl:a.handlerUrl,chunkInfo:c.chunkInfo,asyncstate:c.asyncstate,id:c.id,guid:c.guid,configValues:e,mark:c.mark,gpid:c.gpid,fidx:c.fidx,formDataEx:G_FormData});b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=
RESULTFILELIST[uploaders[upload_complete_count]].originName;break;case "uploadZeroFile":e={handlerUrl:a.handlerUrl,folderNameRule:a.folderNameRule,fileNameRule:a.fileNameRule,fileNameRuleEx:a.fileNameRuleEx,encryptParam:a.security.encryptParam,checkFileExtension:UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","),ServerReleaseVer:UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer};uploadworkers[0].postMessage({type:"uploadZeroFile",blob:c.blob,
filename:c.filename,handlerurl:a.handlerUrl,asyncstate:c.asyncstate,id:c.id,guid:c.guid,configValues:e,mark:c.mark,gpid:c.gpid,fidx:c.fidx,formDataEx:G_FormData});b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=RESULTFILELIST[uploaders[upload_complete_count]].originName;break;case "error":removeWorker();c=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200006",Dext5Upload_Lang.error_info.error_code_200006,c)}catch(k){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&
fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel();break;case "log":dextWriteLog(c.message+"\r\n",!0)}};c.onerror=function(a){removeWorker();a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200007",Dext5Upload_Lang.error_info.error_code_200007,a)}catch(b){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&
fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()};fileworkers.push(c)}var c={integrity:UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity,keyValue:UPLOADTOP.G_CURRUPLOADER._config.security.keyValue},e=upload_complete_count.toString();e==(uploaders.length-1).toString()&&(e+="z");fileworkers[0].postMessage({type:"start",guid:RESULTFILELIST[uploaders[upload_complete_count]].guid,
file:RESULTFILELIST[uploaders[upload_complete_count]].file,mark:RESULTFILELIST[uploaders[upload_complete_count]].mark,gpid:a.groupId,fidx:e,id:uploaders[upload_complete_count],asyncstate:a.asyncUpload,bytesperchunk:a.chunkSize,allowedzerofilesize:a.allowedZeroFileSize,securityInfo:c,originalname:RESULTFILELIST[uploaders[upload_complete_count]].originName})}
function sliceAndUploadMulti(){var b=getDialogDocument();G_UploadStartTime=(new Date).getTime()/1E3;for(var a=RESULTFILELIST.length,c=0;c<a;c++)if(RESULTFILELIST[c].file){var e="",f=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(f=(new Date).getTime());e=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"dext5upload.uploadchunk.min.js?ver="+f:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.uploadchunk.js?ver="+f;e=new Worker(e);
e.onmessage=function(a){a=a.data;switch(a.type){case "complete":b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+a.id).innerHTML=Dext5Upload_Lang.upload_status.uploaded;RESULTFILELIST[a.id].uploadName=a.uploadFileName;RESULTFILELIST[a.id].uploadPath=a.uploadFilePath;RESULTFILELIST[a.id].status="complete";RESULTFILELIST[a.id].logicalPath="";RESULTFILELIST[a.id].responseCustomValue=a.uploadCustomValue;RESULTFILELIST[a.id].originName=a.originalFileName;a.uploadGroupId&&""!=a.uploadGroupId&&(RESULTFILELIST[a.id].groupId=
a.uploadGroupId,UPLOADTOP.G_CURRUPLOADER._config.groupId=a.uploadGroupId);""==RESULTFILELIST[a.id].groupId&&""!=UPLOADTOP.G_CURRUPLOADER._config.groupId&&(RESULTFILELIST[a.id].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId);upload_complete_count++;upload_complete_count==TOTALUPLOADNUM&&totalUploadComplete();break;case "progress":b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+a.id).innerHTML=Dext5Upload_Lang.upload_status.uploading;uploadProgress(a.uploadedSize,a.uploadedChunks,a.chunkInfo,a.id);
break;case "reupload":uploadworkers[a.id].postMessage({type:"upload",chunk:a.chunk,filename:a.filename,handlerurl:a.configValues.handlerUrl,chunkInfo:a.chunkInfo,asyncstate:a.asyncstate,id:a.id,guid:a.guid,configValues:a.configValues,mark:a.mark,gpid:a.gpid,fidx:a.fidx,formDataEx:G_FormData});break;case "error":removeWorker();a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200008",Dext5Upload_Lang.error_info.error_code_200008,a)}catch(c){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&
fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};e.onerror=function(a){a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200009",Dext5Upload_Lang.error_info.error_code_200009,a)}catch(b){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&
UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)};uploadworkers[c]=e}for(e=c=0;e<a;e++)RESULTFILELIST[e].file&&c++;for(var g=0,e=0;e<a;e++)if(RESULTFILELIST[e].file){var k=RESULTFILELIST[e].file,f="",f=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(f=(new Date).getTime());f=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.js+"dext5upload.processfile.min.js?ver="+f:UPLOADTOP.G_CURRUPLOADER._config.webPath.jsDev+"dext5upload.processfile.js?ver="+
f;f=new Worker(f);f.onmessage=function(a){a=a.data;switch(a.type){case "upload":var b=UPLOADTOP.G_CURRUPLOADER._config,c={handlerUrl:b.handlerUrl,uploadChunkSize:b.chunkSize,folderNameRule:b.folderNameRule,fileNameRule:b.fileNameRule,fileNameRuleEx:b.fileNameRuleEx,ServerReleaseVer:UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer};uploadworkers[a.id].postMessage({type:"upload",chunk:a.blob,filename:a.filename,handlerurl:b.handlerUrl,chunkInfo:a.chunkInfo,asyncstate:a.asyncstate,id:a.id,guid:a.guid,configValues:c,
mark:a.mark,gpid:a.gpid,fidx:a.fidx,formDataEx:G_FormData});break;case "uploadZeroFile":b=UPLOADTOP.G_CURRUPLOADER._config;c={handlerUrl:b.handlerUrl,folderNameRule:b.folderNameRule,fileNameRule:b.fileNameRule,fileNameRuleEx:b.fileNameRuleEx,ServerReleaseVer:UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer};uploadworkers[a.id].postMessage({type:"uploadZeroFile",blob:a.blob,filename:a.filename,handlerurl:b.handlerUrl,asyncstate:a.asyncstate,id:a.id,guid:a.guid,configValues:c,mark:a.mark,gpid:a.gpid,fidx:a.fidx,
formDataEx:G_FormData});break;case "error":removeWorker();a=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200010",Dext5Upload_Lang.error_info.error_code_200010,a)}catch(e){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};f.onerror=function(a){a=getUploadedFileListObj();
try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200011",Dext5Upload_Lang.error_info.error_code_200011,a)}catch(b){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()};var n=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();RESULTFILELIST[e].guid=n;var p={integrity:UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity,
keyValue:UPLOADTOP.G_CURRUPLOADER._config.security.keyValue},l=g.toString();l==(c-1).toString()&&(l+="z");var q=UPLOADTOP.G_CURRUPLOADER._config;f.postMessage({type:"start",guid:n,file:k,id:e,mark:RESULTFILELIST[e].mark,gpid:q.groupId,fidx:l,asyncstate:q.asyncUpload,bytesperchunk:q.chunkSize,allowedzerofilesize:q.allowedZeroFileSize,securityInfo:p,originalname:RESULTFILELIST[e].originName});fileworkers.push(f);g++}}
function uploadWithoutWorker(){G_numberOfServerUploadedChunks=0;RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();var b=upload_complete_count.toString();b==(uploaders.length-1).toString()&&(b+="z");sliceAndUploadFileWithoutWorker(uploaders[upload_complete_count],RESULTFILELIST[uploaders[upload_complete_count]].guid,RESULTFILELIST[uploaders[upload_complete_count]].file,RESULTFILELIST[uploaders[upload_complete_count]].mark,UPLOADTOP.G_CURRUPLOADER._config.groupId,
b,!0,1)}
function sliceAndUploadFileWithoutWorker(b,a,c,e,f,g,k,n){var p=getDialogDocument();p.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=" "+RESULTFILELIST[uploaders[upload_complete_count]].originName;0==upload_complete_count&&1==n&&(G_UploadStartTime=(new Date).getTime()/1E3);var l=UPLOADTOP.G_CURRUPLOADER._config.chunkSize,q=c.size,r=Math.ceil(c.size/l),u=UPLOADTOP.G_CURRUPLOADER._config,w=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,y={currentNumber:n,currentSize:0,numberOfChunks:r,size:c.size,
fileBlob:c},t=RESULTFILELIST[b].originName;if("1"==UPLOADTOP.G_CURRUPLOADER._config.allowedZeroFileSize&&0==q)uploadProgress(0,1,{currentNumber:1,numberOfChunks:1,size:0},b),uploadZeroFileWithoutWorker(b,t,a,e,f,g,w,k,c,u);else{n=(n-1)*l;var z=n+l;z>q&&(z=q);var B;B=c.mozSlice?c.mozSlice(n,z):c.webkitSlice?c.webkitSlice(n,z):c.slice(n,z);y.currentSize=B.size;var x=XHRFactory.getInstance(),l=w,l=-1<l.indexOf("?")?l+"&dext=slice":l+"?dext=slice";x.open("POST",l,k);x.onreadystatechange=function(){if(4==
x.readyState)if(200==x.status){var c=x.responseText;XHRFactory.release(x);var l=c.split("|");1==l.length&&(c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),l=c.split("|"));if(0==c.indexOf("error")){var n=getUploadedFileListObj();900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(l[1])&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(l[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
l[1],l[2],n):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+l[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,n):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,l[1],eval("Dext5Upload_Lang.error_info.error_code_"+l[1]),n);"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);
closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{G_numberOfServerUploadedChunks++;try{p.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b).innerHTML=Dext5Upload_Lang.upload_status.uploading,uploadProgress(y.currentSize,G_numberOfServerUploadedChunks,y,b),G_numberOfServerUploadedChunks==y.numberOfChunks?(uploadMergeWithoutWorker(b,t,a,e,f,g,y,w,k,u),G_numberOfServerUploadedChunks=0):0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&sliceAndUploadFileWithoutWorker(b,a,y.fileBlob,e,f,g,k,G_numberOfServerUploadedChunks+
1)}catch(r){G_numberOfServerUploadedChunks=0}}}else if(400==x.status||413==x.status||415==x.status||500==x.status){n=getUploadedFileListObj();try{c=x.responseText;l=c.split("|");1==l.length&&(c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),l=c.split("|"));var q=errMessage="";2==l.length?(q=l[0],errMessage=l[1]):3==l.length&&(q=l[1],errMessage=l[2]);900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(q)&&
999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(q)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,q,errMessage,n):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+q)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,n):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,q,eval("Dext5Upload_Lang.error_info.error_code_"+q),n)}catch(z){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();
"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel();XHRFactory.release(x)}};try{var A=function(){var b=new FileReader;b.onloadend=function(b){b=b.target.result.match(/,(.*)$/)[1];var e="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var f;f=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"uploadRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);
f+="d18"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+y.currentNumber+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+t+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f+="d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+
"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f+="d37"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.chunkSize+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f+="d47"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+q+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;f=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(f);e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="d00"\r\n';
e+="\r\n";e+=f}else e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="dext5CMD"',e+="\r\n",e+="\r\n",e+="uploadRequest",e+="\r\n",e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="chunkNumber"',e+="\r\n",e+="\r\n",e+=y.currentNumber,e+="\r\n",e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="GUID"',e+="\r\n",e+="\r\n",e+=a,e+="\r\n",e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="fileName"',e+="\r\n",e+="\r\n",e+=t,e+="\r\n",
e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="cfe"',e+="\r\n",e+="\r\n",e+=UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","),e+="\r\n",e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="cs"',e+="\r\n",e+="\r\n",e+=UPLOADTOP.G_CURRUPLOADER._config.chunkSize,e+="\r\n",e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="fs"',e+="\r\n",e+="\r\n",e+=q;e+="\r\n";e+='------12345678wertysdfg\r\nContent-Disposition: form-data; name="Slice"; filename="blob"';
e+="\r\n";e+="Content-Type: application/octet-stream; charset=UTF-8";e+="\r\n";e+="\r\n";e+=b+"\r\n";e+="------12345678wertysdfg--\r\n";x.setRequestHeader("Content-Type","multipart/form-data; boundary=----12345678wertysdfg");x.setRequestHeader("Content-Length",e.length);x.setRequestHeader("Dext5-Encoded","base64");try{0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&x.send(e)}catch(g){XHRFactory.release(x)}B=c=null};b.readAsDataURL(B)};if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity){var D=
new FileReader;D.onloadend=function(a){a=a.target.result;var b=!1;z==q&&(b=!0);G_FDIData=D5FileDataIntegrity(a,UPLOADTOP.G_CURRUPLOADER._config.security.keyValue,b).toString();A()};D.readAsArrayBuffer(B)}else A()}catch(C){D=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,D)}catch(v){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&
UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)}}}
function uploadMergeWithoutWorker(b,a,c,e,f,g,k,n,p,l){var q=getDialogDocument(),r=XHRFactory.getInstance();r.open("POST",n,p);r.onreadystatechange=function(c){if(4==r.readyState)if(200==r.status)try{var e=r.responseText;XHRFactory.release(r);var f=e.split("|");1==f.length&&(e=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(e):Dext5Base64.makeDecryptReponseMessage(e),f=e.split("|"));if(0==e.indexOf("error")){var g=getUploadedFileListObj();
900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(f[1])&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(f[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,f[1],f[2],g):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+f[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,g):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,f[1],eval("Dext5Upload_Lang.error_info.error_code_"+f[1]),g);"0"==
UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{var k,l,n=f[0],p=n.indexOf("::");-1<p?(k=n.substring(0,p),l=n.substring(p+2)):(k=a,l=f[0]);q.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b).innerHTML=Dext5Upload_Lang.upload_status.uploaded;RESULTFILELIST[b].uploadPath=l;RESULTFILELIST[b].status="complete";
RESULTFILELIST[b].logicalPath="";RESULTFILELIST[b].originName=k;2==f.length?-1<f[1].indexOf("\b")?(RESULTFILELIST[b].uploadName=f[1].split("\b")[0],RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=f[1].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[b].groupId):(RESULTFILELIST[b].uploadName=f[1],RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId):3==f.length&&(-1<f[2].indexOf("\b")?(RESULTFILELIST[b].uploadName=
f[1],RESULTFILELIST[b].responseCustomValue=f[2].split("\b")[0],RESULTFILELIST[b].groupId=f[2].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[b].groupId):(RESULTFILELIST[b].uploadName=f[1],RESULTFILELIST[b].responseCustomValue=f[2],RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId));upload_complete_count++;if(upload_complete_count==TOTALUPLOADNUM)totalUploadComplete();else{var u=uploaders.indexOf(b);adjustUploadFileListScroll(u,upload_complete_count,TOTALUPLOADNUM);
UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported?startAjaxOnprogressUpload():uploadWithoutWorker()}}}catch(w){}else if(400==r.status||413==r.status||415==r.status||500==r.status){g=getUploadedFileListObj();try{e=r.responseText;f=e.split("|");1==f.length&&(e=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(e):Dext5Base64.makeDecryptReponseMessage(e),f=e.split("|"));var E=errMessage="";2==f.length?(E=f[0],errMessage=f[1]):
3==f.length&&(E=f[1],errMessage=f[2]);900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(E)&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(E)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,E,errMessage,g):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+E)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,g):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,E,eval("Dext5Upload_Lang.error_info.error_code_"+
E),g)}catch(F){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(r);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(n=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"mergeRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),n+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d19"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+k.numberOfChunks+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+f+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,n=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(n),
c="d00="+n,"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity&&(n+="&fdi="+G_FDIData,G_FDIData="")):(c="dext5CMD=mergeRequest&fileName="+encodeURIComponent(a)+"&GUID="+c+"&numberOfChunks="+k.numberOfChunks+"&gpid="+f+"&fidx="+g,c+="&folderNameRule="+l.folderNameRule+"&fileNameRule="+l.fileNameRule+"&fileNameRuleEx="+l.fileNameRuleEx,"1"==UPLOADTOP.G_CURRUPLOADER._config.security.fileIntegrity&&(c+="&fdi="+G_FDIData,G_FDIData=""));c+="&mark="+e;e=G_FormData.length;for(l=0;l<e;l++)c+="&"+
G_FormData[l].form_name+"="+encodeURIComponent(G_FormData[l].form_value);r.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");try{0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&r.send(c)}catch(u){XHRFactory.release(r);e=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,e)}catch(w){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&
UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)}}
function uploadZeroFileWithoutWorker(b,a,c,e,f,g,k,n,p,l){var q=getDialogDocument(),r=XHRFactory.getInstance();r.open("POST",k,n);r.onreadystatechange=function(c){if(4==r.readyState)if(200==r.status)try{var e=r.responseText,f=e.split("|");1==f.length&&(e=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(e):Dext5Base64.makeDecryptReponseMessage(e),f=e.split("|"));if(0==e.indexOf("error")){var g=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
f[1],f[2],g)}catch(k){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(r);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{var l,n,p=f[0],u=p.indexOf("::");-1<u?(l=p.substring(0,u),n=p.substring(u+2)):(l=a,n=f[0]);q.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+b).innerHTML=Dext5Upload_Lang.upload_status.uploaded;RESULTFILELIST[b].uploadPath=
n;RESULTFILELIST[b].status="complete";RESULTFILELIST[b].logicalPath="";RESULTFILELIST[b].originName=l;2==f.length?-1<f[1].indexOf("\b")?(RESULTFILELIST[b].uploadName=f[1].split("\b")[0],RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=f[1].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[b].groupId):(RESULTFILELIST[b].uploadName=f[1],RESULTFILELIST[b].responseCustomValue="",RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId):3==f.length&&(-1<
f[2].indexOf("\b")?(RESULTFILELIST[b].uploadName=f[1],RESULTFILELIST[b].responseCustomValue=f[2].split("\b")[0],RESULTFILELIST[b].groupId=f[2].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[b].groupId):(RESULTFILELIST[b].uploadName=f[1],RESULTFILELIST[b].responseCustomValue=f[2],RESULTFILELIST[b].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId));upload_complete_count++;if(upload_complete_count==TOTALUPLOADNUM)"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==
UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&(G_PreUploadedSizeForOnprogress=0),totalUploadComplete();else{var w=uploaders.indexOf(b);adjustUploadFileListScroll(w,upload_complete_count,TOTALUPLOADNUM);"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"0"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4?UploadSWFSingle():uploadWithoutWorker()}XHRFactory.release(r)}}catch(E){}else if(400==r.status||413==r.status||415==r.status||500==r.status){g=getUploadedFileListObj();try{e=r.responseText;
f=e.split("|");1==f.length&&(e=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(e):Dext5Base64.makeDecryptReponseMessage(e),f=e.split("|"));var F=errMessage="";2==f.length?(F=f[0],errMessage=f[1]):3==f.length&&(F=f[1],errMessage=f[2]);900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(F)&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(F)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,F,errMessage,g):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+
F)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,g):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,F,eval("Dext5Upload_Lang.error_info.error_code_"+F),g)}catch(G){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(r);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};
"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam?(k="d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"uzr"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+c+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
f+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
l.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k+="d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(l.extension.allowOrLimit+"|"+l.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,k=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(k),c="d00="+k):(c="dext5CMD=uzr&fileName="+encodeURIComponent(a)+"&GUID="+c+"&mark="+e+"&gpid="+f+"&fidx="+g,c+="&folderNameRule="+l.folderNameRule+"&fileNameRule="+l.fileNameRule+"&fileNameRuleEx="+l.fileNameRuleEx,
c+="&cfe="+l.extension.allowOrLimit+"|"+l.extension.extArr.join(","));c+="&mark="+e;e=G_FormData.length;for(l=0;l<e;l++)c+="&"+G_FormData[l].form_name+"="+encodeURIComponent(G_FormData[l].form_value);r.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");try{0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&r.send(c)}catch(u){XHRFactory.release(r);e=getUploadedFileListObj();try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,
e)}catch(w){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID)}}
function startAjaxOnprogressUpload(){var b=RESULTFILELIST[uploaders[upload_complete_count]].fileSize,a=UPLOADTOP.G_CURRUPLOADER._config.sizeForChunkUpload;0!=a&&b>=a||0==b?UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported?sliceAndUploadSingle():(b=upload_complete_count.toString(),b==(uploaders.length-1).toString()&&(b+="z"),G_numberOfServerUploadedChunks=0,RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid(),sliceAndUploadFileWithoutWorker(uploaders[upload_complete_count],
RESULTFILELIST[uploaders[upload_complete_count]].guid,RESULTFILELIST[uploaders[upload_complete_count]].file,RESULTFILELIST[uploaders[upload_complete_count]].mark,UPLOADTOP.G_CURRUPLOADER._config.groupId,b,!0,1)):(G_PreUploadedSizeForOnprogress=PREVIOUSUPLOADEDSIZE,uploadAjaxOnprogress())}
function uploadAjaxOnprogress(){var b=getDialogDocument();0==upload_complete_count&&(adjustUploadFileListScroll(0,upload_complete_count,TOTALUPLOADNUM),G_UploadStartTime=(new Date).getTime()/1E3);RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();UPLOADTOP.G_CURRUPLOADER.xhr_onprogress=new XMLHttpRequest;var a=UPLOADTOP.G_CURRUPLOADER.xhr_onprogress;a.upload.onprogress=function(c){try{b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count]).innerHTML=
Dext5Upload_Lang.upload_status.uploading;var e=RESULTFILELIST[uploaders[upload_complete_count]].fileSize,f=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(c.loaded/c.total*e);uploadProgress_ajax(f,e)}catch(p){a=null}};a.onload=function(){if(a)if(200==a.status){upload_complete_count++;var c=a.responseText,e=c.split("|");1==e.length&&(c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|"));
if(0==c.indexOf("error")){var f=getUploadedFileListObj();try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+e[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],e[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],eval("Dext5Upload_Lang.error_info.error_code_"+e[1]),f)}catch(p){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);
a=null;closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{var l;try{var q=e[1],r=q.indexOf("::");-1<r?(f=q.substring(0,r),l=q.substring(r+2)):(f=RESULTFILELIST[uploaders[upload_complete_count-1]].originName,l=e[1]);RESULTFILELIST[uploaders[upload_complete_count-1]].uploadName=e[2];RESULTFILELIST[uploaders[upload_complete_count-1]].uploadPath=l;RESULTFILELIST[uploaders[upload_complete_count-1]].status="complete";RESULTFILELIST[uploaders[upload_complete_count-1]].logicalPath="";RESULTFILELIST[uploaders[upload_complete_count-
1]].originName=f;b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count-1]).innerHTML=Dext5Upload_Lang.upload_status.uploaded;4==e.length?-1<e[3].indexOf("\b")?(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue="",RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=e[3].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[uploaders[upload_complete_count-1]].groupId):(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue=
"",RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId):5==e.length&&(-1<e[4].indexOf("\b")?(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue=e[4].split("\b")[0],RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=e[4].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[uploaders[upload_complete_count-1]].groupId):(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue=e[4],RESULTFILELIST[uploaders[upload_complete_count-
1]].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId));if(upload_complete_count==TOTALUPLOADNUM)G_PreUploadedSizeForOnprogress=0,a=null,totalUploadComplete();else{try{var u=uploaders.indexOf(upload_complete_count-1);adjustUploadFileListScroll(u,upload_complete_count,TOTALUPLOADNUM)}catch(w){}a=null;startAjaxOnprogressUpload()}}catch(y){a=null}}}else{if(400==a.status||413==a.status||415==a.status||500==a.status){f=getUploadedFileListObj();try{c=a.responseText;e=c.split("|");1==e.length&&(c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||
"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|"));var t=errMessage="";2==e.length?(t=e[0],errMessage=e[1]):3==e.length&&(t=e[1],errMessage=e[2]);900<=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(t)&&999>=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(t)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,t,errMessage,f):void 0==eval("Dext5Upload_Lang.error_info.error_code_"+t)?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
"200004",Dext5Upload_Lang.error_info.error_code_200004,f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,t,eval("Dext5Upload_Lang.error_info.error_code_"+t),f)}catch(z){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}a=null}};a.onerror=function(){a=null;var b=getUploadedFileListObj();
try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,b)}catch(c){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()};var c=new FormData,e=upload_complete_count.toString();e==(uploaders.length-1).toString()&&(e+="z");if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){var f=
"d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"uploadPRequest"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,f=f+("d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[uploaders[upload_complete_count]].guid+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+RESULTFILELIST[uploaders[upload_complete_count]].originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),
f=f+("d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+e+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER._config.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),f=f+("d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),
f=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(f);c.append("d00",f)}else c.append("dext5CMD","uploadPRequest"),c.append("fileName",RESULTFILELIST[uploaders[upload_complete_count]].originName),c.append("GUID",RESULTFILELIST[uploaders[upload_complete_count]].guid),c.append("gpid",UPLOADTOP.G_CURRUPLOADER._config.groupId),c.append("fidx",e),c.append("folderNameRule",UPLOADTOP.G_CURRUPLOADER._config.folderNameRule),c.append("fileNameRule",UPLOADTOP.G_CURRUPLOADER._config.fileNameRule),c.append("fileNameRuleEx",
UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx),c.append("cfe",UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","));c.append("mark",RESULTFILELIST[uploaders[upload_complete_count]].mark);e=G_FormData.length;for(f=0;f<e;f++)c.append(G_FormData[f].form_name,G_FormData[f].form_value);c.append("fileToUpload",RESULTFILELIST[uploaders[upload_complete_count]].file);a.open("POST",UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,!0);0==UPLOADTOP.G_CURRUPLOADER._config.uploadCancel&&
(b.getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=" "+RESULTFILELIST[uploaders[upload_complete_count]].originName,a.send(c))}
function soketUploadStart(){0==upload_complete_count&&(G_UploadStartTime=(new Date).getTime()/1E3);RESULTFILELIST[uploaders[upload_complete_count]].guid=UPLOADTOP.DEXT5UPLOAD.util.makeGuid();getDialogDocument().getElementById("DEXT_fiVe_UP_current_upload_file_name").innerHTML=" "+RESULTFILELIST[uploaders[upload_complete_count]].originName;UPLOADTOP.DEXT5UPLOAD.browser.WorkerSupported?socketUpload(uploaders[upload_complete_count],RESULTFILELIST[uploaders[upload_complete_count]].guid,RESULTFILELIST[uploaders[upload_complete_count]].file,
!0,1):socketUploadWithoutWorker(uploaders[upload_complete_count],RESULTFILELIST[uploaders[upload_complete_count]].guid,RESULTFILELIST[uploaders[upload_complete_count]].file,!0,1)}
function socketUploadWithoutWorker(b,a,c,e,f){var g=UPLOADTOP.G_CURRUPLOADER._config.wsChunkSize;e=Math.ceil(c.size/g);var k={currentNumber:f,currentSize:0,numberOfChunks:e,size:c.size,fileBlob:c},n=RESULTFILELIST[b].file.name,p=XHRFactory.getInstance();p.open("POST",UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,!1);c="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam)c=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"h5pbe"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),
c+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+n+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+a+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d19"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+k.numberOfChunks+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.folderNameRule+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+
"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+l+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,c=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(c),c="d00="+c;else{c="dext5CMD=h5pbe&fileName="+
encodeURIComponent(n)+"&GUID="+a+"&numberOfChunks="+k.numberOfChunks;c+="&folderNameRule="+UPLOADTOP.G_CURRUPLOADER._config.folderNameRule;c+="&fileNameRule="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRule+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx;c+="&cfe="+UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(",");var l=upload_complete_count.toString();l==(uploaders.length-1).toString()&&(l+="z");c+="&gpid="+UPLOADTOP.G_CURRUPLOADER._config.groupId;
c+="&fidx="+l}c+="&mark="+RESULTFILELIST[b].mark;a=G_FormData.length;for(l=0;l<a;l++)c+="&"+G_FormData[l].form_name+"="+encodeURIComponent(G_FormData[l].form_value);p.onreadystatechange=function(a){if(4==p.readyState)if(200==p.status)try{var c=p.responseText,c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|");if("error"==e[0]){var f=getUploadedFileListObj();try{void 0==
eval("Dext5Upload_Lang.error_info.error_code_"+e[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],e[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],eval("Dext5Upload_Lang.error_info.error_code_"+e[1]),f)}catch(l){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(p);closeSendDialog();
UPLOADTOP.G_CURRUPLOADER.cancel()}else{var t={tempPath:e[0],whiteList:e[1],blackList:e[2],blackWordList:e[3],newFileLocation:e[4],responseFileServerPath:e[5],responseFileName:e[6],groupId:e[7],fileIdx:e[8]};null==G_curr_socket&&(G_curr_socket=new socketTransfer(n,k,g,b,t));G_socketArr.push(G_curr_socket);G_curr_socket.start();XHRFactory.release(p)}}catch(z){}else{f=getUploadedFileListObj();try{c=p.responseText,c=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?
Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),e=c.split("|")}catch(B){UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,f)}try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+e[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],e[2],f):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,e[1],eval("Dext5Upload_Lang.error_info.error_code_"+e[1]),f)}catch(x){}"0"==
UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(p);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};p.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");p.send(c)}
function socketUpload(b,a,c,e,f){var g=UPLOADTOP.G_CURRUPLOADER._config,k=RESULTFILELIST[uploaders[upload_complete_count]],n=XHRFactory.getInstance();n.open("POST",g.handlerUrl,!1);a="";if("1"==g.security.encryptParam)a=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"h5pbe"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter),a+="d08"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+k.originName+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d07"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
k.guid+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d11"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g.folderNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d09"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g.fileNameRule+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d10"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+g.fileNameRuleEx+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d35"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
(UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(","))+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d12"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER._config.groupId+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a+="d38"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+p+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter,a=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(a),
a="d00="+a;else{a="dext5CMD=h5pbe&fileName="+encodeURIComponent(k.originName)+"&GUID="+k.guid;a+="&folderNameRule="+g.folderNameRule+"&fileNameRule="+g.fileNameRule+"&fileNameRuleEx="+g.fileNameRuleEx;a+="&cfe="+UPLOADTOP.G_CURRUPLOADER._config.extension.allowOrLimit+"|"+UPLOADTOP.G_CURRUPLOADER._config.extension.extArr.join(",");var p=upload_complete_count.toString();p==(uploaders.length-1).toString()&&(p+="z");a+="&gpid="+UPLOADTOP.G_CURRUPLOADER._config.groupId;a+="&fidx="+p}a+="&mark="+RESULTFILELIST[b].mark;
b=G_FormData.length;for(p=0;p<b;p++)a+="&"+G_FormData[p].form_name+"="+encodeURIComponent(G_FormData[p].form_value);n.onreadystatechange=function(a){if(4==n.readyState)if(200==n.status)try{var b=n.responseText,b=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),c=b.split("|");if("error"==c[0]){var e=getUploadedFileListObj();try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+
c[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,c[1],c[2],e):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,c[1],eval("Dext5Upload_Lang.error_info.error_code_"+c[1]),e)}catch(f){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(n);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{var p=
{tempPath:c[0],whiteList:c[1],blackList:c[2],blackWordList:c[3],newFileLocation:c[4],responseFileServerPath:c[5],responseFileName:c[6],groupId:c[7],fileIdx:c[8]},t={length:g.socketWorkerCount,socketWorkerJobSize:g.socketWorkerJobSize,totalWorkerLength:1};"1"==g.allowedZeroFileSize&&0==k.fileSize?(t.totalWorkerLength=1,t.socketWorkerJobSize=0):0==g.socketWorkerJobSize?k.fileSize>g.minSingleSocketWorkerSize?(t.totalWorkerLength=g.socketWorkerCount,t.socketWorkerJobSize=0==k.fileSize%t.totalWorkerLength?
k.fileSize/t.totalWorkerLength:UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(k.fileSize/t.totalWorkerLength)+1):(t.totalWorkerLength=1,t.socketWorkerJobSize=k.fileSize):t.totalWorkerLength=Math.ceil(k.fileSize/t.socketWorkerJobSize);for(a=0;a<t.totalWorkerLength;a++)G_SocketUploadWorkArray.push(upload_complete_count+"|"+(a+1)+"|"+t.totalWorkerLength);var z=t.totalWorkerLength>t.length?t.length:t.totalWorkerLength;if(0==upload_complete_count)for(a=0;a<z;a++)G_SocketWorkerPool.push(createSocketWorker());else if(G_SocketWorkerPool.length<
z){var B=z-G_SocketWorkerPool.length;for(a=0;a<B;a++)G_SocketWorkerPool.push(createSocketWorker())}for(var x=G_SocketWorkerPool.length,B=0;B<x;B++)setSocketEvent(G_SocketWorkerPool[B],p,t);for(x=0;x<z;x++){var A=G_SocketWorkerPool[x],D=G_SocketUploadWorkArray.pop();A.postMessage({type:"start",guid:RESULTFILELIST[uploaders[upload_complete_count]].guid,file:RESULTFILELIST[uploaders[upload_complete_count]].file,id:uploaders[upload_complete_count],asyncstate:g.asyncUpload,bytesperchunk:g.wsChunkSize,
configValue:g,formdataex:G_FormData,beforeEventValue:p,currWorkInfo:D,socketWorkerJobSize:t.socketWorkerJobSize,workerIdx:x})}}XHRFactory.release(n)}catch(C){}else{e=getUploadedFileListObj();try{b=n.responseText,b=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),c=b.split("|")}catch(v){UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200004",Dext5Upload_Lang.error_info.error_code_200004,
e)}try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+c[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,c[1],c[2],e):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,c[1],eval("Dext5Upload_Lang.error_info.error_code_"+c[1]),e)}catch(H){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);XHRFactory.release(n);
closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}};n.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");n.send(a)}
function socketTransfer(b,a,c,e,f){this.file=a.fileBlob;this.fileName=b;this.fileExt=getExtension(b);this.guid=RESULTFILELIST[e].guid;this.blockSize=c;this.chunkInfo=a;this.reader=this.socket=null;this.curIndex=0;this.lastBlock=!1;this.type="binary";this.idx=e;this.beforeEventValue=f;this.wsHandlerUrl=UPLOADTOP.G_CURRUPLOADER._config.wsHandlerUrl;this.isZeroFile=0==this.file.size?"1":"0";var g=this;this.start=function(){g.socket=g.createSocket(this.wsHandlerUrl);"binary"==g.type&&(g.socket.binaryType=
"blob");g.socket.onopen=function(a){g.onOpen(a)};g.socket.onmessage=function(a){g.onMessage(a)};g.socket.onerror=function(a){g.onError(a)};g.socket.onclose=function(a){g.onClose(a)}};this.createSocket=function(a){if("WebSocket"in window)return new WebSocket(a);if("MozWebSocket"in window)return new MozWebSocket(a)};this.onOpen=function(a){g.initializeUpload()};this.initializeUpload=function(){var a=g.beforeEventValue.tempPath+"|"+g.beforeEventValue.blackList+"|"+g.beforeEventValue.whiteList+"|"+g.beforeEventValue.blackWordList+
"|",a=a+(g.beforeEventValue.newFileLocation+"|"+g.beforeEventValue.responseFileServerPath+"|"+g.beforeEventValue.responseFileName);""!=g.beforeEventValue.responseCustomValue&&(a+="|"+g.beforeEventValue.responseCustomValue);g.socket.send("DEXTINIT "+JSON.encode({filename:g.file.name,fileext:g.fileExt,size:g.file.size,type:"binary",guid:g.guid,beforeEventInfo:a,parameters:[],currentJobNumber:"1",isZeroFile:g.isZeroFile}))};this.onMessage=function(a){response=JSON.decode(a.data);if(200!=response.code)return a=
{message:response.message,code:response.code},g.onError(a),!1;if("AUTH"==response.type)g.initializeUpload();else if("DEXTINIT"==response.type)g.readSlice(0,g.blockSize);else if("DEXTINIT_0"==response.type)uploadProgress(0,g.chunkInfo.currentNumber,g.chunkInfo,upload_complete_count,g.blockSize),a={numberOfChunks:1},socketUploadMergeAndAfterEvent(g.idx,g.fileName,g.guid,a,UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,!0,g.beforeEventValue);else if("DATA"==response.type||"DEXTINIT_0"==response.type)a=
g.lastBlock,uploadProgress(response.bytesRead,g.chunkInfo.currentNumber,g.chunkInfo,upload_complete_count,g.blockSize),g.chunkInfo.currentNumber++,a?(a={numberOfChunks:1},socketUploadMergeAndAfterEvent(g.idx,g.fileName,g.guid,a,UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,!0,g.beforeEventValue)):g.readSlice(g.curIndex+g.blockSize,g.blockSize);else return a={message:""!=response.message?response.message:"socket error",code:response.code?response.code:0},g.onError(a),!1};this.readSlice=function(a,b){g.curIndex=
a;var c=Math.min(a+b-1,g.file.size-1);g.lastBlock=c==g.file.size-1;c=g.file["mozSlice"in g.file?"mozSlice":"webkitSlice"in g.file?"webkitSlice":"slice"](a,a+(c-a+1));if("binary"==g.type)return g.sendSlice(c),!0;g.reader=new FileReader;g.reader.onabort=function(){};g.reader.onerror=function(a){};g.reader.onloadend=function(a){g.sendB64Slice(a.target.result)};g.reader.readAsBinaryString(c)};this.onError=function(a){try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200013",Dext5Upload_Lang.error_info.error_code_200013)}catch(b){}try{g.socket.close()}catch(c){}closeSendDialog();
UPLOADTOP.G_CURRUPLOADER.cancel()};this.onClose=function(a){try{g.socket.close()}catch(b){}};this.sendSlice=function(a){g.socket.send(a)}};function _addUploadedFile(b,a,c,e,f,g){if(void 0==b||null==b)b="";g=null;void 0==a||null==a?a="":"object"===typeof a&&(g=a,a=a.FileName,g.FileNameFormat="",1==g.Bold&&(g.FileNameFormat="B"),1==g.Italic&&(g.FileNameFormat+="I"),1==g.Strike&&(g.FileNameFormat+="S"),1==g.UnderLine&&(g.FileNameFormat+="U"),g.FileNameColor||(g.FileNameColor=""));if(void 0==c||null==c)c="";if(void 0==e||null==e)e="";if(void 0==f||null==f)f="";e=e.toString();e=e.replace(/,/g,"");var k=UPLOADTOP.G_CURRUPLOADER.frameWin;if(k&&
0!=k.isExecuteApi()){if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var n=c;"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload?(n=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(n=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl),c="&fileVirtualPath="+encodeURIComponent(c)+"&fileOrgName="+encodeURIComponent(a),f&&""!=f&&(c=c+"&customValue="+encodeURIComponent(f)),n=0>n.indexOf("?")?n+"?dext5CMD=downloadRequest&resumeMode="+
UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+c:n+"&dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+c):"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(k.Dext5PL.nPe=0);try{e=isNaN(Number(e))||0>Number(e)||""==e?9E18:parseInt(e,10),g&&(a=g.FileName+"|"+g.FileNameColor+"|"+g.FileNameFormat),k.Dext5PL.AddUploadedFile(b,
a,n,e),"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(k.Dext5PL.nPe=1)}catch(p){}}else a=a.split("\\"),a=a[a.length-1],e=isNaN(Number(e))||0>Number(e)||""==e?"":parseInt(e,10),b={file:null,guid:"",fileIdx:b,originName:a,originNameColor:"",originNameFormat:"",webPath:c,fileSize:e,uploadName:"",fileExt:"",isDelete:"n",isWebFile:"y",customValue:f,localPath:"",mark:""},g&&(b.originNameColor=g.FileNameColor,b.originNameFormat=g.FileNameFormat),
k.RESULTFILELIST.push(b),k.addFileList(b,!0),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc)),k.calcTotalSize(),
k.displayTotalSizeAndNum(),k.setFileListBorder(),k.setListvalue(),k.setLargeFileAllList();k.setTabOrder();k.fileListSortIconReset()}}
function _addUploadedFileEx(b,a,c,e,f,g,k){if(void 0==b||null==b)b="";k=null;void 0==a||null==a?a="":"object"===typeof a&&(k=a,a=a.FileName,k.FileNameFormat="",1==k.Bold&&(k.FileNameFormat="B"),1==k.Italic&&(k.FileNameFormat+="I"),1==k.Strike&&(k.FileNameFormat+="S"),1==k.UnderLine&&(k.FileNameFormat+="U"),k.FileNameColor||(k.FileNameColor=""));if(void 0==c||null==c)c="";if(void 0==e||null==e)e="";if(void 0==f||null==f)f="";if(void 0==g||null==g)g="";var n=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length;
if(""==g)for(var p=1;p<n;p++)g+="|";else{var l=g.split("|"),q=l.length;if(q<n)for(p=0;p<n-q;p++)g+="|";else if(q>n)for(g="",p=0;p<n;p++)0!=p&&(g+="|"),g+=l[p]}if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(n=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,p=0;p<n;p++)document.getElementById("user_header_bar_"+p).getElementsByTagName("span")[0].style.display="none";e=e.toString();e=e.replace(/,/g,"");if((n=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=n.isExecuteApi()){if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){p=
c;"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload?(p=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(p=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl),c="&fileVirtualPath="+encodeURIComponent(c)+"&fileOrgName="+encodeURIComponent(a),f&&""!=f&&(c=c+"&customValue="+encodeURIComponent(f)),p=0>p.indexOf("?")?p+"?dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+
c:p+"&dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+c):"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(n.Dext5PL.nPe=0);try{e=isNaN(Number(e))||0>Number(e)||""==e?9E18:parseInt(e,10),k&&(a=k.FileName+"|"+k.FileNameColor+"|"+k.FileNameFormat),n.Dext5PL.AddUploadedFileEx(b,a,p,e,g),"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&
(n.Dext5PL.nPe=1)}catch(r){}}else a=a.split("\\"),a=a[a.length-1],e=isNaN(Number(e))||0>Number(e)||""==e?"":parseInt(e,10),p={file:null,guid:"",fileIdx:b,originName:a,originNameColor:"",originNameFormat:"",webPath:c,fileSize:e,uploadName:"",fileExt:"",isDelete:"n",isWebFile:"y",customValue:f,localPath:"",mark:"",headerEx:g},k&&(p.originNameColor=k.FileNameColor,p.originNameFormat=k.FileNameFormat),n.RESULTFILELIST.push(p),"form"==UPLOADTOP.G_CURRUPLOADER._config.subMode&&n.RESULTFILELISTCLON.push({file:null,
guid:"",fileIdx:b,originName:a,webPath:c,fileSize:e,uploadName:"",fileExt:"",isDelete:"n",isWebFile:"y",customValue:f,localPath:"",mark:"",headerEx:g}),n.addFileList(p,!0),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||
sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc)),n.calcTotalSize(),n.displayTotalSizeAndNum(),n.setFileListBorder(),n.setListvalue();n.setTabOrder();n.fileListSortIconReset()}};function setContextMenu(b){UPLOADTOP.DEXT5UPLOAD.util.cancelEvent(b);DEXT5UPLOAD_CONTEXT.show(b)}
var DEXT5UPLOAD_CONTEXT={_reverse:!1,_context_id:"",_context_iframe:null,_context_zIndex:0,_context_bg_zIndex:0,_context_document:null,_context_main:null,_context_bg:null,_context_bg_id:"",_width:"180",_height:"200",_currFileLiIdx:null,_create:function(b){b&&""!=b&&(b=UPLOADTOP.DEXT5UPLOAD.util._setDext5Uploader(b))&&(UPLOADTOP.G_CURRUPLOADER=b);var a=getDialogDocument();this._context_id="dext5upload_context_menu_"+UPLOADTOP.G_CURRUPLOADER.ID;this._context_bg_id="dext5upload_context_back_"+UPLOADTOP.G_CURRUPLOADER.ID;
this._context_zIndex=4E3;this._context_bg_zIndex=3E3;if(b=a.getElementById(this._context_id))a.body.removeChild(b),b=null;if(b)_context_document=b.contentWindow.document,this._context_iframe=b,this._context_document=_context_document,this._context_main=_context_document.getElementById("ul_context_main_"+UPLOADTOP.G_CURRUPLOADER.ID);else{try{b=a.createElement("iframe")}catch(c){b=a.createElement("<iframe>")}b.id=this._context_id;b.title="DEXT5Upload Context "+UPLOADTOP.G_CURRUPLOADER.ID;b.style.margin=
"0px";b.style.padding="0px";b.style.zIndex=this._context_zIndex;b.style.position="absolute";b.scrolling="no";b.style.overflow="auto";b.style.overflowX="hidden";b.style.display="none";b.allowTransparency="true";b.frameBorder="0";a.body.appendChild(b);a=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();b.src=a;var a='<html lang="ko"><head>',e="",e=UPLOADTOP.DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&(e=(new Date).getTime());e=UPLOADTOP.DEXT5UPLOAD.isRelease?UPLOADTOP.G_CURRUPLOADER._config.webPath.css+
"dext5upload.context.min.css?ver="+e:UPLOADTOP.G_CURRUPLOADER._config.webPath.cssDev+"dext5upload.context.css?ver="+e;a=a+('<link rel="stylesheet" type="text/css" href="'+e+'" charset="utf-8">')+"</head>";a+='<body style="margin:0px; padding:0px; background-color:transparent;" oncontextmenu="return false">';a+='<div onselectstart="return false;" style="width:100%; height:100%" id="context_iframe_bg_'+UPLOADTOP.G_CURRUPLOADER.ID+'">';a+='<div class="DEXT_fiVe_ED_popup">';a+='<div id="div_main_context_'+
UPLOADTOP.G_CURRUPLOADER.ID+'" class="DEXT_fiVe_EDP">';a+='<div class="Dfe_popup DEXT_fiVe_'+UPLOADTOP.G_CURRUPLOADER._config.skinName+'">';a+='<dl id="ul_context_main_'+UPLOADTOP.G_CURRUPLOADER.ID+'"></dl>';a+="</div>";a+="</div>";a+="</div>";a+="</div>";e=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();a+='<iframe src="'+e+'" style="HEIGHT: 100%; WIDTH: 100%; POSITION: absolute; LEFT: 0px; FILTER: alpha(opacity = 0); Z-INDEX: -1; DISPLAY: block; TOP: 0px" frameBorder="0" scrolling="no" tabkey="-1" title="DEXT5Upload context"></iframe>';
a+="</body></html>";_context_document=b.contentWindow.document;UPLOADTOP.DEXT5UPLOAD.util.getDocWindow(_context_document);_context_document.open("text/html","replace");_context_document.write(a);_context_document.close();this._context_iframe=b;this._context_document=_context_document;this._context_main=_context_document.getElementById("ul_context_main_"+UPLOADTOP.G_CURRUPLOADER.ID);var f=this;b=function(){return function(a){f.hide()}}();UPLOADTOP.DEXT5UPLOAD.util.addEvent(this._context_document.getElementById("context_iframe_bg_"+
UPLOADTOP.G_CURRUPLOADER.ID),"click",b);UPLOADTOP.DEXT5UPLOAD.util.addEvent(this._context_document.getElementById("context_iframe_bg_"+UPLOADTOP.G_CURRUPLOADER.ID),"contextmenu",b)}this._context_main.innerHTML=""},show:function(b){var a=getDialogWindow();null==getDialogDocument().getElementById("dext5upload_context_menu_"+UPLOADTOP.G_CURRUPLOADER.ID)&&this._create();this._context_main.innerHTML="";this._currFileLiIdx=null;var c=b.target?b.target:b.srcElement,e=UPLOADTOP.DEXT5UPLOAD.util.getParentbyTagName(c,
"ol");if(e){var f=UPLOADTOP.DEXT5UPLOAD.util.getParentbyTagName(c,"ul");if(f&&(c=f.parentNode)){f.className=""==f.className?"r_click":f.className+" r_click";for(var f=e.childNodes,g=f.length,e=0;e<g;e++){var k=f[e].getElementsByTagName("input")[0];f[e]==c?(this._currFileLiIdx=e,fileListSelection(k)):fileListNoneSelection(k)}}}else for(e=document.getElementById("file_list"),c=e.getElementsByTagName("input"),f=e.getElementsByTagName("input").length,e=0;e<f;e++)c[e].getAttribute("dext_select")&&"1"==
c[e].getAttribute("dext_select")&&fileListNoneSelection(c[e]);this._setMenu();if(3<UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(this._height)){this._context_iframe.style.width=this._width+"px";this._context_iframe.style.height=this._height;c=Math.round(b.clientX);e=Math.round(b.clientY);b={};b=getWindowScrollPos(a);UPLOADTOP.DEXT5UPLOAD.util.getClientRect(window.frameElement);f=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(window.frameElement);k=g=0;if(null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow)for(var n=window.parent;n!=
a;)var p=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(n.frameElement),g=g+p.left,k=k+p.top,n=n.parent;this._context_iframe.style.left=c+b[0]+f.left+g+"px";this._context_iframe.style.top=e+b[1]+f.top+k+"px";c=parseInt(this._context_iframe.style.left,10)+parseInt(this._width,10);e=parseInt(this._context_iframe.style.top,10)+parseInt(this._height,10);g=getWindowClientSize(a);f=b[0]+g[0]-20;b=b[1]+g[1]-10;c>=f&&(g=c-f,g=parseInt(this._context_iframe.style.left,10)-g,c-f<parseInt(this._width,10)/2&&(g-=parseInt(this._width,
10)/2),this._context_iframe.style.left=g+"px");e>=b&&(b=e-b,b=parseInt(this._context_iframe.style.top,10)-b,this._context_iframe.style.top=b+"px");this._context_iframe.style.display="";if(null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow)for(b=window;b=b.parent,UPLOADTOP.DEXT5UPLOAD.util.addEvent(b.document.documentElement,"click",fn_hideContext),UPLOADTOP.DEXT5UPLOAD.util.addEvent(b.document.documentElement,"contextmenu",fn_hideContext),b!=a;);else UPLOADTOP.DEXT5UPLOAD.util.addEvent(UPLOADTOP.UPLOADDOC.documentElement,
"click",fn_hideContext),UPLOADTOP.DEXT5UPLOAD.util.addEvent(UPLOADTOP.UPLOADDOC.documentElement,"contextmenu",fn_hideContext);a=UPLOADTOP.UPLOADWIN.document.getElementById("dext5uploader_frame_"+UPLOADTOP.G_CURRUPLOADER.ID);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a.contentWindow.document.documentElement,"click",fn_hideContext);b=document.getElementById("DEXT_fiVe_UP_content");a=UPLOADTOP.DEXT5UPLOAD.util.getElementsByClass("DEXT_fiVe_UP_uploadbox_tit",b,"div")[0];b=UPLOADTOP.DEXT5UPLOAD.util.getElementsByClass("DEXT_fiVe_UP_info_area",
b,"div")[0];c=document.getElementById("DEXT_fiVe_UP_uploadbox_btm");UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"contextmenu",fn_hideContext);UPLOADTOP.DEXT5UPLOAD.util.addEvent(b,"contextmenu",fn_hideContext);UPLOADTOP.DEXT5UPLOAD.util.addEvent(c,"contextmenu",fn_hideContext)}},_setMenu:function(){var b=[],a=[];switch(UPLOADTOP.G_CURRUPLOADER._config.mode){case "upload":b=["add_file","remove_current_file","remove_all_file"];"1"==UPLOADTOP.G_CURRUPLOADER._config.fileMoveContextMenu&&(b.push(""),b.push("move_first"),
b.push("move_forward"),b.push("move_backward"),b.push("move_end"));"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&b.splice(0,1);null==this._currFileLiIdx&&a.push("remove_current_file");var c=document.getElementById("file_list"),c=c.getElementsByTagName("input"),c=c.length;0==c&&a.push("remove_all_file");break;case "view":b=["open_current_file","download_current_file","download_all_file"];"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&(b.splice(2,0,"save_and_open"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&
(b.splice(2,0,"download_zipfile"),b.push("download_all_zipfile")));if(null==this._currFileLiIdx||"y"!=RESULTFILELIST[this._currFileLiIdx].isWebFile)a.push("open_current_file"),a.push("download_current_file"),"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&(a.push("save_and_open"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&a.push("download_zipfile"));c=document.getElementById("file_list");c=c.getElementsByTagName("input");c=c.length;0==c&&(a.push("download_all_file"),"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&a.push("download_all_zipfile"));break;case "open":b=["open_current_file"];null!=this._currFileLiIdx&&"y"==RESULTFILELIST[this._currFileLiIdx].isWebFile||a.push("open_current_file");break;case "download":b=["download_current_file","download_all_file"];"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&(b.splice(1,0,"save_and_open"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&(b.splice(1,0,"download_zipfile"),b.push("download_all_zipfile")));
if(null==this._currFileLiIdx||"y"!=RESULTFILELIST[this._currFileLiIdx].isWebFile)a.push("download_current_file"),"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&(a.push("save_and_open"),"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&a.push("download_zipfile"));c=document.getElementById("file_list");c=c.getElementsByTagName("input");c=c.length;0==c&&(a.push("download_all_file"),"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&
a.push("download_all_zipfile"));break;default:b=["add_file"],a=[]}"1"==UPLOADTOP.G_CURRUPLOADER._config.hybridDownload&&b.push("setting");var c=UPLOADTOP.G_CURRUPLOADER._config.removeContextItem.split(","),e=c.length;this._height="0px";for(var f=b.length,g=0;g<f;g++){if(0==g){var k=this._context_document.createElement("dt");k.innerHTML="context_main_menu";this._context_main.appendChild(k)}for(var k=!1,n=0;n<e;n++)if(""!=b[g]&&b[g]==c[n]){k=!0;break}k||this._addItem(b[g])}this._height=parseInt(this._height,
10)+2+"px";this._disableItem(a);if("1"==UPLOADTOP.G_CURRUPLOADER._config.useZipDownload&&0>c.toString().indexOf("download_zipfile"))try{this._context_main.style.width="190px";this._context_main.parentNode.parentNode.style.width="190px";this._width="190";for(var p=this._context_main.childNodes.length,g=0;g<p;g++)try{this._context_main.childNodes[g].style.width="190px",this._context_main.childNodes[g].childNodes[1].style.width="160px"}catch(l){}}catch(q){}},_addItem:function(b){var a=this._context_document.createElement("dd"),
c=this,e=this._getText(b);if(""==e)a.className="b_border",this._height=parseInt(this._height,10)+1+"px";else{this._height=parseInt(this._height,10)+24+"px";var f=this._context_document.createElement("div");this._context_document.createElement("div");var g=this._context_document.createElement("div");"textContent"in f?f.textContent=e:f.innerText=e;g.className="s_left";""!=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor&&(g.style.backgroundColor=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor);
f.className="s_right";a.appendChild(g);a.appendChild(f);e=function(a,b){return function(b){"item_disabled"!=a.className&&(a.className="on_mouseover",""!=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor&&(a.style.backgroundColor=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor),UPLOADTOP.DEXT5UPLOAD.util.stopEvent(b))}}(a,g);g=function(a,b){return function(b){"on_mouseover"==a.className&&(a.className="",""!=UPLOADTOP.G_CURRUPLOADER._config.customFooterColor&&(a.style.backgroundColor=""),UPLOADTOP.DEXT5UPLOAD.util.stopEvent(b))}}(a,
g);f=function(a){return function(e){"on_mouseover"==a.className&&(c.hide(),UPLOADTOP.G_CURRUPLOADER._dextCommands(UPLOADTOP.G_CURRUPLOADER.ID,b,c,c._currFileLiIdx))}}(a);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"mouseover",e);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"mouseout",g);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"click",f)}0<b.length&&(a.id="context_"+b);a.unselectable="on";this._context_main.appendChild(a)},_getText:function(b){var a="";""!=b&&(a=Dext5Upload_Lang.context[b]);return a},_disableItem:function(b){for(var a=
b.length,c=0;c<a;c++){var e=this._context_document.getElementById("context_"+b[c]);e&&(e.className="item_disabled")}},hide:function(){this._context_document&&this._context_document.getElementById("div_sub_context_"+UPLOADTOP.G_CURRUPLOADER.ID)&&(this._context_document.getElementById("div_sub_context_"+UPLOADTOP.G_CURRUPLOADER.ID).style.display="none");this._context_iframe&&(this._context_iframe.style.display="none");this._context_bg&&(this._context_bg.style.display="none");for(var b=document.getElementById("file_list").getElementsByTagName("ul"),
a=b.length,c=0;c<a;c++)for(var e=b[c].className.split(" "),f=e.length,g=0;g<f;g++)"r_click"==e[g]&&(b[c].className=0<g?b[c].className.replace(" r_click",""):b[c].className.replace("r_click",""));b=getDialogWindow();if(null!=UPLOADTOP.G_CURRUPLOADER.dialogWindow)for(a=window;a=a.parent,UPLOADTOP.DEXT5UPLOAD.util.removeEvent(a.document.documentElement,"click",fn_hideContext),UPLOADTOP.DEXT5UPLOAD.util.removeEvent(a.document.documentElement,"contextmenu",fn_hideContext),a!=b;);else UPLOADTOP.DEXT5UPLOAD.util.removeEvent(UPLOADTOP.UPLOADDOC.documentElement,
"click",fn_hideContext),UPLOADTOP.DEXT5UPLOAD.util.removeEvent(UPLOADTOP.UPLOADDOC.documentElement,"contextmenu",fn_hideContext);b=UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_frame_"+UPLOADTOP.G_CURRUPLOADER.ID);UPLOADTOP.DEXT5UPLOAD.util.removeEvent(b.contentWindow.document.documentElement,"click",fn_hideContext);a=document.getElementById("DEXT_fiVe_UP_content");b=UPLOADTOP.DEXT5UPLOAD.util.getElementsByClass("DEXT_fiVe_UP_uploadbox_tit",a,"div")[0];a=UPLOADTOP.DEXT5UPLOAD.util.getElementsByClass("DEXT_fiVe_UP_info_area",
a,"div")[0];c=document.getElementById("DEXT_fiVe_UP_uploadbox_btm");UPLOADTOP.DEXT5UPLOAD.util.removeEvent(b,"contextmenu",fn_hideContext);UPLOADTOP.DEXT5UPLOAD.util.removeEvent(a,"contextmenu",fn_hideContext);UPLOADTOP.DEXT5UPLOAD.util.removeEvent(c,"contextmenu",fn_hideContext)},_makeBackground:function(){var b=this,a=getDialogWindow(),c=getDialogDocument();if(this._context_bg=c.getElementById(b._context_bg_id))this._context_bg.style.display="";if(!this._context_bg){this._context_bg=c.createElement("div");
this._context_bg.id=this._context_bg_id;this._context_bg.innerHTML='<div oncontextmenu="return false" style="filter:alpha(opacity=0); opacity:0.0; -moz-opacity:0.0;  -khtml-opacity: 0.0; background-color:#ffffff; z-index:'+this._context_bg_zIndex+'; position:absolute; left:0px; top:0px;overflow:hidden; width:1px; height:1px; "></div>';c.body.appendChild(this._context_bg);var e=function(){return function(a){b.hide()}}(),f=function(){return function(a){b.hide()}}(),g=function(){return function(a){a=
"CSS1Compat"==c.compatMode?c.documentElement.scrollHeight:c.body.scrollHeight;b._context_bg.firstChild.style.width=("CSS1Compat"==c.compatMode?c.documentElement.scrollWidth:c.body.scrollWidth)+"px";b._context_bg.firstChild.style.height=a+"px"}}();UPLOADTOP.DEXT5UPLOAD.util.addEvent(this._context_bg,"click",e);UPLOADTOP.DEXT5UPLOAD.util.addEvent(this._context_bg,"contextmenu",f);UPLOADTOP.DEXT5UPLOAD.util.addEvent(a,"resize",g);a="CSS1Compat"==c.compatMode?c.documentElement.scrollHeight:c.body.scrollHeight;
this._context_bg.firstChild.style.width=("CSS1Compat"==c.compatMode?c.documentElement.scrollWidth:c.body.scrollWidth)-10+"px";this._context_bg.firstChild.style.height=a-10+"px"}}},fn_hideContext=function(){void 0!=DEXT5UPLOAD_CONTEXT&&DEXT5UPLOAD_CONTEXT.hide()};var Dext5Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_trans_unitDelimiter:"\x0B",_trans_unitAttributeDelimiter:"\f",encode:function(b){var a="",c,e,f,g,k,n,p=0;for(b=Dext5Base64._utf8_encode(b);p<b.length;)c=b.charCodeAt(p++),e=b.charCodeAt(p++),f=b.charCodeAt(p++),g=c>>2,c=(c&3)<<4|e>>4,k=(e&15)<<2|f>>6,n=f&63,isNaN(e)?k=n=64:isNaN(f)&&(n=64),a=a+this._keyStr.charAt(g)+this._keyStr.charAt(c)+this._keyStr.charAt(k)+this._keyStr.charAt(n);return a},decode:function(b){var a=
"",c,e,f,g,k,n=0;for(b=b.replace(/[^A-Za-z0-9\+\/\=]/g,"");n<b.length;)c=this._keyStr.indexOf(b.charAt(n++)),e=this._keyStr.indexOf(b.charAt(n++)),g=this._keyStr.indexOf(b.charAt(n++)),k=this._keyStr.indexOf(b.charAt(n++)),c=c<<2|e>>4,e=(e&15)<<4|g>>2,f=(g&3)<<6|k,a+=String.fromCharCode(c),64!=g&&(a+=String.fromCharCode(e)),64!=k&&(a+=String.fromCharCode(f));return a=Dext5Base64._utf8_decode(a)},_utf8_encode:function(b){b=b.replace(/\r\n/g,"\n");for(var a="",c=0;c<b.length;c++){var e=b.charCodeAt(c);
128>e?a+=String.fromCharCode(e):(127<e&&2048>e?a+=String.fromCharCode(e>>6|192):(a+=String.fromCharCode(e>>12|224),a+=String.fromCharCode(e>>6&63|128)),a+=String.fromCharCode(e&63|128))}return a},_utf8_decode:function(b){for(var a="",c=0,e=c1=c2=0;c<b.length;)e=b.charCodeAt(c),128>e?(a+=String.fromCharCode(e),c++):191<e&&224>e?(c2=b.charCodeAt(c+1),a+=String.fromCharCode((e&31)<<6|c2&63),c+=2):(c2=b.charCodeAt(c+1),c3=b.charCodeAt(c+2),a+=String.fromCharCode((e&15)<<12|(c2&63)<<6|c3&63),c+=3);return a},
makeEncryptParam:function(b){b=Dext5Base64.encode(b);b="R"+b;b=Dext5Base64.encode(b);return b=b.replace(/[+]/g,"%2B")},makeDecryptReponseMessage:function(b){b=Dext5Base64.decode(b);b=b.substring(1);return b=Dext5Base64.decode(b)}};/*
 Copyright (c) 2015, Raonwiz Technology Inc. All rights reserved.
*/
var D5FED={Dd0:String.fromCharCode(100,111,99),Ddx:String.fromCharCode(100,111,99,120),Dx0:String.fromCharCode(120,108,115),Dxx:String.fromCharCode(120,108,115,120),Dp0:String.fromCharCode(112,112,116),Dpx:String.fromCharCode(112,112,116,120),Dh0:String.fromCharCode(104,119,112),Dhx:String.fromCharCode(104,119,112,120),Ib0:String.fromCharCode(98,109,112),Ij0:String.fromCharCode(106,112,103),Ij01:String.fromCharCode(106,112,101,103),Ip0:String.fromCharCode(112,110,103),It0:String.fromCharCode(116,
105,102),It01:String.fromCharCode(116,105,102,102),Ig0:String.fromCharCode(103,105,102),Id0:String.fromCharCode(100,119,103),Iw0:String.fromCharCode(119,109,102),Ie0:String.fromCharCode(101,109,102),Ip1:String.fromCharCode(112,115,100),Ta0:String.fromCharCode(97,115,112),Ta0x:String.fromCharCode(97,115,112,120),Tj0:String.fromCharCode(106,115,112),Tt0:String.fromCharCode(116,120,116),Th0:String.fromCharCode(104,116,109),Th01:String.fromCharCode(104,116,109,108),Tj1:String.fromCharCode(106,97,118,
97),Tc0:String.fromCharCode(99,115),Tp0:String.fromCharCode(112,104,112),Zj0:String.fromCharCode(106,97,114),Zr0:String.fromCharCode(114,97,114),Zt0:String.fromCharCode(116,97,114),Zt1:String.fromCharCode(116,103,122),Zg0:String.fromCharCode(103,122),Z70:String.fromCharCode(55,122),Zb0:String.fromCharCode(98,122,50),Zz0:String.fromCharCode(122,105,112),Ze0:String.fromCharCode(101,103,103),Mm0:String.fromCharCode(109,112,52),Ms0:String.fromCharCode(115,119,102),Mf0:String.fromCharCode(102,108,118),
Ma0:String.fromCharCode(97,118,105),Mw0:String.fromCharCode(119,109,118),Mw1:String.fromCharCode(119,109,97),Ma1:String.fromCharCode(97,115,102),Mm1:String.fromCharCode(109,112,51),Mm2:String.fromCharCode(109,111,118),Mm3:String.fromCharCode(109,105,100),Mm4:String.fromCharCode(109,112,103),Mm41:String.fromCharCode(109,112,101,103),Mr0:String.fromCharCode(114,97),Mr1:String.fromCharCode(114,97,109),Mi0:String.fromCharCode(105,115,111),M30:String.fromCharCode(51,103,103),M31:String.fromCharCode(51,
103,112),M32:String.fromCharCode(51,103,50),Mm5:String.fromCharCode(109,52,118),Mr2:String.fromCharCode(114,109,105),Mc0:String.fromCharCode(99,100,97),Mw3:String.fromCharCode(119,97,118),Mq0:String.fromCharCode(113,99,112),Mo0:String.fromCharCode(111,103,103),Mo1:String.fromCharCode(111,103,97),Mo2:String.fromCharCode(111,103,118),Mo3:String.fromCharCode(111,103,120),Mm31:String.fromCharCode(109,105,100,105),Mi1:String.fromCharCode(105,115,122),Mf1:String.fromCharCode(102,108,97,99),Ma2:String.fromCharCode(97,
105,102,102),Mm6:String.fromCharCode(109,107,118),Mm7:String.fromCharCode(109,107,97),Mm8:String.fromCharCode(109,100,115),Mm9:String.fromCharCode(109,112,99),Mv0:String.fromCharCode(118,111,98),Ma3:String.fromCharCode(97,99,99),Ma4:String.fromCharCode(97,112,101),Mm10:String.fromCharCode(109,109,102),Md0:String.fromCharCode(100,109,103),Mi2:String.fromCharCode(105,109,103),Ep001:String.fromCharCode(112,100,102),Em002:String.fromCharCode(109,100,98),Er003:String.fromCharCode(114,116,102),Em004:String.fromCharCode(109,
115,103),Ep005:String.fromCharCode(112,115,116),Ec006:String.fromCharCode(99,97,98),Ec007:String.fromCharCode(99,104,109),Ec008:String.fromCharCode(99,104,109,105),Ee009:String.fromCharCode(101,109,108),Eh010:String.fromCharCode(104,108,112),El011:String.fromCharCode(108,110,107),Et012:String.fromCharCode(116,116,102),Em013:String.fromCharCode(109,100,102),Ei014:String.fromCharCode(105,99,111),Ec015:String.fromCharCode(99,117,114),Ex016:String.fromCharCode(120,109,112),Ec017:String.fromCharCode(99,
111,109),Ee018:String.fromCharCode(101,120,101),Ed019:String.fromCharCode(100,108,108),Eo020:String.fromCharCode(111,99,120),El021:String.fromCharCode(108,105,98),Em022:String.fromCharCode(109,115,105),Eo023:String.fromCharCode(111,98,106),Ep024:String.fromCharCode(112,115),Ee025:String.fromCharCode(101,112,115)},d5cdh="d0 cf 11 e0 a1 b1 1a e1".split(" "),d5zdh=["50","4b","03","04","14"];
function is_match(b,a){var c=!0;if(void 0==b||0==b.length)return!1;for(var e=a.length,f=0;f<e;f++)if(b[f]!=a[f]){c=!1;break}return c}function is_cdf(b){var a=!1;b=arrBuf2Str(b,0,d5cdh.length);return a=is_match(b,d5cdh)}function Is_ZipFileType(b){var a=!1;b=arrBuf2Str(b,0,d5zdh.length);return a=is_match(b,d5zdh)}function appendBuffer(b,a){var c=new Uint8Array(b.byteLength+a.byteLength);c.set(new Uint8Array(b),0);c.set(new Uint8Array(a),b.byteLength);return c}
function D5FileDetector(b,a,c){var e=null;FileReadBySlice(b,a,function(f,g,k){0==k?c(b,a,0):(null!=f&&(null==e?e=new Uint8Array(f):(f=new Uint8Array(f),e=appendBuffer(e,f))),-1==k&&(f=FileExtensionDetector(e,a),c(b,a,f)))})}
function FileReadBySlice(b,a,c){var e=b.size,f=0,g=e;D5FED.Mi0==a?g=36885:D5FED.Ddx!=a&&D5FED.Dxx!=a&&D5FED.Dp0!=a&&D5FED.Dpx!=a&&D5FED.Dh0!=a&&D5FED.Dhx!=a&&(g=4096);var k=function(a,b,c){var e=new FileReader;a=c.slice(a,b+a);e.onload=n;e.readAsArrayBuffer(a)},n=function(a){null==a.target.error?(f+=a.target.result.byteLength,f>=g?c(a.target.result,f,-1):f>=e?c(a.target.result,f,-1):(c(a.target.result,f,e),setTimeout(k.bind(null,f,3145728,b),10))):c(null,f,0)};k(f,3145728,b)}
function FileExtensionDetector(b,a){var c=-1;a.toLowerCase();if(a==D5FED.It01||a==D5FED.Ec008||a==D5FED.Th01||a==D5FED.Mm31)a=a.substr(0,3);else if(a==D5FED.Ij01||a==D5FED.Mm41)a=a.replace("e","");return c=D5FED.Dd0==a?Is_D_o_c(b):D5FED.Ddx==a?Is_D_o_cx(b):D5FED.Dx0==a?Is_X_l_s(b):D5FED.Dxx==a?Is_X_l_sx(b):D5FED.Dp0==a?Is_P_p_t(b):D5FED.Dpx==a?Is_P_p_tx(b):D5FED.Dh0==a?Is_H_w_p(b):D5FED.Dhx==a?Is_H_w_px(b):D5FED.Ib0==a||D5FED.Ij0==a||D5FED.Ip0==a||D5FED.It0==a||D5FED.Ig0==a||D5FED.Id0==a||D5FED.Iw0==
a||D5FED.Ie0==a||D5FED.Ip1==a?Is_ImageFormat(b,a):D5FED.Ta0==a||D5FED.Ta0x==a||D5FED.Tj0==a||D5FED.Tt0==a||D5FED.Th0==a||D5FED.Tj1==a||D5FED.Tc0==a||D5FED.Tp0==a?Is_TextFormat(b,a):D5FED.Zj0==a||D5FED.Zr0==a||D5FED.Zt0==a||D5FED.Zt1==a||D5FED.Zg0==a||D5FED.Z70==a||D5FED.Zb0==a||D5FED.Zz0==a||D5FED.Ze0==a?Is_ZipFormat(b,a):D5FED.Mm0==a||D5FED.Ms0==a||D5FED.Mf0==a||D5FED.Ma0==a||D5FED.Mw0==a||D5FED.Mw1==a||D5FED.Ma1==a||D5FED.Mm1==a||D5FED.Mm2==a||D5FED.Mm3==a||D5FED.Mm4==a||D5FED.Mr0==a||D5FED.Mr1==
a||D5FED.Mi0==a||D5FED.M30==a||D5FED.M31==a||D5FED.M32==a||D5FED.Mm5==a||D5FED.Mr2==a||D5FED.Mc0==a||D5FED.Mw3==a||D5FED.Mq0==a||D5FED.Mo0==a||D5FED.Mo1==a||D5FED.Mo2==a||D5FED.Mo3==a||D5FED.Mm31==a||D5FED.Mi1==a||D5FED.Mf1==a||D5FED.Ma2==a||D5FED.Mm6==a||D5FED.Mm7==a||D5FED.Mm8==a||D5FED.Mm9==a||D5FED.Mv0==a||D5FED.Ma3==a||D5FED.Ma4==a||D5FED.Mm10==a||D5FED.Md0==a||D5FED.Mi2==a?Is_MovieAndMediaFormat(b,a):Is_EtcFormat(b,a)}
function arrBuf2Str(b,a,c){var e=[];if(b.length<c-a){for(var f=0;f<c;f++)e.push("00");return e}c=a+c;for(f=a;f<c;f++)a=b[f].toString(16),2>a.length&&(a="0"+a),e.push(a);return e}function arrOne2Str(b){var a="00",a=b.toString(16);2>a.length&&(a="0"+a);return a}
function Is_D_o_c(b){var a=0;if(1024>b.length)return 0;var c=["ec","a5","c1","00"],e=["db","a5","2d","00"],f=["7f","fe","34","0a"],g="12 34 56 78 90 ff".split(" "),k="31 be 00 00 00 ab 00 00".split(" "),n=["0d","44","4f","43"],p="cf 11 e0 a1 b1 1a e1 00".split(" ");is_cdf(b)&&(a=is_match(arrBuf2Str(b,512,8),c),0==a&&(a=is_match(arrBuf2Str(b,512,8),e)),0==a&&(a=is_match(arrBuf2Str(b,512,8),f)),0==a&&(a=is_match(arrBuf2Str(b,512,8),g)),0==a&&(a=is_match(arrBuf2Str(b,512,8),k)),0==a&&(a=is_match(arrBuf2Str(b,
512,8),n)),0==a&&(a=is_match(arrBuf2Str(b,512,8),p)));return a}function Is_D_o_cx(b){var a=0;if(1024>b.length)return 0;var c=["77","6f","72","64","2f"];if(Is_ZipFileType(b))for(var e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);else a=is_cdf(b);return a}
function Is_X_l_s(b){var a=0;if(1024>b.length)return 0;var c="09 08 10 00 00 06 05 00".split(" "),e=["fd","ff","ff","ff","10"],f=["fd","ff","ff","ff","1f"],g=["fd","ff","ff","ff","22"],k=["fd","ff","ff","ff","23"],n=["fd","ff","ff","ff","28"],p=["fd","ff","ff","ff","29"],l="fd ff ff ff 20 00 00 00".split(" ");is_cdf(b)&&(a=is_match(arrBuf2Str(b,512,8),c),0==a&&(a=is_match(arrBuf2Str(b,512,8),e)),0==a&&(a=is_match(arrBuf2Str(b,512,8),f)),0==a&&(a=is_match(arrBuf2Str(b,512,8),g)),0==a&&(a=is_match(arrBuf2Str(b,
512,8),k)),0==a&&(a=is_match(arrBuf2Str(b,512,8),n)),0==a&&(a=is_match(arrBuf2Str(b,512,8),p)),0==a&&(a=is_match(arrBuf2Str(b,512,8),l)),0==a&&(a="57 00 6f 00 72 00 6b 00 62 00 6f 00 6f 00 6b 00".split(" "),a=is_match(arrBuf2Str(b,1152,a.length),a)));return a}
function Is_X_l_sx(b){var a=0;if(1024>b.length)return 0;var c=["78","6c","2f"];if(Is_ZipFileType(b))for(var e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);else a=is_cdf(b);return a}
function Is_P_p_t(b){var a=0;if(1024>b.length)return 0;var c=["00","6e","1e","f0"],e=["0f","00","e8","03"],f=["a0","46","1d","f0"],g="fd ff ff ff 0e 00 00 00".split(" "),k="fd ff ff ff 1c 00 00 00".split(" "),n="fd ff ff ff 26 00 00 00".split(" "),p="fd ff ff ff 43 00 00 00".split(" ");if(is_cdf(b)&&(a=is_match(arrBuf2Str(b,512,8),c),0==a&&(a=is_match(arrBuf2Str(b,512,8),e)),0==a&&(a=is_match(arrBuf2Str(b,512,8),f)),0==a&&(a=is_match(arrBuf2Str(b,512,8),g)),0==a&&(a=is_match(arrBuf2Str(b,512,8),k)),
0==a&&(a=is_match(arrBuf2Str(b,512,8),n)),0==a&&(a=is_match(arrBuf2Str(b,512,8),p)),0==a))for(c="50 6f 77 65 72 50 6f 69 6e".split(" "),e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);return a}
function Is_P_p_tx(b){var a=0;if(1024>b.length)return 0;var c=["70","70","74","2f"];if(Is_ZipFileType(b))for(var e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);else a=is_cdf(b);return a}
function Is_H_w_p(b){var a=0;if(1024>b.length)return 0;var c="48 57 50 20 44 6f 63 75 6d 65 6e 74 20 46 69 6c 65".split(" ");if(is_cdf(b))for(var e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);return a?a:a=is_match(arrBuf2Str(b,0,17),c)}
function Is_H_w_px(b){var a=0;if(1024>b.length)return 0;var c=["2f","68","77","70"];if(Is_ZipFileType(b))for(var e=b.length-c.length,f=0;f<e&&(arrOne2Str(b[f])!=c[0]||arrOne2Str(b[f+1])!=c[1]||!(a=is_match(arrBuf2Str(b,f,c.length),c)));f++);else a=is_cdf(b);return a}
function Is_ImageFormat(b,a){var c=-1;if(5>b.length)return 0;var e=["42","4d"],f=["ff","d8"],g=["89","50","4e","47"],k=["49","20","49"],n=["49","49","2a"],p=["4d","4d"],l=["47","49","46","38"],q=["41","43","31","30"],r=["d7","cd","c6","9a"],u=["01","00","00","00"],w=["38","42","50","53"];D5FED.Ib0==a?c=is_match(arrBuf2Str(b,0,e.length),e):D5FED.Ij0==a?c=is_match(arrBuf2Str(b,0,f.length),f):D5FED.Ip0==a?c=is_match(arrBuf2Str(b,0,g.length),g):D5FED.It0==a?(c=is_match(arrBuf2Str(b,0,k.length),k),0==
c&&(c=is_match(arrBuf2Str(b,0,n.length),n)),0==c&&(c=is_match(arrBuf2Str(b,0,p.length),p))):D5FED.Ig0==a?c=is_match(arrBuf2Str(b,0,l.length),l):D5FED.Id0==a?c=is_match(arrBuf2Str(b,0,q.length),q):D5FED.Iw0==a?c=is_match(arrBuf2Str(b,0,r.length),r):D5FED.Ie0==a?c=is_match(arrBuf2Str(b,0,u.length),u):D5FED.Ip1==a&&(c=is_match(arrBuf2Str(b,0,w.length),w));return c}
function Is_MovieAndMediaFormat(b,a){var c=-1;if(20>b.length)return 0;if(D5FED.Mm0==a||D5FED.M30==a||D5FED.M31==a||D5FED.M32==a||D5FED.Mm5==a)var e=["66","74","79","70"],c=is_match(arrBuf2Str(b,4,e.length),e);else if(D5FED.Ms0==a){var e=["43","57","53"],f=["46","57","53"],c=is_match(arrBuf2Str(b,0,e.length),e);0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f))}else if(D5FED.Mf0==a)e=["46","4c","56"],c=is_match(arrBuf2Str(b,0,e.length),e);else if(D5FED.Ma0==a||D5FED.Mr2==a||D5FED.Mc0==a||D5FED.Mw3==a||
D5FED.Mq0==a)e=["52","49","46","46"],f=["41","56","49","20"],c=is_match(arrBuf2Str(b,0,e.length),e),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f));else if(D5FED.Mw0==a||D5FED.Mw1==a||D5FED.Ma1==a)e="30 26 b2 75 8e 66 cf 11".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e);else if(D5FED.Mm1==a){var e=["49","44","33"],f=["ff","fa"],g=["ff","fb"],c=is_match(arrBuf2Str(b,0,e.length),e);0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f));0==c&&(c=is_match(arrBuf2Str(b,0,g.length),g))}else if(D5FED.Mm2==a){var e=
["6d","6f","6f","76"],f=["66","72","65","65"],g=["6d","64","61","74"],k=["77","69","64","65"],n=["70","6e","6f","74"],p=["73","6b","69","70"],l=["66","74","79","70","74"],c=is_match(arrBuf2Str(b,4,e.length),e);0==c&&(c=is_match(arrBuf2Str(b,4,f.length),f));0==c&&(c=is_match(arrBuf2Str(b,4,g.length),g));0==c&&(c=is_match(arrBuf2Str(b,4,k.length),k));0==c&&(c=is_match(arrBuf2Str(b,4,n.length),n));0==c&&(c=is_match(arrBuf2Str(b,4,p.length),p));0==c&&(c=is_match(arrBuf2Str(b,4,l.length),l))}else D5FED.Mm3==
a?(e=["4d","54","68","64"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mm4==a?(e=["00","00","01","ba"],f=["00","00","01","b3"],c=is_match(arrBuf2Str(b,0,e.length),e),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f))):D5FED.Mr0==a?(e=["2e","72","61","fd","00"],f="2e 52 4d 46 00 00 00 12".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f))):D5FED.Mr1==a?(e="72 74 73 70 3a 2f 2f".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mi0==a?(e=["43","44",
"30","30","31"],f="00 ff ff ff ff ff ff ff ff ff ff 00 00 02 00".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f)),0==c&&b.length>32769+e.length&&(c=is_match(arrBuf2Str(b,32769,e.length),e)),0==c&&b.length>34817+e.length&&(c=is_match(arrBuf2Str(b,34817,e.length),e)),0==c&&b.length>36865+e.length&&(c=is_match(arrBuf2Str(b,36865,e.length),e))):D5FED.Mi1==a?(e=["49","73","5a","21"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mo0==a||D5FED.Mo1==a||D5FED.Mo2==
a||D5FED.Mo3==a?(e=["4f","67","67","53"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mf1==a?(e=["66","4c","61","43"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Ma2==a?(e=["46","4f","52","4d","00"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mm6==a||D5FED.Mm7==a?(e=["1a","45","df","a3"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mm8==a?(e="4d 45 44 49 41 20 44 45 53 43 52 49 50 54 4f 52".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mm9==a?(e=["4d","50","2b"],c=is_match(arrBuf2Str(b,
0,e.length),e)):D5FED.Mv0==a?(e=["00","00","01","ba"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Ma3==a?(e="41 41 43 00 01 00".split(" "),c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Ma4==a?(e=["4d","41","43","20"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mm10==a?(e=["4d","4d","4d","44"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Md0==a?(e=["78"],c=is_match(arrBuf2Str(b,0,e.length),e)):D5FED.Mi2==a&&(e=["51","46","49","fb"],f="50 49 43 54 00 08".split(" "),c=is_match(arrBuf2Str(b,0,e.length),
e),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f)));return c}
function Is_TextFormat(b,a){var c=0,c=Is_ImageFormat(b,D5FED.Ib0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Ij0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Ip0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.It0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Ig0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Id0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Iw0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Ie0);if(1==c)return 0;c=Is_ImageFormat(b,D5FED.Ip1);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zj0);if(1==c)return 0;
c=Is_ZipFormat(b,D5FED.Zr0);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zt0);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zt1);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zg0);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Z70);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zb0);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Zz0);if(1==c)return 0;c=Is_ZipFormat(b,D5FED.Ze0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ms0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mf0);
if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ma0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mw0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mw1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ma1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm2);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm3);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm4);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mr0);if(1==
c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mr1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mi0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.M30);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.M31);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.M32);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm5);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mr2);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mc0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mw3);if(1==c)return 0;
c=Is_MovieAndMediaFormat(b,D5FED.Mq0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mo0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mo1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mo2);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mo3);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm31);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mi1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mf1);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ma2);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,
D5FED.Mm6);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm7);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm8);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm9);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mv0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ma3);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Ma4);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mm10);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Md0);if(1==c)return 0;c=Is_MovieAndMediaFormat(b,D5FED.Mi2);
if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ep001);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Em002);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Er003);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Em004);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ep005);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ec006);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ec007);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ec008);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ee009);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Eh010);if(1==c)return 0;c=Is_EtcFormat(b,
D5FED.El011);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Et012);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Em013);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ei014);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ec015);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ex016);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ec017);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ee018);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ed019);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Eo020);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.El021);if(1==c)return 0;c=
Is_EtcFormat(b,D5FED.Em022);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Eo023);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ep024);if(1==c)return 0;c=Is_EtcFormat(b,D5FED.Ee025);return 1==c||1024<b.length&&(is_cdf(b)||Is_ZipFileType(b))?0:-1}
function Is_ZipFormat(b,a){var c=-1;if(12>b.length)return 0;if(D5FED.Zj0==a){var c=["50","4b","03","04"],e=["5f","27","a8","89"],f="4a 41 52 43 53 00".split(" "),g="50 4b 03 04 14 00 08 00".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c);0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e));0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f));0==c&&(c=is_match(arrBuf2Str(b,0,g.length),g))}else D5FED.Zr0==a?(c="52 61 72 21 1a 07 00".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Zt0==a||D5FED.Zt1==
a?(c=["1f","8b"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Zg0==a?(c=["1f","8b","08"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Z70==a?(c="37 7a bc af 27 1c".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Zb0==a?(c=["42","5a","68"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Zz0==a?(c=["50","4b"],e="57 69 6e 5a 69 70".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c),0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e))):D5FED.Ze0==a&&(c=["45","47","47","41"],c=is_match(arrBuf2Str(b,
0,c.length),c));return c}
function Is_EtcFormat(b,a){var c=-1;if(12>b.length)return 0;if(D5FED.Ep001==a)c=["25","50","44","46"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Em002==a)c="53 74 61 6e 64 61 72 64 20 4a 65 74".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Er003==a)c="7b 5c 72 74 66 31".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Em004==a)c=is_cdf(b);else if(D5FED.Ep005==a)c=["21","42","44","4e"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ec006==a){var c=["49",
"53","63","28"],e=["4d","53","43","46"],c=is_match(arrBuf2Str(b,0,c.length),c);0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e))}else if(D5FED.Ec007==a)c=["49","54","53","46"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ee009==a){var c=["58","2d"],e="52 65 74 75 72 6e 2d 50".split(" "),f=["46","72","6f","6d"],c=is_match(arrBuf2Str(b,0,c.length),c);0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e));0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f))}else if(D5FED.Eh010==a)c="00 00 ff ff ff ff".split(" "),
e=["3f","5f","03","00"],f=["4c","4e","02","00"],c=is_match(arrBuf2Str(b,0,c.length),c),0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e)),0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f));else if(D5FED.El011==a)c="4c 00 00 00 01 14 02 00".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Et012==a)c=["00","01","00","00","00"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Em013==a)c=["01","0f","00","00"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ei014==a)c=["00","00","01","00"],
c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ec015==a)c=["00","00","02","00"],c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ex016==a)c="2f 2a 20 58 50 4d 20 2a 2f".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c);else if(D5FED.Ec017==a){var c=["e8"],e=["e9"],f=["eb"],g=["4d","5a"],c=is_match(arrBuf2Str(b,0,c.length),c);0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e));0==c&&(c=is_match(arrBuf2Str(b,0,f.length),f));0==c&&(c=is_match(arrBuf2Str(b,0,g.length),g))}else D5FED.Ee018==a||D5FED.Ed019==
a||D5FED.Eo020==a?(c=["4d","5a"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.El021==a?(c="21 3c 61 72 63 68 3e 0a".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Em022==a?c=is_cdf(b):D5FED.Eo023==a?(c=["4c","01"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Ep024==a?(c=["25","21"],c=is_match(arrBuf2Str(b,0,c.length),c)):D5FED.Ee025==a&&(c=["c5","d0","d3","c6"],e="25 21 50 53 2d 41 64 6f".split(" "),c=is_match(arrBuf2Str(b,0,c.length),c),0==c&&(c=is_match(arrBuf2Str(b,0,e.length),e)));
return c};

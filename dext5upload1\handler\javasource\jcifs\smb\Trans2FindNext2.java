package jcifs.smb;

import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2FindNext2.class */
class Trans2FindNext2 extends SmbComTransaction {
    private int sid;
    private int informationLevel;
    private int resumeKey;
    private int flags;
    private String filename;

    Trans2FindNext2(int sid, int resumeKey, String filename) {
        this.sid = sid;
        this.resumeKey = resumeKey;
        this.filename = filename;
        this.command = (byte) 50;
        this.subCommand = (byte) 2;
        this.informationLevel = 260;
        this.flags = 0;
        this.maxParameterCount = 8;
        this.maxDataCount = Trans2FindFirst2.LIST_SIZE;
        this.maxSetupCount = (byte) 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    void reset(int resumeKey, String lastName) {
        super.reset();
        this.resumeKey = resumeKey;
        this.filename = lastName;
        this.flags2 = 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        int i = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        return 2;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.sid, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        writeInt2(Trans2FindFirst2.LIST_COUNT, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 2;
        writeInt2(this.informationLevel, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 2;
        writeInt4(this.resumeKey, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 4;
        writeInt2(this.flags, dst, dstIndex5);
        int dstIndex6 = dstIndex5 + 2;
        return (dstIndex6 + writeString(this.filename, dst, dstIndex6)) - dstIndex;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2FindNext2[" + super.toString() + ",sid=" + this.sid + ",searchCount=" + Trans2FindFirst2.LIST_SIZE + ",informationLevel=0x" + Hexdump.toHexString(this.informationLevel, 3) + ",resumeKey=0x" + Hexdump.toHexString(this.resumeKey, 4) + ",flags=0x" + Hexdump.toHexString(this.flags, 2) + ",filename=" + this.filename + "]");
    }
}

﻿using System;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Plugin
{
	// Token: 0x0200003C RID: 60
	public class Utils : Base
	{
		// Token: 0x06000337 RID: 823 RVA: 0x00026DD0 File Offset: 0x00024FD0
		public Utils(HttpContext context, string pWorkType, string pTempPath) : base(context)
		{
			this.workType = pWorkType;
			this.tempPath = pTempPath;
		}

		// Token: 0x06000338 RID: 824 RVA: 0x00026DF4 File Offset: 0x00024FF4
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			if (!base.CheckCaller("ieplugin"))
			{
				base.ClientResponse("[FAIL]", "error|013|Bad Request Type", "");
				return null;
			}
			this.tempPath = base.GetTempPath(this.tempPath);
			string a;
			if ((a = this.workType) != null)
			{
				if (!(a == "ProcessCheckFileSize"))
				{
					if (!(a == "ProcessGetPartialSize"))
					{
						if (!(a == "ProcessPartialDelete"))
						{
							if (a == "ProcessDeleteFile")
							{
								this.ProcessDeleteFile();
							}
						}
						else
						{
							this.ProcessPartialDelete();
						}
					}
					else
					{
						this.ProcessGetPartialSize();
					}
				}
				else
				{
					this.ProcessCheckFileSize();
				}
			}
			return null;
		}

		// Token: 0x06000339 RID: 825 RVA: 0x00026E98 File Offset: 0x00025098
		private void ProcessCheckFileSize()
		{
			string[] fiAry = this._entity_dextParam.fiAry;
			if (fiAry == null)
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			if (fiAry.Length != 1)
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			fiAry[0] = HttpUtility.UrlDecode(fiAry[0], Encoding.UTF8);
			string text = fiAry[0];
			text = text.Replace("http://", "");
			text = text.Replace("https://", "");
			text = text.Substring(text.IndexOf("/"));
			text = this.hContext.Request.MapPath(text);
			if (File.Exists(text))
			{
				FileInfo fileInfo = new FileInfo(text);
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[OK]" + Dext5Parameter.MakeParameter(fileInfo.Length.ToString()));
				return;
			}
			base.ClientResponse("[FAIL]", "error|010|Not found file on server", "");
		}

		// Token: 0x0600033A RID: 826 RVA: 0x00026FA8 File Offset: 0x000251A8
		private void ProcessGetPartialSize()
		{
			string g = this._entity_dextParam.g;
			if (string.IsNullOrEmpty(g))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			string text = base.FileLocationInfoReadWrite("R", g, "");
			if (File.Exists(text))
			{
				FileInfo fileInfo = new FileInfo(text);
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[OK]" + Dext5Parameter.MakeParameter(fileInfo.Length.ToString()));
				return;
			}
			base.ClientResponse("[FAIL]", "error|010|Not found file on server", "");
		}

		// Token: 0x0600033B RID: 827 RVA: 0x00027054 File Offset: 0x00025254
		private void ProcessPartialDelete()
		{
			string g = this._entity_dextParam.g;
			if (string.IsNullOrEmpty(g))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			string path = base.FileLocationInfoReadWrite("R", g, "");
			if (Directory.Exists(path))
			{
				base.ForceDeleteDirectory(path);
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[OK]");
				return;
			}
			base.ClientResponse("[FAIL]", "error|010|Not found file on server", "");
		}

		// Token: 0x0600033C RID: 828 RVA: 0x000270E8 File Offset: 0x000252E8
		private void ProcessDeleteFile()
		{
			string p = this._entity_dextParam.p;
			if (string.IsNullOrEmpty(p))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			if (File.Exists(p))
			{
				File.Delete(p);
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[OK]");
				return;
			}
			if (File.Exists(this.hContext.Request.MapPath(p)))
			{
				File.Delete(this.hContext.Request.MapPath(p));
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[OK]");
				return;
			}
			base.ClientResponse("[FAIL]", "error|010|Not found file on server", "");
		}

		// Token: 0x040001C9 RID: 457
		private string workType = string.Empty;
	}
}

package org.apache.struts.taglib.nested;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.BodyTagSupport;
import org.apache.struts.util.ResponseUtils;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/NestedPropertyTag.class */
public class NestedPropertyTag extends BodyTagSupport implements NestedNameSupport {
    private String property = null;
    private String originalNest = null;
    private String originalName = null;
    private String originalProperty = null;

    @Override // org.apache.struts.taglib.nested.NestedNameSupport
    public String getName() {
        return null;
    }

    @Override // org.apache.struts.taglib.nested.NestedNameSupport
    public void setName(String newNamed) {
    }

    @Override // org.apache.struts.taglib.nested.NestedPropertySupport
    public String getProperty() {
        return this.property;
    }

    @Override // org.apache.struts.taglib.nested.NestedPropertySupport
    public void setProperty(String newProperty) {
        this.property = newProperty;
    }

    public int doStartTag() throws JspException {
        this.originalProperty = this.property;
        HttpServletRequest request = this.pageContext.getRequest();
        this.originalNest = NestedPropertyHelper.getCurrentProperty(request);
        this.originalName = NestedPropertyHelper.getCurrentName(request, this);
        String nested = NestedPropertyHelper.getAdjustedProperty(request, this.originalProperty);
        NestedPropertyHelper.setProperty(request, nested);
        NestedPropertyHelper.setName(request, this.originalName);
        return 2;
    }

    public int doAfterBody() throws JspException {
        if (this.bodyContent != null) {
            ResponseUtils.writePrevious(this.pageContext, this.bodyContent.getString());
            this.bodyContent.clearBody();
            return 0;
        }
        return 0;
    }

    public int doEndTag() throws JspException {
        HttpServletRequest request = this.pageContext.getRequest();
        if (this.originalNest == null) {
            NestedPropertyHelper.deleteReference(request);
        } else {
            NestedPropertyHelper.setName(request, this.originalName);
            NestedPropertyHelper.setProperty(request, this.originalNest);
        }
        this.property = this.originalProperty;
        return 6;
    }

    public void release() {
        super.release();
        this.property = null;
        this.originalNest = null;
        this.originalName = null;
        this.originalProperty = null;
    }
}

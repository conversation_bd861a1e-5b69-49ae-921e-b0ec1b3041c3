package jcifs.dcerpc.msrpc;

import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import jcifs.dcerpc.msrpc.srvsvc;
import jcifs.smb.ACE;
import jcifs.smb.SecurityDescriptor;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/MsrpcShareGetInfo.class */
public class MsrpcShareGetInfo extends srvsvc.ShareGetInfo {
    public MsrpcShareGetInfo(String server, String sharename) {
        super(server, sharename, HttpServletResponse.SC_BAD_GATEWAY, new srvsvc.ShareInfo502());
        this.ptype = 0;
        this.flags = 3;
    }

    public ACE[] getSecurity() throws IOException {
        srvsvc.ShareInfo502 info502 = (srvsvc.ShareInfo502) this.info;
        if (info502.security_descriptor != null) {
            SecurityDescriptor sd = new SecurityDescriptor(info502.security_descriptor, 0, info502.sd_size);
            return sd.aces;
        }
        return null;
    }
}

/*
Copyright (C) NAVER corp.  

This library is free software; you can redistribute it and/or  
modify it under the terms of the GNU Lesser General Public  
License as published by the Free Software Foundation; either  
version 2.1 of the License, or (at your option) any later version.  

This library is distributed in the hope that it will be useful,  
but WITHOUT ANY WARRANTY; without even the implied warranty of  
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU  
Lesser General Public License for more details.  

You should have received a copy of the GNU Lesser General Public  
License along with this library; if not, write to the Free Software  
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA  
*/
if(typeof window.nhn=='undefined'){window.nhn = {};}
/**
 * @fileOverview This file contains a message mapping(Korean), which is used to map the message code to the actual message
 * @name husky_SE2B_Lang_ko_KR.js
 * @ unescape
 */
var oMessageMap = {
	'SE_EditingAreaManager.onExit' : '내용이 변경되었습니다.',
	'SE_Color.invalidColorCode' : '색상 코드를 올바르게 입력해 주세요. \n\n 예) #000000, #FF0000, #FFFFFF, #ffffff, ffffff',
	'SE_Hyperlink.invalidURL' : '입력하신 URL이 올바르지 않습니다.',
	'SE_FindReplace.keywordMissing' : '찾으실 단어를 입력해 주세요.',
	'SE_FindReplace.keywordNotFound' : '찾으실 단어가 없습니다.',
	'SE_FindReplace.replaceAllResultP1' : '일치하는 내용이 총 ',
	'SE_FindReplace.replaceAllResultP2' : '건 바뀌었습니다.',
	'SE_FindReplace.notSupportedBrowser' : '현재 사용하고 계신 브라우저에서는 사용하실수 없는 기능입니다.\n\n이용에 불편을 드려 죄송합니다.',
	'SE_FindReplace.replaceKeywordNotFound' : '바뀔 단어가 없습니다',
	'SE_LineHeight.invalidLineHeight' : '잘못된 값입니다.',
	'SE_Footnote.defaultText' : '각주내용을 입력해 주세요',
	'SE.failedToLoadFlash' : '플래시가 차단되어 있어 해당 기능을 사용할 수 없습니다.',
	'SE2M_EditingModeChanger.confirmTextMode' : '텍스트 모드로 전환하면 작성된 내용은 유지되나, \n\n글꼴 등의 편집효과와 이미지 등의 첨부내용이 모두 사라지게 됩니다.\n\n전환하시겠습니까?',
	'SE2M_FontNameWithLayerUI.sSampleText' : '가나다라'
};
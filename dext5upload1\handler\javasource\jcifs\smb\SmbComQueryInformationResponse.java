package jcifs.smb;

import java.util.Date;
import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComQueryInformationResponse.class */
class SmbComQueryInformationResponse extends ServerMessageBlock implements Info {
    private long serverTimeZoneOffset;
    private int fileAttributes = 0;
    private long lastWriteTime = 0;
    private int fileSize = 0;

    SmbComQueryInformationResponse(long serverTimeZoneOffset) {
        this.serverTimeZoneOffset = serverTimeZoneOffset;
        this.command = (byte) 8;
    }

    @Override // jcifs.smb.Info
    public int getAttributes() {
        return this.fileAttributes;
    }

    @Override // jcifs.smb.Info
    public long getCreateTime() {
        return this.lastWriteTime + this.serverTimeZoneOffset;
    }

    @Override // jcifs.smb.Info
    public long getLastWriteTime() {
        return this.lastWriteTime + this.serverTimeZoneOffset;
    }

    @Override // jcifs.smb.Info
    public long getSize() {
        return this.fileSize;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        if (this.wordCount == 0) {
            return 0;
        }
        this.fileAttributes = readInt2(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 2;
        this.lastWriteTime = readUTime(buffer, bufferIndex2);
        this.fileSize = readInt4(buffer, bufferIndex2 + 4);
        return 20;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComQueryInformationResponse[" + super.toString() + ",fileAttributes=0x" + Hexdump.toHexString(this.fileAttributes, 4) + ",lastWriteTime=" + new Date(this.lastWriteTime) + ",fileSize=" + this.fileSize + "]");
    }
}

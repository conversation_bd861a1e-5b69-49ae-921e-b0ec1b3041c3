<%@page import="site.unp.core.util.WebUtil"%>
<%@page import="java.util.Map"%>
<%@page import="java.io.File"%>
<%@page import="java.util.UUID"%>
<%@page import="java.io.InputStream" %>
<%@page import="java.io.OutputStream" %>
<%@page import="java.io.FileOutputStream" %>
<%@page import="java.util.Iterator"%>
<%@page import="org.apache.commons.fileupload.FileItem"%>
<%@page import="java.util.List"%>

<%@page import="org.apache.commons.fileupload.servlet.ServletFileUpload"%>
<%@page import="org.apache.commons.fileupload.disk.DiskFileItemFactory"%>
<%@ taglib uri="http://bibeault.org/tld/ccc" prefix="ccc" %>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%


/**
public static String filePathBlackList(String value) {
    String returnValue = value;
    if (returnValue == null || returnValue.trim().equals("")) {
        return "";
    }

    returnValue = returnValue.replaceAll("\\.\\./", ""); // ../
    returnValue = returnValue.replaceAll("\\.\\.\\\\", ""); // ..\

    return returnValue;
}
*/

String sFileInfo = "";
String name = request.getHeader("file-name");
String ext = name.substring(name.lastIndexOf(".")+1);

org.springframework.context.ApplicationContext context = org.springframework.web.context.support.WebApplicationContextUtils.getWebApplicationContext(getServletContext());
site.unp.core.conf.GlobalsProperties globalsProperties = (site.unp.core.conf.GlobalsProperties)context.getBean("globalsProperties");

String path = globalsProperties.getEditorUploadPath() + File.separator;
path = WebUtil.filePathBlackList(path);
File file = new File(path);
if(!file.exists()) {
  file.mkdirs();
}
String realname = UUID.randomUUID().toString() + "." + ext;
InputStream is = request.getInputStream();
OutputStream os=new FileOutputStream(path + realname);
int numRead;
//파일쓰기
byte b[] = new byte[Integer.parseInt(request.getHeader("file-size"))];
while((numRead = is.read(b,0,b.length)) != -1){
  os.write(b,0,numRead);
}
if(is != null) {
  is.close();
}
os.flush();
os.close();
sFileInfo += "&bNewLine=true&sFileName="+ name+"&sFileURL="+"/editorUpload/"+realname;
out.println(sFileInfo);
%>
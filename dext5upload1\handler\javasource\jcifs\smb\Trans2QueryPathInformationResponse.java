package jcifs.smb;

import java.util.Date;
import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryPathInformationResponse.class */
class Trans2QueryPathInformationResponse extends SmbComTransactionResponse {
    static final int SMB_QUERY_FILE_BASIC_INFO = 257;
    static final int SMB_QUERY_FILE_STANDARD_INFO = 258;
    private int informationLevel;
    Info info;

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryPathInformationResponse$SmbQueryFileBasicInfo.class */
    class SmbQueryFileBasicInfo implements Info {
        long createTime;
        long lastAccessTime;
        long lastWriteTime;
        long changeTime;
        int attributes;

        SmbQueryFileBasicInfo() {
        }

        @Override // jcifs.smb.Info
        public int getAttributes() {
            return this.attributes;
        }

        @Override // jcifs.smb.Info
        public long getCreateTime() {
            return this.createTime;
        }

        @Override // jcifs.smb.Info
        public long getLastWriteTime() {
            return this.lastWriteTime;
        }

        @Override // jcifs.smb.Info
        public long getSize() {
            return 0L;
        }

        public String toString() {
            return new String("SmbQueryFileBasicInfo[createTime=" + new Date(this.createTime) + ",lastAccessTime=" + new Date(this.lastAccessTime) + ",lastWriteTime=" + new Date(this.lastWriteTime) + ",changeTime=" + new Date(this.changeTime) + ",attributes=0x" + Hexdump.toHexString(this.attributes, 4) + "]");
        }
    }

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryPathInformationResponse$SmbQueryFileStandardInfo.class */
    class SmbQueryFileStandardInfo implements Info {
        long allocationSize;
        long endOfFile;
        int numberOfLinks;
        boolean deletePending;
        boolean directory;

        SmbQueryFileStandardInfo() {
        }

        @Override // jcifs.smb.Info
        public int getAttributes() {
            return 0;
        }

        @Override // jcifs.smb.Info
        public long getCreateTime() {
            return 0L;
        }

        @Override // jcifs.smb.Info
        public long getLastWriteTime() {
            return 0L;
        }

        @Override // jcifs.smb.Info
        public long getSize() {
            return this.endOfFile;
        }

        public String toString() {
            return new String("SmbQueryInfoStandard[allocationSize=" + this.allocationSize + ",endOfFile=" + this.endOfFile + ",numberOfLinks=" + this.numberOfLinks + ",deletePending=" + this.deletePending + ",directory=" + this.directory + "]");
        }
    }

    Trans2QueryPathInformationResponse(int informationLevel) {
        this.informationLevel = informationLevel;
        this.subCommand = (byte) 5;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 2;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        switch (this.informationLevel) {
            case SMB_QUERY_FILE_BASIC_INFO /* 257 */:
                return readSmbQueryFileBasicInfoWireFormat(buffer, bufferIndex);
            case SMB_QUERY_FILE_STANDARD_INFO /* 258 */:
                return readSmbQueryFileStandardInfoWireFormat(buffer, bufferIndex);
            default:
                return 0;
        }
    }

    int readSmbQueryFileStandardInfoWireFormat(byte[] buffer, int bufferIndex) {
        SmbQueryFileStandardInfo info = new SmbQueryFileStandardInfo();
        info.allocationSize = readInt8(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 8;
        info.endOfFile = readInt8(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 8;
        info.numberOfLinks = readInt4(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 4;
        int bufferIndex5 = bufferIndex4 + 1;
        info.deletePending = (buffer[bufferIndex4] & 255) > 0;
        int bufferIndex6 = bufferIndex5 + 1;
        info.directory = (buffer[bufferIndex5] & 255) > 0;
        this.info = info;
        return bufferIndex6 - bufferIndex;
    }

    int readSmbQueryFileBasicInfoWireFormat(byte[] buffer, int bufferIndex) {
        SmbQueryFileBasicInfo info = new SmbQueryFileBasicInfo();
        info.createTime = readTime(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 8;
        info.lastAccessTime = readTime(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 8;
        info.lastWriteTime = readTime(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 8;
        info.changeTime = readTime(buffer, bufferIndex4);
        int bufferIndex5 = bufferIndex4 + 8;
        info.attributes = readInt2(buffer, bufferIndex5);
        this.info = info;
        return (bufferIndex5 + 2) - bufferIndex;
    }

    @Override // jcifs.smb.SmbComTransactionResponse, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2QueryPathInformationResponse[" + super.toString() + "]");
    }
}

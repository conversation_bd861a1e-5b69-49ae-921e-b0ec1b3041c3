﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: SetSelectItemEx</h3>
    <p class="ttl">void SetSelectItemEx(uniqueKey, appendMode, uploadID)</p>
    <p class="txt">
        업로드 영역 외부에서 파일을 선택합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">uniqueKey</span>&nbsp;&nbsp;선택하려는 파일의 uniqueKey 값을 의미합니다.<br/>
        <span style="padding-left:65px">(DEXT5 Upload API인 AddUploadedFile의 첫번째 파라미터 값)을 의미합니다.</span><br/>
        <span class="firebrick">appendMode</span>&nbsp;&nbsp;0 – 기존 선택된 파일 선택해제<br /><span style="padding-left:77px"></span>1 – 기존 선택된 파일 유지<br/>
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;적용할 업로드의 id값을 의미합니다.<br />
        <br /><strong>uniqueKey가 빈 값일 경우</strong><br/>
        <span class="firebrick">appendMode</span>&nbsp;&nbsp;0 – 전체 파일 선택해제<br /><span style="padding-left:77px"></span>1 – 전체 파일 선택       
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function setSelectItemEx() {
            DEXT5UPLOAD.SetSelectItemEx('1', '0', 'upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


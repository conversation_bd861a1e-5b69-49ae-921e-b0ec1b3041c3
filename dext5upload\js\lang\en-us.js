var Dext5Upload_Lang={file_name:"File Name",file_size:"File Size",file_maximum:"<PERSON>",file_maximum_limit:"Limited to {0}",file_no_limit:"Unlimited size",file_unit:"files",file_inserted:"added",uploading:"Files Uploading...",total_upload:"Total Upload",upload_file:"Send File",upload_size:"Send Size",upload_speed:"Speed",upload_remainingtime:"Remain",upload_timeunit:{day:"Day",hour:"H",min:"min",sec:"sec"},upload_status:{status:"Status",wait:"Waiting",uploading:"Uploading",merge:"Merge",uploaded:"Uploaded"},
upload_pause:"Stop Upload",upload_resume:"Restart",upload_cancel:"Cancel",upload_close:"Close",get_size:"Processing",message_drag_dbclick:"<span>Double-click</span> or <span>drag files</span> here",message_dbclick:"<span>Double-click</span> here",message_drag_drop:"<span>Drag files</span> here",message_insert_file:"Please add the file",message_size_zero:"You can not add the file size 0byte file.",message_size_zeros:"Failed to add {0} files 0 Byte file.",message_not_allow_ext:"extension is not allowed. \n\nAllowed Extensions : {0}",
message_not_limit_ext:"extension is not allowed. \n\nLimited Extensions : {0}",message_not_allow_exts:"{0} file extension is not allowed.\n\nAllowed extensions : {1}",message_not_limit_exts:"{0} file extension is not allowed.\n\nLimited Extensions : {1}",message_limit_num:"Only upload files",message_limit_size:"Only upload",message_limit_one_size:"Files larger than {0} can not be uploaded.",message_limit_one_sizes:"More than over {0} failed to add {1} files.",message_duplication_file:"Duplicate files",
message_all_remove:"Are you sure you want to remove total items?",message_selected_remove:"Are you sure you want to remove the selected items?",message_upload_cancel:"This file is being transferred.\nAre you sure you want to cancel the transfer?",message_large_files_size:"[{0}] You have exceeded the maximum attachment size. The maximum attachment size is available {1}.",message_large_files_count:"[{0}] A maximum of {1} files can be uploaded.",message_wrong_approach:"The wrong approach.",message_file_unchosen:"There is no file selected.",
message_file_notexist:"File does not exist.",message_file_ext_detect:"{0} file(s) extension has been detected modulation.",message_file_ext_detect_html4:"The modulation file extension has been detected during the upload.",btn_add:"Add File",btn_add_folder:"Add Folder",btn_send:"Send",btn_remove:"Remove",btn_remove_All:"Remove All",btn_download:"Download",btn_download_All:"Download All",btn_open:"Open",btn_saveandopen:"Save and Open",btn_saveandfolderopen:"Save and Folder Open",btn_print:"Print",btn_move_first:"Move to First",
btn_move_forward:"Forward",btn_move_backward:"Backward",btn_move_end:"Move to End",btn_download_zipfile:"Download(zip)",btn_download_all_zipfile:"Download All(zip)",error_info:{error_code_200001:"Error occurred reading the Upload Settings",error_code_200003:"Error occurred reading the css",error_code_200004:"Error occurred during upload",error_code_200005:"Upload worker error",error_code_200006:"Error occurred when slice file",error_code_200007:"Error has occurred in the File Worker",error_code_200008:"Error occurred during multi-upload",
error_code_200009:"Error has occurred in a multi-upload Worker",error_code_200010:"Error occurred when the slice of the multi-file upload",error_code_200011:"Error has occurred in the multi-file upload Worker",error_code_200012:"Error occurred during download",error_code_200013:"Web Socket connection failed",error_code_200014:"Exceeds the queue limit the number of files",error_code_200015:"Exceeded the size limit of the file",error_code_200016:"Invalid file type",error_code_200017:"Exceeded the size limit for the file to be added to the queue",
error_code_001:"Wrong operation type",error_code_002:"Wrong operation type",error_code_003:"There is no chunk file upload",error_code_004:"Error occurred while uploading progress",error_code_005:"Chunk file number is less than the total number",error_code_006:"Not found directory of physical path on server",error_code_007:"Not found directory of virtual path on server",error_code_008:"Error occurred while merging files from a server",error_code_009:"Invalid parameter on server",error_code_010:"Can not find the file on the server",
error_code_011:"Can not find the file on the server",error_code_012:"Not allowed file extension",error_code_013:"Bad Request Type",error_code_014:"File is exceeded the max one file size",error_code_015:"Error has occurred in the http module",error_code_016:"Error has occurred in the progress module",error_code_017:"0 byte files can not be transferred.",error_code_018:"There does not allow the string included in the file name",error_code_019:"This file is an extension of falsification has been detected.",
error_code_020:"Error occured on the server side",error_code_021:"Multipart has already been accessed",error_code_022:"An error has occurred before Web Sockets uploading",error_code_023:"An error has occurred after Web Sockets uploading",error_code_024:"After the completion of file data transfer and file data before transmitting do not match",error_code_025:"The space of the disk where the file is stored is insufficient",error_code_026:"Files larger than {0} can not be uploaded."},context:{add_file:"Add File",
remove_current_file:"Removes selected item",remove_all_file:"Remove All Items",open_current_file:"Open",download_current_file:"Download selected item",download_all_file:"Download All",move_first:"Move to First",move_forward:"Forward",move_backward:"Backward",move_end:"Move to End",setting:"Setting",save_and_open:"Save and Open",print:"Print",download_zipfile:"Download selected item(zip)",download_all_zipfile:"Download All(zip)"},large_files:"Large Files",upload_information:"Status",upload_progress:"Progress",
folder_count:"Subfolders",folder_file_count:"files",install_guide:{header:"DEXT5 Hybrid Installation",desc1:"Install the program for the file transfer service.",desc2:'Please run <span id="file_name">DEXT5Hybrid50.exe</span> downloaded automatically.',desc3:'If the program does not download, please click to install the <a id="download_link" href="#" class="link_download">[download]</a>.',desc4:"When the installation is complete, the window closes automatically.",agent_info:'You\u2019re using <span id="browser_info">Chrome 49</span> in <span id="os_info">Windows 10</span>.'},
plugin_install_guide:{header:"DEXT5 Plugin Installation",desc1:"Install the program for the file transfer service.",desc2:'Please run <span id="file_name">{0}</span> downloaded automatically.',desc3:'If the program does not download, please click to install the <a id="download_link" href="#" class="link_download">[download]</a>.',desc4:"When the installation is complete, the window closes automatically.",agent_info:'You\u2019re using <span id="browser_info">Chrome 49</span> in <span id="os_info">Windows 10</span>.'},
file_type_all_description:"All Files",file_type_description:"Files"};

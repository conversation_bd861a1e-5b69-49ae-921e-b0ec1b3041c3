﻿using System;
using System.Web;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x02000031 RID: 49
	internal class UploadWorkerRequest : HttpWorkerRequest
	{
		// Token: 0x060002C4 RID: 708 RVA: 0x00021225 File Offset: 0x0001F425
		public UploadWorkerRequest(HttpWorkerRequest request, byte[] buffer)
		{
			this.m_buffer = buffer;
			this.m_request = request;
		}

		// Token: 0x060002C5 RID: 709 RVA: 0x0002123B File Offset: 0x0001F43B
		public override int ReadEntityBody(byte[] buffer, int size)
		{
			return this.ReadEntityBody(buffer, 0, size);
		}

		// Token: 0x060002C6 RID: 710 RVA: 0x00021246 File Offset: 0x0001F446
		public override int GetTotalEntityBodyLength()
		{
			if (this.m_buffer == null)
			{
				return 0;
			}
			return this.m_buffer.Length;
		}

		// Token: 0x060002C7 RID: 711 RVA: 0x0002125A File Offset: 0x0001F45A
		public override byte[] GetPreloadedEntityBody()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_buffer;
		}

		// Token: 0x060002C8 RID: 712 RVA: 0x0002126C File Offset: 0x0001F46C
		public override int GetPreloadedEntityBody(byte[] buffer, int offset)
		{
			if (this.m_buffer == null)
			{
				return 0;
			}
			Buffer.BlockCopy(this.m_buffer, 0, buffer, offset, this.m_buffer.Length);
			return this.m_buffer.Length;
		}

		// Token: 0x060002C9 RID: 713 RVA: 0x00021296 File Offset: 0x0001F496
		public override int GetPreloadedEntityBodyLength()
		{
			if (this.m_buffer == null)
			{
				return 0;
			}
			return this.m_buffer.Length;
		}

		// Token: 0x060002CA RID: 714 RVA: 0x000212AA File Offset: 0x0001F4AA
		public override int ReadEntityBody(byte[] buffer, int offset, int size)
		{
			return 0;
		}

		// Token: 0x060002CB RID: 715 RVA: 0x000212B0 File Offset: 0x0001F4B0
		public override string GetKnownRequestHeader(int index)
		{
			if (index != 11)
			{
				if (this.m_request == null)
				{
					return null;
				}
				return this.m_request.GetKnownRequestHeader(index);
			}
			else
			{
				if (this.m_buffer == null)
				{
					return null;
				}
				return this.m_buffer.Length.ToString();
			}
		}

		// Token: 0x060002CC RID: 716 RVA: 0x000212F3 File Offset: 0x0001F4F3
		public override bool IsEntireEntityBodyIsPreloaded()
		{
			return this.m_request != null && this.m_request.IsEntireEntityBodyIsPreloaded();
		}

		// Token: 0x060002CD RID: 717 RVA: 0x0002130A File Offset: 0x0001F50A
		public override void CloseConnection()
		{
			if (this.m_request != null)
			{
				this.m_request.CloseConnection();
			}
		}

		// Token: 0x060002CE RID: 718 RVA: 0x0002131F File Offset: 0x0001F51F
		public override void EndOfRequest()
		{
			if (this.m_request != null)
			{
				this.m_request.EndOfRequest();
			}
		}

		// Token: 0x060002CF RID: 719 RVA: 0x00021334 File Offset: 0x0001F534
		public override bool Equals(object obj)
		{
			if (this.m_request == null)
			{
				return obj == null;
			}
			return this.m_request.Equals(obj);
		}

		// Token: 0x060002D0 RID: 720 RVA: 0x0002134F File Offset: 0x0001F54F
		public override void FlushResponse(bool finalFlush)
		{
			if (this.m_request != null)
			{
				this.m_request.FlushResponse(finalFlush);
			}
		}

		// Token: 0x060002D1 RID: 721 RVA: 0x00021365 File Offset: 0x0001F565
		public override string GetAppPath()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetAppPath();
		}

		// Token: 0x060002D2 RID: 722 RVA: 0x0002137C File Offset: 0x0001F57C
		public override string GetAppPathTranslated()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetAppPathTranslated();
		}

		// Token: 0x060002D3 RID: 723 RVA: 0x00021393 File Offset: 0x0001F593
		public override string GetAppPoolID()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetAppPoolID();
		}

		// Token: 0x060002D4 RID: 724 RVA: 0x000213AA File Offset: 0x0001F5AA
		public override long GetBytesRead()
		{
			if (this.m_request == null)
			{
				return 0L;
			}
			return this.m_request.GetBytesRead();
		}

		// Token: 0x060002D5 RID: 725 RVA: 0x000213C2 File Offset: 0x0001F5C2
		public override byte[] GetClientCertificate()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetClientCertificate();
		}

		// Token: 0x060002D6 RID: 726 RVA: 0x000213D9 File Offset: 0x0001F5D9
		public override byte[] GetClientCertificateBinaryIssuer()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetClientCertificateBinaryIssuer();
		}

		// Token: 0x060002D7 RID: 727 RVA: 0x000213F0 File Offset: 0x0001F5F0
		public override int GetClientCertificateEncoding()
		{
			if (this.m_request == null)
			{
				return 0;
			}
			return this.m_request.GetClientCertificateEncoding();
		}

		// Token: 0x060002D8 RID: 728 RVA: 0x00021407 File Offset: 0x0001F607
		public override byte[] GetClientCertificatePublicKey()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetClientCertificatePublicKey();
		}

		// Token: 0x060002D9 RID: 729 RVA: 0x0002141E File Offset: 0x0001F61E
		public override DateTime GetClientCertificateValidFrom()
		{
			if (this.m_request == null)
			{
				return DateTime.Now;
			}
			return this.m_request.GetClientCertificateValidFrom();
		}

		// Token: 0x060002DA RID: 730 RVA: 0x00021439 File Offset: 0x0001F639
		public override DateTime GetClientCertificateValidUntil()
		{
			if (this.m_request == null)
			{
				return DateTime.Now;
			}
			return this.m_request.GetClientCertificateValidUntil();
		}

		// Token: 0x060002DB RID: 731 RVA: 0x00021454 File Offset: 0x0001F654
		public override long GetConnectionID()
		{
			if (this.m_request == null)
			{
				return 0L;
			}
			return this.m_request.GetConnectionID();
		}

		// Token: 0x060002DC RID: 732 RVA: 0x0002146C File Offset: 0x0001F66C
		public override string GetFilePath()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetFilePath();
		}

		// Token: 0x060002DD RID: 733 RVA: 0x00021483 File Offset: 0x0001F683
		public override string GetFilePathTranslated()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetFilePathTranslated();
		}

		// Token: 0x060002DE RID: 734 RVA: 0x0002149A File Offset: 0x0001F69A
		public override int GetHashCode()
		{
			if (this.m_request == null)
			{
				return 0;
			}
			return this.m_request.GetHashCode();
		}

		// Token: 0x060002DF RID: 735 RVA: 0x000214B1 File Offset: 0x0001F6B1
		public override string GetHttpVerbName()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetHttpVerbName();
		}

		// Token: 0x060002E0 RID: 736 RVA: 0x000214C8 File Offset: 0x0001F6C8
		public override string GetHttpVersion()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetHttpVersion();
		}

		// Token: 0x060002E1 RID: 737 RVA: 0x000214DF File Offset: 0x0001F6DF
		public override string GetLocalAddress()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetLocalAddress();
		}

		// Token: 0x060002E2 RID: 738 RVA: 0x000214F6 File Offset: 0x0001F6F6
		public override int GetLocalPort()
		{
			if (this.m_request == null)
			{
				return 0;
			}
			return this.m_request.GetLocalPort();
		}

		// Token: 0x060002E3 RID: 739 RVA: 0x0002150D File Offset: 0x0001F70D
		public override string GetPathInfo()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetPathInfo();
		}

		// Token: 0x060002E4 RID: 740 RVA: 0x00021524 File Offset: 0x0001F724
		public override string GetProtocol()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetProtocol();
		}

		// Token: 0x060002E5 RID: 741 RVA: 0x0002153B File Offset: 0x0001F73B
		public override string GetQueryString()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetQueryString();
		}

		// Token: 0x060002E6 RID: 742 RVA: 0x00021552 File Offset: 0x0001F752
		public override byte[] GetQueryStringRawBytes()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetQueryStringRawBytes();
		}

		// Token: 0x060002E7 RID: 743 RVA: 0x00021569 File Offset: 0x0001F769
		public override string GetRawUrl()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetRawUrl();
		}

		// Token: 0x060002E8 RID: 744 RVA: 0x00021580 File Offset: 0x0001F780
		public override string GetRemoteAddress()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetRemoteAddress();
		}

		// Token: 0x060002E9 RID: 745 RVA: 0x00021597 File Offset: 0x0001F797
		public override string GetRemoteName()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetRemoteName();
		}

		// Token: 0x060002EA RID: 746 RVA: 0x000215AE File Offset: 0x0001F7AE
		public override int GetRemotePort()
		{
			if (this.m_request == null)
			{
				return 0;
			}
			return this.m_request.GetRemotePort();
		}

		// Token: 0x060002EB RID: 747 RVA: 0x000215C5 File Offset: 0x0001F7C5
		public override int GetRequestReason()
		{
			if (this.m_request == null)
			{
				return 0;
			}
			return this.m_request.GetRequestReason();
		}

		// Token: 0x060002EC RID: 748 RVA: 0x000215DC File Offset: 0x0001F7DC
		public override string GetServerName()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetServerName();
		}

		// Token: 0x060002ED RID: 749 RVA: 0x000215F3 File Offset: 0x0001F7F3
		public override string GetServerVariable(string name)
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetServerVariable(name);
		}

		// Token: 0x060002EE RID: 750 RVA: 0x0002160B File Offset: 0x0001F80B
		public override string GetUnknownRequestHeader(string name)
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetUnknownRequestHeader(name);
		}

		// Token: 0x060002EF RID: 751 RVA: 0x00021623 File Offset: 0x0001F823
		public override string[][] GetUnknownRequestHeaders()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetUnknownRequestHeaders();
		}

		// Token: 0x060002F0 RID: 752 RVA: 0x0002163A File Offset: 0x0001F83A
		public override string GetUriPath()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.GetUriPath();
		}

		// Token: 0x060002F1 RID: 753 RVA: 0x00021651 File Offset: 0x0001F851
		public override long GetUrlContextID()
		{
			if (this.m_request == null)
			{
				return 0L;
			}
			return this.m_request.GetUrlContextID();
		}

		// Token: 0x060002F2 RID: 754 RVA: 0x00021669 File Offset: 0x0001F869
		public override IntPtr GetUserToken()
		{
			if (this.m_request == null)
			{
				return IntPtr.Zero;
			}
			return this.m_request.GetUserToken();
		}

		// Token: 0x060002F3 RID: 755 RVA: 0x00021684 File Offset: 0x0001F884
		public override IntPtr GetVirtualPathToken()
		{
			if (this.m_request == null)
			{
				return IntPtr.Zero;
			}
			return this.m_request.GetVirtualPathToken();
		}

		// Token: 0x060002F4 RID: 756 RVA: 0x0002169F File Offset: 0x0001F89F
		public override bool HeadersSent()
		{
			return this.m_request != null && this.m_request.HeadersSent();
		}

		// Token: 0x060002F5 RID: 757 RVA: 0x000216B6 File Offset: 0x0001F8B6
		public override bool IsClientConnected()
		{
			return this.m_request != null && this.m_request.IsClientConnected();
		}

		// Token: 0x060002F6 RID: 758 RVA: 0x000216CD File Offset: 0x0001F8CD
		public override bool IsSecure()
		{
			return this.m_request != null && this.m_request.IsSecure();
		}

		// Token: 0x060002F7 RID: 759 RVA: 0x000216E4 File Offset: 0x0001F8E4
		public override string MapPath(string virtualPath)
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.MapPath(virtualPath);
		}

		// Token: 0x060002F8 RID: 760 RVA: 0x000216FC File Offset: 0x0001F8FC
		public override void SendCalculatedContentLength(int contentLength)
		{
			if (this.m_request != null)
			{
				this.m_request.SendCalculatedContentLength(contentLength);
			}
		}

		// Token: 0x060002F9 RID: 761 RVA: 0x00021712 File Offset: 0x0001F912
		public override void SendCalculatedContentLength(long contentLength)
		{
			if (this.m_request != null)
			{
				this.m_request.SendCalculatedContentLength(contentLength);
			}
		}

		// Token: 0x060002FA RID: 762 RVA: 0x00021728 File Offset: 0x0001F928
		public override void SendKnownResponseHeader(int index, string value)
		{
			this.m_request.SendKnownResponseHeader(index, value);
		}

		// Token: 0x060002FB RID: 763 RVA: 0x00021737 File Offset: 0x0001F937
		public override void SendResponseFromFile(IntPtr handle, long offset, long length)
		{
			if (this.m_request != null)
			{
				this.m_request.SendResponseFromFile(handle, offset, length);
			}
		}

		// Token: 0x060002FC RID: 764 RVA: 0x0002174F File Offset: 0x0001F94F
		public override void SendResponseFromFile(string filename, long offset, long length)
		{
			this.m_request.SendResponseFromFile(filename, offset, length);
		}

		// Token: 0x060002FD RID: 765 RVA: 0x0002175F File Offset: 0x0001F95F
		public override void SendResponseFromMemory(byte[] data, int length)
		{
			if (this.m_request != null)
			{
				this.m_request.SendResponseFromMemory(data, length);
			}
		}

		// Token: 0x060002FE RID: 766 RVA: 0x00021776 File Offset: 0x0001F976
		public override void SendResponseFromMemory(IntPtr data, int length)
		{
			if (this.m_request != null)
			{
				this.m_request.SendResponseFromMemory(data, length);
			}
		}

		// Token: 0x060002FF RID: 767 RVA: 0x0002178D File Offset: 0x0001F98D
		public override void SendStatus(int statusCode, string statusDescription)
		{
			if (this.m_request != null)
			{
				this.m_request.SendStatus(statusCode, statusDescription);
			}
		}

		// Token: 0x06000300 RID: 768 RVA: 0x000217A4 File Offset: 0x0001F9A4
		public override void SendUnknownResponseHeader(string name, string value)
		{
			if (this.m_request != null)
			{
				this.m_request.SendUnknownResponseHeader(name, value);
			}
		}

		// Token: 0x06000301 RID: 769 RVA: 0x000217BB File Offset: 0x0001F9BB
		public override void SetEndOfSendNotification(HttpWorkerRequest.EndOfSendNotification callback, object extraData)
		{
			if (this.m_request != null)
			{
				this.m_request.SetEndOfSendNotification(callback, extraData);
			}
		}

		// Token: 0x06000302 RID: 770 RVA: 0x000217D2 File Offset: 0x0001F9D2
		public override string ToString()
		{
			if (this.m_request == null)
			{
				return null;
			}
			return this.m_request.ToString();
		}

		// Token: 0x170000EF RID: 239
		// (get) Token: 0x06000303 RID: 771 RVA: 0x000217E9 File Offset: 0x0001F9E9
		public override string MachineConfigPath
		{
			get
			{
				if (this.m_request == null)
				{
					return null;
				}
				return this.m_request.MachineConfigPath;
			}
		}

		// Token: 0x170000F0 RID: 240
		// (get) Token: 0x06000304 RID: 772 RVA: 0x00021800 File Offset: 0x0001FA00
		public override string MachineInstallDirectory
		{
			get
			{
				if (this.m_request == null)
				{
					return null;
				}
				return this.m_request.MachineInstallDirectory;
			}
		}

		// Token: 0x170000F1 RID: 241
		// (get) Token: 0x06000305 RID: 773 RVA: 0x00021817 File Offset: 0x0001FA17
		public override Guid RequestTraceIdentifier
		{
			get
			{
				if (this.m_request == null)
				{
					return Guid.Empty;
				}
				return this.m_request.RequestTraceIdentifier;
			}
		}

		// Token: 0x170000F2 RID: 242
		// (get) Token: 0x06000306 RID: 774 RVA: 0x00021832 File Offset: 0x0001FA32
		public override string RootWebConfigPath
		{
			get
			{
				if (this.m_request == null)
				{
					return null;
				}
				return this.m_request.RootWebConfigPath;
			}
		}

		// Token: 0x04000190 RID: 400
		private readonly HttpWorkerRequest m_request;

		// Token: 0x04000191 RID: 401
		private readonly byte[] m_buffer;
	}
}

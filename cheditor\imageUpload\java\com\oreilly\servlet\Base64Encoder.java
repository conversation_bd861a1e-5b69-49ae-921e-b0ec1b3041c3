package com.oreilly.servlet;

import java.io.ByteArrayOutputStream;
import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;

/* loaded from: cos.jar:com/oreilly/servlet/Base64Encoder.class */
public class Base64Encoder extends FilterOutputStream {
    private static final char[] chars = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/'};
    private int charCount;
    private int carryOver;

    public Base64Encoder(OutputStream outputStream) {
        super(outputStream);
    }

    @Override // java.io.FilterOutputStream, java.io.OutputStream
    public void write(int i) throws IOException {
        if (i < 0) {
            i += 256;
        }
        if (this.charCount % 3 == 0) {
            this.carryOver = i & 3;
            this.out.write(chars[i >> 2]);
        } else if (this.charCount % 3 == 1) {
            int i2 = ((this.carryOver << 4) + (i >> 4)) & 63;
            this.carryOver = i & 15;
            this.out.write(chars[i2]);
        } else if (this.charCount % 3 == 2) {
            this.out.write(chars[((this.carryOver << 2) + (i >> 6)) & 63]);
            this.out.write(chars[i & 63]);
            this.carryOver = 0;
        }
        this.charCount++;
        if (this.charCount % 57 == 0) {
            this.out.write(10);
        }
    }

    @Override // java.io.FilterOutputStream, java.io.OutputStream
    public void write(byte[] bArr, int i, int i2) throws IOException {
        for (int i3 = 0; i3 < i2; i3++) {
            write(bArr[i + i3]);
        }
    }

    @Override // java.io.FilterOutputStream, java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        if (this.charCount % 3 == 1) {
            this.out.write(chars[(this.carryOver << 4) & 63]);
            this.out.write(61);
            this.out.write(61);
        } else if (this.charCount % 3 == 2) {
            this.out.write(chars[(this.carryOver << 2) & 63]);
            this.out.write(61);
        }
        super.close();
    }

    public static String encode(String str) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream((int) (str.length() * 1.37d));
        Base64Encoder base64Encoder = new Base64Encoder(byteArrayOutputStream);
        byte[] bArr = null;
        try {
            bArr = str.getBytes("8859_1");
        } catch (UnsupportedEncodingException e) {
        }
        try {
            base64Encoder.write(bArr);
            base64Encoder.close();
            return byteArrayOutputStream.toString("8859_1");
        } catch (IOException e2) {
            return null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x005d, code lost:
    
        if (r9 == null) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0060, code lost:
    
        r9.close();
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0065, code lost:
    
        if (r8 == null) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0068, code lost:
    
        r8.close();
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0059, code lost:
    
        throw r12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static void main(java.lang.String[] r7) throws java.lang.Exception {
        /*
            r0 = r7
            int r0 = r0.length
            r1 = 1
            if (r0 == r1) goto Le
            java.io.PrintStream r0 = java.lang.System.err
            java.lang.String r1 = "Usage: java com.oreilly.servlet.Base64Encoder fileToEncode"
            r0.println(r1)
        Le:
            r0 = 0
            r8 = r0
            r0 = 0
            r9 = r0
            com.oreilly.servlet.Base64Encoder r0 = new com.oreilly.servlet.Base64Encoder     // Catch: java.lang.Throwable -> L52
            r1 = r0
            java.io.PrintStream r2 = java.lang.System.out     // Catch: java.lang.Throwable -> L52
            r1.<init>(r2)     // Catch: java.lang.Throwable -> L52
            r8 = r0
            java.io.BufferedInputStream r0 = new java.io.BufferedInputStream     // Catch: java.lang.Throwable -> L52
            r1 = r0
            java.io.FileInputStream r2 = new java.io.FileInputStream     // Catch: java.lang.Throwable -> L52
            r3 = r2
            r4 = r7
            r5 = 0
            r4 = r4[r5]     // Catch: java.lang.Throwable -> L52
            r3.<init>(r4)     // Catch: java.lang.Throwable -> L52
            r1.<init>(r2)     // Catch: java.lang.Throwable -> L52
            r9 = r0
            r0 = 4096(0x1000, float:5.74E-42)
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L52
            r10 = r0
            goto L40
        L38:
            r0 = r8
            r1 = r10
            r2 = 0
            r3 = r11
            r0.write(r1, r2, r3)     // Catch: java.lang.Throwable -> L52
        L40:
            r0 = r9
            r1 = r10
            int r0 = r0.read(r1)     // Catch: java.lang.Throwable -> L52
            r1 = r0
            r11 = r1
            r1 = -1
            if (r0 != r1) goto L38
            r0 = jsr -> L5a
        L4f:
            goto L6e
        L52:
            r12 = move-exception
            r0 = jsr -> L5a
        L57:
            r1 = r12
            throw r1
        L5a:
            r13 = r0
            r0 = r9
            if (r0 == 0) goto L64
            r0 = r9
            r0.close()
        L64:
            r0 = r8
            if (r0 == 0) goto L6c
            r0 = r8
            r0.close()
        L6c:
            ret r13
        L6e:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.oreilly.servlet.Base64Encoder.main(java.lang.String[]):void");
    }
}

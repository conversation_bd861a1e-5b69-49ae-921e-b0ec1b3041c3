package org.apache.commons.fileupload;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

/* loaded from: fileupload.jar:org/apache/commons/fileupload/FileUploadBase.class */
public abstract class FileUploadBase {
    public static final String CONTENT_TYPE = "Content-type";
    public static final String CONTENT_DISPOSITION = "Content-disposition";
    public static final String FORM_DATA = "form-data";
    public static final String ATTACHMENT = "attachment";
    public static final String MULTIPART = "multipart/";
    public static final String MULTIPART_FORM_DATA = "multipart/form-data";
    public static final String MULTIPART_MIXED = "multipart/mixed";
    public static final int MAX_HEADER_SIZE = 1024;
    private long sizeMax = -1;
    private String headerEncoding;

    public abstract FileItemFactory getFileItemFactory();

    public abstract void setFileItemFactory(FileItemFactory fileItemFactory);

    public static final boolean isMultipartContent(HttpServletRequest req) {
        String contentType = req.getHeader(CONTENT_TYPE);
        if (contentType != null && contentType.startsWith(MULTIPART)) {
            return true;
        }
        return false;
    }

    public long getSizeMax() {
        return this.sizeMax;
    }

    public void setSizeMax(long sizeMax) {
        this.sizeMax = sizeMax;
    }

    public String getHeaderEncoding() {
        return this.headerEncoding;
    }

    public void setHeaderEncoding(String encoding) {
        this.headerEncoding = encoding;
    }

    public List parseRequest(HttpServletRequest req) throws FileUploadException {
        OutputStream os;
        if (null == req) {
            throw new NullPointerException("req parameter");
        }
        ArrayList items = new ArrayList();
        String contentType = req.getHeader(CONTENT_TYPE);
        if (null == contentType || !contentType.startsWith(MULTIPART)) {
            throw new InvalidContentTypeException(new StringBuffer().append("the request doesn't contain a multipart/form-data or multipart/mixed stream, content type header is ").append(contentType).toString());
        }
        int requestSize = req.getContentLength();
        if (requestSize == -1) {
            throw new UnknownSizeException("the request was rejected because it's size is unknown");
        }
        if (this.sizeMax >= 0 && requestSize > this.sizeMax) {
            throw new SizeLimitExceededException("the request was rejected because it's size exceeds allowed range");
        }
        try {
            int boundaryIndex = contentType.indexOf("boundary=");
            if (boundaryIndex < 0) {
                throw new FileUploadException("the request was rejected because no multipart boundary was found");
            }
            byte[] boundary = contentType.substring(boundaryIndex + 9).getBytes();
            MultipartStream multi = new MultipartStream(req.getInputStream(), boundary);
            multi.setHeaderEncoding(this.headerEncoding);
            for (boolean nextPart = multi.skipPreamble(); nextPart; nextPart = multi.readBoundary()) {
                Map headers = parseHeaders(multi.readHeaders());
                String fieldName = getFieldName(headers);
                if (fieldName != null) {
                    String subContentType = getHeader(headers, CONTENT_TYPE);
                    if (subContentType != null && subContentType.startsWith(MULTIPART_MIXED)) {
                        byte[] subBoundary = subContentType.substring(subContentType.indexOf("boundary=") + 9).getBytes();
                        multi.setBoundary(subBoundary);
                        for (boolean nextSubPart = multi.skipPreamble(); nextSubPart; nextSubPart = multi.readBoundary()) {
                            Map headers2 = parseHeaders(multi.readHeaders());
                            if (getFileName(headers2) != null) {
                                FileItem item = createItem(headers2, false);
                                os = item.getOutputStream();
                                try {
                                    multi.readBodyData(os);
                                    items.add(item);
                                } finally {
                                }
                            } else {
                                multi.discardBodyData();
                            }
                        }
                        multi.setBoundary(boundary);
                    } else if (getFileName(headers) != null) {
                        FileItem item2 = createItem(headers, false);
                        os = item2.getOutputStream();
                        try {
                            multi.readBodyData(os);
                            os.close();
                            items.add(item2);
                        } finally {
                        }
                    } else {
                        FileItem item3 = createItem(headers, true);
                        os = item3.getOutputStream();
                        try {
                            multi.readBodyData(os);
                            items.add(item3);
                        } finally {
                        }
                    }
                } else {
                    multi.discardBodyData();
                }
            }
            return items;
        } catch (IOException e) {
            throw new FileUploadException(new StringBuffer().append("Processing of multipart/form-data request failed. ").append(e.getMessage()).toString());
        }
    }

    protected String getFileName(Map headers) {
        String fileName = null;
        String cd = getHeader(headers, CONTENT_DISPOSITION);
        if (cd.startsWith(FORM_DATA) || cd.startsWith(ATTACHMENT)) {
            int start = cd.indexOf("filename=\"");
            int end = cd.indexOf(34, start + 10);
            if (start != -1 && end != -1) {
                fileName = cd.substring(start + 10, end).trim();
            }
        }
        return fileName;
    }

    protected String getFieldName(Map headers) {
        String fieldName = null;
        String cd = getHeader(headers, CONTENT_DISPOSITION);
        if (cd != null && cd.startsWith(FORM_DATA)) {
            int start = cd.indexOf("name=\"");
            int end = cd.indexOf(34, start + 6);
            if (start != -1 && end != -1) {
                fieldName = cd.substring(start + 6, end);
            }
        }
        return fieldName;
    }

    protected FileItem createItem(Map headers, boolean isFormField) throws FileUploadException {
        return getFileItemFactory().createItem(getFieldName(headers), getHeader(headers, CONTENT_TYPE), isFormField, getFileName(headers));
    }

    protected Map parseHeaders(String headerPart) {
        Map headers = new HashMap();
        char[] buffer = new char[MAX_HEADER_SIZE];
        boolean done = false;
        int j = 0;
        while (!done) {
            int i = 0;
            while (true) {
                if (i >= 2 && buffer[i - 2] == '\r' && buffer[i - 1] == '\n') {
                    break;
                }
                try {
                    int i2 = i;
                    i++;
                    int i3 = j;
                    j++;
                    buffer[i2] = headerPart.charAt(i3);
                } catch (IndexOutOfBoundsException e) {
                }
            }
            String header = new String(buffer, 0, i - 2);
            if (header.equals("")) {
                done = true;
            } else if (header.indexOf(58) != -1) {
                String headerName = header.substring(0, header.indexOf(58)).trim().toLowerCase();
                String headerValue = header.substring(header.indexOf(58) + 1).trim();
                if (getHeader(headers, headerName) != null) {
                    headers.put(headerName, new StringBuffer().append(getHeader(headers, headerName)).append(',').append(headerValue).toString());
                } else {
                    headers.put(headerName, headerValue);
                }
            }
        }
        return headers;
    }

    protected final String getHeader(Map headers, String name) {
        return (String) headers.get(name.toLowerCase());
    }

    /* loaded from: fileupload.jar:org/apache/commons/fileupload/FileUploadBase$InvalidContentTypeException.class */
    public static class InvalidContentTypeException extends FileUploadException {
        public InvalidContentTypeException() {
        }

        public InvalidContentTypeException(String message) {
            super(message);
        }
    }

    /* loaded from: fileupload.jar:org/apache/commons/fileupload/FileUploadBase$UnknownSizeException.class */
    public static class UnknownSizeException extends FileUploadException {
        public UnknownSizeException() {
        }

        public UnknownSizeException(String message) {
            super(message);
        }
    }

    /* loaded from: fileupload.jar:org/apache/commons/fileupload/FileUploadBase$SizeLimitExceededException.class */
    public static class SizeLimitExceededException extends FileUploadException {
        public SizeLimitExceededException() {
        }

        public SizeLimitExceededException(String message) {
            super(message);
        }
    }
}

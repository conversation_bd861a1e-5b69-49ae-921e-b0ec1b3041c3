package jcifs.netbios;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/SessionRequestPacket.class */
public class SessionRequestPacket extends SessionServicePacket {
    private Name calledName;
    private Name callingName;

    SessionRequestPacket() {
        this.calledName = new Name();
        this.callingName = new Name();
    }

    public SessionRequestPacket(Name calledName, Name callingName) {
        this.type = NbtException.NOT_LISTENING_CALLING;
        this.calledName = calledName;
        this.callingName = callingName;
    }

    @Override // jcifs.netbios.SessionServicePacket
    int writeTrailerWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + this.calledName.writeWireFormat(dst, dstIndex);
        return (dstIndex2 + this.callingName.writeWireFormat(dst, dstIndex2)) - dstIndex;
    }

    @Override // jcifs.netbios.SessionServicePacket
    int readTrailerWireFormat(InputStream in, byte[] buffer, int bufferIndex) throws IOException {
        if (in.read(buffer, bufferIndex, this.length) != this.length) {
            throw new IOException("invalid session request wire format");
        }
        int bufferIndex2 = bufferIndex + this.calledName.readWireFormat(buffer, bufferIndex);
        return (bufferIndex2 + this.callingName.readWireFormat(buffer, bufferIndex2)) - bufferIndex;
    }
}

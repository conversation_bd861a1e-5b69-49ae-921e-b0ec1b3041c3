package jcifs.netbios;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NameQueryResponse.class */
class NameQueryResponse extends NameServicePacket {
    NameQueryResponse() {
        this.recordName = new Name();
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeBodyWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    int readBodyWireFormat(byte[] src, int srcIndex) {
        return readResourceRecordWireFormat(src, srcIndex);
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeRDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    int readRDataWireFormat(byte[] src, int srcIndex) {
        if (this.resultCode != 0 || this.opCode != 0) {
            return 0;
        }
        boolean groupName = (src[srcIndex] & 128) == 128;
        int nodeType = (src[srcIndex] & 96) >> 5;
        int address = readInt4(src, srcIndex + 2);
        if (address != 0) {
            this.addrEntry[this.addrIndex] = new NbtAddress(this.recordName, address, groupName, nodeType);
            return 6;
        }
        this.addrEntry[this.addrIndex] = null;
        return 6;
    }

    @Override // jcifs.netbios.NameServicePacket
    public String toString() {
        return new String("NameQueryResponse[" + super.toString() + ",addrEntry=" + this.addrEntry + "]");
    }
}

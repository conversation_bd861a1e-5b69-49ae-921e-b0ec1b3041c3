var G_LoadSecurityJs=!1,BYTES_PER_CHUNK=1048576;
function processFile(a,f,c,g,h,k,l,e,m,n){var p=c.size,q={currentNumber:1,numberOfChunks:Math.ceil(c.size/BYTES_PER_CHUNK),size:c.size},b=0,d=BYTES_PER_CHUNK;if("1"==e&&0==c.size)self.postMessage({type:"uploadZeroFile",filename:n,blob:c,mark:g,gpid:h,fidx:k,asyncstate:l,id:a,guid:f});else for(;b<p;){e=c.mozSlice?c.mozSlice(b,d):c.webkitSlice?c.webkitSlice(b,d):c.slice(b,d);b="";if("1"==m.integrity){G_LoadSecurityJs||(importScripts("dext5upload.fdi.aes.js"),importScripts("hmac-sha256.js"),G_LoadSecurityJs=
!0);var b=(new FileReaderSync).readAsArrayBuffer(e),r=!1;d>=p&&(r=!0);b=D5FileDataIntegrity(b,m.keyValue,r).toString()}self.postMessage({type:"upload",filename:n,blob:e,mark:g,gpid:h,fidx:k,chunkInfo:q,asyncstate:l,id:a,guid:f,FDIData:b});q.currentNumber++;b=d;d=b+BYTES_PER_CHUNK}}
self.onmessage=function(a){a=a.data;switch(a.type){case "start":BYTES_PER_CHUNK=a.bytesperchunk;processFile(a.id,a.guid,a.file,a.mark,a.gpid,a.fidx,a.asyncstate,a.allowedzerofilesize,a.securityInfo,a.originalname);break;case "check_formdata":a=!0,"undefined"==typeof FormData&&(a=!1),self.postMessage({type:"check_formdata",isFormDataSupport:a})}};

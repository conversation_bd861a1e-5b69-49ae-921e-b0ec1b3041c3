﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Web;
using System.Web.Configuration;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x0200002F RID: 47
	public class UploadModule : IHttpModule
	{
		// Token: 0x060002BD RID: 701 RVA: 0x00020403 File Offset: 0x0001E603
		void IHttpModule.Dispose()
		{
		}

		// Token: 0x060002BE RID: 702 RVA: 0x00020405 File Offset: 0x0001E605
		void IHttpModule.Init(HttpApplication context)
		{
			context.BeginRequest += this.context_BeginRequest;
			context.EndRequest += this.context_EndRequest;
		}

		// Token: 0x060002BF RID: 703 RVA: 0x0002042C File Offset: 0x0001E62C
		private void context_EndRequest(object sender, EventArgs e)
		{
			HttpApplication httpApplication = (HttpApplication)sender;
			HttpContext context = httpApplication.Context;
		}

		// Token: 0x060002C0 RID: 704 RVA: 0x00020448 File Offset: 0x0001E648
		private void context_BeginRequest(object sender, EventArgs e)
		{
			HttpApplication httpApplication = (HttpApplication)sender;
			HttpContext context = httpApplication.Context;
			if (string.Compare(context.Request.HttpMethod, "POST", StringComparison.InvariantCultureIgnoreCase) == 0)
			{
				bool flag = false;
				if (!string.IsNullOrEmpty(context.Request.QueryString["dext5CMD"]) && context.Request.ContentType.StartsWith("multipart/form-data", StringComparison.InvariantCultureIgnoreCase))
				{
					string a = context.Request.QueryString["dext5CMD"];
					if (a == "uh4fc")
					{
						string text = context.Request.QueryString["g"];
						Trace.WriteLine("[START Get size] : " + text + "\n");
						UploadStatus uploadStatus = new UploadStatus(text);
						context.Application.Add(text, uploadStatus);
						uploadStatus.TotalBytes = -1L;
						uploadStatus.Message = "";
						HttpWorkerRequest httpWorkerRequest = (HttpWorkerRequest)((IServiceProvider)httpApplication.Context).GetService(typeof(HttpWorkerRequest));
						long num = long.Parse(httpWorkerRequest.GetKnownRequestHeader(11));
						if (httpWorkerRequest.HasEntityBody())
						{
							FileProcessor fileProcessor = new FileProcessor("", "");
							string empty = string.Empty;
							try
							{
								byte[] array = httpWorkerRequest.GetPreloadedEntityBody();
								int num2 = 0;
								if (array == null)
								{
									array = new byte[0];
								}
								else
								{
									fileProcessor.GetFieldSeperators(ref array);
									num2 = fileProcessor.GetProcessBufferStartLocation(ref array, true, "Content-Type: ");
								}
								if (!httpWorkerRequest.IsEntireEntityBodyIsPreloaded() && num2 == 0)
								{
									long num3 = 10485760L;
									byte[] array2 = new byte[num3];
									httpWorkerRequest.ReadEntityBody(array2, array2.Length);
									flag = true;
									if (string.IsNullOrEmpty(fileProcessor.FieldSeperator))
									{
										fileProcessor.GetFieldSeperators(ref array2);
									}
									num2 = fileProcessor.GetProcessBufferStartLocation(ref array2, true, "Content-Type: ");
								}
								num = num - (long)num2 - (long)fileProcessor.FieldSeperator.Length - (long)"--\r\n\r\n".Length;
								uploadStatus.Message = "size|" + num.ToString();
								uploadStatus.TotalBytes = num;
								Trace.WriteLine("[GetSize] : " + num.ToString() + "\n");
							}
							catch (Exception ex)
							{
								uploadStatus.Message = "error|015|Http Module Error - " + ex.Message;
								uploadStatus.TotalBytes = 0L;
							}
							finally
							{
								fileProcessor.CloseStreams();
							}
						}
						if (flag)
						{
							Type type = httpWorkerRequest.GetType();
							BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
							MemoryStream memoryStream = new MemoryStream();
							int num4 = 0;
							while (type != null)
							{
								num4++;
								Trace.WriteLine("[[RUN loop Get size] : " + type.FullName + "\n");
								if (string.Compare(type.FullName, "System.Web.Hosting.ISAPIWorkerRequest", StringComparison.InvariantCultureIgnoreCase) == 0)
								{
									Trace.WriteLine("[[RUN loop] : AAAA\n");
									type.GetField("_contentAvailLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									type.GetField("_contentTotalLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									type.GetField("_preloadedContent", bindingAttr).SetValue(httpWorkerRequest, memoryStream.GetBuffer());
									type.GetField("_preloadedContentRead", bindingAttr).SetValue(httpWorkerRequest, true);
									httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr).SetValue(httpApplication.Request, (int)memoryStream.Length);
								}
								else if (string.Compare(type.FullName, "System.Web.Hosting.IIS7WorkerRequest", StringComparison.InvariantCultureIgnoreCase) == 0)
								{
									Trace.WriteLine("[[RUN loop] : BBBB\n");
									type = type.BaseType;
									UploadWorkerRequest value = new UploadWorkerRequest(httpWorkerRequest, memoryStream.GetBuffer());
									BindingFlags bindingAttr2 = BindingFlags.Instance | BindingFlags.NonPublic;
									httpApplication.Context.Request.GetType().GetField("_wr", bindingAttr2).SetValue(httpApplication.Context.Request, value);
									httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr2).SetValue(httpApplication.Request, (int)memoryStream.Length);
								}
								else if (string.Compare(type.FullName, "Microsoft.VisualStudio.WebHost.Request", StringComparison.InvariantCultureIgnoreCase) == 0)
								{
									Trace.WriteLine("[[RUN loop] : " + type.BaseType + "\n");
									type.GetField("_contentLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									type.GetField("_preloadedContent", bindingAttr).SetValue(httpWorkerRequest, memoryStream.GetBuffer());
									type.GetField("_preloadedContentLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr).SetValue(httpApplication.Request, (int)memoryStream.Length);
									if (num4 > 1000)
									{
										break;
									}
								}
								else
								{
									Trace.WriteLine("[[RUN loop] : DDDD\n");
									type = type.BaseType;
									type.GetField("_contentLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									type.GetField("_preloadedContent", bindingAttr).SetValue(httpWorkerRequest, memoryStream.GetBuffer());
									type.GetField("_preloadedContentLength", bindingAttr).SetValue(httpWorkerRequest, (int)memoryStream.Length);
									httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr).SetValue(httpApplication.Request, (int)memoryStream.Length);
								}
							}
						}
						Trace.WriteLine("[END Get size] : " + text + "\n");
					}
				}
				if (!string.IsNullOrEmpty(context.Request.QueryString["dext5CMD"]) && context.Request.ContentType.StartsWith("multipart/form-data", StringComparison.InvariantCultureIgnoreCase))
				{
					string a2 = context.Request.QueryString["dext5CMD"];
					if (a2 == "uploadHtml4Request")
					{
						this._baseFileLocation = Path.Combine(HttpContext.Current.Server.MapPath("./"), "temp");
						string text2 = context.Request.QueryString["g"];
						FileProcessor fileProcessor2 = new FileProcessor(this._baseFileLocation, text2 + ".tmp");
						UploadStatus uploadStatus2 = new UploadStatus(text2);
						HttpWorkerRequest httpWorkerRequest2 = (HttpWorkerRequest)((IServiceProvider)context).GetService(typeof(HttpWorkerRequest));
						if (httpWorkerRequest2.HasEntityBody())
						{
							MemoryStream memoryStream2 = new MemoryStream();
							try
							{
								context.Application.Add(text2, uploadStatus2);
								uploadStatus2.Message = "uploading|0";
								long totalBytes = long.Parse(httpWorkerRequest2.GetKnownRequestHeader(11));
								uploadStatus2.TotalBytes = totalBytes;
								long num5 = 10485760L;
								byte[] array3 = httpWorkerRequest2.GetPreloadedEntityBody();
								if (array3 == null)
								{
									array3 = new byte[0];
								}
								else
								{
									fileProcessor2.GetFieldSeperators(ref array3);
									fileProcessor2.ProcessBuffer(ref array3, true, "Content-Type: ", ref memoryStream2);
									uploadStatus2.CurrentBytesTransfered += (long)array3.Length;
									uploadStatus2.Message = "uploading|" + Math.Ceiling((double)uploadStatus2.CurrentBytesTransfered / (double)uploadStatus2.TotalBytes * 100.0).ToString();
								}
								if (!httpWorkerRequest2.IsEntireEntityBodyIsPreloaded())
								{
									while (httpWorkerRequest2.IsClientConnected())
									{
										if (uploadStatus2.CloseConnection)
										{
											uploadStatus2.Message = "cancel";
											throw new Exception("The Application was disconnected");
										}
										long num6 = uploadStatus2.TotalBytes - uploadStatus2.CurrentBytesTransfered;
										if (num6 < num5)
										{
											num5 = num6;
										}
										if (num5 == 0L)
										{
											goto IL_84B;
										}
										byte[] array4 = new byte[num5];
										long num7 = (long)httpWorkerRequest2.ReadEntityBody(array4, array4.Length);
										flag = true;
										if (string.IsNullOrEmpty(fileProcessor2.FieldSeperator))
										{
											fileProcessor2.GetFieldSeperators(ref array4);
										}
										fileProcessor2.ProcessBuffer(ref array4, true, "Content-Type: ", ref memoryStream2);
										uploadStatus2.CurrentBytesTransfered += (long)array4.Length;
										uploadStatus2.Message = "uploading|" + Math.Ceiling((double)uploadStatus2.CurrentBytesTransfered / (double)uploadStatus2.TotalBytes * 100.0).ToString();
										if (num7 == 0L)
										{
											goto IL_84B;
										}
									}
									throw new Exception("The Application was disconnected");
								}
								IL_84B:
								uploadStatus2.TempFileName = fileProcessor2.FinishedFiles[0];
							}
							catch (Exception ex2)
							{
								uploadStatus2.Message = "error|015|Http Module Error - " + ex2.Message;
							}
							finally
							{
								fileProcessor2.CloseStreams();
								if (flag)
								{
									Type type2 = httpWorkerRequest2.GetType();
									BindingFlags bindingAttr3 = BindingFlags.Instance | BindingFlags.NonPublic;
									while (type2 != null)
									{
										Trace.WriteLine("[DEXT5 Check UploadFile] : " + type2.FullName);
										if (type2.FullName.IndexOf("System.Web.Hosting.ISAPIWorkerRequest") > -1)
										{
											try
											{
												type2 = type2.BaseType;
												UploadWorkerRequest value2 = new UploadWorkerRequest(httpWorkerRequest2, memoryStream2.GetBuffer());
												BindingFlags bindingAttr4 = BindingFlags.Instance | BindingFlags.NonPublic;
												httpApplication.Context.Request.GetType().GetField("_wr", bindingAttr4).SetValue(httpApplication.Context.Request, value2);
												httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr4).SetValue(httpApplication.Request, (int)memoryStream2.Length);
												goto IL_B75;
											}
											catch (Exception ex3)
											{
												string message = ex3.Message;
												goto IL_B75;
											}
											goto IL_961;
										}
										goto IL_961;
										IL_B75:
										if (!(type2.FullName == "System.Web.HttpWorkerRequest") && uploadStatus2.Message.IndexOf("|100") <= -1)
										{
											continue;
										}
										break;
										IL_961:
										if (string.Compare(type2.FullName, "System.Web.Hosting.IIS7WorkerRequest", StringComparison.InvariantCultureIgnoreCase) == 0)
										{
											try
											{
												type2 = type2.BaseType;
												UploadWorkerRequest value3 = new UploadWorkerRequest(httpWorkerRequest2, memoryStream2.GetBuffer());
												BindingFlags bindingAttr5 = BindingFlags.Instance | BindingFlags.NonPublic;
												httpApplication.Context.Request.GetType().GetField("_wr", bindingAttr5).SetValue(httpApplication.Context.Request, value3);
												httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr5).SetValue(httpApplication.Request, (int)memoryStream2.Length);
												goto IL_B75;
											}
											catch (Exception ex4)
											{
												string message2 = ex4.Message;
												goto IL_B75;
											}
										}
										if (string.Compare(type2.FullName, "Microsoft.VisualStudio.WebHost.Request", StringComparison.InvariantCultureIgnoreCase) == 0)
										{
											try
											{
												type2.GetField("_contentLength", bindingAttr3).SetValue(httpWorkerRequest2, (int)memoryStream2.Length);
												type2.GetField("_preloadedContent", bindingAttr3).SetValue(httpWorkerRequest2, memoryStream2.GetBuffer());
												type2.GetField("_preloadedContentLength", bindingAttr3).SetValue(httpWorkerRequest2, (int)memoryStream2.Length);
												httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr3).SetValue(httpApplication.Request, (int)memoryStream2.Length);
												goto IL_B75;
											}
											catch (Exception ex5)
											{
												string message3 = ex5.Message;
												goto IL_B75;
											}
										}
										try
										{
											type2 = type2.BaseType;
											type2.GetField("_contentLength", bindingAttr3).SetValue(httpWorkerRequest2, (int)memoryStream2.Length);
											type2.GetField("_preloadedContent", bindingAttr3).SetValue(httpWorkerRequest2, memoryStream2.GetBuffer());
											type2.GetField("_preloadedContentLength", bindingAttr3).SetValue(httpWorkerRequest2, (int)memoryStream2.Length);
											httpApplication.Context.Request.GetType().GetField("_contentLength", bindingAttr3).SetValue(httpApplication.Request, (int)memoryStream2.Length);
										}
										catch (Exception ex6)
										{
											string message4 = ex6.Message;
										}
										goto IL_B75;
									}
									if (type2 == null)
									{
										uploadStatus2.Message = "error|015|Http Module Error - Unsupported Platform";
									}
								}
							}
							uploadStatus2.FileName = fileProcessor2.FileName;
							uploadStatus2.FolderNameRule = fileProcessor2.FolderNameRule;
							uploadStatus2.FileNameRule = fileProcessor2.FileNameRule;
							uploadStatus2.FileNameRuleEx = fileProcessor2.FileNameRuleEx;
							uploadStatus2.D5_prefix = fileProcessor2.D5_prefix;
							uploadStatus2.D5_subfix = fileProcessor2.D5_subfix;
							fileProcessor2.Dispose();
							memoryStream2.Dispose();
						}
					}
				}
			}
		}

		// Token: 0x060002C1 RID: 705 RVA: 0x00021130 File Offset: 0x0001F330
		private int GetMaxRequestLength(HttpContext context)
		{
			int result = 1024;
			HttpRuntimeSection httpRuntimeSection = context.GetSection("system.web/httpRuntime") as HttpRuntimeSection;
			if (httpRuntimeSection != null)
			{
				return httpRuntimeSection.MaxRequestLength;
			}
			return result;
		}

		// Token: 0x0400018F RID: 399
		private string _baseFileLocation = "";
	}
}

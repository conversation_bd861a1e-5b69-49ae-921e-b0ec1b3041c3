package org.apache.struts.taglib.nested.logic;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import org.apache.struts.taglib.logic.EqualTag;
import org.apache.struts.taglib.nested.NestedNameSupport;
import org.apache.struts.taglib.nested.NestedPropertyHelper;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/logic/NestedEqualTag.class */
public class NestedEqualTag extends EqualTag implements NestedNameSupport {
    private String originalName = null;
    private String originalProperty = null;

    @Override // org.apache.struts.taglib.logic.ConditionalTagBase
    public int doStartTag() throws JspException {
        this.originalName = getName();
        this.originalProperty = getProperty();
        HttpServletRequest request = this.pageContext.getRequest();
        NestedPropertyHelper.setNestedProperties(request, this);
        return super.doStartTag();
    }

    @Override // org.apache.struts.taglib.logic.ConditionalTagBase
    public int doEndTag() throws JspException {
        int i = super.doEndTag();
        setName(this.originalName);
        setProperty(this.originalProperty);
        return i;
    }

    @Override // org.apache.struts.taglib.logic.CompareTagBase, org.apache.struts.taglib.logic.ConditionalTagBase
    public void release() {
        super.release();
        this.originalName = null;
        this.originalProperty = null;
    }
}

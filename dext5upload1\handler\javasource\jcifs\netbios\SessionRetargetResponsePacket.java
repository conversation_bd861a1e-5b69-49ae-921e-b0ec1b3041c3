package jcifs.netbios;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/SessionRetargetResponsePacket.class */
class SessionRetargetResponsePacket extends SessionServicePacket {
    private NbtAddress retargetAddress;
    private int retargetPort;

    SessionRetargetResponsePacket() {
        this.type = 132;
        this.length = 6;
    }

    @Override // jcifs.netbios.SessionServicePacket
    int writeTrailerWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.netbios.SessionServicePacket
    int readTrailerWireFormat(InputStream in, byte[] buffer, int bufferIndex) throws IOException {
        if (in.read(buffer, bufferIndex, this.length) != this.length) {
            throw new IOException("unexpected EOF reading netbios retarget session response");
        }
        int addr = readInt4(buffer, bufferIndex);
        this.retargetAddress = new NbtAddress(null, addr, false, 0);
        this.retargetPort = readInt2(buffer, bufferIndex + 4);
        return this.length;
    }
}

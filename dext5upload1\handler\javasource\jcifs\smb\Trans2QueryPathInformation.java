package jcifs.smb;

import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryPathInformation.class */
class Trans2QueryPathInformation extends SmbComTransaction {
    private int informationLevel;

    Trans2QueryPathInformation(String filename, int informationLevel) {
        this.path = filename;
        this.informationLevel = informationLevel;
        this.command = (byte) 50;
        this.subCommand = (byte) 5;
        this.totalDataCount = 0;
        this.maxParameterCount = 2;
        this.maxDataCount = 40;
        this.maxSetupCount = (byte) 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        int i = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        return 2;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.informationLevel, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        int dstIndex3 = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        int dstIndex4 = dstIndex3 + 1;
        dst[dstIndex3] = 0;
        int dstIndex5 = dstIndex4 + 1;
        dst[dstIndex4] = 0;
        int dstIndex6 = dstIndex5 + 1;
        dst[dstIndex5] = 0;
        return (dstIndex6 + writeString(this.path, dst, dstIndex6)) - dstIndex;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2QueryPathInformation[" + super.toString() + ",informationLevel=0x" + Hexdump.toHexString(this.informationLevel, 3) + ",filename=" + this.path + "]");
    }
}

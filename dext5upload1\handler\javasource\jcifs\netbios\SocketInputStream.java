package jcifs.netbios;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/SocketInputStream.class */
class SocketInputStream extends InputStream {
    private static final int TMP_BUFFER_SIZE = 256;
    private InputStream in;
    private SessionServicePacket ssp;
    private int tot;
    private int bip;
    private int n;
    private byte[] header = new byte[4];
    private byte[] tmp = new byte[256];

    SocketInputStream(InputStream in) {
        this.in = in;
    }

    @Override // java.io.InputStream
    public synchronized int read() throws IOException {
        if (read(this.tmp, 0, 1) < 0) {
            return -1;
        }
        return this.tmp[0] & 255;
    }

    @Override // java.io.InputStream
    public synchronized int read(byte[] b) throws IOException {
        return read(b, 0, b.length);
    }

    @Override // java.io.InputStream
    public synchronized int read(byte[] b, int off, int len) throws IOException {
        if (len == 0) {
            return 0;
        }
        this.tot = 0;
        while (true) {
            if (this.bip > 0) {
                this.n = this.in.read(b, off, Math.min(len, this.bip));
                if (this.n == -1) {
                    if (this.tot > 0) {
                        return this.tot;
                    }
                    return -1;
                }
                this.tot += this.n;
                off += this.n;
                len -= this.n;
                this.bip -= this.n;
                if (len == 0) {
                    return this.tot;
                }
            } else {
                switch (SessionServicePacket.readPacketType(this.in, this.header, 0)) {
                    case -1:
                        if (this.tot > 0) {
                            return this.tot;
                        }
                        return -1;
                    case 0:
                        this.bip = SessionServicePacket.readLength(this.header, 0);
                        break;
                }
            }
        }
    }

    @Override // java.io.InputStream
    public synchronized long skip(long numbytes) throws IOException {
        long n;
        int r;
        if (numbytes <= 0) {
            return 0L;
        }
        long j = numbytes;
        while (true) {
            n = j;
            if (n <= 0 || (r = read(this.tmp, 0, (int) Math.min(256L, n))) < 0) {
                break;
            }
            j = n - r;
        }
        return numbytes - n;
    }

    @Override // java.io.InputStream
    public int available() throws IOException {
        if (this.bip > 0) {
            return this.bip;
        }
        return this.in.available();
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        this.in.close();
    }
}

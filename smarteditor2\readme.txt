﻿SmartEditor Basic 2.0 릴리즈 패키지

SmartEdtitor™는 Javascript로 구현된 웹 기반의 WYSIWYG 에디터입니다. SmartEdtitor™는 WYSIWYG 모드 및 HTML 편집 모드와 TEXT 모드를 제공하고, 자유로운 폰트 크기 설정 기능, 줄 간격 설정 기능, 단어 찾기/바꾸기 기능 등 편집에 필요한 다양한 기능을 제공하므로 사용자들은 SmartEdtitor™를 사용하여 쉽고 편리하게 원하는 형태의 글을 작성할 수 있습니다.
또한, SmartEdtitor™의 구조는 기능을 쉽게 추가할 수 있는 플러그인 구조로 되어 있어 정해진 규칙에 따라 플러그인을 만들기만 하면 됩니다.

현재 SmartEdtitor™는 네이버, 한게임 등 NHN의 주요 서비스에 적용되어 있습니다.
지원하는 브라우저 환경은 아래와 같으며 지속적으로 지원 대상 브라우저를 확장할 예정입니다.

* 지원하는 브라우저
Internet Explorer 7.0+ / 10.0-
FireFox 3.5+
Safari 4.0+
Chrome 4.0+

또한 지속적인 기능 추가를 통해 편리하고 강력한 에디터로 거듭날 것입니다.

라이센스 : LGPL v2 
홈페이지 : http://dev.naver.com/projects/smarteditor 

===================================================================================

릴리즈 패키지에 포함된 파일은 아래와 같습니다.
/css : 에디터에서 사용하는 css 파일
/img : 에디터에서 사용하는 이미지 파일
/js : 에디터를 적용할 때 사용하는 JS 파일
/photo_uploader : 사진 퀵 업로더 팝업 UI를 구성하는 파일
readme.txt : 간략한 설명
release_notes.txt : 릴리즈 노트
sample.php : SmartEditor2.html을 이용해 편집한 내용을 서버에서 받는 php 예제
smart_editor2_inputarea.html : 에디터의 편집 영역을 나타내는 HTML로 에디터를 적용할 때 반드시 필요
smart_editor2_inputarea_ie8.html : smart_editor2_inputarea.html와 동일한 기능이나 사용자의 브라우저 Internet Explorer 8.x 이상인 경우에 사용
SmartEditor2.html : 에디터 데모 페이지. 에디터 적용 시에도 참고 할 수 있다.
SmartEditor2Skin.html : 에디터를 적용한 페이지에서 로드하는 에디터의 스킨 HTML 파일로 에디터에서 사용하는 JS 파일과 css 파일을 링크하며 에디터의 마크업을 가지고 있다. SmartEditor2.html 에서도 확인할 수 있다.
src_include.txt : 자바스크립트 플러그인 소스를 직접 수정하고자 할 경우 참고할 수 있는 파일

===================================================================================

사용 중 불편한 점이 있거나 버그를 발견하는 경우 SmartEdtitor™ 프로젝트의 이슈에 올려 주세요~~~
http://dev.naver.com/projects/smarteditor/issue
여기입니다! :)
#!/usr/bin/env python3
import requests
import re
import urllib3
import sys
import time
from urllib.parse import urljoin, urlparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import random
import string
import threading
import os

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def random_string(length=12):
    """Generate a random string of fixed length."""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

class HuskyScanner:
    def __init__(self, timeout=10, max_workers=10):
        self.timeout = timeout
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.results = []
        # Regex to find paths like ".../js/HuskyEZCreator.js"
        self.husky_pattern = re.compile(r'["\']([^"\']*/js/HuskyEZCreator\.js[^"\']*)["\']', re.IGNORECASE)
        self.vulnerability_paths = [
            'sample/photo_uploader/file_uploader_html5.php',
            'sample/photo_uploader/file_uploader_html5.jsp',
            'sample/photo_uploader/file_uploader.jsp',
            'sample/photo_uploader/file_uploader.php',
            'sample/photo_uploader/photo_uploader.html',
            'photo_uploader/popup/photo_uploader.html',
            'photo_uploader/popup/file_uploader.php',
            'photo_uploader/popup/file_uploader.jsp',
            'photo_uploader/popup/file_uploader_html5.html',
            'photo_uploader/popup/file_uploader_html5.php',
        ]
        self.common_directories = [
            '', # Root directory
            'smarteditor/',
            'SmartEditor/',
            'se2/',
            'editor/',
            'common/',
            'resources/',
            'resource/',
            'js/',
            'lib/',
            'scripts/',
            'static/',
            'assets/',
            'resources/js/SmartEditor2/',
            'resources/js/SmartEditor/',
        ]
        self.file_lock = threading.Lock()

        # 蜜罐检测配置
        self.honeypot_detection = {
            'max_vulnerable_paths': 5,  # 最多发现的漏洞路径数量
            'suspicious_response_threshold': 0.8  # 可疑响应阈值
        }

    def normalize_url(self, url):
        """Standardize URL, ensure it has a protocol."""
        if not url.startswith(('http://', 'https://')):
            # Try https first, then http
            return f"https://{url}", f"http://{url}"
        return url, None

    def test_url_accessibility(self, url):
        """Test if URL is accessible, return (accessible_url, response_object)."""
        primary_url, fallback_url = self.normalize_url(url)
        
        # Test primary URL
        try:
            response = self.session.get(primary_url, allow_redirects=True)
            if response.status_code == 200:
                return primary_url, response
        except requests.exceptions.RequestException:
            pass
        
        # If there is a fallback, test it
        if fallback_url:
            try:
                response = self.session.get(fallback_url, allow_redirects=True)
                if response.status_code == 200:
                    return fallback_url, response
            except requests.exceptions.RequestException:
                pass
        
        return None, None

    def find_husky_script(self, html_content, base_url):
        """Extract HuskyEZCreator.js path from HTML content."""
        matches = self.husky_pattern.findall(html_content)
        
        if matches:
            # Take the first match
            script_path = matches[0]
            full_url = urljoin(base_url, script_path)
            return full_url
            
        return None

    def scan_single_target(self, target):
        """扫描单个目标"""
        print(f"[*] 扫描目标: {target}")
        
        # 1. 测试URL可访问性
        accessible_url, response = self.test_url_accessibility(target)
        if not accessible_url:
            print(f"[-] {target} - 无法访问")
            return None
        
        print(f"[+] {target} - 可访问 ({accessible_url})")
        
        # 2. 获取"未找到"页面签名
        print(f"[*] {target} - 正在检查自定义404页面签名...")
        not_found_signature = self.get_not_found_signature(accessible_url)
        if not_found_signature:
            print(f"[+] {target} - 已识别自定义404签名 (长度: {len(not_found_signature)}).")
        else:
            print(f"[-] {target} - 无法确定404签名，结果可能包含误报。")

        # 3. 首选策略：尝试从主页内容中查找编辑器
        print(f"[*] {target} - 优先尝试从主页内容中查找编辑器...")
        husky_script_url_on_home = self.find_husky_script(response.text, accessible_url)
        if husky_script_url_on_home:
            base_path = self.extract_base_path(husky_script_url_on_home)
            if base_path:
                self.learn_and_save_new_directory(base_path)
                print(f"[+] {target} - 从主页发现并确认编辑器基础路径: {base_path}")
                print(f"[*] {target} - 开始在基础路径下测试漏洞...")
                vulnerable_paths = self.test_vulnerability_paths(base_path, not_found_signature)

                # 蜜罐和异常情况检测
                if vulnerable_paths:
                    is_suspicious, reason = self.detect_honeypot_or_suspicious(vulnerable_paths, base_path)
                    if is_suspicious:
                        print(f"[!] {target} - 检测到可疑情况: {reason}")
                        print(f"[!] {target} - 可能是蜜罐、WAF或通配符路由，建议人工验证")
                        self.save_suspicious_result(target, base_path, vulnerable_paths, reason)
                        return None

                if vulnerable_paths:
                    print(f"[+] {target} - 在主页发现的路径中找到脆弱点，扫描结束。")
                    result = {
                        'target': target,
                        'accessible_url': accessible_url,
                        'husky_script_url': husky_script_url_on_home,
                        'base_editor_url': base_path,
                        'vulnerable_paths': vulnerable_paths
                    }
                    self.save_results([result])
                    return [result]
                else:
                    print(f"[-] {target} - 主页发现的路径 {base_path} 未发现脆弱点。根据策略，不再进行目录 fuzzing。")
                    return None

        # 4. 备用策略：如果主页未发现，则进行目录 fuzzing
        print(f"[-] {target} - 主页未发现编辑器路径，开始进行目录 fuzzing...")
        all_found_results = []
        fuzz_paths = set()
        for directory in self.common_directories:
            full_path = urljoin(accessible_url, directory)
            if not full_path.endswith('/'):
                full_path += '/'
            fuzz_paths.add(full_path)
            
        print(f"[*] {target} - 将 fuzz {len(fuzz_paths)} 个常见目录...")
        
        for base_path in fuzz_paths:
            script_url = urljoin(base_path, 'js/HuskyEZCreator.js')
            try:
                res = self.session.head(script_url, timeout=5, allow_redirects=True)
                if res.status_code != 200:
                    continue
            except requests.exceptions.RequestException:
                continue
            
            print(f"[*] {target} - Fuzzing 在 {base_path} 确认存在编辑器, 开始测试漏洞...")
            vulnerable_paths = self.test_vulnerability_paths(base_path, not_found_signature)

            # 蜜罐和异常情况检测
            if vulnerable_paths:
                is_suspicious, reason = self.detect_honeypot_or_suspicious(vulnerable_paths, base_path)
                if is_suspicious:
                    print(f"[!] {target} - Fuzzing在 {base_path} 检测到可疑情况: {reason}")
                    self.save_suspicious_result(target, base_path, vulnerable_paths, reason)
                    continue

            if vulnerable_paths:
                print(f"[+] {target} - Fuzzing 在 {base_path} 发现脆弱路径!")
                result = {
                    'target': target,
                    'accessible_url': accessible_url,
                    'husky_script_url': script_url,
                    'base_editor_url': base_path,
                    'vulnerable_paths': vulnerable_paths
                }
                all_found_results.append(result)
        
        if all_found_results:
            self.save_results(all_found_results)
            return all_found_results
        
        print(f"[-] {target} - Fuzzing 结束，未发现有效的编辑器或脆弱路径")
        return None

    def learn_and_save_new_directory(self, base_path):
        """
        从发现的脚本路径中学习新目录，如果它不是已知的公共目录，
        就将其保存到文件中。
        """
        try:
            dir_to_save = urlparse(base_path).path.lstrip('/')
            if not dir_to_save:
                return

            # 标准化以便比较
            dir_to_save_normalized = dir_to_save.strip().strip('/')
            
            # 检查它是否是内置的公共目录之一
            normalized_common_dirs = {d.strip().strip('/') for d in self.common_directories if d.strip()}
            if dir_to_save_normalized in normalized_common_dirs:
                return

            with self.file_lock:
                # 检查目录是否已在文件中
                existing_dirs = set()
                if os.path.exists('commdir.txt'):
                    with open('commdir.txt', 'r', encoding='utf-8') as f:
                        existing_dirs = {line.strip().strip('/') for line in f if line.strip()}
                
                if dir_to_save_normalized not in existing_dirs:
                    print(f"[*] 发现新的非标准目录: {dir_to_save}，已保存到 commdir.txt")
                    # 为保持一致性，保存时附带斜杠
                    with open('commdir.txt', 'a', encoding='utf-8') as f:
                        f.write(dir_to_save_normalized + '/\n')
        except Exception as e:
            print(f"[!] 保存新目录时出错: {e}")

    def get_directory_404_signature(self, base_url, directory_path):
        """获取指定目录的404特征"""
        # 生成随机文件名，测试多种扩展名
        random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))

        signatures = {}

        # 测试常见的扩展名
        for ext in ['jsp', 'php', 'html']:
            random_file = f"{random_name}.{ext}"
            test_url = urljoin(base_url, directory_path + random_file)

            try:
                response = self.session.get(test_url, allow_redirects=False, timeout=5)
                signatures[ext] = {
                    'status_code': response.status_code,
                    'content': response.text,
                    'content_length': len(response.text),
                    'headers': dict(response.headers)
                }
            except:
                signatures[ext] = None

        return signatures

    def get_not_found_signature(self, base_url):
        """获取404页面签名（保持兼容性）"""
        signatures = self.get_directory_404_signature(base_url, '/')
        # 返回php扩展名的404特征内容，保持向后兼容
        if signatures.get('php'):
            return signatures['php']['content']
        return None

    def extract_base_path(self, script_url):
        """Extracts the base path of the editor from the script URL."""
        # This regex is designed to find the part of the URL before '/js/HuskyEZCreator.js'
        match = re.search(r'(.*/js/HuskyEZCreator\.js)', script_url, re.IGNORECASE)
        if match:
            # We want the path before 'js/HuskyEZCreator.js'
            full_script_path = match.group(1)
            # rsplit removes the script part, leaving the base directory.
            # urljoin handles constructing the absolute URL correctly.
            base_editor_path = full_script_path.rsplit('js/HuskyEZCreator.js', 1)[0]
            return urljoin(script_url, base_editor_path)
        return None

    def test_vulnerability_paths(self, base_editor_url, not_found_signature):
        """根据编辑器的基础URL测试脆弱路径，同时检查GET和POST方法。"""
        found_paths = []

        # 获取目录404特征
        directory_path = urlparse(base_editor_url).path
        base_url_without_path = base_editor_url.replace(directory_path, '')
        directory_404_signatures = self.get_directory_404_signature(base_url_without_path, directory_path)

        for path in self.vulnerability_paths:
            vuln_url = urljoin(base_editor_url, path)

            # 获取期望的文件扩展名
            expected_ext = path.split('.')[-1].lower()

            try:
                # 1. 尝试GET请求
                res_get = self.session.get(vuln_url, allow_redirects=False, timeout=15)

                if self.is_valid_vulnerability_response(res_get, expected_ext, directory_404_signatures, not_found_signature):
                    print(f"[+] {base_editor_url} - 发现有效路径 (GET): {vuln_url}")
                    found_paths.append(vuln_url)
                    continue  # 找到即停止，检查下一个路径

                # 2. 如果GET不成功，则尝试POST请求
                res_post = self.session.post(vuln_url, data={}, allow_redirects=False, timeout=15)

                if self.is_valid_vulnerability_response(res_post, expected_ext, directory_404_signatures, not_found_signature):
                    print(f"[+] {base_editor_url} - 发现有效路径 (POST): {vuln_url}")
                    found_paths.append(vuln_url)

            except requests.exceptions.RequestException as e:
                print(f"[-] 请求失败: {vuln_url} - {str(e)}")
                pass

        return found_paths

    def is_valid_vulnerability_response(self, response, expected_ext, directory_404_signatures, not_found_signature):
        """验证漏洞响应是否有效"""
        # 基础检查：状态码必须是200
        if response.status_code != 200:
            return False

        # 检查是否有跳转
        if 'Location' in response.headers:
            return False

        # 检查是否匹配404特征
        if directory_404_signatures and expected_ext in directory_404_signatures:
            signature = directory_404_signatures[expected_ext]
            if signature and response.text == signature['content']:
                return False

        # 兼容旧的404签名检查
        if not_found_signature and response.text == not_found_signature:
            return False

        # 检查明显的错误页面
        content_lower = response.text.lower()
        error_indicators = ['404', 'not found', 'error', 'exception', '错误', '异常', 'file not found']
        if any(indicator in content_lower for indicator in error_indicators):
            return False

        # 检查扩展名与内容的匹配性
        if expected_ext == 'jsp':
            # JSP页面不应该输出PHP代码
            if '<?php' in response.text or '<?=' in response.text:
                print(f"[-] JSP页面包含PHP代码，过滤")
                return False
        elif expected_ext == 'php':
            # PHP页面不应该输出JSP代码
            if '<%' in response.text and '%>' in response.text:
                print(f"[-] PHP页面包含JSP代码，过滤")
                return False

        # 检查是否包含.php报错信息（针对JSP页面）
        if expected_ext == 'jsp' and '.php' in content_lower:
            print(f"[-] JSP页面包含.php报错信息，过滤")
            return False

        # 内容长度检查
        if len(response.text) < 10:
            return False

        # 检查是否包含上传相关内容
        upload_indicators = ['upload', 'file', 'form', 'multipart', 'enctype', 'input']
        if any(indicator in content_lower for indicator in upload_indicators):
            return True

        # 如果内容长度合理且没有明显问题，认为有效
        if len(response.text) > 50:
            return True

        return False

    def detect_honeypot_or_suspicious(self, vulnerable_paths, base_editor_url):
        """检测蜜罐或可疑情况"""
        if not vulnerable_paths:
            return False, "无漏洞路径"

        # 检查1: 漏洞路径数量异常
        if len(vulnerable_paths) > self.honeypot_detection['max_vulnerable_paths']:
            return True, f"发现{len(vulnerable_paths)}个漏洞路径，超过正常阈值({self.honeypot_detection['max_vulnerable_paths']})"

        # 检查2: 所有路径都存在（原有逻辑）
        if self.is_all_paths_vulnerable(vulnerable_paths):
            return True, f"所有漏洞路径都存在，疑似通配符路由"

        # 检查3: 路径模式检查
        # 如果发现的路径包含不同扩展名但都有效，可能是可疑的
        extensions = set()
        for path in vulnerable_paths:
            ext = path.split('.')[-1].lower()
            extensions.add(ext)

        # 如果同时发现jsp和php文件都有效，可能是可疑的
        if 'jsp' in extensions and 'php' in extensions and len(vulnerable_paths) >= 4:
            return True, f"同时发现JSP和PHP文件都有效，且数量较多({len(vulnerable_paths)}个)"

        return False, "未检测到异常"

    def save_suspicious_result(self, target, base_editor_url, vulnerable_paths, reason):
        """保存可疑结果"""
        filename = 'husky_suspicious_results.txt'

        with open(filename, 'a', encoding='utf-8') as f:
            f.write(f"[可疑目标检测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}]\n")
            f.write(f"目标: {target}\n")
            f.write(f"编辑器基础URL: {base_editor_url}\n")
            f.write(f"可疑原因: {reason}\n")
            f.write(f"发现的路径数量: {len(vulnerable_paths)}\n")
            f.write("发现的路径:\n")
            for path in vulnerable_paths:
                f.write(f"  - {path}\n")
            f.write("\n" + "="*50 + "\n\n")

        print(f"[!] 可疑结果已保存到 {filename}")

    def is_all_paths_vulnerable(self, vulnerable_paths):
        """
        检查是否所有漏洞路径都被标记为存在
        如果所有路径都存在，很可能是遇到了通配符路由或其他异常情况
        """
        if not vulnerable_paths:
            return False

        # 获取所有漏洞路径的总数
        total_vulnerability_paths = len(self.vulnerability_paths)

        # 如果发现的漏洞数量等于总的漏洞路径数量，则认为是异常情况
        if len(vulnerable_paths) >= total_vulnerability_paths:
            return True

        # 额外检查：如果发现的漏洞数量超过一个合理的阈值（比如总数的80%），也认为是异常
        threshold = max(1, int(total_vulnerability_paths * 0.8))
        if len(vulnerable_paths) >= threshold:
            return True

        return False

    def save_results(self, results):
        """将结果列表保存到文件。"""
        with open('husky_results.txt', 'a', encoding='utf-8') as f:
            for result in results:
                f.write(f"目标: {result['target']}\n")
                f.write(f"可访问URL: {result['accessible_url']}\n")
                f.write(f"编辑器脚本URL: {result['husky_script_url']}\n")
                f.write(f"编辑器基础URL: {result['base_editor_url']}\n")
                f.write("发现的脆弱路径:\n")
                for path in result['vulnerable_paths']:
                    f.write(f"  - {path}\n")
                f.write("\n" + "-"*50 + "\n\n")
        
        if results:
            print(f"[+] {results[0]['target']} - 结果已保存到 husky_results.txt")

    def scan_targets(self, targets):
        """Batch scan targets."""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_target = {
                executor.submit(self.scan_single_target, target.strip()): target.strip() 
                for target in targets if target.strip()
            }
            
            for future in as_completed(future_to_target):
                target = future_to_target[future]
                try:
                    res_list = future.result()
                    if res_list:
                        self.results.extend(res_list)
                except Exception as e:
                    print(f"[!] {target} - 扫描出错: {e}")
        
        self.print_summary()

    def print_summary(self):
        """Print scan results summary."""
        print("\n" + "="*80)
        print("扫描结果摘要:")
        print("="*80)
        
        if not self.results:
            print("[-] 未发现任何存在漏洞的目标。")
            return
        
        # Group results by target
        results_by_target = {}
        for result in self.results:
            target = result['target']
            if target not in results_by_target:
                results_by_target[target] = []
            results_by_target[target].append(result)
        
        print(f"[+] 在 {len(results_by_target)} 个目标上发现了脆弱路径:")
        for target, results in results_by_target.items():
            print(f"\n[+] 目标: {target}")
            print(f"    - 可访问URL: {results[0]['accessible_url']}")
            for result in results:
                print(f"    - ▶ 在编辑器基础路径: {result['base_editor_url']}")
                print(f"      - 编辑器脚本: {result['husky_script_url']}")
                print(f"      - 发现的脆弱路径 ({len(result['vulnerable_paths'])}):")
                for path in result['vulnerable_paths']:
                    print(f"        - {path}")

def main():
    print("HuskyEZCreator.js 漏洞扫描器")
    print("="*40)
    
    if len(sys.argv) != 2:
        print("用法:")
        print("  python husky_scanner.py <目标文件.txt>")
        print("  python husky_scanner.py <单个目标URL>")
        print("\n示例:")
        print("  python husky_scanner.py targets.txt")
        print("  python husky_scanner.py example.com")
        sys.exit(1)
    
    target_input = sys.argv[1]
    
    try:
        # Try to read as a file
        with open(target_input, 'r', encoding='utf-8') as f:
            targets = [line.strip() for line in f if line.strip()]
        print(f"[*] 从文件 {target_input} 加载了 {len(targets)} 个目标")
    except FileNotFoundError:
        # Treat as a single target
        targets = [target_input]
        print(f"[*] Single target mode: {target_input}")
    
    if not targets:
        print("[-] No valid targets found.")
        sys.exit(1)
    
    scanner = HuskyScanner(timeout=10, max_workers=10)
    scanner.scan_targets(targets)

if __name__ == "__main__":
    main() 
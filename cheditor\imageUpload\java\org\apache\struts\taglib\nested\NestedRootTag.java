package org.apache.struts.taglib.nested;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.BodyTagSupport;
import org.apache.struts.util.ResponseUtils;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/NestedRootTag.class */
public class NestedRootTag extends BodyTagSupport implements NestedNameSupport {
    private String name = null;
    private String originalName = "";
    private String originalNesting = "";
    private String originalNestingName = "";

    @Override // org.apache.struts.taglib.nested.NestedPropertySupport
    public String getProperty() {
        return "";
    }

    @Override // org.apache.struts.taglib.nested.NestedPropertySupport
    public void setProperty(String property) {
    }

    @Override // org.apache.struts.taglib.nested.NestedNameSupport
    public String getName() {
        return this.name;
    }

    @Override // org.apache.struts.taglib.nested.NestedNameSupport
    public void setName(String name) {
        this.name = name;
    }

    public int doStartTag() throws JspException {
        HttpServletRequest request = this.pageContext.getRequest();
        this.originalName = this.name;
        this.originalNesting = NestedPropertyHelper.getCurrentProperty(request);
        this.originalNestingName = NestedPropertyHelper.getCurrentName(request, this);
        if (this.name != null) {
            NestedPropertyHelper.setProperty(request, "");
            NestedPropertyHelper.setName(request, this.name);
            return 2;
        }
        return 2;
    }

    public int doAfterBody() throws JspException {
        if (this.bodyContent != null) {
            ResponseUtils.writePrevious(this.pageContext, this.bodyContent.getString());
            this.bodyContent.clearBody();
            return 0;
        }
        return 0;
    }

    public int doEndTag() throws JspException {
        HttpServletRequest request = this.pageContext.getRequest();
        if (this.originalNesting == null) {
            NestedPropertyHelper.deleteReference(request);
        } else {
            NestedPropertyHelper.setName(request, this.originalNestingName);
            NestedPropertyHelper.setProperty(request, "nestOne");
        }
        this.name = this.originalName;
        return 6;
    }

    public void release() {
        super.release();
        this.name = null;
        this.originalName = null;
        this.originalNesting = null;
        this.originalNestingName = null;
    }
}

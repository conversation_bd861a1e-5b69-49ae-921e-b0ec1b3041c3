package jcifs.smb;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import jcifs.util.LogStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2FindFirst2Response.class */
class Trans2FindFirst2Response extends SmbComTransactionResponse {
    static final int SMB_INFO_STANDARD = 1;
    static final int SMB_INFO_QUERY_EA_SIZE = 2;
    static final int SMB_INFO_QUERY_EAS_FROM_LIST = 3;
    static final int SMB_FIND_FILE_DIRECTORY_INFO = 257;
    static final int SMB_FIND_FILE_FULL_DIRECTORY_INFO = 258;
    static final int SMB_FILE_NAMES_INFO = 259;
    static final int SMB_FILE_BOTH_DIRECTORY_INFO = 260;
    int sid;
    boolean isEndOfSearch;
    int eaErrorOffset;
    int lastNameOffset;
    int lastNameBufferIndex;
    String lastName;
    int resumeKey;

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2FindFirst2Response$SmbFindFileBothDirectoryInfo.class */
    class SmbFindFileBothDirectoryInfo implements FileEntry {
        int nextEntryOffset;
        int fileIndex;
        long creationTime;
        long lastAccessTime;
        long lastWriteTime;
        long changeTime;
        long endOfFile;
        long allocationSize;
        int extFileAttributes;
        int fileNameLength;
        int eaSize;
        int shortNameLength;
        String shortName;
        String filename;

        SmbFindFileBothDirectoryInfo() {
        }

        @Override // jcifs.smb.FileEntry
        public String getName() {
            return this.filename;
        }

        @Override // jcifs.smb.FileEntry
        public int getType() {
            return 1;
        }

        @Override // jcifs.smb.FileEntry
        public int getAttributes() {
            return this.extFileAttributes;
        }

        @Override // jcifs.smb.FileEntry
        public long createTime() {
            return this.creationTime;
        }

        @Override // jcifs.smb.FileEntry
        public long lastModified() {
            return this.lastWriteTime;
        }

        @Override // jcifs.smb.FileEntry
        public long length() {
            return this.endOfFile;
        }

        public String toString() {
            return new String("SmbFindFileBothDirectoryInfo[nextEntryOffset=" + this.nextEntryOffset + ",fileIndex=" + this.fileIndex + ",creationTime=" + new Date(this.creationTime) + ",lastAccessTime=" + new Date(this.lastAccessTime) + ",lastWriteTime=" + new Date(this.lastWriteTime) + ",changeTime=" + new Date(this.changeTime) + ",endOfFile=" + this.endOfFile + ",allocationSize=" + this.allocationSize + ",extFileAttributes=" + this.extFileAttributes + ",fileNameLength=" + this.fileNameLength + ",eaSize=" + this.eaSize + ",shortNameLength=" + this.shortNameLength + ",shortName=" + this.shortName + ",filename=" + this.filename + "]");
        }
    }

    Trans2FindFirst2Response() {
        this.command = (byte) 50;
        this.subCommand = (byte) 1;
    }

    String readString(byte[] src, int srcIndex, int len) {
        String str = null;
        try {
            if (this.useUnicode) {
                str = new String(src, srcIndex, len, SmbConstants.UNI_ENCODING);
            } else {
                if (len > 0 && src[(srcIndex + len) - 1] == 0) {
                    len--;
                }
                str = new String(src, srcIndex, len, ServerMessageBlock.OEM_ENCODING);
            }
        } catch (UnsupportedEncodingException uee) {
            LogStream logStream = log;
            if (LogStream.level > 1) {
                uee.printStackTrace(log);
            }
        }
        return str;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        if (this.subCommand == 1) {
            this.sid = readInt2(buffer, bufferIndex);
            bufferIndex += 2;
        }
        this.numEntries = readInt2(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 2;
        this.isEndOfSearch = (buffer[bufferIndex2] & 1) == 1;
        int bufferIndex3 = bufferIndex2 + 2;
        this.eaErrorOffset = readInt2(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 2;
        this.lastNameOffset = readInt2(buffer, bufferIndex4);
        return (bufferIndex4 + 2) - bufferIndex;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        this.lastNameBufferIndex = bufferIndex + this.lastNameOffset;
        this.results = new SmbFindFileBothDirectoryInfo[this.numEntries];
        for (int i = 0; i < this.numEntries; i++) {
            SmbFindFileBothDirectoryInfo e = new SmbFindFileBothDirectoryInfo();
            this.results[i] = e;
            e.nextEntryOffset = readInt4(buffer, bufferIndex);
            e.fileIndex = readInt4(buffer, bufferIndex + 4);
            e.creationTime = readTime(buffer, bufferIndex + 8);
            e.lastWriteTime = readTime(buffer, bufferIndex + 24);
            e.endOfFile = readInt8(buffer, bufferIndex + 40);
            e.extFileAttributes = readInt4(buffer, bufferIndex + 56);
            e.fileNameLength = readInt4(buffer, bufferIndex + 60);
            e.filename = readString(buffer, bufferIndex + 94, e.fileNameLength);
            if (this.lastNameBufferIndex >= bufferIndex && (e.nextEntryOffset == 0 || this.lastNameBufferIndex < bufferIndex + e.nextEntryOffset)) {
                this.lastName = e.filename;
                this.resumeKey = e.fileIndex;
            }
            bufferIndex += e.nextEntryOffset;
        }
        return this.dataCount;
    }

    @Override // jcifs.smb.SmbComTransactionResponse, jcifs.smb.ServerMessageBlock
    public String toString() {
        String c;
        if (this.subCommand == 1) {
            c = "Trans2FindFirst2Response[";
        } else {
            c = "Trans2FindNext2Response[";
        }
        return new String(c + super.toString() + ",sid=" + this.sid + ",searchCount=" + this.numEntries + ",isEndOfSearch=" + this.isEndOfSearch + ",eaErrorOffset=" + this.eaErrorOffset + ",lastNameOffset=" + this.lastNameOffset + ",lastName=" + this.lastName + "]");
    }
}

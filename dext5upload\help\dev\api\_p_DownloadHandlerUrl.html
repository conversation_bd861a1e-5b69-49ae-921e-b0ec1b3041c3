﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: DownloadHandlerUrl</h3>
    <p class="ttl">config.DownloadHandlerUrl</p>
    <p class="txt">
        사용 용도에 따라서 다운로드 경로를 상황에 맞게 개별 Download Handler를 구현하여 사용하고자 할 때 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        설정하실 때 http:// 부터의 전체경로를 설정합니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 Download handler url을 JSP언어로 설정합니다.
        DEXT5UPLOAD.config.DownloadHandlerUrl = 'http://www.dext5.com/handler/dext5handler.jsp';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


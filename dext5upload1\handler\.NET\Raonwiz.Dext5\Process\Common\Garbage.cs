﻿using System;
using System.IO;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x0200001F RID: 31
	public class Garbage : Base
	{
		// Token: 0x0600023A RID: 570 RVA: 0x00018D34 File Offset: 0x00016F34
		public Garbage(HttpContext context, string pTempPath, int pCleanDay, string pPhysicalPath, string pVirtualPath) : base(context)
		{
			this.tempPath = pTempPath;
			this.cleanDay = pCleanDay;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
		}

		// Token: 0x0600023B RID: 571 RVA: 0x00018D84 File Offset: 0x00016F84
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			try
			{
				string text = string.Empty;
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text = this.physicalPath;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					text = this.hContext.Request.MapPath(this.virtualPath);
				}
				this.tempPath = base.GetTempPath(this.tempPath);
				if (text.IndexOf(this.tempPath) == 0)
				{
					return "";
				}
				if (Directory.Exists(this.tempPath))
				{
					string[] files = Directory.GetFiles(this.tempPath, "*" + this.m_strHSTempSuffix, SearchOption.AllDirectories);
					foreach (string text2 in files)
					{
						FileInfo fileInfo = new FileInfo(text2);
						if (fileInfo.CreationTime < DateTime.Now.AddDays((double)(-1 * this.cleanDay)))
						{
							string path = Dext5Encoding.Base64Decoding(File.ReadAllText(text2));
							if (File.Exists(path))
							{
								File.Delete(path);
							}
						}
					}
					string[] directories = Directory.GetDirectories(this.tempPath);
					foreach (string path2 in directories)
					{
						DirectoryInfo directoryInfo = new DirectoryInfo(path2);
						if (directoryInfo.CreationTime < DateTime.Now.AddDays((double)(-1 * this.cleanDay)))
						{
							directoryInfo.Delete(true);
						}
					}
					string[] files2 = Directory.GetFiles(this.tempPath);
					foreach (string text3 in files2)
					{
						FileInfo fileInfo2 = new FileInfo(text3);
						if (fileInfo2.CreationTime < DateTime.Now.AddDays((double)(-1 * this.cleanDay)))
						{
							if (fileInfo2.Name.IndexOf(this.m_strHSTempSuffix) == -1)
							{
								fileInfo2.Delete();
							}
							else
							{
								string path3 = Dext5Encoding.Base64Decoding(File.ReadAllText(text3));
								if (File.Exists(path3))
								{
									File.Delete(path3);
								}
								fileInfo2.Delete();
							}
						}
					}
				}
			}
			catch
			{
			}
			return null;
		}

		// Token: 0x04000130 RID: 304
		private int cleanDay = 2;

		// Token: 0x04000131 RID: 305
		private string physicalPath = string.Empty;

		// Token: 0x04000132 RID: 306
		private string virtualPath = string.Empty;
	}
}

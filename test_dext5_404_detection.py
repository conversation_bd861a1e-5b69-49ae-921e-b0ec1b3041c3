#!/usr/bin/env python3
"""
测试改进后的DEXT5扫描器404检测机制
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入DEXT5扫描器
try:
    from dext5_scanner import DEXT5Scanner
except ImportError:
    # 如果在editor目录下
    sys.path.insert(0, r'e:\docs\untitled\editor')
    from dext5_scanner import DEXT5Scanner

def test_404_signature_generation():
    """测试404特征生成功能"""
    print("测试404特征生成功能")
    print("=" * 50)
    
    scanner = DEXT5Scanner()
    
    # 测试目标（可以是任何可访问的网站）
    test_targets = [
        "httpbin.org",
        "example.com",
        "google.com"
    ]
    
    for target in test_targets:
        print(f"\n[*] 测试目标: {target}")
        
        # 测试URL可访问性
        accessible_url, response = scanner.test_url_accessibility(target)
        if not accessible_url:
            print(f"[-] {target} - 无法访问")
            continue
        
        print(f"[+] {target} - 可访问 ({accessible_url})")
        
        # 测试根目录404特征获取
        print(f"[*] {target} - 获取根目录404特征...")
        root_signatures = scanner.get_directory_404_signature(accessible_url, '/')
        
        print(f"[+] {target} - 根目录404特征:")
        for ext, signature in root_signatures.items():
            if signature:
                print(f"  {ext.upper()}: 状态码={signature['status_code']}, 长度={signature['content_length']}")
                print(f"    内容预览: {signature['content'][:100]}...")
            else:
                print(f"  {ext.upper()}: 获取失败")
        
        # 测试子目录404特征获取
        test_dirs = ['test/', 'admin/', 'upload/']
        for test_dir in test_dirs:
            print(f"\n[*] {target} - 获取 {test_dir} 目录404特征...")
            dir_signatures = scanner.get_directory_404_signature(accessible_url, test_dir)
            
            print(f"[+] {target} - {test_dir} 目录404特征:")
            for ext, signature in dir_signatures.items():
                if signature:
                    print(f"  {ext.upper()}: 状态码={signature['status_code']}, 长度={signature['content_length']}")
                else:
                    print(f"  {ext.upper()}: 获取失败")

def test_handler_validation():
    """测试handler验证功能"""
    print("\n\n测试Handler验证功能")
    print("=" * 50)
    
    scanner = DEXT5Scanner()
    
    # 模拟测试数据
    test_cases = [
        {
            'name': '有效的DEXT5 Handler',
            'url': 'http://example.com',
            'base_path': '/dext5upload/',
            'handler_file': 'handler/dext5handler.jsp',
            'expected': True
        },
        {
            'name': '404页面',
            'url': 'http://example.com',
            'base_path': '/nonexistent/',
            'handler_file': 'handler/dext5handler.jsp',
            'expected': False
        }
    ]
    
    for case in test_cases:
        print(f"\n[*] 测试用例: {case['name']}")
        
        # 获取404特征
        accessible_url, _ = scanner.test_url_accessibility(case['url'])
        if accessible_url:
            signatures = scanner.get_directory_404_signature(accessible_url, case['base_path'])
            
            # 测试handler
            is_valid, handler_url, content_length = scanner.test_handler_path(
                accessible_url, case['base_path'], case['handler_file'], signatures
            )
            
            print(f"  URL: {handler_url}")
            print(f"  有效性: {is_valid}")
            print(f"  内容长度: {content_length}")
            print(f"  期望结果: {case['expected']}")
            
            if is_valid == case['expected']:
                print(f"  ✅ 测试通过")
            else:
                print(f"  ❌ 测试失败")

def test_improved_scanning():
    """测试改进后的完整扫描流程"""
    print("\n\n测试改进后的完整扫描流程")
    print("=" * 50)
    
    # 创建扫描器
    scanner = DEXT5Scanner(timeout=5, max_workers=1)
    
    # 测试目标
    test_target = "httpbin.org"  # 使用httpbin作为测试目标
    
    print(f"[*] 测试目标: {test_target}")
    
    # 执行扫描
    result = scanner.scan_single_target(test_target)
    
    if result:
        print(f"[+] 扫描完成，发现结果:")
        print(f"  目标: {result['target']}")
        print(f"  访问URL: {result['accessible_url']}")
        print(f"  DEXT5资源数量: {len(result['dext5_resources'])}")
        print(f"  有效Handler数量: {len(result['valid_handlers'])}")
        
        for handler in result['valid_handlers']:
            print(f"    - {handler['handler_url']} (长度: {handler['content_length']})")
    else:
        print(f"[-] 未发现DEXT5组件")

def main():
    print("DEXT5扫描器404检测机制测试")
    print("=" * 60)
    
    try:
        # 测试404特征生成
        test_404_signature_generation()
        
        # 测试handler验证
        test_handler_validation()
        
        # 测试完整扫描流程
        test_improved_scanning()
        
        print("\n" + "=" * 60)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

﻿using System;
using System.IO;
using System.Security;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Raonwiz.Dext5.License.Library.General.Security
{
	// Token: 0x02000004 RID: 4
	public class LicenseAuthorization
	{
		// Token: 0x0600001F RID: 31 RVA: 0x00002248 File Offset: 0x00000448
		public static string CheckDext5UploadLicense(string productKey, string licenseKey, string domain)
		{
			string result = string.Empty;
			try
			{
				string[] array = null;
				try
				{
					string text = LicenseAuthorization.Decrypt(licenseKey, "dext5upload_licensekey");
					array = text.Split(new char[]
					{
						'|'
					});
				}
				catch (Exception)
				{
					throw new SecurityException("Invalid license Key");
				}
				if (array.Length == 3)
				{
					LicenseInfo licenseInfo = new LicenseInfo
					{
						ProductKey = array[0],
						ExpireDate = Convert.ToDateTime(array[1].ToString() + " 23:59:59"),
						DomainList = array[2]
					};
					LicenseAuthorization.ValidateDext5UploadLicense(licenseInfo, productKey, domain);
				}
				result = "ok";
			}
			catch (Exception ex)
			{
				result = ex.Message;
			}
			return result;
		}

		// Token: 0x06000020 RID: 32 RVA: 0x0000230C File Offset: 0x0000050C
		private static void ValidateDext5UploadLicense(LicenseInfo licenseInfo, string productKey, string domainValue)
		{
			if (DateTime.Now.CompareTo(licenseInfo.ExpireDate) > 0)
			{
				throw new SecurityException("License Expired On: " + licenseInfo.ExpireDate.ToShortDateString());
			}
			if (productKey == licenseInfo.ProductKey)
			{
				string text = licenseInfo.DomainList.ToLower();
				text += ",localhost";
				text += ",127.0.0.1";
				string[] array = text.Split(new char[]
				{
					','
				});
				string empty = string.Empty;
				foreach (string text2 in array)
				{
					if (!string.IsNullOrEmpty(text2))
					{
						string text3 = text2.Replace("*.", "");
						string[] array3 = text3.Split(new string[]
						{
							"-"
						}, StringSplitOptions.RemoveEmptyEntries);
						int num;
						if ((array3.Length != 3 && array3.Length != 4) || !int.TryParse(array3[0], out num) || !int.TryParse(array3[1], out num) || !int.TryParse(array3[2], out num))
						{
							if (domainValue.ToLower().IndexOf(text3) == -1)
							{
								string text4 = text3.Replace("www.", "");
								text4 = "/" + text4;
								if (domainValue.ToLower().IndexOf(text4) == -1)
								{
									goto IL_135;
								}
							}
						}
						return;
					}
					IL_135:;
				}
				throw new SecurityException("Invalid Domain: " + domainValue);
			}
			throw new SecurityException("Invalid Product Key: " + productKey);
		}

		// Token: 0x06000021 RID: 33 RVA: 0x000024A0 File Offset: 0x000006A0
		public static LicenseInfo CheckCommonLicense(string productName, string productKey, string licenseKey, string domain)
		{
			LicenseInfo licenseInfo = new LicenseInfo();
			try
			{
				string[] array = null;
				try
				{
					string text = LicenseAuthorization.Decrypt(licenseKey, "dext5upload_licensekey");
					array = text.Split(new char[]
					{
						'|'
					});
				}
				catch (Exception)
				{
					throw new SecurityException("Invalid license Key");
				}
				if (array.Length > 3)
				{
					licenseInfo.CustomName = array[0];
					licenseInfo.SystemName = array[1];
					licenseInfo.LicenseCheckType = array[2];
					licenseInfo.DomainList = array[3];
					licenseInfo.ExpireDate = Convert.ToDateTime(array[4].ToString() + " 23:59:59");
					licenseInfo.UserCount = array[5];
					licenseInfo.ProductKey = array[6];
					licenseInfo.ProductName = array[7].Split(new char[]
					{
						','
					})[0];
					licenseInfo.InterworkingModule = null;
					if (array[7].Split(new char[]
					{
						','
					}).Length >= 3)
					{
						licenseInfo.InterworkingModule = array[7].Split(new char[]
						{
							','
						})[2];
					}
					LicenseAuthorization.ValidateCommonLicense(licenseInfo, productName, productKey, domain);
				}
				else
				{
					licenseInfo.ProductKey = array[0];
					licenseInfo.ExpireDate = Convert.ToDateTime(array[1].ToString() + " 23:59:59");
					licenseInfo.DomainList = array[2];
					LicenseAuthorization.ValidateDext5UploadLicense(licenseInfo, productKey, domain);
				}
				licenseInfo.Validate = "ok";
			}
			catch (Exception ex)
			{
				licenseInfo.Validate = ex.Message;
			}
			return licenseInfo;
		}

		// Token: 0x06000022 RID: 34 RVA: 0x0000263C File Offset: 0x0000083C
		private static void ValidateCommonLicense(LicenseInfo licenseInfo, string productName, string productKey, string domainValue)
		{
			if (DateTime.Now.CompareTo(licenseInfo.ExpireDate) > 0)
			{
				throw new SecurityException("License Expired On: " + licenseInfo.ExpireDate.ToShortDateString());
			}
			if (licenseInfo.ProductName.Substring(0, 4).IndexOf(productName.Substring(5, 1).ToUpper()) <= -1 && (!(productName.Substring(5, 1).ToUpper() == "U") || licenseInfo.ProductName.Substring(0, 4).IndexOf("M") <= -1))
			{
				throw new SecurityException("Invalid Product Name: " + productName);
			}
			if (productKey == licenseInfo.ProductKey)
			{
				string text = licenseInfo.DomainList.ToLower();
				if (licenseInfo.ProductName.Substring(0, 4).IndexOf("R") < 0)
				{
					text += ",localhost";
					text += ",127.0.0.1";
				}
				string[] array = text.Split(new char[]
				{
					','
				});
				string empty = string.Empty;
				foreach (string text2 in array)
				{
					if (!string.IsNullOrEmpty(text2))
					{
						string text3 = text2.Replace("*.", "");
						string[] array3 = text3.Split(new string[]
						{
							"-"
						}, StringSplitOptions.RemoveEmptyEntries);
						int num;
						if ((array3.Length != 3 && array3.Length != 4) || !int.TryParse(array3[0], out num) || !int.TryParse(array3[1], out num) || !int.TryParse(array3[2], out num))
						{
							if (domainValue.ToLower().IndexOf(text3) == -1)
							{
								string text4 = text3.Replace("www.", "");
								text4 = "/" + text4;
								if (domainValue.ToLower().IndexOf(text4) == -1)
								{
									goto IL_1AA;
								}
							}
						}
						return;
					}
					IL_1AA:;
				}
				throw new SecurityException("Invalid Domain: " + domainValue);
			}
			throw new SecurityException("Invalid Product Key: " + productKey);
		}

		// Token: 0x06000023 RID: 35 RVA: 0x00002858 File Offset: 0x00000A58
		private static RijndaelManaged GetRijndaelManaged(string secretKey)
		{
			byte[] array = new byte[16];
			byte[] bytes = Encoding.UTF8.GetBytes(secretKey);
			Array.Copy(bytes, array, Math.Min(array.Length, bytes.Length));
			return new RijndaelManaged
			{
				Mode = CipherMode.CBC,
				Padding = PaddingMode.PKCS7,
				KeySize = 128,
				BlockSize = 128,
				Key = array,
				IV = array
			};
		}

		// Token: 0x06000024 RID: 36 RVA: 0x000028C4 File Offset: 0x00000AC4
		private static byte[] Decrypt(byte[] encryptedData, RijndaelManaged rijndaelManaged)
		{
			return rijndaelManaged.CreateDecryptor().TransformFinalBlock(encryptedData, 0, encryptedData.Length);
		}

		// Token: 0x06000025 RID: 37 RVA: 0x000028D8 File Offset: 0x00000AD8
		private static string Decrypt(string encryptedText, string key)
		{
			byte[] encryptedData = HttpServerUtility.UrlTokenDecode(encryptedText);
			byte[] bytes = LicenseAuthorization.Decrypt(encryptedData, LicenseAuthorization.GetRijndaelManaged(key));
			return Encoding.UTF8.GetString(bytes);
		}

		// Token: 0x0400000B RID: 11
		private static char m_PathChar = Path.DirectorySeparatorChar;
	}
}

package jcifs.netbios;

import java.io.IOException;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NbtException.class */
public class NbtException extends IOException {
    public static final int SUCCESS = 0;
    public static final int ERR_NAM_SRVC = 1;
    public static final int ERR_SSN_SRVC = 2;
    public static final int FMT_ERR = 1;
    public static final int SRV_ERR = 2;
    public static final int IMP_ERR = 4;
    public static final int RFS_ERR = 5;
    public static final int ACT_ERR = 6;
    public static final int CFT_ERR = 7;
    public static final int CONNECTION_REFUSED = -1;
    public static final int NOT_LISTENING_CALLED = 128;
    public static final int NOT_LISTENING_CALLING = 129;
    public static final int CALLED_NOT_PRESENT = 130;
    public static final int NO_RESOURCES = 131;
    public static final int UNSPECIFIED = 143;
    public int errorClass;
    public int errorCode;

    public static String getErrorString(int errorClass, int errorCode) {
        String result;
        switch (errorClass) {
            case 0:
                result = "SUCCESS";
                break;
            case 1:
                String result2 = "ERR_NAM_SRVC/";
                switch (errorCode) {
                    case 1:
                        result2 = result2 + "FMT_ERR: Format Error";
                        break;
                }
                result = result2 + "Unknown error code: " + errorCode;
                break;
            case 2:
                String result3 = "ERR_SSN_SRVC/";
                switch (errorCode) {
                    case -1:
                        result = result3 + "Connection refused";
                        break;
                    case 128:
                        result = result3 + "Not listening on called name";
                        break;
                    case NOT_LISTENING_CALLING /* 129 */:
                        result = result3 + "Not listening for calling name";
                        break;
                    case 130:
                        result = result3 + "Called name not present";
                        break;
                    case 131:
                        result = result3 + "Called name present, but insufficient resources";
                        break;
                    case UNSPECIFIED /* 143 */:
                        result = result3 + "Unspecified error";
                        break;
                    default:
                        result = result3 + "Unknown error code: " + errorCode;
                        break;
                }
            default:
                result = "unknown error class: " + errorClass;
                break;
        }
        return result;
    }

    public NbtException(int errorClass, int errorCode) {
        super(getErrorString(errorClass, errorCode));
        this.errorClass = errorClass;
        this.errorCode = errorCode;
    }

    @Override // java.lang.Throwable
    public String toString() {
        return new String("errorClass=" + this.errorClass + ",errorCode=" + this.errorCode + ",errorString=" + getErrorString(this.errorClass, this.errorCode));
    }
}

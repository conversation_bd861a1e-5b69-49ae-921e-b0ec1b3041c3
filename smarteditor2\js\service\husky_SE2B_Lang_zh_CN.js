/*
Copyright (C) NAVER corp.  

This library is free software; you can redistribute it and/or  
modify it under the terms of the GNU Lesser General Public  
License as published by the Free Software Foundation; either  
version 2.1 of the License, or (at your option) any later version.  

This library is distributed in the hope that it will be useful,  
but WITHOUT ANY WARRANTY; without even the implied warranty of  
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU  
Lesser General Public License for more details.  

You should have received a copy of the GNU Lesser General Public  
License along with this library; if not, write to the Free Software  
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA  
*/
if(typeof window.nhn=='undefined'){window.nhn = {};}
/**
 * @fileOverview This file contains a message mapping(Korean), which is used to map the message code to the actual message
 * @name husky_SE2B_Lang_zh_CN.js
 * @ unescape
 */
var oMessageMap_zh_CN = {
	'SE_EditingAreaManager.onExit' : '内容有了变化。',
	'SE_Color.invalidColorCode' : '请你输入正确的色相代码。 \n\n 例) #000000, #FF0000, #FFFFFF, #ffffff, ffffff',
	'SE_Hyperlink.invalidURL' : '你输入的URL不符条件。',
	'SE_FindReplace.keywordMissing' : '请你输入要找的词汇。',
	'SE_FindReplace.keywordNotFound' : '没有词汇符合条件。',
	'SE_FindReplace.replaceAllResultP1' : '符合条件的内容改编为',
	'SE_FindReplace.replaceAllResultP2' : '件',
	'SE_FindReplace.notSupportedBrowser' : '这是你现在使用的浏览器不可支持的功能。\n\n麻烦你很道歉。',
	'SE_FindReplace.replaceKeywordNotFound' : '没有词汇要改变。',
	'SE_LineHeight.invalidLineHeight' : '这是有问题的值。',
	'SE_Footnote.defaultText' : '请你输入脚注内容。',
	'SE.failedToLoadFlash' : 'flash被隔绝，不能使用该功能。',
	'SE2M_EditingModeChanger.confirmTextMode' : '转换为text模式就能维持制作内容，\n\n但字体等编辑效果和图像等附件内容都会消失。\n\n你还要继续吗？',
	'SE2M_FontNameWithLayerUI.sSampleText' : 'ABCD'
};
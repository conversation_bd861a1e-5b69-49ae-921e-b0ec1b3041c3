﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5
{
	// Token: 0x02000035 RID: 53
	public class ResumeUpload : Base
	{
		// Token: 0x0600030F RID: 783 RVA: 0x00022DDC File Offset: 0x00020FDC
		public ResumeUpload(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000310 RID: 784 RVA: 0x00022E60 File Offset: 0x00021060
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string dext5CMD = this._entity_dextParam.dext5CMD;
			string a;
			if ((a = dext5CMD) != null)
			{
				if (!(a == "h5rur"))
				{
					if (!(a == "h5rer"))
					{
						if (!(a == "h5ps"))
						{
							if (a == "h5pdf")
							{
								this.PartialDeleteFile();
							}
						}
						else
						{
							this.PartialSize();
						}
					}
					else
					{
						this.ResumeEndRequest(pCompleteBeforeEvent, pCompleteBeforeEventEx, pCompleteEvent, pCompleteEventEx, ref pCustomError);
					}
				}
				else
				{
					this.ResumeUploadRequest(pBeforeInitializeEvent, pBeforeInitializeEventEx, ref pCustomError);
				}
			}
			return null;
		}

		// Token: 0x06000311 RID: 785 RVA: 0x00022EE0 File Offset: 0x000210E0
		private void ResumeUploadRequest(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string str = string.Empty;
			try
			{
				if (!base.CheckCaller("html5"))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|013|Bad Request Type"));
					return;
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					return;
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					return;
				}
				this.tempPath = base.GetTempPath(this.tempPath);
				byte[] array = null;
				if (this.hContext.Request.Headers["Dext5-Encoded"] != null)
				{
					using (Stream inputStream = this.hContext.Request.Files["Slice"].InputStream)
					{
						byte[] array2 = new byte[inputStream.Length];
						inputStream.Read(array2, 0, Convert.ToInt32(inputStream.Length));
						string @string = Encoding.UTF8.GetString(array2);
						array = Convert.FromBase64String(@string);
						inputStream.Close();
						goto IL_1BC;
					}
				}
				using (Stream inputStream2 = this.hContext.Request.Files["Slice"].InputStream)
				{
					array = new byte[inputStream2.Length];
					inputStream2.Read(array, 0, Convert.ToInt32(inputStream2.Length));
					inputStream2.Close();
				}
				IL_1BC:
				if (array == null)
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|003|No file chunk uploaded"));
					return;
				}
				string text = string.Empty;
				string text2 = string.Empty;
				string text3 = string.Empty;
				string guid = this._entity_dextParam.GUID;
				string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
				string path = string.Concat(new object[]
				{
					tempFileFolder,
					this.m_PathChar,
					guid,
					this.m_strHSTempSuffix
				});
				if (File.Exists(path))
				{
					text2 = base.FileLocationInfoReadWrite("R", guid, "");
					if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
						return;
					}
					text3 = Path.GetFileName(text2);
				}
				else
				{
					text = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
					if (string.IsNullOrEmpty(text))
					{
						throw new Exception("Error occured on the server side");
					}
					if (text.IndexOf("error") == 0)
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(Dext5Parameter.MakeParameter(text));
						return;
					}
					string[] array3 = text.Split(new char[]
					{
						'|'
					});
					text2 = array3[0];
					text3 = array3[1];
					bool flag = false;
					string text4 = text2;
					try
					{
						pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
						if (!text4.Equals(text2))
						{
							flag = true;
						}
					}
					catch
					{
					}
					try
					{
						UploadEventEntity uploadEventEntity = new UploadEventEntity();
						uploadEventEntity.Context = this.hContext;
						uploadEventEntity.NewFileLocation = text2;
						uploadEventEntity.ResponseFileName = text3;
						pBeforeInitializeEventEx(uploadEventEntity);
						text2 = uploadEventEntity.NewFileLocation;
						text3 = uploadEventEntity.ResponseFileName;
						if (!text4.Equals(text2))
						{
							flag = true;
						}
					}
					catch
					{
					}
					if (flag)
					{
						string[] array4 = base.InitializeEventFileExec(text2, text3);
						text2 = array4[0];
						text3 = array4[1];
					}
					base.FileLocationInfoReadWrite("W", guid, text2);
				}
				string empty = string.Empty;
				if (pCustomError == null)
				{
					int num;
					if (File.Exists(text2))
					{
						num = 1;
					}
					else
					{
						num = 0;
					}
					using (FileStream fileStream = new FileStream(text2, (num == 0) ? FileMode.Create : FileMode.Append))
					{
						using (BinaryWriter binaryWriter = new BinaryWriter(fileStream))
						{
							binaryWriter.Write(array);
							binaryWriter.Close();
							fileStream.Close();
						}
					}
					str = text2;
				}
				else
				{
					this.hContext.Response.Clear();
					string text5 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
					str = text5;
					text5 = Dext5Parameter.MakeParameter(text5);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text5);
				}
			}
			catch (Exception ex)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|004|ProcessUpload error occured on the server side"));
				str = ex.Message;
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html5 Upload, " + str, this._str_DebugFilePath);
			}
		}

		// Token: 0x06000312 RID: 786 RVA: 0x000234BC File Offset: 0x000216BC
		private void ResumeEndRequest(UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, ref Dext5CustomError pCustomError)
		{
			string str = string.Empty;
			string guid = this._entity_dextParam.GUID;
			string text = base.FileLocationInfoReadWrite("R", guid, "");
			string text2 = string.Empty;
			if (string.IsNullOrEmpty(text) || !File.Exists(text))
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				return;
			}
			text2 = Path.GetFileName(text);
			string text3 = this._entity_dextParam.fileName;
			text3 = text3.Normalize(NormalizationForm.FormC);
			string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
			string.Concat(new object[]
			{
				tempFileFolder,
				this.m_PathChar,
				guid,
				this.m_strHSTempSuffix
			});
			Directory.Delete(tempFileFolder, true);
			string text4 = string.Empty;
			string text5 = string.Empty;
			if (!string.IsNullOrEmpty(this.physicalPath))
			{
				text4 = text;
			}
			else if (!string.IsNullOrEmpty(this.virtualPath))
			{
				string oldValue = HostingEnvironment.MapPath("~/");
				text4 = "/" + text.Replace(oldValue, "");
				text4 = text4.Replace(this.m_PathChar, '/');
			}
			try
			{
				pCompleteBeforeEvent(this.hContext, ref text, ref text4, ref text2, ref this._str_ResponseCustomValue);
			}
			catch
			{
			}
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.NewFileLocation = text;
				uploadEventEntity.ResponseFileServerPath = text4;
				uploadEventEntity.ResponseFileName = text2;
				uploadEventEntity.ResponseGroupId = this._entity_dextParam.fileGroupID;
				uploadEventEntity.FileIndex = this._entity_dextParam.fileIndex;
				pCompleteBeforeEventEx(uploadEventEntity);
				text = uploadEventEntity.NewFileLocation;
				text4 = uploadEventEntity.ResponseFileServerPath;
				text2 = uploadEventEntity.ResponseFileName;
				this._str_ResponseCustomValue = uploadEventEntity.ResponseCustomValue;
				this._str_ResponseGroupId = uploadEventEntity.ResponseGroupId;
			}
			catch
			{
			}
			string text6 = "";
			if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
			{
				text6 = text6 + "|" + this._str_ResponseCustomValue;
			}
			if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
			{
				text6 = text6 + "\b" + this._str_ResponseGroupId;
			}
			text5 = string.Concat(new string[]
			{
				text3,
				"::",
				text4,
				"|",
				text2,
				text6
			});
			str = text5;
			text5 = Dext5Parameter.MakeParameter(text5);
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text5);
			if (pCustomError != null)
			{
				File.Delete(text);
				this.hContext.Response.Clear();
				text5 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
				str = text5;
				text5 = Dext5Parameter.MakeParameter(text5);
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text5);
			}
			if (pCustomError == null)
			{
				try
				{
					pCompleteEvent(this.hContext, text, text4, text2);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text,
						ResponseFileServerPath = text4,
						ResponseFileName = text2
					});
				}
				catch
				{
				}
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html5 Merge, " + str, this._str_DebugFilePath);
			}
		}

		// Token: 0x06000313 RID: 787 RVA: 0x000238D0 File Offset: 0x00021AD0
		private void PartialSize()
		{
			string text = string.Empty;
			bool flag = false;
			if (this._entity_dextParam.crossDomain != null && this._entity_dextParam.crossDomain.Equals("1"))
			{
				flag = true;
			}
			string guid = this._entity_dextParam.GUID;
			string text2 = base.FileLocationInfoReadWrite("R", guid, "");
			string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
			string.Concat(new object[]
			{
				tempFileFolder,
				this.m_PathChar,
				guid,
				this.m_strHSTempSuffix
			});
			if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
			{
				Directory.Delete(tempFileFolder, true);
				if (flag)
				{
					text += "<html><head>";
					text += "<script type=\"text/javascript\">";
					text += "if (window.postMessage) {";
					text += "if (window.addEventListener) {";
					text += "window.addEventListener('message', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "}, false);";
					text += "}";
					text += "else if (window.attachEvent) {";
					text += "window.attachEvent('onmessage', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "});";
					text += "}";
					text += "}";
					text += "</script>";
					text += "</head>";
					text += "<body>";
					text += "{0}";
					text += "</body>";
					text += "</html>";
				}
				else
				{
					text = "{0}";
				}
				text = text.Replace("{0}", "[SKIP]" + Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return;
			}
			if (File.Exists(text2))
			{
				if (flag)
				{
					text += "<html><head>";
					text += "<script type=\"text/javascript\">";
					text += "if (window.postMessage) {";
					text += "if (window.addEventListener) {";
					text += "window.addEventListener('message', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "}, false);";
					text += "}";
					text += "else if (window.attachEvent) {";
					text += "window.attachEvent('onmessage', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "});";
					text += "}";
					text += "}";
					text += "</script>";
					text += "</head>";
					text += "<body>";
					text += "{0}";
					text += "</body>";
					text += "</html>";
				}
				else
				{
					text = "{0}";
				}
				text = text.Replace("{0}", "[SKIP]" + Dext5Parameter.MakeParameter(new FileInfo(text2).Length.ToString()));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return;
			}
			Directory.Delete(tempFileFolder, true);
			if (flag)
			{
				text += "<html><head>";
				text += "<script type=\"text/javascript\">";
				text += "if (window.postMessage) {";
				text += "if (window.addEventListener) {";
				text += "window.addEventListener('message', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "}, false);";
				text += "}";
				text += "else if (window.attachEvent) {";
				text += "window.attachEvent('onmessage', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "});";
				text += "}";
				text += "}";
				text += "</script>";
				text += "</head>";
				text += "<body>";
				text += "{0}";
				text += "</body>";
				text += "</html>";
			}
			else
			{
				text = "{0}";
			}
			text = text.Replace("{0}", "[SKIP]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text);
		}

		// Token: 0x06000314 RID: 788 RVA: 0x00023DF8 File Offset: 0x00021FF8
		private void PartialDeleteFile()
		{
			string text = string.Empty;
			bool flag = false;
			if (this._entity_dextParam.crossDomain != null && this._entity_dextParam.crossDomain.Equals("1"))
			{
				flag = true;
			}
			string guid = this._entity_dextParam.GUID;
			string text2 = base.FileLocationInfoReadWrite("R", guid, "");
			string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
			string.Concat(new object[]
			{
				tempFileFolder,
				this.m_PathChar,
				guid,
				this.m_strHSTempSuffix
			});
			this.tempPath = base.GetTempPath(this.tempPath);
			if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
			{
				Directory.Delete(tempFileFolder, true);
				if (flag)
				{
					text += "<html><head>";
					text += "<script type=\"text/javascript\">";
					text += "if (window.postMessage) {";
					text += "if (window.addEventListener) {";
					text += "window.addEventListener('message', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "}, false);";
					text += "}";
					text += "else if (window.attachEvent) {";
					text += "window.attachEvent('onmessage', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "});";
					text += "}";
					text += "}";
					text += "</script>";
					text += "</head>";
					text += "<body>";
					text += "{0}";
					text += "</body>";
					text += "</html>";
				}
				else
				{
					text = "{0}";
				}
				text = text.Replace("{0}", "[SKIP]" + Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return;
			}
			if (File.Exists(text2))
			{
				Directory.Delete(tempFileFolder, true);
				File.Delete(text2);
				return;
			}
			Directory.Delete(tempFileFolder, true);
			if (flag)
			{
				text += "<html><head>";
				text += "<script type=\"text/javascript\">";
				text += "if (window.postMessage) {";
				text += "if (window.addEventListener) {";
				text += "window.addEventListener('message', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "}, false);";
				text += "}";
				text += "else if (window.attachEvent) {";
				text += "window.attachEvent('onmessage', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "});";
				text += "}";
				text += "}";
				text += "</script>";
				text += "</head>";
				text += "<body>";
				text += "{0}";
				text += "</body>";
				text += "</html>";
			}
			else
			{
				text = "{0}";
			}
			text = text.Replace("{0}", "[SKIP]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text);
		}

		// Token: 0x0400019C RID: 412
		private string physicalPath = string.Empty;

		// Token: 0x0400019D RID: 413
		private string virtualPath = string.Empty;

		// Token: 0x0400019E RID: 414
		private string fileWhiteList = string.Empty;

		// Token: 0x0400019F RID: 415
		private string fileBlackList = string.Empty;

		// Token: 0x040001A0 RID: 416
		private string[] fileBlackWordList;

		// Token: 0x040001A1 RID: 417
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

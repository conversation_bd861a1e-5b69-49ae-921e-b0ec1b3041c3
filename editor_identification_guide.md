# 📚 编辑器识别与漏洞扫描通用方法指南

## 🎯 核心识别策略总结

基于对 `dext5_scanner.py` 和 `husky_scanner.py` 的深入分析，以及各种编辑器源码的研究，总结出以下通用的编辑器识别方法：

### 1. 特征文件识别法 (主要方法)

每个编辑器都有其独特的核心JavaScript/CSS文件，这是最可靠的识别方式：

#### 核心特征文件映射表

| 编辑器 | 核心文件 | 正则表达式 | 可靠性 |
|--------|----------|------------|--------|
| DEXT5Upload | `dext5upload.js`, `dext5upload.css` | `["\']([^"\']*dext5upload\.js[^"\']*)["\']` | ⭐⭐⭐⭐⭐ |
| SmartEditor | `HuskyEZCreator.js`, `smart_editor2.js` | `["\']([^"\']*HuskyEZCreator\.js[^"\']*)["\']` | ⭐⭐⭐⭐⭐ |
| CHEditor | `cheditor.js`, `cheditor.css` | `["\']([^"\']*cheditor\.js[^"\']*)["\']` | ⭐⭐⭐⭐ |
| CKEditor | `ckeditor.js` | `["\']([^"\']*ckeditor\.js[^"\']*)["\']` | ⭐⭐⭐⭐ |
| CKFinder | `ckfinder.js` | `["\']([^"\']*ckfinder\.js[^"\']*)["\']` | ⭐⭐⭐⭐ |
| FCKeditor | `fckeditor.js` | `["\']([^"\']*fckeditor\.js[^"\']*)["\']` | ⭐⭐⭐ |
| KindEditor | `kindeditor.js`, `kindeditor-min.js` | `["\']([^"\']*kindeditor[^"\']*\.js[^"\']*)["\']` | ⭐⭐⭐⭐ |

#### 代码特征识别

除了文件名，还可以通过代码特征识别：

```javascript
// CKEditor
CKEDITOR.replace()
CKEDITOR_BASEPATH

// CKFinder  
CKFinder.create()
CKFinder.setupCKEditor()

// FCKeditor
FCKeditor()
FCK_RUNTIME_PATH

// KindEditor
KindEditor.create()
K.create()

// CHEditor
CHEditor()
new CHEditor()
```

### 2. 路径提取策略 (关键技术)

从特征文件路径中提取编辑器基础目录的通用算法：

#### 算法1: 基于编辑器名称目录
```python
def extract_base_path_by_name(resource_path, editor_type):
    patterns = {
        'dext5upload': r'(.*?dext5upload[^/]*/).*',
        'smarteditor': r'(.*?(?:smarteditor|se2)[^/]*/).*',
        'cheditor': r'(.*?cheditor[^/]*/).*',
        'ckeditor': r'(.*?ckeditor[^/]*/).*',
        'ckfinder': r'(.*?ckfinder[^/]*/).*'
    }
    
    pattern = patterns.get(editor_type)
    if pattern:
        match = re.search(pattern, resource_path, re.IGNORECASE)
        if match:
            return match.group(1)
    return None
```

#### 算法2: 基于核心文件名
```python
def extract_base_path_by_file(resource_path, core_files):
    for core_file in core_files:
        if core_file in resource_path:
            base_path = resource_path.replace(core_file, '').rstrip('/')
            # 移除常见的子目录
            base_path = re.sub(r'/(js|css|scripts|assets|lib)/?$', '', base_path) + '/'
            return base_path
    return None
```

#### 算法3: 通用后备方案
```python
def extract_base_path_fallback(resource_path):
    # 简单地移除最后一个路径组件
    return '/'.join(resource_path.split('/')[:-1]) + '/'
```

### 3. 多层次扫描策略

#### 第1层: 主页内容分析 (优先级最高)
```python
def scan_homepage_first(target, editor_type):
    # 1. 获取主页内容
    accessible_url, response = test_url_accessibility(target)
    
    # 2. 在主页查找编辑器特征
    editor_resources = find_editor_resources(response.text, accessible_url, editor_type)
    
    # 3. 如果找到，直接测试漏洞
    if editor_resources:
        for resource in editor_resources:
            base_path = extract_editor_base_path(resource['path'], editor_type)
            vulnerable_paths = test_vulnerability_paths(base_path, editor_type)
            
            if vulnerable_paths and not is_all_paths_vulnerable(vulnerable_paths):
                return create_result(target, base_path, vulnerable_paths)
    
    return None
```

#### 第2层: 目录Fuzzing (备用策略)
```python
def fuzz_common_directories(target, editor_type):
    # 只有在主页未发现时才进行fuzzing
    common_dirs = get_common_directories(editor_type)
    core_files = get_core_files(editor_type)
    
    for directory in common_dirs:
        for core_file in core_files:
            test_url = f"{target}/{directory}{core_file}"
            
            if verify_file_exists(test_url):
                base_path = f"{target}/{directory}"
                vulnerable_paths = test_vulnerability_paths(base_path, editor_type)
                
                if vulnerable_paths and not is_all_paths_vulnerable(vulnerable_paths):
                    return create_result(target, base_path, vulnerable_paths)
    
    return None
```

### 4. 通用目录字典

#### 基础通用目录
```python
COMMON_DIRECTORIES = [
    '',                    # 根目录
    'editor/', 'editors/', # 编辑器目录
    'common/', 'resources/', 'resource/',  # 资源目录
    'js/', 'lib/', 'libs/', 'scripts/',    # 脚本目录
    'static/', 'assets/',  # 静态资源
    'upload/', 'file/', 'files/',          # 上传目录
    'admin/', 'manage/', 'system/'         # 管理目录
]
```

#### 编辑器特定目录
```python
EDITOR_SPECIFIC_DIRECTORIES = {
    'dext5upload': [
        'dext5upload/', 'Dext5Upload/', 'dext5/',
        'js/component/dext5upload/', 'resources/dext5upload/'
    ],
    'smarteditor': [
        'smarteditor/', 'SmartEditor/', 'SmartEditor2/', 'se2/',
        'resources/js/SmartEditor2/', 'js/smarteditor/'
    ],
    'cheditor': [
        'cheditor/', 'CHEditor/', 'ch/', 'editor/ch/',
        'js/cheditor/', 'resources/cheditor/'
    ],
    'ckeditor': [
        'ckeditor/', 'CKEditor/', 'ck/', 'editor/ckeditor/',
        'js/ckeditor/', 'lib/ckeditor/'
    ],
    'ckfinder': [
        'ckfinder/', 'CKFinder/', 'filemanager/', 'filebrowser/',
        'editor/ckfinder/', 'js/ckfinder/'
    ]
}
```

### 5. 漏洞路径验证

#### 响应有效性判断
```python
def is_vulnerable_response(response, not_found_signature, vuln_info):
    # 基础检查
    if response.status_code != 200:
        return False
    
    # 软404检查
    if not_found_signature and response.text == not_found_signature:
        return False
    
    # 根据漏洞类型进行特定检查
    vuln_type = vuln_info.get('type', '')
    
    if vuln_type == 'upload':
        # 上传页面特征
        indicators = ['upload', 'file', 'form', 'multipart', 'enctype']
        return any(indicator in response.text.lower() for indicator in indicators)
    
    elif vuln_type == 'info':
        # 信息泄露页面特征
        return (len(response.text) > 100 and 
                '<title>' not in response.text.lower() and
                'error' not in response.text.lower())
    
    elif vuln_type == 'rce':
        # RCE相关页面特征
        indicators = ['connector', 'command', 'execute', 'file']
        return any(indicator in response.text.lower() for indicator in indicators)
    
    # 默认检查
    return len(response.text) > 50
```

#### 异常情况检测
```python
def is_all_paths_vulnerable(vulnerable_paths, total_paths):
    """检测是否遇到通配符路由等异常情况"""
    if not vulnerable_paths:
        return False
    
    # 如果所有路径都返回200，很可能是异常情况
    if len(vulnerable_paths) >= total_paths:
        return True
    
    # 如果超过80%的路径都返回200，也认为是异常
    threshold = max(1, int(total_paths * 0.8))
    return len(vulnerable_paths) >= threshold
```

### 6. 学习机制

#### 动态目录学习
```python
def learn_and_save_new_directory(discovered_path, editor_type):
    """从发现的路径中学习新的部署目录"""
    
    # 提取目录前缀
    if editor_type in discovered_path.lower():
        prefix = discovered_path.lower().split(editor_type, 1)[0]
        
        if prefix and prefix.strip('/'):
            # 检查是否为新目录
            if prefix not in known_directories:
                # 保存到文件
                with open(f'learned_dirs_{editor_type}.txt', 'a') as f:
                    f.write(prefix + '\n')
                
                print(f"[*] 学习到新目录: {prefix}")
```

## 🛠️ 实际应用建议

### 1. 扫描器设计原则

1. **优先级策略**: 主页分析 > 目录Fuzzing
2. **效率优化**: 找到即停止，避免重复扫描
3. **误报控制**: 实施软404检测和异常情况过滤
4. **学习能力**: 动态学习新的部署路径

### 2. 扩展新编辑器的步骤

1. **收集特征**: 确定核心文件名和代码特征
2. **分析路径**: 研究常见部署目录结构
3. **识别漏洞**: 找出已知的漏洞路径
4. **测试验证**: 在真实环境中验证识别准确性
5. **集成配置**: 添加到扫描器配置中

### 3. 性能优化建议

1. **并发控制**: 合理设置线程数，避免过载
2. **超时设置**: 设置合适的请求超时时间
3. **缓存机制**: 缓存404签名和已测试路径
4. **智能跳过**: 检测到异常情况时及时跳过

## 📈 识别准确率提升方法

1. **多特征融合**: 结合文件名、代码特征、目录结构
2. **版本识别**: 通过特定文件或注释识别版本
3. **上下文分析**: 分析周围的HTML结构和其他资源
4. **行为验证**: 通过实际请求验证编辑器功能

通过以上方法，可以构建一个高效、准确的通用编辑器识别和漏洞扫描系统。

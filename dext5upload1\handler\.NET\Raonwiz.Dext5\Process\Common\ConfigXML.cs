﻿using System;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x0200001C RID: 28
	public class ConfigXML : Base
	{
		// Token: 0x06000226 RID: 550 RVA: 0x000166D6 File Offset: 0x000148D6
		public ConfigXML(HttpContext context, string configPhysicalPath) : base(context)
		{
			this._configPhysicalPath = configPhysicalPath;
		}

		// Token: 0x06000227 RID: 551 RVA: 0x000166FC File Offset: 0x000148FC
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string empty = string.Empty;
			string configFileName = this._entity_dextParam.configFileName;
			if (string.IsNullOrEmpty(this._configPhysicalPath) || string.IsNullOrEmpty(configFileName))
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[FAIL-NE]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				return null;
			}
			string path = Path.Combine(this._configPhysicalPath, configFileName);
			if (!File.Exists(path))
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[FAIL-NE]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				return null;
			}
			string text = File.ReadAllText(path, Encoding.UTF8);
			text = Dext5Parameter.MakeParameter(text);
			this.hContext.Response.Clear();
			this.hContext.Response.Write("[OK-NE]" + text);
			return null;
		}

		// Token: 0x0400012A RID: 298
		private string _configPhysicalPath = string.Empty;

		// Token: 0x0400012B RID: 299
		private string _configFIleName = string.Empty;
	}
}

﻿using System;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x0200001A RID: 26
	internal sealed class GPSRational
	{
		// Token: 0x170000C3 RID: 195
		// (get) Token: 0x0600021A RID: 538 RVA: 0x000157A5 File Offset: 0x000139A5
		// (set) Token: 0x0600021B RID: 539 RVA: 0x000157AD File Offset: 0x000139AD
		public Rational Hours
		{
			get
			{
				return this._hours;
			}
			set
			{
				this._hours = value;
			}
		}

		// Token: 0x170000C4 RID: 196
		// (get) Token: 0x0600021C RID: 540 RVA: 0x000157B6 File Offset: 0x000139B6
		// (set) Token: 0x0600021D RID: 541 RVA: 0x000157BE File Offset: 0x000139BE
		public Rational Minutes
		{
			get
			{
				return this._minutes;
			}
			set
			{
				this._minutes = value;
			}
		}

		// Token: 0x170000C5 RID: 197
		// (get) Token: 0x0600021E RID: 542 RVA: 0x000157C7 File Offset: 0x000139C7
		// (set) Token: 0x0600021F RID: 543 RVA: 0x000157CF File Offset: 0x000139CF
		public Rational Seconds
		{
			get
			{
				return this._seconds;
			}
			set
			{
				this._seconds = value;
			}
		}

		// Token: 0x06000220 RID: 544 RVA: 0x000157D8 File Offset: 0x000139D8
		public GPSRational(byte[] bytes)
		{
			byte[] array = new byte[8];
			byte[] array2 = new byte[8];
			byte[] array3 = new byte[8];
			Array.Copy(bytes, 0, array, 0, 8);
			Array.Copy(bytes, 8, array2, 0, 8);
			Array.Copy(bytes, 16, array3, 0, 8);
			this._hours = new Rational(array);
			this._minutes = new Rational(array2);
			this._seconds = new Rational(array3);
		}

		// Token: 0x06000221 RID: 545 RVA: 0x00015844 File Offset: 0x00013A44
		public override string ToString()
		{
			return string.Concat(new object[]
			{
				this._hours.ToDouble(),
				"° ",
				this._minutes.ToDouble(),
				"' ",
				this._seconds.ToDouble(),
				"\""
			});
		}

		// Token: 0x06000222 RID: 546 RVA: 0x000158B0 File Offset: 0x00013AB0
		public string ToString(string separator)
		{
			return string.Concat(new object[]
			{
				this._hours.ToDouble(),
				separator,
				this._minutes.ToDouble(),
				separator,
				this._seconds.ToDouble()
			});
		}

		// Token: 0x04000123 RID: 291
		private Rational _hours;

		// Token: 0x04000124 RID: 292
		private Rational _minutes;

		// Token: 0x04000125 RID: 293
		private Rational _seconds;
	}
}

package jcifs.dcerpc.ndr;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/ndr/NdrLong.class */
public class NdrLong extends NdrObject {
    public int value;

    public NdrLong(int value) {
        this.value = value;
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void encode(NdrBuffer dst) throws NdrException {
        dst.enc_ndr_long(this.value);
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void decode(NdrBuffer src) throws NdrException {
        this.value = src.dec_ndr_long();
    }
}

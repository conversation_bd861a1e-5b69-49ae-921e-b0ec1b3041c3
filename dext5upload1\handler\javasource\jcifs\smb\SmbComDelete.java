package jcifs.smb;

import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComDelete.class */
class SmbComDelete extends ServerMessageBlock {
    private int searchAttributes;

    SmbComDelete(String fileName) {
        this.path = fileName;
        this.command = (byte) 6;
        this.searchAttributes = 6;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.searchAttributes, dst, dstIndex);
        return 2;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = 4;
        return (dstIndex2 + writeString(this.path, dst, dstIndex2)) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComDelete[" + super.toString() + ",searchAttributes=0x" + Hexdump.toHexString(this.searchAttributes, 4) + ",fileName=" + this.path + "]");
    }
}

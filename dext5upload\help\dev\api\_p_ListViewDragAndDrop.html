﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: ListViewDragAndDrop</h3>
    <p class="ttl">config.ListViewDragAndDrop</p>
    <p class="txt">
        업로드 영역에 드래그 앤 드랍해서 파일추가할 수 있도록 설정합니다.(편집 모드)
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "1" 이고 드래그 앤 드랍 파일 추가 적용, "0"으로 설정시 드래그 앤 드랍 파일추가 적용되지 않습니다.<br />
        (드래그 앤 드랍 파일추가 기능은 IE10 이상부터 가능합니다. 플러그인 모드는 IE버전에 상관 없이 사용 가능합니다.) 
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 영역에 드래그 앤 드랍 파일추가 미 설정합니다.
        DEXT5UPLOAD.config.ListViewDragAndDrop = '0';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿using System;

namespace Raonwiz.Dext5.License.Library.General.Security
{
	// Token: 0x02000003 RID: 3
	public class LicenseInfo
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x0600000A RID: 10 RVA: 0x00002196 File Offset: 0x00000396
		// (set) Token: 0x0600000B RID: 11 RVA: 0x0000219E File Offset: 0x0000039E
		public string CustomName { get; set; }

		// Token: 0x17000002 RID: 2
		// (get) Token: 0x0600000C RID: 12 RVA: 0x000021A7 File Offset: 0x000003A7
		// (set) Token: 0x0600000D RID: 13 RVA: 0x000021AF File Offset: 0x000003AF
		public string SystemName { get; set; }

		// Token: 0x17000003 RID: 3
		// (get) Token: 0x0600000E RID: 14 RVA: 0x000021B8 File Offset: 0x000003B8
		// (set) Token: 0x0600000F RID: 15 RVA: 0x000021C0 File Offset: 0x000003C0
		public string ProductName { get; set; }

		// Token: 0x17000004 RID: 4
		// (get) Token: 0x06000010 RID: 16 RVA: 0x000021C9 File Offset: 0x000003C9
		// (set) Token: 0x06000011 RID: 17 RVA: 0x000021D1 File Offset: 0x000003D1
		public string ProductKey { get; set; }

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x06000012 RID: 18 RVA: 0x000021DA File Offset: 0x000003DA
		// (set) Token: 0x06000013 RID: 19 RVA: 0x000021E2 File Offset: 0x000003E2
		public DateTime ExpireDate { get; set; }

		// Token: 0x17000006 RID: 6
		// (get) Token: 0x06000014 RID: 20 RVA: 0x000021EB File Offset: 0x000003EB
		// (set) Token: 0x06000015 RID: 21 RVA: 0x000021F3 File Offset: 0x000003F3
		public string DomainList { get; set; }

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x06000016 RID: 22 RVA: 0x000021FC File Offset: 0x000003FC
		// (set) Token: 0x06000017 RID: 23 RVA: 0x00002204 File Offset: 0x00000404
		public string UserCount { get; set; }

		// Token: 0x17000008 RID: 8
		// (get) Token: 0x06000018 RID: 24 RVA: 0x0000220D File Offset: 0x0000040D
		// (set) Token: 0x06000019 RID: 25 RVA: 0x00002215 File Offset: 0x00000415
		public string LicenseCheckType { get; set; }

		// Token: 0x17000009 RID: 9
		// (get) Token: 0x0600001A RID: 26 RVA: 0x0000221E File Offset: 0x0000041E
		// (set) Token: 0x0600001B RID: 27 RVA: 0x00002226 File Offset: 0x00000426
		public string Validate { get; set; }

		// Token: 0x1700000A RID: 10
		// (get) Token: 0x0600001C RID: 28 RVA: 0x0000222F File Offset: 0x0000042F
		// (set) Token: 0x0600001D RID: 29 RVA: 0x00002237 File Offset: 0x00000437
		public string InterworkingModule { get; set; }
	}
}

﻿using System;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000018 RID: 24
	internal sealed class Rational
	{
		// Token: 0x06000212 RID: 530 RVA: 0x00015670 File Offset: 0x00013870
		public Rational(byte[] bytes)
		{
			byte[] array = new byte[4];
			byte[] array2 = new byte[4];
			Array.Copy(bytes, 0, array, 0, 4);
			Array.Copy(bytes, 4, array2, 0, 4);
			this._num = BitConverter.ToInt32(array, 0);
			this._denom = BitConverter.ToInt32(array2, 0);
		}

		// Token: 0x06000213 RID: 531 RVA: 0x000156BF File Offset: 0x000138BF
		public double ToDouble()
		{
			return Math.Round(Convert.ToDouble(this._num) / Convert.ToDouble(this._denom), 2);
		}

		// Token: 0x06000214 RID: 532 RVA: 0x000156DE File Offset: 0x000138DE
		public string ToString(string separator)
		{
			return this._num.ToString() + separator + this._denom.ToString();
		}

		// Token: 0x06000215 RID: 533 RVA: 0x000156FC File Offset: 0x000138FC
		public override string ToString()
		{
			return this.ToString("/");
		}

		// Token: 0x0400011F RID: 287
		private int _num;

		// Token: 0x04000120 RID: 288
		private int _denom;
	}
}

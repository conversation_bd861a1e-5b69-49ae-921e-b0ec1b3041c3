package javax.servlet.http;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import javax.servlet.ServletOutputStream;

/* compiled from: HttpServlet.java */
/* loaded from: servlet-api.jar:javax/servlet/http/NoBodyResponse.class */
class NoBodyResponse extends HttpServletResponseWrapper {
    private NoBodyOutputStream noBody;
    private PrintWriter writer;
    private boolean didSetContentLength;

    NoBodyResponse(HttpServletResponse r) {
        super(r);
        this.noBody = new NoBodyOutputStream();
    }

    void setContentLength() {
        if (!this.didSetContentLength) {
            super.setContentLength(this.noBody.getContentLength());
        }
    }

    @Override // javax.servlet.ServletResponseWrapper, javax.servlet.ServletResponse
    public void setContentLength(int len) {
        super.setContentLength(len);
        this.didSetContentLength = true;
    }

    @Override // javax.servlet.ServletResponseWrapper, javax.servlet.ServletResponse
    public ServletOutputStream getOutputStream() throws IOException {
        return this.noBody;
    }

    @Override // javax.servlet.ServletResponseWrapper, javax.servlet.ServletResponse
    public PrintWriter getWriter() throws UnsupportedEncodingException {
        if (this.writer == null) {
            OutputStreamWriter w = new OutputStreamWriter(this.noBody, getCharacterEncoding());
            this.writer = new PrintWriter(w);
        }
        return this.writer;
    }
}

package jcifs.smb;

import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import jcifs.util.LogStream;
import jcifs.util.transport.TransportException;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbFileInputStream.class */
public class SmbFileInputStream extends InputStream {
    private long fp;
    private int readSize;
    private int openFlags;
    private int access;
    private byte[] tmp;
    SmbFile file;

    public SmbFileInputStream(String url) throws SmbException, MalformedURLException, UnknownHostException {
        this(new SmbFile(url));
    }

    public SmbFileInputStream(SmbFile file) throws SmbException, MalformedURLException, UnknownHostException {
        this(file, 1);
    }

    SmbFileInputStream(SmbFile file, int openFlags) throws SmbException, MalformedURLException, UnknownHostException {
        this.tmp = new byte[1];
        this.file = file;
        this.openFlags = openFlags & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH;
        this.access = (openFlags >>> 16) & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH;
        if (file.type != 16) {
            file.open(openFlags, this.access, 128, 0);
            this.openFlags &= -81;
        } else {
            file.connect0();
        }
        this.readSize = Math.min(file.tree.session.transport.rcv_buf_size - 70, file.tree.session.transport.server.maxBufferSize - 70);
    }

    protected IOException seToIoe(SmbException se) {
        IOException ioe = se;
        Throwable root = se.getRootCause();
        if (root instanceof TransportException) {
            ioe = (TransportException) root;
            root = ((TransportException) ioe).getRootCause();
        }
        if (root instanceof InterruptedException) {
            ioe = new InterruptedIOException(root.getMessage());
            ioe.initCause(root);
        }
        return ioe;
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        try {
            this.file.close();
            this.tmp = null;
        } catch (SmbException se) {
            throw seToIoe(se);
        }
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (read(this.tmp, 0, 1) == -1) {
            return -1;
        }
        return this.tmp[0] & 255;
    }

    @Override // java.io.InputStream
    public int read(byte[] b) throws IOException {
        return read(b, 0, b.length);
    }

    @Override // java.io.InputStream
    public int read(byte[] b, int off, int len) throws IOException {
        return readDirect(b, off, len);
    }

    public int readDirect(byte[] b, int off, int len) throws IOException {
        int r;
        int n;
        if (len <= 0) {
            return 0;
        }
        long start = this.fp;
        if (this.tmp == null) {
            throw new IOException("Bad file descriptor");
        }
        this.file.open(this.openFlags, this.access, 128, 0);
        SmbFile smbFile = this.file;
        LogStream logStream = SmbFile.log;
        if (LogStream.level >= 4) {
            SmbFile smbFile2 = this.file;
            SmbFile.log.println("read: fid=" + this.file.fid + ",off=" + off + ",len=" + len);
        }
        SmbComReadAndXResponse response = new SmbComReadAndXResponse(b, off);
        if (this.file.type == 16) {
            response.responseTimeout = 0L;
        }
        do {
            r = len > this.readSize ? this.readSize : len;
            SmbFile smbFile3 = this.file;
            LogStream logStream2 = SmbFile.log;
            if (LogStream.level >= 4) {
                SmbFile smbFile4 = this.file;
                SmbFile.log.println("read: len=" + len + ",r=" + r + ",fp=" + this.fp);
            }
            try {
                SmbComReadAndX request = new SmbComReadAndX(this.file.fid, this.fp, r, null);
                if (this.file.type == 16) {
                    request.remaining = 1024;
                    request.maxCount = 1024;
                    request.minCount = 1024;
                }
                this.file.send(request, response);
                n = response.dataLength;
                if (n > 0) {
                    this.fp += n;
                    len -= n;
                    response.off += n;
                    if (len <= 0) {
                        break;
                    }
                } else {
                    return (int) (this.fp - start > 0 ? this.fp - start : -1L);
                }
            } catch (SmbException se) {
                if (this.file.type == 16 && se.getNtStatus() == -1073741493) {
                    return -1;
                }
                throw seToIoe(se);
            }
        } while (n == r);
        return (int) (this.fp - start);
    }

    @Override // java.io.InputStream
    public int available() throws IOException {
        if (this.file.type != 16) {
            return 0;
        }
        try {
            SmbNamedPipe pipe = (SmbNamedPipe) this.file;
            this.file.open(32, pipe.pipeType & 16711680, 128, 0);
            TransPeekNamedPipe req = new TransPeekNamedPipe(this.file.unc, this.file.fid);
            TransPeekNamedPipeResponse resp = new TransPeekNamedPipeResponse(pipe);
            pipe.send(req, resp);
            if (resp.status == 1 || resp.status == 4) {
                this.file.opened = false;
                return 0;
            }
            return resp.available;
        } catch (SmbException se) {
            throw seToIoe(se);
        }
    }

    @Override // java.io.InputStream
    public long skip(long n) throws IOException {
        if (n > 0) {
            this.fp += n;
            return n;
        }
        return 0L;
    }
}

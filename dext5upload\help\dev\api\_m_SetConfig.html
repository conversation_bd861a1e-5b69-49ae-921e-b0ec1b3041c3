﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">          
    <h3 class="title">DEXT5 Upload :: SetConfig</h3>
    <p class="ttl">void SetConfig(configName, configValue, uploadID)</p>
    <p class="txt">
        업로드 설정 값을 API로 설정 할 수 있습니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음. 
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">configName</span>&nbsp;&nbsp;업로드의 설정값 명을 의미 합니다.<br/>
        <span class="firebrick">configValue</span>&nbsp;&nbsp;위의 configName에 할당할 값을 의미 합니다.<br />
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;실행할 업로드의 id를 의미합니다. (단일 업로드일 경우 생략 가능)
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        업로드 생성 후 handler url 변경 등 환경설정 값 변경에 유용합니다.<br />
        &nbsp;&nbsp;&nbsp;&nbsp;예) 업로드 생성 후 handler url 변경을 원하는 경우<br />
        &nbsp;&nbsp;&nbsp;&nbsp; DEXT5UPLOAD.SetConfig("HandlerUrl", "http://www.dext5.com/handler.ashx", "upload1") <br />
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;          
        function setHandlerUrl() {
            DEXT5UPLOAD.SetConfig("HandlerUrl", "http://www.dext5.com/handler.ashx", "upload1");
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;

&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


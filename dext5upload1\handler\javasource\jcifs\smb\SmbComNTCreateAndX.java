package jcifs.smb;

import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComNTCreateAndX.class */
class SmbComNTCreateAndX extends AndXServerMessageBlock {
    static final int FILE_SUPERSEDE = 0;
    static final int FILE_OPEN = 1;
    static final int FILE_CREATE = 2;
    static final int FILE_OPEN_IF = 3;
    static final int FILE_OVERWRITE = 4;
    static final int FILE_OVERWRITE_IF = 5;
    static final int FILE_WRITE_THROUGH = 2;
    static final int FILE_SEQUENTIAL_ONLY = 4;
    static final int FILE_SYNCHRONOUS_IO_ALERT = 16;
    static final int FILE_SYNCHRONOUS_IO_NONALERT = 32;
    static final int SECURITY_CONTEXT_TRACKING = 1;
    static final int SECURITY_EFFECTIVE_ONLY = 2;
    private int rootDirectoryFid;
    private int extFileAttributes;
    private int shareAccess;
    private int createDisposition;
    private int createOptions;
    private int impersonationLevel;
    private long allocationSize;
    private byte securityFlags;
    private int namelen_index;
    int flags0;
    int desiredAccess;

    SmbComNTCreateAndX(String name, int flags, int access, int shareAccess, int extFileAttributes, int createOptions, ServerMessageBlock andx) {
        super(andx);
        this.path = name;
        this.command = (byte) -94;
        this.desiredAccess = access;
        this.desiredAccess |= 137;
        this.extFileAttributes = extFileAttributes;
        this.shareAccess = shareAccess;
        if ((flags & 64) == 64) {
            if ((flags & 16) == 16) {
                this.createDisposition = 5;
            } else {
                this.createDisposition = 4;
            }
        } else if ((flags & 16) == 16) {
            if ((flags & 32) == 32) {
                this.createDisposition = 2;
            } else {
                this.createDisposition = 3;
            }
        } else {
            this.createDisposition = 1;
        }
        if ((createOptions & 1) == 0) {
            this.createOptions = createOptions | 64;
        } else {
            this.createOptions = createOptions;
        }
        this.impersonationLevel = 2;
        this.securityFlags = (byte) 3;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = 0;
        this.namelen_index = dstIndex2;
        int dstIndex3 = dstIndex2 + 2;
        writeInt4(this.flags0, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 4;
        writeInt4(this.rootDirectoryFid, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 4;
        writeInt4(this.desiredAccess, dst, dstIndex5);
        int dstIndex6 = dstIndex5 + 4;
        writeInt8(this.allocationSize, dst, dstIndex6);
        int dstIndex7 = dstIndex6 + 8;
        writeInt4(this.extFileAttributes, dst, dstIndex7);
        int dstIndex8 = dstIndex7 + 4;
        writeInt4(this.shareAccess, dst, dstIndex8);
        int dstIndex9 = dstIndex8 + 4;
        writeInt4(this.createDisposition, dst, dstIndex9);
        int dstIndex10 = dstIndex9 + 4;
        writeInt4(this.createOptions, dst, dstIndex10);
        int dstIndex11 = dstIndex10 + 4;
        writeInt4(this.impersonationLevel, dst, dstIndex11);
        int dstIndex12 = dstIndex11 + 4;
        dst[dstIndex12] = this.securityFlags;
        return (dstIndex12 + 1) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int n = writeString(this.path, dst, dstIndex);
        writeInt2(this.useUnicode ? this.path.length() * 2 : n, dst, this.namelen_index);
        return n;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComNTCreateAndX[" + super.toString() + ",flags=0x" + Hexdump.toHexString(this.flags0, 2) + ",rootDirectoryFid=" + this.rootDirectoryFid + ",desiredAccess=0x" + Hexdump.toHexString(this.desiredAccess, 4) + ",allocationSize=" + this.allocationSize + ",extFileAttributes=0x" + Hexdump.toHexString(this.extFileAttributes, 4) + ",shareAccess=0x" + Hexdump.toHexString(this.shareAccess, 4) + ",createDisposition=0x" + Hexdump.toHexString(this.createDisposition, 4) + ",createOptions=0x" + Hexdump.toHexString(this.createOptions, 8) + ",impersonationLevel=0x" + Hexdump.toHexString(this.impersonationLevel, 4) + ",securityFlags=0x" + Hexdump.toHexString((int) this.securityFlags, 2) + ",name=" + this.path + "]");
    }
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">
    <span class="pl_type">[플러그인 전용]</span>                
    <h3 class="title">DEXT5 Upload :: Config :: Timeout</h3>
    <p class="ttl">config.Timeout</p>
    <p class="txt">
        서버로부터 응답이 지연 되는 경우 이어받기 시도 시간을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0" 이고, 이어받기 시도 시간을 "사용자 설정값"으로 설정합니다.<br/>
        이어받기 시도 시간은 millisecond(시간의 단위)로 설정됩니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 이어받기 시도 시간을 사용으로 설정합니다.
        DEXT5UPLOAD.config.Timeout  = '0';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


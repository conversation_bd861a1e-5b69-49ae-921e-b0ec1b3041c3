﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000026 RID: 38
	public class ZipStatus : Base
	{
		// Token: 0x06000262 RID: 610 RVA: 0x0001DDC8 File Offset: 0x0001BFC8
		public ZipStatus(HttpContext context, string pTempPath) : base(context)
		{
			this.tempPath = pTempPath;
		}

		// Token: 0x06000263 RID: 611 RVA: 0x0001DDE4 File Offset: 0x0001BFE4
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			string text = this._entity_dextParam.zipFileName;
			string text2 = string.Concat(new object[]
			{
				this.tempPath,
				this.m_PathChar,
				text,
				".zip"
			});
			if (!string.IsNullOrEmpty(text))
			{
				string text3 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text3 += "<html><head>";
					text3 += "<script type=\"text/javascript\">";
					text3 += "if (window.postMessage) {";
					text3 += "if (window.addEventListener) {";
					text3 += "window.addEventListener('message', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "}, false);";
					text3 += "}";
					text3 += "else if (window.attachEvent) {";
					text3 += "window.attachEvent('onmessage', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "});";
					text3 += "}";
					text3 += "}";
					text3 += "</script>";
					text3 += "</head>";
					text3 += "<body>";
					text3 += "{0}";
					text3 += "</body>";
					text3 += "</html>";
				}
				else
				{
					text3 = "{0}";
				}
				if (!File.Exists(text2) || this.IsFileLocked(text2, 1))
				{
					text3 = text3.Replace("{0}", "[OK]" + Dext5Parameter.MakeParameter("w"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text3);
				}
				else
				{
					text3 = text3.Replace("{0}", "[OK]" + Dext5Parameter.MakeParameter("d"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text3);
				}
			}
			else
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
			}
			return null;
		}

		// Token: 0x06000264 RID: 612 RVA: 0x0001E054 File Offset: 0x0001C254
		private bool IsFileLocked(string filePath, int secondsToWait)
		{
			bool flag = true;
			int num = 0;
			while (flag && (num < secondsToWait || secondsToWait == 0))
			{
				try
				{
					using (File.Open(filePath, FileMode.Open))
					{
					}
					return false;
				}
				catch (IOException e)
				{
					int num2 = Marshal.GetHRForException(e) & 65535;
					flag = (num2 == 32 || num2 == 33);
					num++;
					if (secondsToWait != 0)
					{
						new ManualResetEvent(false).WaitOne(1000);
					}
				}
			}
			return flag;
		}

		// Token: 0x0400014C RID: 332
		protected const string RexRangeValuePattern = "^bytes=(d+)-$";

		// Token: 0x0400014D RID: 333
		protected string zipFileName = string.Empty;
	}
}

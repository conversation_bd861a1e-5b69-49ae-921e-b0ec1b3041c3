﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: GetTotalFileSize</h3>
    <p class="ttl">int GetTotalFileSize(uploadID)</p>
    <p class="txt">
        추가된 전체 파일들의 전체 크기를 가져옵니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        <span class="firebrick">int</span>&nbsp;&nbsp;추가된 전체 파일들의 전체 크기를 의미합니다.<br/>
        추가된 전체 파일들의 BYTE를 리턴합니다.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
       <span class="firebrick">uploadID</span>&nbsp;&nbsp;전체 파일 크기를 가져올 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        var total_file_size = DEXT5UPLOAD.GetTotalFileSize("upload1");
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


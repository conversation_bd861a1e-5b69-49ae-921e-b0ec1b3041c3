﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using Ionic.Zip;
using Ionic.Zlib;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000005 RID: 5
	public class MultiDownload : Base
	{
		// Token: 0x06000033 RID: 51 RVA: 0x0000614C File Offset: 0x0000434C
		public MultiDownload(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pDownloadRootPath, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.zipFileName = pZipFileName;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.downloadRootPath = pDownloadRootPath;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000034 RID: 52 RVA: 0x000061D0 File Offset: 0x000043D0
		public MultiDownload(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, List<string> pDownloadList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.zipFileName = pZipFileName;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.strDownloadList = pDownloadList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
			this.bCustom = true;
		}

		// Token: 0x06000035 RID: 53 RVA: 0x00006258 File Offset: 0x00004458
		public MultiDownload(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, List<Stream> pDownloadList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.zipFileName = pZipFileName;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.sDownloadList = pDownloadList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
			this.bCustom = true;
		}

		// Token: 0x06000036 RID: 54 RVA: 0x000062E0 File Offset: 0x000044E0
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			if (this.bCustom)
			{
				string empty = string.Empty;
				string empty2 = string.Empty;
				string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
				if (this._entity_dextParam.mode == "normal")
				{
					for (int i = 0; i < fileOrgNameAry.Length; i++)
					{
						fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
					}
				}
				if (this.strDownloadList != null)
				{
					List<string> list = new List<string>();
					List<string> list2 = new List<string>();
					string text = string.Empty;
					string empty3 = string.Empty;
					if (fileOrgNameAry.Length != this.strDownloadList.Count)
					{
						string text2 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text2 += "<html><head>";
							text2 += "<script type=\"text/javascript\">";
							text2 += "if (window.postMessage) {";
							text2 += "if (window.addEventListener) {";
							text2 += "window.addEventListener('message', function (e) {";
							text2 += "var sendUrl = e.origin;";
							text2 += "var data = document.body.innerHTML;";
							text2 += "e.source.postMessage(data, sendUrl);";
							text2 += "}, false);";
							text2 += "}";
							text2 += "else if (window.attachEvent) {";
							text2 += "window.attachEvent('onmessage', function (e) {";
							text2 += "var sendUrl = e.origin;";
							text2 += "var data = document.body.innerHTML;";
							text2 += "e.source.postMessage(data, sendUrl);";
							text2 += "});";
							text2 += "}";
							text2 += "}";
							text2 += "</script>";
							text2 += "</head>";
							text2 += "<body>";
							text2 += "{0}";
							text2 += "</body>";
							text2 += "</html>";
						}
						else
						{
							text2 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
						}
						text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text2);
						return "error|009|Invalid parameter on server";
					}
					for (int j = 0; j < this.strDownloadList.Count; j++)
					{
						text = this.strDownloadList[j];
						if (!File.Exists(text))
						{
							string text3 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text3 += "<html><head>";
								text3 += "<script type=\"text/javascript\">";
								text3 += "if (window.postMessage) {";
								text3 += "if (window.addEventListener) {";
								text3 += "window.addEventListener('message', function (e) {";
								text3 += "var sendUrl = e.origin;";
								text3 += "var data = document.body.innerHTML;";
								text3 += "e.source.postMessage(data, sendUrl);";
								text3 += "}, false);";
								text3 += "}";
								text3 += "else if (window.attachEvent) {";
								text3 += "window.attachEvent('onmessage', function (e) {";
								text3 += "var sendUrl = e.origin;";
								text3 += "var data = document.body.innerHTML;";
								text3 += "e.source.postMessage(data, sendUrl);";
								text3 += "});";
								text3 += "}";
								text3 += "}";
								text3 += "</script>";
								text3 += "</head>";
								text3 += "<body>";
								text3 += "{0}";
								text3 += "</body>";
								text3 += "</html>";
							}
							else
							{
								text3 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
							}
							text3 = text3.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text3);
							return "error|010|Not found file on server";
						}
						list.Add(text);
						list2.Add(fileOrgNameAry[j]);
						if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list2[j]))
						{
							string text4 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text4 += "<html><head>";
								text4 += "<script type=\"text/javascript\">";
								text4 += "if (window.postMessage) {";
								text4 += "if (window.addEventListener) {";
								text4 += "window.addEventListener('message', function (e) {";
								text4 += "var sendUrl = e.origin;";
								text4 += "var data = document.body.innerHTML;";
								text4 += "e.source.postMessage(data, sendUrl);";
								text4 += "}, false);";
								text4 += "}";
								text4 += "else if (window.attachEvent) {";
								text4 += "window.attachEvent('onmessage', function (e) {";
								text4 += "var sendUrl = e.origin;";
								text4 += "var data = document.body.innerHTML;";
								text4 += "e.source.postMessage(data, sendUrl);";
								text4 += "});";
								text4 += "}";
								text4 += "}";
								text4 += "</script>";
								text4 += "</head>";
								text4 += "<body>";
								text4 += "{0}";
								text4 += "</body>";
								text4 += "</html>";
							}
							else
							{
								text4 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
							}
							text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text4);
							return "error|012|Not allowed file extension";
						}
						if (!base.CheckBlackWord(this.fileBlackWordList, list2[j]))
						{
							string text5 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text5 += "<html><head>";
								text5 += "<script type=\"text/javascript\">";
								text5 += "if (window.postMessage) {";
								text5 += "if (window.addEventListener) {";
								text5 += "window.addEventListener('message', function (e) {";
								text5 += "var sendUrl = e.origin;";
								text5 += "var data = document.body.innerHTML;";
								text5 += "e.source.postMessage(data, sendUrl);";
								text5 += "}, false);";
								text5 += "}";
								text5 += "else if (window.attachEvent) {";
								text5 += "window.attachEvent('onmessage', function (e) {";
								text5 += "var sendUrl = e.origin;";
								text5 += "var data = document.body.innerHTML;";
								text5 += "e.source.postMessage(data, sendUrl);";
								text5 += "});";
								text5 += "}";
								text5 += "}";
								text5 += "</script>";
								text5 += "</head>";
								text5 += "<body>";
								text5 += "{0}";
								text5 += "</body>";
								text5 += "</html>";
							}
							else
							{
								text5 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
							}
							text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text5);
							return "error|018|There does not allow the string included in file name";
						}
					}
					if (string.IsNullOrEmpty(empty2) && list.Count > 0)
					{
						string[] array = new string[list.Count];
						string[] array2 = new string[list2.Count];
						for (int k = 0; k < list.Count; k++)
						{
							array[k] = list[k];
						}
						for (int l = 0; l < list2.Count; l++)
						{
							array2[l] = list2[l];
						}
						string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
						try
						{
							UploadEventEntity uploadEventEntity = new UploadEventEntity();
							uploadEventEntity.Context = this.hContext;
							uploadEventEntity.DownloadFilePath = array;
							uploadEventEntity.DownloadFileName = array2;
							uploadEventEntity.DownloadCustomValue = requestValue;
							pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
							array = uploadEventEntity.DownloadFilePath;
							array2 = uploadEventEntity.DownloadFileName;
							for (int m = 0; m < array.Length; m++)
							{
								list[m] = array[m];
							}
							for (int n = 0; n < array2.Length; n++)
							{
								list2[n] = array2[n];
							}
						}
						catch
						{
						}
						if (pCustomError == null)
						{
							int count = list2.Count;
							string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
							for (int num = count - 1; num >= 0; num--)
							{
								for (int num2 = num - 1; num2 >= 0; num2--)
								{
									if (list2[num].ToLower() == list2[num2].ToLower())
									{
										if (list2[num].IndexOf('\\') > -1)
										{
											list2[num] = list2[num].Substring(0, list2[num].LastIndexOf('\\') + 1) + base.GenerateUniqueKey() + "_" + list2[num].Substring(list2[num].LastIndexOf('\\') + 1);
										}
										else
										{
											list2[num] = base.GenerateUniqueKey() + "_" + list2[num];
										}
									}
								}
							}
							HttpResponse response = this.hContext.Response;
							response.Clear();
							response.BufferOutput = false;
							string headerFileName = base.GetHeaderFileName(this.zipFileName);
							response.AddHeader("Content-Disposition", "attachment; filename=\"" + headerFileName + "\"");
							response.ContentType = "application/octet-stream";
							long num3 = 0L;
							for (int num4 = 0; num4 < list.Count; num4++)
							{
								FileInfo fileInfo = new FileInfo(list[num4]);
								num3 += fileInfo.Length;
							}
							response.AddHeader("X-Raon-Cl", num3.ToString());
							using (ZipOutputStream zipOutputStream = new ZipOutputStream(response.OutputStream))
							{
								zipOutputStream.CompressionLevel = CompressionLevel.None;
								zipOutputStream.AlternateEncodingUsage = ZipOption.AsNecessary;
								zipOutputStream.AlternateEncoding = Encoding.GetEncoding(949);
								zipOutputStream.Comment = "This zip was created from DEXT5 Upload.";
								zipOutputStream.EnableZip64 = this._enableZip64;
								for (int num5 = 0; num5 < list.Count; num5++)
								{
									if (list2[num5].IndexOf('\\') > -1)
									{
										zipOutputStream.PutNextEntry(list2[num5].Replace('\\', '/'));
									}
									else
									{
										zipOutputStream.PutNextEntry(list2[num5]);
									}
									using (FileStream fileStream = File.Open(list[num5], FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
									{
										byte[] array3 = new byte[this.iBufferSize];
										int count2;
										while (response.IsClientConnected && (count2 = fileStream.Read(array3, 0, array3.Length)) > 0)
										{
											zipOutputStream.Write(array3, 0, count2);
										}
									}
								}
							}
							response.Flush();
							for (int num6 = 0; num6 < list.Count; num6++)
							{
								list[num6] = list[num6] + "\f" + list2[num6];
							}
							return list;
						}
						string text6 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text6 += "<html><head>";
							text6 += "<script type=\"text/javascript\">";
							text6 += "if (window.postMessage) {";
							text6 += "if (window.addEventListener) {";
							text6 += "window.addEventListener('message', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "}, false);";
							text6 += "}";
							text6 += "else if (window.attachEvent) {";
							text6 += "window.attachEvent('onmessage', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "});";
							text6 += "}";
							text6 += "}";
							text6 += "</script>";
							text6 += "</head>";
							text6 += "<body>";
							text6 += "{0}";
							text6 += "</body>";
							text6 += "</html>";
						}
						else
						{
							text6 = "{0}";
						}
						string text7 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("[FAIL]" + text7, this._str_DebugFilePath);
						}
						text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text7));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text6);
						return text7;
					}
				}
				else
				{
					if (this.sDownloadList == null)
					{
						string text8 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text8 += "<html><head>";
							text8 += "<script type=\"text/javascript\">";
							text8 += "if (window.postMessage) {";
							text8 += "if (window.addEventListener) {";
							text8 += "window.addEventListener('message', function (e) {";
							text8 += "var sendUrl = e.origin;";
							text8 += "var data = document.body.innerHTML;";
							text8 += "e.source.postMessage(data, sendUrl);";
							text8 += "}, false);";
							text8 += "}";
							text8 += "else if (window.attachEvent) {";
							text8 += "window.attachEvent('onmessage', function (e) {";
							text8 += "var sendUrl = e.origin;";
							text8 += "var data = document.body.innerHTML;";
							text8 += "e.source.postMessage(data, sendUrl);";
							text8 += "});";
							text8 += "}";
							text8 += "}";
							text8 += "</script>";
							text8 += "</head>";
							text8 += "<body>";
							text8 += "{0}";
							text8 += "</body>";
							text8 += "</html>";
						}
						else
						{
							text8 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
						}
						text8 = text8.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text8);
						return "error|009|Invalid parameter on server";
					}
					List<Stream> list3 = new List<Stream>();
					List<string> list4 = new List<string>();
					List<string> list5 = new List<string>();
					string empty4 = string.Empty;
					string empty5 = string.Empty;
					if (fileOrgNameAry.Length != this.sDownloadList.Count)
					{
						string text9 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text9 += "<html><head>";
							text9 += "<script type=\"text/javascript\">";
							text9 += "if (window.postMessage) {";
							text9 += "if (window.addEventListener) {";
							text9 += "window.addEventListener('message', function (e) {";
							text9 += "var sendUrl = e.origin;";
							text9 += "var data = document.body.innerHTML;";
							text9 += "e.source.postMessage(data, sendUrl);";
							text9 += "}, false);";
							text9 += "}";
							text9 += "else if (window.attachEvent) {";
							text9 += "window.attachEvent('onmessage', function (e) {";
							text9 += "var sendUrl = e.origin;";
							text9 += "var data = document.body.innerHTML;";
							text9 += "e.source.postMessage(data, sendUrl);";
							text9 += "});";
							text9 += "}";
							text9 += "}";
							text9 += "</script>";
							text9 += "</head>";
							text9 += "<body>";
							text9 += "{0}";
							text9 += "</body>";
							text9 += "</html>";
						}
						else
						{
							text9 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
						}
						text9 = text9.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text9);
						return "error|009|Invalid parameter on server";
					}
					for (int num7 = 0; num7 < this.sDownloadList.Count; num7++)
					{
						list3.Add(this.sDownloadList[num7]);
						list4.Add(fileOrgNameAry[num7]);
						list5.Add("");
						if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list4[num7]))
						{
							string text10 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text10 += "<html><head>";
								text10 += "<script type=\"text/javascript\">";
								text10 += "if (window.postMessage) {";
								text10 += "if (window.addEventListener) {";
								text10 += "window.addEventListener('message', function (e) {";
								text10 += "var sendUrl = e.origin;";
								text10 += "var data = document.body.innerHTML;";
								text10 += "e.source.postMessage(data, sendUrl);";
								text10 += "}, false);";
								text10 += "}";
								text10 += "else if (window.attachEvent) {";
								text10 += "window.attachEvent('onmessage', function (e) {";
								text10 += "var sendUrl = e.origin;";
								text10 += "var data = document.body.innerHTML;";
								text10 += "e.source.postMessage(data, sendUrl);";
								text10 += "});";
								text10 += "}";
								text10 += "}";
								text10 += "</script>";
								text10 += "</head>";
								text10 += "<body>";
								text10 += "{0}";
								text10 += "</body>";
								text10 += "</html>";
							}
							else
							{
								text10 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
							}
							text10 = text10.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text10);
							return "error|012|Not allowed file extension";
						}
						if (!base.CheckBlackWord(this.fileBlackWordList, list4[num7]))
						{
							string text11 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text11 += "<html><head>";
								text11 += "<script type=\"text/javascript\">";
								text11 += "if (window.postMessage) {";
								text11 += "if (window.addEventListener) {";
								text11 += "window.addEventListener('message', function (e) {";
								text11 += "var sendUrl = e.origin;";
								text11 += "var data = document.body.innerHTML;";
								text11 += "e.source.postMessage(data, sendUrl);";
								text11 += "}, false);";
								text11 += "}";
								text11 += "else if (window.attachEvent) {";
								text11 += "window.attachEvent('onmessage', function (e) {";
								text11 += "var sendUrl = e.origin;";
								text11 += "var data = document.body.innerHTML;";
								text11 += "e.source.postMessage(data, sendUrl);";
								text11 += "});";
								text11 += "}";
								text11 += "}";
								text11 += "</script>";
								text11 += "</head>";
								text11 += "<body>";
								text11 += "{0}";
								text11 += "</body>";
								text11 += "</html>";
							}
							else
							{
								text11 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
							}
							text11 = text11.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text11);
							return "error|018|There does not allow the string included in file name";
						}
					}
					if (string.IsNullOrEmpty(empty2) && list3.Count > 0)
					{
						string[] array4 = new string[list5.Count];
						string[] array5 = new string[list4.Count];
						for (int num8 = 0; num8 < list5.Count; num8++)
						{
							array4[num8] = "";
						}
						for (int num9 = 0; num9 < list4.Count; num9++)
						{
							array5[num9] = list4[num9];
						}
						string[] requestValue2 = base.GetRequestValue(this.hContext, "customValue");
						try
						{
							UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
							uploadEventEntity2.Context = this.hContext;
							uploadEventEntity2.DownloadFilePath = array4;
							uploadEventEntity2.DownloadFileName = array5;
							uploadEventEntity2.DownloadCustomValue = requestValue2;
							pOpenDownloadBeforeInitializeEventEx(uploadEventEntity2);
							array5 = uploadEventEntity2.DownloadFileName;
							for (int num10 = 0; num10 < array5.Length; num10++)
							{
								list4[num10] = array5[num10];
							}
						}
						catch
						{
						}
						if (pCustomError == null)
						{
							int count3 = list4.Count;
							string fileNameRuleEx2 = this._entity_dextParam.fileNameRuleEx;
							for (int num11 = count3 - 1; num11 >= 0; num11--)
							{
								for (int num12 = num11 - 1; num12 >= 0; num12--)
								{
									if (list4[num11].ToLower() == list4[num12].ToLower())
									{
										if (list4[num11].IndexOf('\\') > -1)
										{
											list4[num11] = list4[num11].Substring(0, list4[num11].LastIndexOf('\\') + 1) + base.GenerateUniqueKey() + "_" + list4[num11].Substring(list4[num11].LastIndexOf('\\') + 1);
										}
										else
										{
											list4[num11] = base.GenerateUniqueKey() + "_" + list4[num11];
										}
									}
								}
							}
							HttpResponse response2 = this.hContext.Response;
							response2.Clear();
							response2.BufferOutput = false;
							string headerFileName2 = base.GetHeaderFileName(this.zipFileName);
							response2.AddHeader("Content-Disposition", "attachment; filename=\"" + headerFileName2 + "\"");
							response2.ContentType = "application/octet-stream";
							long num13 = 0L;
							for (int num14 = 0; num14 < list3.Count; num14++)
							{
								num13 += list3[num14].Length;
							}
							response2.AddHeader("X-Raon-Cl", num13.ToString());
							using (ZipOutputStream zipOutputStream2 = new ZipOutputStream(response2.OutputStream))
							{
								zipOutputStream2.CompressionLevel = CompressionLevel.None;
								zipOutputStream2.AlternateEncodingUsage = ZipOption.AsNecessary;
								zipOutputStream2.AlternateEncoding = Encoding.GetEncoding(949);
								zipOutputStream2.Comment = "This zip was created from DEXT5 Upload.";
								zipOutputStream2.EnableZip64 = this._enableZip64;
								for (int num15 = 0; num15 < list3.Count; num15++)
								{
									if (list4[num15].IndexOf('\\') > -1)
									{
										zipOutputStream2.PutNextEntry(list4[num15].Replace('\\', '/'));
									}
									else
									{
										zipOutputStream2.PutNextEntry(list4[num15]);
									}
									byte[] array6 = new byte[this.iBufferSize];
									int count4;
									while (response2.IsClientConnected && (count4 = list3[num15].Read(array6, 0, array6.Length)) > 0)
									{
										zipOutputStream2.Write(array6, 0, count4);
									}
								}
							}
							response2.Flush();
							for (int num16 = 0; num16 < list3.Count; num16++)
							{
								list5[num16] = "\f" + list4[num16];
							}
							return list5;
						}
						string text12 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text12 += "<html><head>";
							text12 += "<script type=\"text/javascript\">";
							text12 += "if (window.postMessage) {";
							text12 += "if (window.addEventListener) {";
							text12 += "window.addEventListener('message', function (e) {";
							text12 += "var sendUrl = e.origin;";
							text12 += "var data = document.body.innerHTML;";
							text12 += "e.source.postMessage(data, sendUrl);";
							text12 += "}, false);";
							text12 += "}";
							text12 += "else if (window.attachEvent) {";
							text12 += "window.attachEvent('onmessage', function (e) {";
							text12 += "var sendUrl = e.origin;";
							text12 += "var data = document.body.innerHTML;";
							text12 += "e.source.postMessage(data, sendUrl);";
							text12 += "});";
							text12 += "}";
							text12 += "}";
							text12 += "</script>";
							text12 += "</head>";
							text12 += "<body>";
							text12 += "{0}";
							text12 += "</body>";
							text12 += "</html>";
						}
						else
						{
							text12 = "{0}";
						}
						string text13 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("[FAIL]" + text13, this._str_DebugFilePath);
						}
						text12 = text12.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text13));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text12);
						return text13;
					}
				}
			}
			else
			{
				string fileVirtualPath = this._entity_dextParam.fileVirtualPath;
				string empty6 = string.Empty;
				string empty7 = string.Empty;
				if (string.IsNullOrEmpty(fileVirtualPath))
				{
					string text14 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text14 += "<html><head>";
						text14 += "<script type=\"text/javascript\">";
						text14 += "if (window.postMessage) {";
						text14 += "if (window.addEventListener) {";
						text14 += "window.addEventListener('message', function (e) {";
						text14 += "var sendUrl = e.origin;";
						text14 += "var data = document.body.innerHTML;";
						text14 += "e.source.postMessage(data, sendUrl);";
						text14 += "}, false);";
						text14 += "}";
						text14 += "else if (window.attachEvent) {";
						text14 += "window.attachEvent('onmessage', function (e) {";
						text14 += "var sendUrl = e.origin;";
						text14 += "var data = document.body.innerHTML;";
						text14 += "e.source.postMessage(data, sendUrl);";
						text14 += "});";
						text14 += "}";
						text14 += "}";
						text14 += "</script>";
						text14 += "</head>";
						text14 += "<body>";
						text14 += "{0}";
						text14 += "</body>";
						text14 += "</html>";
					}
					else
					{
						text14 = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
					}
					text14 = text14.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text14);
					return "error|009|Invalid parameter on server";
				}
				string[] fileVirtualPathAry = this._entity_dextParam.fileVirtualPathAry;
				string[] fileOrgNameAry2 = this._entity_dextParam.fileOrgNameAry;
				if (this._entity_dextParam.mode == "normal")
				{
					for (int num17 = 0; num17 < fileVirtualPathAry.Length; num17++)
					{
						fileVirtualPathAry[num17] = HttpUtility.UrlDecode(fileVirtualPathAry[num17], Encoding.UTF8);
						fileOrgNameAry2[num17] = HttpUtility.UrlDecode(fileOrgNameAry2[num17], Encoding.UTF8);
					}
				}
				List<string> list6 = new List<string>();
				List<string> list7 = new List<string>();
				string text15 = string.Empty;
				string empty8 = string.Empty;
				string str = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Authority;
				for (int num18 = 0; num18 < fileVirtualPathAry.Length; num18++)
				{
					text15 = fileVirtualPathAry[num18];
					if (!string.IsNullOrEmpty(this.downloadRootPath) && File.Exists(this.downloadRootPath + text15))
					{
						list6.Add(this.downloadRootPath + text15);
						list7.Add(fileOrgNameAry2[num18]);
					}
					else if (File.Exists(text15))
					{
						list6.Add(text15);
						list7.Add(fileOrgNameAry2[num18]);
					}
					else
					{
						string text16 = text15.Replace("http://", "");
						text16 = text16.Replace("https://", "");
						if (text16.IndexOf("/") >= 0)
						{
							text16 = text16.Substring(text16.IndexOf("/"));
						}
						if (!string.IsNullOrEmpty(text16))
						{
							if (!string.IsNullOrEmpty(this.downloadRootPath))
							{
								text16 = this.hContext.Request.MapPath(this.downloadRootPath + text16);
							}
							else
							{
								text16 = this.hContext.Request.MapPath(text16);
							}
						}
						if (File.Exists(text16))
						{
							list6.Add(text16);
							list7.Add(fileOrgNameAry2[num18]);
						}
						else
						{
							if (this._b_useExternalDownload && !string.IsNullOrEmpty(text16))
							{
								if (text15.IndexOf("http://") == -1 && text15.IndexOf("https://") == -1)
								{
									text15 = str + text15.Substring(text15.IndexOf("/"));
								}
								text15 = base.CheckExternalWebFile(text15);
							}
							else
							{
								text15 = "";
							}
							if (string.IsNullOrEmpty(text15))
							{
								string text17 = "";
								if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
								{
									text17 += "<html><head>";
									text17 += "<script type=\"text/javascript\">";
									text17 += "if (window.postMessage) {";
									text17 += "if (window.addEventListener) {";
									text17 += "window.addEventListener('message', function (e) {";
									text17 += "var sendUrl = e.origin;";
									text17 += "var data = document.body.innerHTML;";
									text17 += "e.source.postMessage(data, sendUrl);";
									text17 += "}, false);";
									text17 += "}";
									text17 += "else if (window.attachEvent) {";
									text17 += "window.attachEvent('onmessage', function (e) {";
									text17 += "var sendUrl = e.origin;";
									text17 += "var data = document.body.innerHTML;";
									text17 += "e.source.postMessage(data, sendUrl);";
									text17 += "});";
									text17 += "}";
									text17 += "}";
									text17 += "</script>";
									text17 += "</head>";
									text17 += "<body>";
									text17 += "{0}";
									text17 += "</body>";
									text17 += "</html>";
								}
								else
								{
									text17 = "{0}";
								}
								if (this._b_IsDebug)
								{
									LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
								}
								text17 = text17.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
								this.hContext.Response.Clear();
								this.hContext.Response.Write(text17);
								return "error|010|Not found file on server";
							}
							list6.Add(text15);
							list7.Add(fileOrgNameAry2[num18]);
						}
					}
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list7[num18]))
					{
						string text18 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text18 += "<html><head>";
							text18 += "<script type=\"text/javascript\">";
							text18 += "if (window.postMessage) {";
							text18 += "if (window.addEventListener) {";
							text18 += "window.addEventListener('message', function (e) {";
							text18 += "var sendUrl = e.origin;";
							text18 += "var data = document.body.innerHTML;";
							text18 += "e.source.postMessage(data, sendUrl);";
							text18 += "}, false);";
							text18 += "}";
							text18 += "else if (window.attachEvent) {";
							text18 += "window.attachEvent('onmessage', function (e) {";
							text18 += "var sendUrl = e.origin;";
							text18 += "var data = document.body.innerHTML;";
							text18 += "e.source.postMessage(data, sendUrl);";
							text18 += "});";
							text18 += "}";
							text18 += "}";
							text18 += "</script>";
							text18 += "</head>";
							text18 += "<body>";
							text18 += "{0}";
							text18 += "</body>";
							text18 += "</html>";
						}
						else
						{
							text18 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						text18 = text18.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text18);
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list7[num18]))
					{
						string text19 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text19 += "<html><head>";
							text19 += "<script type=\"text/javascript\">";
							text19 += "if (window.postMessage) {";
							text19 += "if (window.addEventListener) {";
							text19 += "window.addEventListener('message', function (e) {";
							text19 += "var sendUrl = e.origin;";
							text19 += "var data = document.body.innerHTML;";
							text19 += "e.source.postMessage(data, sendUrl);";
							text19 += "}, false);";
							text19 += "}";
							text19 += "else if (window.attachEvent) {";
							text19 += "window.attachEvent('onmessage', function (e) {";
							text19 += "var sendUrl = e.origin;";
							text19 += "var data = document.body.innerHTML;";
							text19 += "e.source.postMessage(data, sendUrl);";
							text19 += "});";
							text19 += "}";
							text19 += "}";
							text19 += "</script>";
							text19 += "</head>";
							text19 += "<body>";
							text19 += "{0}";
							text19 += "</body>";
							text19 += "</html>";
						}
						else
						{
							text19 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						text19 = text19.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text19);
						return "error|018|There does not allow the string included in file name";
					}
				}
				if (string.IsNullOrEmpty(empty7) && list6.Count > 0)
				{
					string[] array7 = new string[list6.Count];
					string[] array8 = new string[list7.Count];
					for (int num19 = 0; num19 < list6.Count; num19++)
					{
						array7[num19] = list6[num19];
					}
					for (int num20 = 0; num20 < list7.Count; num20++)
					{
						array8[num20] = list7[num20];
					}
					string[] requestValue3 = base.GetRequestValue(this.hContext, "customValue");
					try
					{
						UploadEventEntity uploadEventEntity3 = new UploadEventEntity();
						uploadEventEntity3.Context = this.hContext;
						uploadEventEntity3.DownloadFilePath = array7;
						uploadEventEntity3.DownloadFileName = array8;
						uploadEventEntity3.DownloadCustomValue = requestValue3;
						pOpenDownloadBeforeInitializeEventEx(uploadEventEntity3);
						array7 = uploadEventEntity3.DownloadFilePath;
						array8 = uploadEventEntity3.DownloadFileName;
						for (int num21 = 0; num21 < array7.Length; num21++)
						{
							list6[num21] = array7[num21];
						}
						for (int num22 = 0; num22 < array8.Length; num22++)
						{
							list7[num22] = array8[num22];
						}
					}
					catch
					{
					}
					if (pCustomError == null)
					{
						int count5 = list7.Count;
						string fileNameRuleEx3 = this._entity_dextParam.fileNameRuleEx;
						for (int num23 = count5 - 1; num23 >= 0; num23--)
						{
							for (int num24 = num23 - 1; num24 >= 0; num24--)
							{
								if (list7[num23].ToLower() == list7[num24].ToLower())
								{
									if (list7[num23].IndexOf('\\') > -1)
									{
										list7[num23] = list7[num23].Substring(0, list7[num23].LastIndexOf('\\') + 1) + base.GenerateUniqueKey() + "_" + list7[num23].Substring(list7[num23].LastIndexOf('\\') + 1);
									}
									else
									{
										list7[num23] = base.GenerateUniqueKey() + "_" + list7[num23];
									}
								}
							}
						}
						HttpResponse response3 = this.hContext.Response;
						response3.Clear();
						response3.BufferOutput = false;
						string headerFileName3 = base.GetHeaderFileName(this.zipFileName);
						response3.AddHeader("Content-Disposition", "attachment; filename=\"" + headerFileName3 + "\"");
						response3.ContentType = "application/octet-stream";
						long num25 = 0L;
						for (int num26 = 0; num26 < list6.Count; num26++)
						{
							if (list6[num26].ToLower().StartsWith("http"))
							{
								HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(list6[num26]);
								httpWebRequest.UserAgent = "DEXT5" + Guid.NewGuid();
								httpWebRequest.Timeout = 60000;
								HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
								num25 += httpWebResponse.ContentLength;
								response3.Close();
							}
							else
							{
								FileInfo fileInfo2 = new FileInfo(list6[num26]);
								num25 += fileInfo2.Length;
							}
						}
						response3.AddHeader("X-Raon-Cl", num25.ToString());
						using (ZipOutputStream zipOutputStream3 = new ZipOutputStream(response3.OutputStream))
						{
							zipOutputStream3.CompressionLevel = CompressionLevel.None;
							zipOutputStream3.AlternateEncodingUsage = ZipOption.AsNecessary;
							zipOutputStream3.AlternateEncoding = Encoding.GetEncoding(949);
							zipOutputStream3.Comment = "This zip was created from DEXT5 Upload.";
							zipOutputStream3.EnableZip64 = this._enableZip64;
							int num27 = 0;
							while (num27 < list6.Count)
							{
								if (list7[num27].IndexOf('\\') > -1)
								{
									zipOutputStream3.PutNextEntry(list7[num27].Replace('\\', '/'));
								}
								else
								{
									zipOutputStream3.PutNextEntry(list7[num27]);
								}
								if (list6[num27].ToLower().StartsWith("http"))
								{
									HttpWebRequest httpWebRequest2 = (HttpWebRequest)WebRequest.Create(list6[num27]);
									httpWebRequest2.Timeout = 60000;
									HttpWebResponse httpWebResponse3;
									HttpWebResponse httpWebResponse2 = httpWebResponse3 = (HttpWebResponse)httpWebRequest2.GetResponse();
									try
									{
										using (Stream responseStream = httpWebResponse2.GetResponseStream())
										{
											byte[] array9 = new byte[this.iBufferSize];
											int count6;
											while (response3.IsClientConnected && (count6 = responseStream.Read(array9, 0, array9.Length)) > 0)
											{
												zipOutputStream3.Write(array9, 0, count6);
											}
										}
										goto IL_29AE;
									}
									finally
									{
										if (httpWebResponse3 != null)
										{
											((IDisposable)httpWebResponse3).Dispose();
										}
									}
									goto IL_2955;
								}
								goto IL_2955;
								IL_29AE:
								num27++;
								continue;
								IL_2955:
								using (FileStream fileStream2 = File.Open(list6[num27], FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
								{
									byte[] array10 = new byte[this.iBufferSize];
									int count7;
									while (response3.IsClientConnected && (count7 = fileStream2.Read(array10, 0, array10.Length)) > 0)
									{
										zipOutputStream3.Write(array10, 0, count7);
									}
								}
								goto IL_29AE;
							}
							response3.Flush();
						}
						for (int num28 = 0; num28 < list6.Count; num28++)
						{
							list6[num28] = list6[num28] + "\f" + list7[num28];
						}
						return list6;
					}
					string text20 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text20 += "<html><head>";
						text20 += "<script type=\"text/javascript\">";
						text20 += "if (window.postMessage) {";
						text20 += "if (window.addEventListener) {";
						text20 += "window.addEventListener('message', function (e) {";
						text20 += "var sendUrl = e.origin;";
						text20 += "var data = document.body.innerHTML;";
						text20 += "e.source.postMessage(data, sendUrl);";
						text20 += "}, false);";
						text20 += "}";
						text20 += "else if (window.attachEvent) {";
						text20 += "window.attachEvent('onmessage', function (e) {";
						text20 += "var sendUrl = e.origin;";
						text20 += "var data = document.body.innerHTML;";
						text20 += "e.source.postMessage(data, sendUrl);";
						text20 += "});";
						text20 += "}";
						text20 += "}";
						text20 += "</script>";
						text20 += "</head>";
						text20 += "<body>";
						text20 += "{0}";
						text20 += "</body>";
						text20 += "</html>";
					}
					else
					{
						text20 = "{0}";
					}
					string text21 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("[FAIL]" + text21, this._str_DebugFilePath);
					}
					text20 = text20.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text21));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text20);
					return text21;
				}
			}
			return null;
		}

		// Token: 0x0400001D RID: 29
		protected const string RexRangeValuePattern = "^bytes=(d+)-$";

		// Token: 0x0400001E RID: 30
		protected string zipFileName = string.Empty;

		// Token: 0x0400001F RID: 31
		protected string fileWhiteList = string.Empty;

		// Token: 0x04000020 RID: 32
		protected string fileBlackList = string.Empty;

		// Token: 0x04000021 RID: 33
		protected string[] fileBlackWordList;

		// Token: 0x04000022 RID: 34
		protected List<string> strDownloadList;

		// Token: 0x04000023 RID: 35
		protected List<Stream> sDownloadList;

		// Token: 0x04000024 RID: 36
		protected string downloadRootPath = string.Empty;

		// Token: 0x04000025 RID: 37
		protected string allowExtensionSpecialSymbol = string.Empty;

		// Token: 0x04000026 RID: 38
		protected bool bCustom;
	}
}

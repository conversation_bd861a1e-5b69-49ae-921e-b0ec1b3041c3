package jcifs.netbios;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NodeStatusRequest.class */
class NodeStatusRequest extends NameServicePacket {
    NodeStatusRequest(Name name) {
        this.questionName = name;
        this.questionType = 33;
        this.isRecurDesired = false;
        this.isBroadcast = false;
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeBodyWireFormat(byte[] dst, int dstIndex) {
        int tmp = this.questionName.hexCode;
        this.questionName.hexCode = 0;
        int result = writeQuestionSectionWireFormat(dst, dstIndex);
        this.questionName.hexCode = tmp;
        return result;
    }

    @Override // jcifs.netbios.NameServicePacket
    int readBodyWireFormat(byte[] src, int srcIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeRDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    int readRDataWireFormat(byte[] src, int srcIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    public String toString() {
        return new String("NodeStatusRequest[" + super.toString() + "]");
    }
}

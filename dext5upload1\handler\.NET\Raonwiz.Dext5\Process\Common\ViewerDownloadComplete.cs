﻿using System;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000004 RID: 4
	public class ViewerDownloadComplete : Base
	{
		// Token: 0x06000031 RID: 49 RVA: 0x0000604B File Offset: 0x0000424B
		public ViewerDownloadComplete(HttpContext context) : base(context)
		{
		}

		// Token: 0x06000032 RID: 50 RVA: 0x00006060 File Offset: 0x00004260
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string dext5CMD = this._entity_dextParam.dext5CMD;
			if (!string.IsNullOrEmpty(dext5CMD) && dext5CMD == "vdc")
			{
				string viewerGUID = this._entity_dextParam.viewerGUID;
				if (!string.IsNullOrEmpty(viewerGUID))
				{
					UploadStatus uploadStatus = (UploadStatus)this.hContext.Application[viewerGUID];
					if (uploadStatus != null)
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(uploadStatus.Message);
						this.hContext.Application.Remove(viewerGUID);
					}
					else
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write("");
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("CMD - " + dext5CMD + " OpenCheck", this._str_DebugFilePath);
					}
				}
				return null;
			}
			return null;
		}

		// Token: 0x0400001C RID: 28
		private string _urlAddress = string.Empty;
	}
}

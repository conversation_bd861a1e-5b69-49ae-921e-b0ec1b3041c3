﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: SetFileMark</h3>
    <p class="ttl">void SetFileMark(itemIndex, value, uploadID)</p>
    <p class="txt">
        파일 추가할 때 각 파일에 특정 정보를 추가 후 전송합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">itemIndex</span>&nbsp;&nbsp;0부터 시작되는 파일의 순서정보, -1입력시 마지막에 추가된 파일을  의미합니다. <br/>
        <span class="firebrick">value</span>&nbsp;&nbsp;해당 파일에 설정할 Mark 정보를 의미합니다.<br/>
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;적용할 업로드의 id값을 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function DEXT5UPLOAD_AfterAddItem(uploadID, strFileName, nFileSize, nAddItemIndex) {
            DEXT5UPLOAD.SetFileMark("-1", "MarkValue", uploadID);
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


package jcifs.netbios;

import java.io.UnsupportedEncodingException;
import jcifs.Config;
import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/Name.class */
public class Name {
    private static final int TYPE_OFFSET = 31;
    private static final int SCOPE_OFFSET = 33;
    private static final String DEFAULT_SCOPE = Config.getProperty("jcifs.netbios.scope");
    static final String OEM_ENCODING = Config.getProperty("jcifs.encoding", System.getProperty("file.encoding"));
    public String name;
    public String scope;
    public int hexCode;
    int srcHashCode;

    Name() {
    }

    public Name(String name, int hexCode, String scope) {
        this.name = (name.length() > 15 ? name.substring(0, 15) : name).toUpperCase();
        this.hexCode = hexCode;
        this.scope = (scope == null || scope.length() <= 0) ? DEFAULT_SCOPE : scope;
        this.srcHashCode = 0;
    }

    int writeWireFormat(byte[] dst, int dstIndex) {
        dst[dstIndex] = 32;
        try {
            byte[] tmp = this.name.getBytes(OEM_ENCODING);
            int i = 0;
            while (i < tmp.length) {
                dst[dstIndex + (2 * i) + 1] = (byte) (((tmp[i] & 240) >> 4) + 65);
                dst[dstIndex + (2 * i) + 2] = (byte) ((tmp[i] & 15) + 65);
                i++;
            }
            while (i < 15) {
                dst[dstIndex + (2 * i) + 1] = 67;
                dst[dstIndex + (2 * i) + 2] = 65;
                i++;
            }
            dst[dstIndex + TYPE_OFFSET] = (byte) (((this.hexCode & 240) >> 4) + 65);
            dst[dstIndex + TYPE_OFFSET + 1] = (byte) ((this.hexCode & 15) + 65);
        } catch (UnsupportedEncodingException e) {
        }
        return 33 + writeScopeWireFormat(dst, dstIndex + 33);
    }

    int readWireFormat(byte[] src, int srcIndex) {
        byte[] tmp = new byte[33];
        int length = 15;
        for (int i = 0; i < 15; i++) {
            tmp[i] = (byte) (((src[srcIndex + ((2 * i) + 1)] & 255) - 65) << 4);
            int i2 = i;
            tmp[i2] = (byte) (tmp[i2] | ((byte) (((src[srcIndex + ((2 * i) + 2)] & 255) - 65) & 15)));
            if (tmp[i] != 32) {
                length = i + 1;
            }
        }
        try {
            this.name = new String(tmp, 0, length, OEM_ENCODING);
        } catch (UnsupportedEncodingException e) {
        }
        this.hexCode = ((src[srcIndex + TYPE_OFFSET] & 255) - 65) << 4;
        this.hexCode |= ((src[(srcIndex + TYPE_OFFSET) + 1] & 255) - 65) & 15;
        return 33 + readScopeWireFormat(src, srcIndex + 33);
    }

    int writeScopeWireFormat(byte[] dst, int dstIndex) {
        int i;
        if (this.scope == null) {
            dst[dstIndex] = 0;
            return 1;
        }
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = 46;
        try {
            System.arraycopy(this.scope.getBytes(OEM_ENCODING), 0, dst, dstIndex2, this.scope.length());
        } catch (UnsupportedEncodingException e) {
        }
        int dstIndex3 = dstIndex2 + this.scope.length();
        dst[dstIndex3] = 0;
        int i2 = (dstIndex3 + 1) - 2;
        int e2 = i2 - this.scope.length();
        int c = 0;
        do {
            if (dst[i2] == 46) {
                dst[i2] = (byte) c;
                c = 0;
            } else {
                c++;
            }
            i = i2;
            i2 = i - 1;
        } while (i > e2);
        return this.scope.length() + 2;
    }

    int readScopeWireFormat(byte[] src, int srcIndex) {
        int srcIndex2 = srcIndex + 1;
        int n = src[srcIndex] & 255;
        if (n == 0) {
            this.scope = null;
            return 1;
        }
        try {
            StringBuffer sb = new StringBuffer(new String(src, srcIndex2, n, OEM_ENCODING));
            int srcIndex3 = srcIndex2 + n;
            while (true) {
                int i = srcIndex3;
                srcIndex2 = srcIndex3 + 1;
                int n2 = src[i] & 255;
                if (n2 == 0) {
                    break;
                }
                sb.append('.').append(new String(src, srcIndex2, n2, OEM_ENCODING));
                srcIndex3 = srcIndex2 + n2;
            }
            this.scope = sb.toString();
        } catch (UnsupportedEncodingException e) {
        }
        return srcIndex2 - srcIndex;
    }

    public int hashCode() {
        int result = this.name.hashCode() + (65599 * this.hexCode) + (65599 * this.srcHashCode);
        if (this.scope != null && this.scope.length() != 0) {
            result += this.scope.hashCode();
        }
        return result;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof Name)) {
            return false;
        }
        Name n = (Name) obj;
        return (this.scope == null && n.scope == null) ? this.name.equals(n.name) && this.hexCode == n.hexCode : this.name.equals(n.name) && this.hexCode == n.hexCode && this.scope.equals(n.scope);
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        String n = this.name;
        if (n == null) {
            n = "null";
        } else if (n.charAt(0) == 1) {
            char[] c = n.toCharArray();
            c[0] = '.';
            c[1] = '.';
            c[14] = '.';
            n = new String(c);
        }
        sb.append(n).append("<").append(Hexdump.toHexString(this.hexCode, 2)).append(">");
        if (this.scope != null) {
            sb.append(".").append(this.scope);
        }
        return sb.toString();
    }
}

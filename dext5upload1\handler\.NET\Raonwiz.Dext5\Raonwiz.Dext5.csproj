﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DBCB1AF5-0ACB-4B5B-B0E3-31FDB9515928}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Raonwiz.Dext5</RootNamespace>
    <AssemblyName>Raonwiz.Dext5</AssemblyName>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Dext5CustomError.cs" />
    <Compile Include="Common\Dext5Encoding.cs" />
    <Compile Include="Common\Dext5Image.cs" />
    <Compile Include="Common\Dext5ImageFormat.cs" />
    <Compile Include="Common\Dext5Impersonator.cs" />
    <Compile Include="Common\Dext5Mime.cs" />
    <Compile Include="Common\Dext5Parameter.cs" />
    <Compile Include="Common\Dext5UNCAccessWithCredentials.cs" />
    <Compile Include="Common\Entity\TextWaterMark.cs" />
    <Compile Include="Common\Exif\ExifEntity.cs" />
    <Compile Include="Common\Exif\ExifTag.cs" />
    <Compile Include="Common\Exif\ExifTagCollection.cs" />
    <Compile Include="Common\Exif\GPSRational.cs" />
    <Compile Include="Common\Exif\Rational.cs" />
    <Compile Include="Common\Exif\SupportedTags.cs" />
    <Compile Include="Common\Exif\URational.cs" />
    <Compile Include="Common\LogUtil.cs" />
    <Compile Include="OpenDownloadBeforeInitializeDelegateEx.cs" />
    <Compile Include="OpenDownloadHandlerDelegate.cs" />
    <Compile Include="OpenDownloadHandlerDelegateEx.cs" />
    <Compile Include="PhotoHandler.cs" />
    <Compile Include="Process\Base.cs" />
    <Compile Include="Process\Base64\Upload.cs" />
    <Compile Include="Process\Common\ConfigXML.cs" />
    <Compile Include="Process\Common\CustomDownload.cs" />
    <Compile Include="Process\Common\Download.cs" />
    <Compile Include="Process\Common\Garbage.cs" />
    <Compile Include="Process\Common\GetFileSize.cs" />
    <Compile Include="Process\Common\HttpResponseHeader.cs" />
    <Compile Include="Process\Common\LicenseCheck.cs" />
    <Compile Include="Process\Common\MakeZip.cs" />
    <Compile Include="Process\Common\MultiDownload.cs" />
    <Compile Include="Process\Common\Open.cs" />
    <Compile Include="Process\Common\OpenFileCheck.cs" />
    <Compile Include="Process\Common\ResumeDownload.cs" />
    <Compile Include="Process\Common\TextFileEncodingDetector.cs" />
    <Compile Include="Process\Common\UploadZeroFile.cs" />
    <Compile Include="Process\Common\ViewerDownloadComplete.cs" />
    <Compile Include="Process\Common\ZipStatus.cs" />
    <Compile Include="Process\Entity\ParamEntity.cs" />
    <Compile Include="Process\Entity\UploadEventEntity.cs" />
    <Compile Include="Process\Html4\Entity\UploadStatus.cs" />
    <Compile Include="Process\Html4\FileCheck.cs" />
    <Compile Include="Process\Html4\FileProcessor.cs" />
    <Compile Include="Process\Html4\Upload.cs" />
    <Compile Include="Process\Html4\UploadModule.cs" />
    <Compile Include="Process\Html4\UploadProgress.cs" />
    <Compile Include="Process\Html4\UploadStatus.cs" />
    <Compile Include="Process\Html4\UploadSWF.cs" />
    <Compile Include="Process\Html4\UploadWorkerRequest.cs" />
    <Compile Include="Process\Html5Plus\UploadAfterEvent.cs" />
    <Compile Include="Process\Html5Plus\UploadBeforeEvent.cs" />
    <Compile Include="Process\Html5\Merge.cs" />
    <Compile Include="Process\Html5\ResumeUpload.cs" />
    <Compile Include="Process\Html5\Upload.cs" />
    <Compile Include="Process\Html5\UploadOnProgress.cs" />
    <Compile Include="Process\HttpResponseHeader.cs" />
    <Compile Include="Process\Plugin\ResumeEnd.cs" />
    <Compile Include="Process\Plugin\Upload.cs" />
    <Compile Include="Process\Plugin\Utils.cs" />
    <Compile Include="Process\Version.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UploadHandler.cs" />
    <Compile Include="UploadHandlerBeforeCompleteDelegate.cs" />
    <Compile Include="UploadHandlerBeforeCompleteDelegateEx.cs" />
    <Compile Include="UploadHandlerBeforeInitializeDelegate.cs" />
    <Compile Include="UploadHandlerBeforeInitializeDelegateEx.cs" />
    <Compile Include="UploadHandlerDelegate.cs" />
    <Compile Include="UploadHandlerDelegateEx.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ionic.Zip\Ionic.Zip.csproj">
      <Project>{DBCB1AF5-0ACB-4B5B-B0E3-31FDB9515927}</Project>
      <Name>Ionic.Zip</Name>
    </ProjectReference>
    <ProjectReference Include="..\Raonwiz.Dext5.License.Library\Raonwiz.Dext5.License.Library.csproj">
      <Project>{DBCB1AF5-0ACB-4B5B-B0E3-31FDB9515929}</Project>
      <Name>Raonwiz.Dext5.License.Library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Raonwiz.Dext5.Security\Raonwiz.Dext5.Security.csproj">
      <Project>{DBCB1AF5-0ACB-4B5B-B0E3-31FDB951592A}</Project>
      <Name>Raonwiz.Dext5.Security</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
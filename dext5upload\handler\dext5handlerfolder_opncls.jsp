<%@ page import="wbframework.com.core.util.FileUtil"%>
<%@ page contentType="text/html;charset=utf-8"%>
<%
	request.setCharacterEncoding("UTF-8");

	Raonwiz.Dext5.UploadHandler upload = new Raonwiz.Dext5.UploadHandler();

	// 디버그시 사용(system.out.println 출력)
	// upload.SetDebugMode(true);

	Raonwiz.Dext5.UploadCompleteEventClass event = new Raonwiz.Dext5.UploadCompleteEventClass();

	// 업로드 완료전 이벤트
	event.addUploadCompleteBeforeEventListener(new Raonwiz.Dext5.UploadCompleteBeforeEventListener() {
		public void UploadCompleteBeforeEvent(Raonwiz.Dext5.Process.Common.ReferenceValues refValues) {
			refValues.setRequest(refValues.getRequest()); //Change Request Value
			refValues.setResponse(refValues.getResponse()); //Change Response Value
			refValues.setNewFileLocation(refValues.getNewFileLocation()); //Change NewFileLocation Value
			refValues.setResponseFileServerPath(refValues.getResponseFileServerPath()); //Change ResponseFileServerPath Value
			refValues.setResponseFileName(refValues.getResponseFileName()); //Change ResponseFileName Value
			//refValues.setResponseCustomValue("ResponseCustomValue"); //Set ResponseCustomValue
			//refValues.setCustomError("사용자오류");
			//refValues.setCustomError("999", "사용자오류"); /* Error Code를 설정하실 때에는 900이상의 3자리로 설정 */
		}
	});

	// 업로드 완료후 이벤트
    event.addUploadCompleteEventListener(new Raonwiz.Dext5.UploadCompleteEventListener() {
		public void UploadCompleteEvent(HttpServletRequest request, HttpServletResponse response, String newFileLocation, String responseFileServerPath, String responseFileName) {

		}
    });

	// 다운로드 완료후 이벤트
	event.addOpenDownloadCompleteEventListener(new Raonwiz.Dext5.OpenDownloadCompleteEventListener() {
		public void OpenDownloadCompleteEvent(HttpServletRequest request, HttpServletResponse response, String[] downloadFilePath, String[] downloadFileName, String[] downloadCustomValue) {
			// 파일 업로드 열기 및 다운로드시 발생하는 이벤트 입니다.
		}
    });
	
	// 임시파일 물리적 경로설정
	upload.SetTempPath(FileUtil.UPLOAD_CONTENTS_PATH+"/temp");
		
	// 실제 업로드 할 기본경로 설정
    upload.SetVirtualPath("/new_contents/openclass");
	upload.SetPhysicalPath(FileUtil.UPLOAD_CONTENTS_PATH+"/openclass");

	// 환경설정파일 물리적 폴더 (서버 환경변수를 사용할 경우)
    // upload.SetConfigPhysicalPath("D:\\temp");

	// 서버 구성정보중 Context Path가 있다며, 아래와 같이 설정해주세요. (SetVirtualPath 사용시만 필요)
	// upload.SetContextPath("Context Path");
    
    // upload.SetZipFileName("download.zip");

	// check file extension (두 방법 모두 설정시 허용확장자가 우선 적용됩니다.)
	// upload.SetFileWhiteList("png,jpg"); // 허용확장자
	// upload.SetFileBlackList("exe,zip"); // 제한확장자
	
	// 불필요한 파일을 삭제 처리하는 설정 (단위: 일)
    upload.SetGarbageCleanDay(2);
	
    String result = upload.Process(request, response, event);
	out.print(result);

	// Servlet으로 handler 작업을 하시려면 다음과 같이 작성해 주세요.
	/*
	String result = "";
    try {
		result = upload.Process(request, response, event);
	} catch (Exception e) {
		LOGGER.error(e.getMessage());
	}
		
    ServletOutputStream out = response.getOutputStream();
    out.print(result);
    out.close();
	*/
%>
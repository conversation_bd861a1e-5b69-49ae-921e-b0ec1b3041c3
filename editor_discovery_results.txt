目标: artplay.sd.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://artplay.sd.go.kr
发现的核心文件:
  - https://artplay.sd.go.kr/dext5upload.js (valid) - 验证成功
  - https://artplay.sd.go.kr/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://artplay.sd.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://artplay.sd.go.kr/handler/dext5handler.ashx (upload) - 基础验证通过
  - https://artplay.sd.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: artplay.sd.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://artplay.sd.go.kr
发现的核心文件:
  - https://artplay.sd.go.kr/HuskyEZCreator.js (valid) - 验证成功
  - https://artplay.sd.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://artplay.sd.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://artplay.sd.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: artplay.sd.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://artplay.sd.go.kr
发现的核心文件:
  - https://artplay.sd.go.kr/cheditor.js (valid) - 验证成功
  - https://artplay.sd.go.kr/cheditor.css (valid) - 验证成功
辅助路径:
  - https://artplay.sd.go.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://artplay.sd.go.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://artplay.sd.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: artplay.sd.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://artplay.sd.go.kr
发现的核心文件:
  - https://artplay.sd.go.kr/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://artplay.sd.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://artplay.sd.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://artplay.sd.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: artplay.sd.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://artplay.sd.go.kr
发现的核心文件:
  - https://artplay.sd.go.kr/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://artplay.sd.go.kr/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://artplay.sd.go.kr/core/connector/connector (rce) - 包含RCE相关内容
  - https://artplay.sd.go.kr/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://artplay.sd.go.kr/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: share.sd.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://share.sd.go.kr/js/
发现的核心文件:
  - https://share.sd.go.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: crafts.yongsan.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://crafts.yongsan.go.kr
发现的核心文件:
  - https://crafts.yongsan.go.kr/dext5upload.js (valid) - 验证成功
  - https://crafts.yongsan.go.kr/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://crafts.yongsan.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://crafts.yongsan.go.kr/handler/dext5handler.ashx (upload) - 基础验证通过
  - https://crafts.yongsan.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: crafts.yongsan.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://crafts.yongsan.go.kr
发现的核心文件:
  - https://crafts.yongsan.go.kr/HuskyEZCreator.js (valid) - 验证成功
  - https://crafts.yongsan.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://crafts.yongsan.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://crafts.yongsan.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: crafts.yongsan.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://crafts.yongsan.go.kr
发现的核心文件:
  - https://crafts.yongsan.go.kr/cheditor.js (valid) - 验证成功
  - https://crafts.yongsan.go.kr/cheditor.css (valid) - 验证成功
辅助路径:
  - https://crafts.yongsan.go.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://crafts.yongsan.go.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://crafts.yongsan.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: crafts.yongsan.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://crafts.yongsan.go.kr
发现的核心文件:
  - https://crafts.yongsan.go.kr/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://crafts.yongsan.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://crafts.yongsan.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://crafts.yongsan.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: crafts.yongsan.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://crafts.yongsan.go.kr
发现的核心文件:
辅助路径:
  - https://crafts.yongsan.go.kr/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://crafts.yongsan.go.kr/core/connector/connector (rce) - 包含RCE相关内容
  - https://crafts.yongsan.go.kr/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://crafts.yongsan.go.kr/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: memory.ddm.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/dext5upload.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: memory.ddm.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/HuskyEZCreator.js (valid) - 验证成功
  - https://memory.ddm.go.kr/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: memory.ddm.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/cheditor.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://memory.ddm.go.kr/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: memory.ddm.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: memory.ddm.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: 3.37.80.124
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/dext5upload.js (valid) - 基础验证通过
  - https://3.37.80.124/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: 3.37.80.124
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/HuskyEZCreator.js (valid) - 验证成功
  - https://3.37.80.124/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://3.37.80.124/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: 3.37.80.124
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/cheditor.js (valid) - 基础验证通过
  - https://3.37.80.124/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://3.37.80.124/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: 3.37.80.124
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: 3.37.80.124
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: chairman.gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: chairman.gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: chairman.gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: chairman.gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: chairman.gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: 211.34.96.55
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/dext5upload.js (valid) - 基础验证通过
  - https://211.34.96.55/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://211.34.96.55/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: 211.34.96.55
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/HuskyEZCreator.js (valid) - 基础验证通过
  - https://211.34.96.55/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://211.34.96.55/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: 211.34.96.55
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/cheditor.js (valid) - 基础验证通过
  - https://211.34.96.55/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://211.34.96.55/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://211.34.96.55/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: 211.34.96.55
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: 211.34.96.55
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/core/connector/php/connector.php (rce) - 基础验证通过
  - https://211.34.96.55/core/connector/connector (rce) - 基础验证通过
  - https://211.34.96.55/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://211.34.96.55/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

--------------------------------------------------

目标: gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: hex.co.kr:80
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://hex.co.kr:80/js/
发现的核心文件:
  - http://hex.co.kr:80/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: 118.219.232.101:8117
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://118.219.232.101:8117/js/
发现的核心文件:
  - http://118.219.232.101:8117/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: www.visionkorea.co.kr:80
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://www.visionkorea.co.kr:80/js/
发现的核心文件:
辅助路径:

--------------------------------------------------

目标: www.sungsinco.com:80
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://www.sungsinco.com:80/js/
发现的核心文件:
  - http://www.sungsinco.com:80/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: www.goldendewclub.com:443
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://www.goldendewclub.com:443
发现的核心文件:
  - https://www.goldendewclub.com:443/dext5upload.js (valid) - 验证成功
  - https://www.goldendewclub.com:443/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://www.goldendewclub.com:443/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: www.goldendewclub.com:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.goldendewclub.com:443
发现的核心文件:
  - https://www.goldendewclub.com:443/HuskyEZCreator.js (valid) - 验证成功
  - https://www.goldendewclub.com:443/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.goldendewclub.com:443/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: www.goldendewclub.com:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://www.goldendewclub.com:443
发现的核心文件:
  - https://www.goldendewclub.com:443/cheditor.js (valid) - 验证成功
  - https://www.goldendewclub.com:443/cheditor.css (valid) - 验证成功
辅助路径:
  - https://www.goldendewclub.com:443/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://www.goldendewclub.com:443/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://www.goldendewclub.com:443/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: www.goldendewclub.com:443
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://www.goldendewclub.com:443
发现的核心文件:
  - https://www.goldendewclub.com:443/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://www.goldendewclub.com:443/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://www.goldendewclub.com:443/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://www.goldendewclub.com:443/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: www.goldendewclub.com:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.goldendewclub.com:443
发现的核心文件:
  - https://www.goldendewclub.com:443/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://www.goldendewclub.com:443/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://www.goldendewclub.com:443/core/connector/connector (rce) - 包含RCE相关内容
  - https://www.goldendewclub.com:443/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://www.goldendewclub.com:443/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: www.everaid.com:443
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://www.everaid.com:443
发现的核心文件:
  - https://www.everaid.com:443/dext5upload.js (valid) - 验证成功
  - https://www.everaid.com:443/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://www.everaid.com:443/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://www.everaid.com:443/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: www.everaid.com:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.everaid.com:443
发现的核心文件:
  - https://www.everaid.com:443/HuskyEZCreator.js (valid) - 验证成功
  - https://www.everaid.com:443/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://www.everaid.com:443/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://www.everaid.com:443/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.everaid.com:443/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: www.everaid.com:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://www.everaid.com:443
发现的核心文件:
  - https://www.everaid.com:443/cheditor.js (valid) - 验证成功
  - https://www.everaid.com:443/cheditor.css (valid) - 验证成功
辅助路径:
  - https://www.everaid.com:443/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://www.everaid.com:443/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://www.everaid.com:443/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: www.everaid.com:443
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://www.everaid.com:443
发现的核心文件:
  - https://www.everaid.com:443/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://www.everaid.com:443/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://www.everaid.com:443/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://www.everaid.com:443/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: www.everaid.com:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.everaid.com:443
发现的核心文件:
  - https://www.everaid.com:443/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://www.everaid.com:443/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://www.everaid.com:443/core/connector/connector (rce) - 包含RCE相关内容
  - https://www.everaid.com:443/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://www.everaid.com:443/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: anapro.com:80
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的核心文件:
  - http://anapro.com:80/dext5upload.js (valid) - 验证成功
  - http://anapro.com:80/dext5upload.css (valid) - 验证成功
辅助路径:
  - http://anapro.com:80/handler/dext5handler.jsp (upload) - 基础验证通过
  - http://anapro.com:80/handler/dext5handler.ashx (upload) - 基础验证通过
  - http://anapro.com:80/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - http://anapro.com:80/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: anapro.com:80
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的核心文件:
  - http://anapro.com:80/HuskyEZCreator.js (valid) - 验证成功
  - http://anapro.com:80/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - http://anapro.com:80/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - http://anapro.com:80/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - http://anapro.com:80/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - http://anapro.com:80/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - http://anapro.com:80/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: anapro.com:80
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的核心文件:
  - http://anapro.com:80/cheditor.js (valid) - 验证成功
  - http://anapro.com:80/cheditor.css (valid) - 验证成功
辅助路径:
  - http://anapro.com:80/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - http://anapro.com:80/imageUpload/delete.jsp (delete) - 基础验证通过
  - http://anapro.com:80/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - http://anapro.com:80/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: anapro.com:80
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的核心文件:
  - http://anapro.com:80/ckeditor.js (valid) - 验证成功
辅助路径:
  - http://anapro.com:80/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - http://anapro.com:80/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - http://anapro.com:80/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: anapro.com:80
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的核心文件:
  - http://anapro.com:80/ckfinder.js (valid) - 验证成功
辅助路径:
  - http://anapro.com:80/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - http://anapro.com:80/core/connector/connector (rce) - 包含RCE相关内容
  - http://anapro.com:80/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - http://anapro.com:80/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: www.gcomija.co.kr:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.gcomija.co.kr:443/js/
发现的核心文件:
  - https://www.gcomija.co.kr:443/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的核心文件:
  - https://mydatasafe.kr:443/dext5upload.js (valid) - 验证成功
  - https://mydatasafe.kr:443/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr:443/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://mydatasafe.kr:443/handler/dext5handler.ashx (upload) - 基础验证通过
  - https://mydatasafe.kr:443/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的核心文件:
  - https://mydatasafe.kr:443/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr:443/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr:443/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr:443/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的核心文件:
  - https://mydatasafe.kr:443/cheditor.js (valid) - 验证成功
  - https://mydatasafe.kr:443/cheditor.css (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr:443/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://mydatasafe.kr:443/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr:443/imageUpload/config.jsp (info) - 信息页面内容充实

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的核心文件:
  - https://mydatasafe.kr:443/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr:443/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr:443/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr:443/samples/sample_posteddata.php (info) - 信息页面内容充实

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的核心文件:
  - https://mydatasafe.kr:443/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr:443/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://mydatasafe.kr:443/core/connector/connector (rce) - 包含RCE相关内容
  - https://mydatasafe.kr:443/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://mydatasafe.kr:443/ckfinder.html (info) - 信息页面内容充实

--------------------------------------------------

目标: 210.219.173.17:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://210.219.173.17:443/js/
发现的核心文件:
  - https://210.219.173.17:443/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------

目标: share.sd.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://share.sd.go.kr/js/
发现的核心文件:
  - https://share.sd.go.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

--------------------------------------------------


package jcifs.smb;

import jcifs.Config;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComReadAndX.class */
class SmbComReadAndX extends AndXServerMessageBlock {
    private static final int BATCH_LIMIT = Config.getInt("jcifs.smb.client.ReadAndX.Close", 1);
    private long offset;
    private int fid;
    private int openTimeout;
    int maxCount;
    int minCount;
    int remaining;

    SmbComReadAndX() {
        super(null);
        this.command = (byte) 46;
        this.openTimeout = -1;
    }

    SmbComReadAndX(int fid, long offset, int maxCount, ServerMessageBlock andx) {
        super(andx);
        this.fid = fid;
        this.offset = offset;
        this.minCount = maxCount;
        this.maxCount = maxCount;
        this.command = (byte) 46;
        this.openTimeout = -1;
    }

    void setParam(int fid, long offset, int maxCount) {
        this.fid = fid;
        this.offset = offset;
        this.minCount = maxCount;
        this.maxCount = maxCount;
    }

    @Override // jcifs.smb.AndXServerMessageBlock
    int getBatchLimit(byte command) {
        if (command == 4) {
            return BATCH_LIMIT;
        }
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.fid, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        writeInt4(this.offset, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 4;
        writeInt2(this.maxCount, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 2;
        writeInt2(this.minCount, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 2;
        writeInt4(this.openTimeout, dst, dstIndex5);
        int dstIndex6 = dstIndex5 + 4;
        writeInt2(this.remaining, dst, dstIndex6);
        int dstIndex7 = dstIndex6 + 2;
        writeInt4(this.offset >> 32, dst, dstIndex7);
        return (dstIndex7 + 4) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComReadAndX[" + super.toString() + ",fid=" + this.fid + ",offset=" + this.offset + ",maxCount=" + this.maxCount + ",minCount=" + this.minCount + ",openTimeout=" + this.openTimeout + ",remaining=" + this.remaining + ",offset=" + this.offset + "]");
    }
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: AfterAddItem</h3>
    <p class="ttl">void DEXT5UPLOAD_AfterAddItem(uploadID, strFileName, nFileSize, nAddItemIndex)</p>
    <p class="txt">
        파일 추가 후 발생합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p> 
    <p class="mttl01">parameters</p>
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;파일 추가 완료된 업로드의 id를 의미합니다.<br/>
        <span class="firebrick">strFileName</span>&nbsp;&nbsp;추가될 파일명을 의미합니다.<br />
        <span class="firebrick">nFileSize</span>&nbsp;&nbsp;파일 사이즈를 의미합니다.<br />
        <span class="firebrick">nAddItemIndex</span>&nbsp;&nbsp;1부터 시작되는 파일의 순서정보를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>
    <p class="txt">
        DEXT5 Upload 설정 config.xml에서 use_after_add_event 값이 1일 경우 발생합니다. 
    </p>   
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;script type="text/javascript"&#62;
        DEXT5UPLOAD_AfterAddItem(uploadID, strFileName, nFileSize, nAddItemIndex) {
            // 파일 추가후 처리할 내용

        }
&#60;/script&#62;

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


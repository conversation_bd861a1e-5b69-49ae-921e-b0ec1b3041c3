package jcifs.smb;

import java.util.Enumeration;
import jcifs.Config;
import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComTransaction.class */
abstract class SmbComTransaction extends ServerMessageBlock implements Enumeration {
    private static final int DEFAULT_MAX_DATA_COUNT = Config.getInt("jcifs.smb.client.transaction_buf_size", 65535) - 512;
    private static final int PRIMARY_SETUP_OFFSET = 61;
    private static final int SECONDARY_PARAMETER_OFFSET = 51;
    private static final int DISCONNECT_TID = 1;
    private static final int ONE_WAY_TRANSACTION = 2;
    private static final int PADDING_SIZE = 2;
    private int fid;
    private int bufParameterOffset;
    private int bufDataOffset;
    static final int TRANSACTION_BUF_SIZE = 65535;
    static final byte TRANS2_FIND_FIRST2 = 1;
    static final byte TRANS2_FIND_NEXT2 = 2;
    static final byte TRANS2_QUERY_FS_INFORMATION = 3;
    static final byte TRANS2_QUERY_PATH_INFORMATION = 5;
    static final byte TRANS2_GET_DFS_REFERRAL = 16;
    static final byte TRANS2_SET_FILE_INFORMATION = 8;
    static final int NET_SHARE_ENUM = 0;
    static final int NET_SERVER_ENUM2 = 104;
    static final int NET_SERVER_ENUM3 = 215;
    static final byte TRANS_PEEK_NAMED_PIPE = 35;
    static final byte TRANS_WAIT_NAMED_PIPE = 83;
    static final byte TRANS_CALL_NAMED_PIPE = 84;
    static final byte TRANS_TRANSACT_NAMED_PIPE = 38;
    protected int parameterCount;
    protected int parameterOffset;
    protected int parameterDisplacement;
    protected int dataCount;
    protected int dataOffset;
    protected int dataDisplacement;
    int totalParameterCount;
    int totalDataCount;
    byte maxSetupCount;
    byte subCommand;
    int maxBufferSize;
    byte[] txn_buf;
    private int flags = 0;
    private int pad = 0;
    private int pad1 = 0;
    private boolean hasMore = true;
    private boolean isPrimary = true;
    int maxDataCount = DEFAULT_MAX_DATA_COUNT;
    int timeout = 0;
    int setupCount = 1;
    String name = "";
    int maxParameterCount = 1024;
    protected int primarySetupOffset = PRIMARY_SETUP_OFFSET;
    protected int secondaryParameterOffset = SECONDARY_PARAMETER_OFFSET;

    abstract int writeSetupWireFormat(byte[] bArr, int i);

    abstract int writeParametersWireFormat(byte[] bArr, int i);

    abstract int writeDataWireFormat(byte[] bArr, int i);

    abstract int readSetupWireFormat(byte[] bArr, int i, int i2);

    abstract int readParametersWireFormat(byte[] bArr, int i, int i2);

    abstract int readDataWireFormat(byte[] bArr, int i, int i2);

    SmbComTransaction() {
    }

    @Override // jcifs.smb.ServerMessageBlock
    void reset() {
        super.reset();
        this.hasMore = true;
        this.isPrimary = true;
    }

    void reset(int key, String lastName) {
        reset();
    }

    @Override // java.util.Enumeration
    public boolean hasMoreElements() {
        return this.hasMore;
    }

    @Override // java.util.Enumeration
    public Object nextElement() {
        if (this.isPrimary) {
            this.isPrimary = false;
            this.parameterOffset = this.primarySetupOffset + (this.setupCount * 2) + 2;
            if (this.command != -96) {
                if (this.command == 37 && !isResponse()) {
                    this.parameterOffset += stringWireLength(this.name, this.parameterOffset);
                }
            } else if (this.command == -96) {
                this.parameterOffset += 2;
            }
            this.pad = this.parameterOffset % 2;
            this.pad = this.pad == 0 ? 0 : 2 - this.pad;
            this.parameterOffset += this.pad;
            this.totalParameterCount = writeParametersWireFormat(this.txn_buf, this.bufParameterOffset);
            this.bufDataOffset = this.totalParameterCount;
            int available = this.maxBufferSize - this.parameterOffset;
            this.parameterCount = Math.min(this.totalParameterCount, available);
            int available2 = available - this.parameterCount;
            this.dataOffset = this.parameterOffset + this.parameterCount;
            this.pad1 = this.dataOffset % 2;
            this.pad1 = this.pad1 == 0 ? 0 : 2 - this.pad1;
            this.dataOffset += this.pad1;
            this.totalDataCount = writeDataWireFormat(this.txn_buf, this.bufDataOffset);
            this.dataCount = Math.min(this.totalDataCount, available2);
        } else {
            if (this.command != -96) {
                this.command = (byte) 38;
            } else {
                this.command = (byte) -95;
            }
            this.parameterOffset = SECONDARY_PARAMETER_OFFSET;
            if (this.totalParameterCount - this.parameterDisplacement > 0) {
                this.pad = this.parameterOffset % 2;
                this.pad = this.pad == 0 ? 0 : 2 - this.pad;
                this.parameterOffset += this.pad;
            }
            this.parameterDisplacement += this.parameterCount;
            int available3 = (this.maxBufferSize - this.parameterOffset) - this.pad;
            this.parameterCount = Math.min(this.totalParameterCount - this.parameterDisplacement, available3);
            int available4 = available3 - this.parameterCount;
            this.dataOffset = this.parameterOffset + this.parameterCount;
            this.pad1 = this.dataOffset % 2;
            this.pad1 = this.pad1 == 0 ? 0 : 2 - this.pad1;
            this.dataOffset += this.pad1;
            this.dataDisplacement += this.dataCount;
            this.dataCount = Math.min(this.totalDataCount - this.dataDisplacement, available4 - this.pad1);
        }
        if (this.parameterDisplacement + this.parameterCount >= this.totalParameterCount && this.dataDisplacement + this.dataCount >= this.totalDataCount) {
            this.hasMore = false;
        }
        return this;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2;
        writeInt2(this.totalParameterCount, dst, dstIndex);
        int dstIndex3 = dstIndex + 2;
        writeInt2(this.totalDataCount, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 2;
        if (this.command != 38) {
            writeInt2(this.maxParameterCount, dst, dstIndex4);
            int dstIndex5 = dstIndex4 + 2;
            writeInt2(this.maxDataCount, dst, dstIndex5);
            int dstIndex6 = dstIndex5 + 2;
            int dstIndex7 = dstIndex6 + 1;
            dst[dstIndex6] = this.maxSetupCount;
            int dstIndex8 = dstIndex7 + 1;
            dst[dstIndex7] = 0;
            writeInt2(this.flags, dst, dstIndex8);
            int dstIndex9 = dstIndex8 + 2;
            writeInt4(this.timeout, dst, dstIndex9);
            int dstIndex10 = dstIndex9 + 4;
            int dstIndex11 = dstIndex10 + 1;
            dst[dstIndex10] = 0;
            dstIndex4 = dstIndex11 + 1;
            dst[dstIndex11] = 0;
        }
        writeInt2(this.parameterCount, dst, dstIndex4);
        int dstIndex12 = dstIndex4 + 2;
        writeInt2(this.parameterOffset, dst, dstIndex12);
        int dstIndex13 = dstIndex12 + 2;
        if (this.command == 38) {
            writeInt2(this.parameterDisplacement, dst, dstIndex13);
            dstIndex13 += 2;
        }
        writeInt2(this.dataCount, dst, dstIndex13);
        int dstIndex14 = dstIndex13 + 2;
        writeInt2(this.dataCount == 0 ? 0 : this.dataOffset, dst, dstIndex14);
        int dstIndex15 = dstIndex14 + 2;
        if (this.command == 38) {
            writeInt2(this.dataDisplacement, dst, dstIndex15);
            dstIndex2 = dstIndex15 + 2;
        } else {
            int dstIndex16 = dstIndex15 + 1;
            dst[dstIndex15] = (byte) this.setupCount;
            int dstIndex17 = dstIndex16 + 1;
            dst[dstIndex16] = 0;
            dstIndex2 = dstIndex17 + writeSetupWireFormat(dst, dstIndex17);
        }
        return dstIndex2 - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int p = this.pad;
        if (this.command == 37 && !isResponse()) {
            dstIndex += writeString(this.name, dst, dstIndex);
        }
        if (this.parameterCount > 0) {
            while (true) {
                int i = p;
                p = i - 1;
                if (i <= 0) {
                    break;
                }
                int i2 = dstIndex;
                dstIndex++;
                dst[i2] = 0;
            }
            System.arraycopy(this.txn_buf, this.bufParameterOffset, dst, dstIndex, this.parameterCount);
            dstIndex += this.parameterCount;
        }
        if (this.dataCount > 0) {
            int p2 = this.pad1;
            while (true) {
                int i3 = p2;
                p2 = i3 - 1;
                if (i3 <= 0) {
                    break;
                }
                int i4 = dstIndex;
                dstIndex++;
                dst[i4] = 0;
            }
            System.arraycopy(this.txn_buf, this.bufDataOffset, dst, dstIndex, this.dataCount);
            this.bufDataOffset += this.dataCount;
            dstIndex += this.dataCount;
        }
        return dstIndex - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String(super.toString() + ",totalParameterCount=" + this.totalParameterCount + ",totalDataCount=" + this.totalDataCount + ",maxParameterCount=" + this.maxParameterCount + ",maxDataCount=" + this.maxDataCount + ",maxSetupCount=" + ((int) this.maxSetupCount) + ",flags=0x" + Hexdump.toHexString(this.flags, 2) + ",timeout=" + this.timeout + ",parameterCount=" + this.parameterCount + ",parameterOffset=" + this.parameterOffset + ",parameterDisplacement=" + this.parameterDisplacement + ",dataCount=" + this.dataCount + ",dataOffset=" + this.dataOffset + ",dataDisplacement=" + this.dataDisplacement + ",setupCount=" + this.setupCount + ",pad=" + this.pad + ",pad1=" + this.pad1);
    }
}

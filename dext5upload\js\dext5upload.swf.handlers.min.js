function dext5upload_swf_preLoad(){if(!this.support.loading)return UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4="1",!1}var swfLoadedFlag=!1;function dext5upload_swf_Loaded(){swfLoadedFlag=!0}var filesListSWF=[];function dext5upload_swf_fileDialogStart(){filesListSWF=[]}function dext5upload_swf_fileQueued(c){fileHandler_html4_swf(c)}function dext5upload_swf_fileDialogComplete(c,d){fileHandlerComplete_html4_swf(c,d)}
function dext5upload_swf_fileQueueError(c,d,b){try{var a=SWFUpload.instances["SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID];switch(d){case SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE:if(null!=c.size&&void 0!=c.size&&0==c.size)fileHandler_html4_swf(c);else{a.cancelUpload(c.id);try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200017",Dext5Upload_Lang.error_info.error_code_200017)}catch(e){}}break;case SWFUpload.QUEUE_ERROR.QUEUE_LIMIT_EXCEEDED:a.cancelUpload(c.id);try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
"200014",Dext5Upload_Lang.error_info.error_code_200014)}catch(l){}break;case SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT:a.cancelUpload(c.id);try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200015",Dext5Upload_Lang.error_info.error_code_200015)}catch(f){}break;case SWFUpload.QUEUE_ERROR.INVALID_FILETYPE:a.cancelUpload(c.id);try{UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,"200016",Dext5Upload_Lang.error_info.error_code_200016)}catch(g){}}}catch(h){}}
function dext5upload_swf_uploadStart(c){return!0}function dext5upload_swf_uploadProgress(c,d){var b=getDialogDocument();try{b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count]).innerHTML=Dext5Upload_Lang.upload_status.uploading;var a=RESULTFILELIST[uploaders[upload_complete_count]].fileSize,e=UPLOADTOP.DEXT5UPLOAD.util.parseIntOr0(d/c.size*a);uploadProgress_swf(e,a)}catch(l){}}
function dext5upload_swf_uploadSuccess(c,d,b){b=getDialogDocument();try{upload_complete_count++;var a=d.split("|");1==a.length&&(d=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?Dext5Base64.decode(d):Dext5Base64.makeDecryptReponseMessage(d),a=d.split("|"));if(0==d.indexOf("error")){var e=getUploadedFileListObj();try{void 0==eval("Dext5Upload_Lang.error_info.error_code_"+a[1])?UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,
a[1],a[2],e):UPLOADTOP.UPLOADWIN.DEXT5UPLOAD_OnError(UPLOADTOP.G_CURRUPLOADER.ID,a[1],eval("Dext5Upload_Lang.error_info.error_code_"+a[1]),e)}catch(l){}"0"==UPLOADTOP.G_CURRUPLOADER._config.skipSentFile&&fileListReset();"1"==UPLOADTOP.G_CURRUPLOADER.frameWin.G_UseAddLocalFileObject&&UPLOADTOP.DEXT5UPLOAD.ResetUpload(UPLOADTOP.G_CURRUPLOADER.ID);closeSendDialog();UPLOADTOP.G_CURRUPLOADER.cancel()}else{var f,g;try{var h=a[1],k=h.indexOf("::");-1<k?(f=h.substring(0,k),g=h.substring(k+2)):(f=RESULTFILELIST[uploaders[upload_complete_count-
1]].originName,g=a[1]);RESULTFILELIST[uploaders[upload_complete_count-1]].uploadName=a[2];RESULTFILELIST[uploaders[upload_complete_count-1]].uploadPath=g;RESULTFILELIST[uploaders[upload_complete_count-1]].status="complete";RESULTFILELIST[uploaders[upload_complete_count-1]].logicalPath="";RESULTFILELIST[uploaders[upload_complete_count-1]].originName=f;b.getElementById("DEXT_fiVe_UP_uploadFileStatus_"+uploaders[upload_complete_count-1]).innerHTML=Dext5Upload_Lang.upload_status.uploaded;4==a.length?
-1<a[3].indexOf("\b")?(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue="",RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=a[3].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[uploaders[upload_complete_count-1]].groupId):(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue="",RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId):5==a.length&&(-1<a[4].indexOf("\b")?(RESULTFILELIST[uploaders[upload_complete_count-
1]].responseCustomValue=a[4].split("\b")[0],RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=a[4].split("\b")[1],UPLOADTOP.G_CURRUPLOADER._config.groupId=RESULTFILELIST[uploaders[upload_complete_count-1]].groupId):(RESULTFILELIST[uploaders[upload_complete_count-1]].responseCustomValue=a[4],RESULTFILELIST[uploaders[upload_complete_count-1]].groupId=UPLOADTOP.G_CURRUPLOADER._config.groupId));if(upload_complete_count==TOTALUPLOADNUM)G_PreUploadedSizeForOnprogress=0,totalUploadComplete();else{try{var m=
uploaders.indexOf(upload_complete_count-1);adjustUploadFileListScroll(m,upload_complete_count,TOTALUPLOADNUM)}catch(n){}UploadSWFSingle()}}catch(p){}}SWFUpload.instances["SWFUpload_"+UPLOADTOP.G_CURRUPLOADER.ID].requeueUpload(c.id)}catch(q){}}function dext5upload_swf_uploadComplete(c){}function dext5upload_swf_uploadError(c,d,b){}
function HashTable(c){this.length=0;this.items={};for(var d in c)c.hasOwnProperty(d)&&(this.items[d]=c[d],this.length++);this.setItem=function(b,a){var c=void 0;this.hasItem(b)?c=this.items[b]:this.length++;this.items[b]=a;return c};this.getItem=function(b){return this.hasItem(b)?this.items[b]:void 0};this.hasItem=function(b){return this.items.hasOwnProperty(b)};this.removeItem=function(b){if(this.hasItem(b))return previous=this.items[b],this.length--,delete this.items[b],previous};this.keys=function(){var b=
[],a;for(a in this.items)this.hasItem(a)&&b.push(a);return b};this.values=function(){var b=[],a;for(a in this.items)this.hasItem(a)&&b.push(this.items[a]);return b};this.each=function(b){for(var a in this.items)this.hasItem(a)&&b(a,this.items[a])};this.clear=function(){this.items={};this.length=0}};

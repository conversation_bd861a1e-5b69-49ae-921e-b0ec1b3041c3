﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con ">
    <span class="pl_type">[플러그인 전용]</span>           
    <h3 class="title">DEXT5 Upload :: GetFileObjectList</h3>
    <p class="ttl">void GetFileObjectList(uploadID)</p>
    <p class="txt">
        첨부된 파일의 file object를 리턴합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        file object를 리턴합니다.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;실행할 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
       없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        DEXT5UPLOAD.GetFileObjectList('upload1');
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px;"  
    &#60;script type="text/javascript"&#62;
        new Dext5Upload("upload1");
    &#60;/script&#62;       
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


package com.oreilly.servlet;

import java.io.IOException;
import java.net.ServerSocket;

/* compiled from: DaemonHttpServlet.java */
/* loaded from: cos.jar:com/oreilly/servlet/Daemon.class */
class Daemon extends Thread {
    private ServerSocket serverSocket;
    private DaemonHttpServlet servlet;

    public Daemon(DaemonHttpServlet daemonHttpServlet) {
        this.servlet = daemonHttpServlet;
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public void run() {
        try {
            this.serverSocket = new ServerSocket(this.servlet.getSocketPort());
            while (true) {
                try {
                    try {
                        this.servlet.handleClient(this.serverSocket.accept());
                    } catch (ThreadDeath e) {
                        try {
                            this.serverSocket.close();
                            return;
                        } catch (IOException e2) {
                            this.servlet.log(new StringBuffer().append("Problem closing server socket: ").append(e2.getClass().getName()).append(": ").append(e2.getMessage()).toString());
                            return;
                        }
                    }
                } catch (IOException e3) {
                    this.servlet.log(new StringBuffer().append("Problem accepting client's socket connection: ").append(e3.getClass().getName()).append(": ").append(e3.getMessage()).toString());
                }
            }
        } catch (Exception e4) {
            this.servlet.log(new StringBuffer().append("Problem establishing server socket: ").append(e4.getClass().getName()).append(": ").append(e4.getMessage()).toString());
        }
    }
}

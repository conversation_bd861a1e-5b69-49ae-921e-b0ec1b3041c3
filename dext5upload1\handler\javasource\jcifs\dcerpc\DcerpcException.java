package jcifs.dcerpc;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import jcifs.smb.WinError;
import jcifs.util.Hexdump;
import org.apache.commons.io.IOUtils;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/DcerpcException.class */
public class DcerpcException extends IOException implements DcerpcError, WinError {
    private int error;
    private Throwable rootCause;

    static String getMessageByDcerpcError(int errcode) {
        int min = 0;
        int max = DCERPC_FAULT_CODES.length;
        while (max >= min) {
            int mid = (min + max) / 2;
            if (errcode > DCERPC_FAULT_CODES[mid]) {
                min = mid + 1;
            } else if (errcode < DCERPC_FAULT_CODES[mid]) {
                max = mid - 1;
            } else {
                return DCERPC_FAULT_MESSAGES[mid];
            }
        }
        return "0x" + Hexdump.toHexString(errcode, 8);
    }

    DcerpcException(int error) {
        super(getMessageByDcerpcError(error));
        this.error = error;
    }

    public DcerpcException(String msg) {
        super(msg);
    }

    public DcerpcException(String msg, Throwable rootCause) {
        super(msg);
        this.rootCause = rootCause;
    }

    public int getErrorCode() {
        return this.error;
    }

    public Throwable getRootCause() {
        return this.rootCause;
    }

    @Override // java.lang.Throwable
    public String toString() {
        if (this.rootCause != null) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            this.rootCause.printStackTrace(pw);
            return super.toString() + IOUtils.LINE_SEPARATOR_UNIX + sw;
        }
        return super.toString();
    }
}

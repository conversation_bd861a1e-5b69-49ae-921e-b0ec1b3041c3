package jcifs.smb;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.Vector;
import jcifs.Config;
import jcifs.UniAddress;
import jcifs.netbios.NbtAddress;
import jcifs.util.LogStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbSession.class */
public final class SmbSession {
    private static final String LOGON_SHARE = Config.getProperty("jcifs.smb.client.logonShare", null);
    private static final int LOOKUP_RESP_LIMIT = Config.getInt("jcifs.netbios.lookupRespLimit", 3);
    private static final String DOMAIN = Config.getProperty("jcifs.smb.client.domain", null);
    private static final String USERNAME = Config.getProperty("jcifs.smb.client.username", null);
    private static final int CACHE_POLICY = Config.getInt("jcifs.netbios.cachePolicy", 600) * 60;
    static NbtAddress[] dc_list = null;
    static long dc_list_expiration;
    static int dc_list_counter;
    int uid;
    private UniAddress address;
    private int port;
    private int localPort;
    private InetAddress localAddr;
    NtlmPasswordAuthentication auth;
    long expiration;
    SmbTransport transport = null;
    String netbiosName = null;
    Vector trees = new Vector();
    int connectionState = 0;

    private static NtlmChallenge interrogate(NbtAddress addr) throws SmbException {
        UniAddress dc = new UniAddress(addr);
        SmbTransport trans = SmbTransport.getSmbTransport(dc, 0);
        if (USERNAME == null) {
            trans.connect();
            LogStream logStream = SmbTransport.log;
            if (LogStream.level >= 3) {
                SmbTransport.log.println("Default credentials (jcifs.smb.client.username/password) not specified. SMB signing may not work propertly.  Skipping DC interrogation.");
            }
        } else {
            SmbSession ssn = trans.getSmbSession(NtlmPasswordAuthentication.DEFAULT);
            ssn.getSmbTree(LOGON_SHARE, null).treeConnect(null, null);
        }
        return new NtlmChallenge(trans.server.encryptionKey, dc);
    }

    public static NtlmChallenge getChallengeForDomain() throws SmbException, UnknownHostException {
        int i;
        if (DOMAIN == null) {
            throw new SmbException("A domain was not specified");
        }
        synchronized (DOMAIN) {
            long now = System.currentTimeMillis();
            int retry = 1;
            do {
                if (dc_list_expiration < now) {
                    NbtAddress[] list = NbtAddress.getAllByName(DOMAIN, 28, null, null);
                    dc_list_expiration = now + (CACHE_POLICY * 1000);
                    if (list != null && list.length > 0) {
                        dc_list = list;
                    } else {
                        dc_list_expiration = now + 900000;
                        LogStream logStream = SmbTransport.log;
                        if (LogStream.level >= 2) {
                            SmbTransport.log.println("Failed to retrieve DC list from WINS");
                        }
                    }
                }
                int max = Math.min(dc_list.length, LOOKUP_RESP_LIMIT);
                for (int j = 0; j < max; j++) {
                    int i2 = dc_list_counter;
                    dc_list_counter = i2 + 1;
                    int i3 = i2 % max;
                    if (dc_list[i3] != null) {
                        try {
                            return interrogate(dc_list[i3]);
                        } catch (SmbException se) {
                            LogStream logStream2 = SmbTransport.log;
                            if (LogStream.level >= 2) {
                                SmbTransport.log.println("Failed validate DC: " + dc_list[i3]);
                                LogStream logStream3 = SmbTransport.log;
                                if (LogStream.level > 2) {
                                    se.printStackTrace(SmbTransport.log);
                                }
                            }
                            dc_list[i3] = null;
                        }
                    }
                }
                dc_list_expiration = 0L;
                i = retry;
                retry = i - 1;
            } while (i > 0);
            dc_list_expiration = now + 900000;
            throw new UnknownHostException("Failed to negotiate with a suitable domain controller for " + DOMAIN);
        }
    }

    public static byte[] getChallenge(UniAddress dc) throws SmbException, UnknownHostException {
        return getChallenge(dc, 0);
    }

    public static byte[] getChallenge(UniAddress dc, int port) throws SmbException, UnknownHostException {
        SmbTransport trans = SmbTransport.getSmbTransport(dc, port);
        trans.connect();
        return trans.server.encryptionKey;
    }

    public static void logon(UniAddress dc, NtlmPasswordAuthentication auth) throws SmbException {
        logon(dc, 0, auth);
    }

    public static void logon(UniAddress dc, int port, NtlmPasswordAuthentication auth) throws SmbException {
        SmbTree tree = SmbTransport.getSmbTransport(dc, port).getSmbSession(auth).getSmbTree(LOGON_SHARE, null);
        if (LOGON_SHARE == null) {
            tree.treeConnect(null, null);
            return;
        }
        Trans2FindFirst2 req = new Trans2FindFirst2("\\", "*", 16);
        Trans2FindFirst2Response resp = new Trans2FindFirst2Response();
        tree.send(req, resp);
    }

    SmbSession(UniAddress address, int port, InetAddress localAddr, int localPort, NtlmPasswordAuthentication auth) {
        this.address = address;
        this.port = port;
        this.localAddr = localAddr;
        this.localPort = localPort;
        this.auth = auth;
    }

    synchronized SmbTree getSmbTree(String share, String service) {
        if (share == null) {
            share = "IPC$";
        }
        Enumeration e = this.trees.elements();
        while (e.hasMoreElements()) {
            SmbTree t = (SmbTree) e.nextElement();
            if (t.matches(share, service)) {
                return t;
            }
        }
        SmbTree t2 = new SmbTree(this, share, service);
        this.trees.addElement(t2);
        return t2;
    }

    boolean matches(NtlmPasswordAuthentication auth) {
        return this.auth == auth || this.auth.equals(auth);
    }

    synchronized SmbTransport transport() {
        if (this.transport == null) {
            this.transport = SmbTransport.getSmbTransport(this.address, this.port, this.localAddr, this.localPort, null);
        }
        return this.transport;
    }

    void send(ServerMessageBlock request, ServerMessageBlock response) throws SmbException {
        synchronized (transport()) {
            if (response != null) {
                response.received = false;
            }
            this.expiration = System.currentTimeMillis() + SmbTransport.SO_TIMEOUT;
            sessionSetup(request, response);
            if (response == null || !response.received) {
                if (request instanceof SmbComTreeConnectAndX) {
                    SmbComTreeConnectAndX tcax = (SmbComTreeConnectAndX) request;
                    if (this.netbiosName != null && tcax.path.endsWith("\\IPC$")) {
                        tcax.path = "\\\\" + this.netbiosName + "\\IPC$";
                    }
                }
                request.uid = this.uid;
                request.auth = this.auth;
                try {
                    this.transport.send(request, response);
                } catch (SmbException se) {
                    if (request instanceof SmbComTreeConnectAndX) {
                        logoff(true);
                    }
                    request.digest = null;
                    throw se;
                }
            }
        }
    }

    void sessionSetup(ServerMessageBlock andx, ServerMessageBlock andxResponse) throws SmbException {
        byte[] signingKey;
        synchronized (transport()) {
            NtlmContext nctx = null;
            SmbException ex = null;
            byte[] token = new byte[0];
            int state = 10;
            while (this.connectionState != 0) {
                if (this.connectionState == 2 || this.connectionState == 3) {
                    return;
                }
                try {
                    this.transport.wait();
                } catch (InterruptedException ie) {
                    throw new SmbException(ie.getMessage(), ie);
                }
            }
            this.connectionState = 1;
            try {
                try {
                    this.transport.connect();
                    SmbTransport smbTransport = this.transport;
                    LogStream logStream = SmbTransport.log;
                    if (LogStream.level >= 4) {
                        SmbTransport smbTransport2 = this.transport;
                        SmbTransport.log.println("sessionSetup: accountName=" + this.auth.username + ",primaryDomain=" + this.auth.domain);
                    }
                    this.uid = 0;
                    do {
                        switch (state) {
                            case 10:
                                if (this.auth != NtlmPasswordAuthentication.ANONYMOUS && this.transport.hasCapability(Integer.MIN_VALUE)) {
                                    state = 20;
                                    break;
                                } else {
                                    SmbComSessionSetupAndX request = new SmbComSessionSetupAndX(this, andx, this.auth);
                                    SmbComSessionSetupAndXResponse response = new SmbComSessionSetupAndXResponse(andxResponse);
                                    if (this.transport.isSignatureSetupRequired(this.auth)) {
                                        if (this.auth.hashesExternal && NtlmPasswordAuthentication.DEFAULT_PASSWORD != "") {
                                            this.transport.getSmbSession(NtlmPasswordAuthentication.DEFAULT).getSmbTree(LOGON_SHARE, null).treeConnect(null, null);
                                        } else {
                                            request.digest = new SigningDigest(this.auth.getSigningKey(this.transport.server.encryptionKey), false);
                                        }
                                    }
                                    request.auth = this.auth;
                                    try {
                                        this.transport.send(request, response);
                                    } catch (SmbAuthException sae) {
                                        throw sae;
                                    } catch (SmbException se) {
                                        ex = se;
                                    }
                                    if (response.isLoggedInAsGuest && !"GUEST".equalsIgnoreCase(this.auth.username) && this.transport.server.security != 0 && this.auth != NtlmPasswordAuthentication.ANONYMOUS) {
                                        throw new SmbAuthException(NtStatus.NT_STATUS_LOGON_FAILURE);
                                    }
                                    if (ex != null) {
                                        throw ex;
                                    }
                                    this.uid = response.uid;
                                    if (request.digest != null) {
                                        this.transport.digest = request.digest;
                                    }
                                    this.connectionState = 2;
                                    state = 0;
                                    break;
                                }
                                break;
                            case 20:
                                if (nctx == null) {
                                    boolean doSigning = (this.transport.flags2 & 4) != 0;
                                    nctx = new NtlmContext(this.auth, doSigning);
                                }
                                LogStream logStream2 = SmbTransport.log;
                                if (LogStream.level >= 4) {
                                    SmbTransport.log.println(nctx);
                                }
                                if (nctx.isEstablished()) {
                                    this.netbiosName = nctx.getNetbiosName();
                                    this.connectionState = 2;
                                    state = 0;
                                    break;
                                } else {
                                    try {
                                        token = nctx.initSecContext(token, 0, token.length);
                                        if (token != null) {
                                            SmbComSessionSetupAndX request2 = new SmbComSessionSetupAndX(this, null, token);
                                            SmbComSessionSetupAndXResponse response2 = new SmbComSessionSetupAndXResponse(null);
                                            if (this.transport.isSignatureSetupRequired(this.auth) && (signingKey = nctx.getSigningKey()) != null) {
                                                request2.digest = new SigningDigest(signingKey, true);
                                            }
                                            request2.uid = this.uid;
                                            this.uid = 0;
                                            try {
                                                this.transport.send(request2, response2);
                                            } catch (SmbAuthException sae2) {
                                                throw sae2;
                                            } catch (SmbException se2) {
                                                ex = se2;
                                                try {
                                                    this.transport.disconnect(true);
                                                } catch (Exception e) {
                                                }
                                            }
                                            if (response2.isLoggedInAsGuest && !"GUEST".equalsIgnoreCase(this.auth.username)) {
                                                throw new SmbAuthException(NtStatus.NT_STATUS_LOGON_FAILURE);
                                            }
                                            if (ex != null) {
                                                throw ex;
                                            }
                                            this.uid = response2.uid;
                                            if (request2.digest != null) {
                                                this.transport.digest = request2.digest;
                                            }
                                            token = response2.blob;
                                            break;
                                        }
                                    } catch (SmbException se3) {
                                        try {
                                            this.transport.disconnect(true);
                                        } catch (IOException e2) {
                                        }
                                        this.uid = 0;
                                        throw se3;
                                    }
                                }
                                break;
                            default:
                                throw new SmbException("Unexpected session setup state: " + state);
                        }
                    } while (state != 0);
                } catch (SmbException se4) {
                    logoff(true);
                    this.connectionState = 0;
                    throw se4;
                }
            } finally {
                this.transport.notifyAll();
            }
        }
    }

    void logoff(boolean inError) {
        synchronized (transport()) {
            if (this.connectionState != 2) {
                return;
            }
            this.connectionState = 3;
            this.netbiosName = null;
            Enumeration e = this.trees.elements();
            while (e.hasMoreElements()) {
                SmbTree t = (SmbTree) e.nextElement();
                t.treeDisconnect(inError);
            }
            if (!inError && this.transport.server.security != 0) {
                SmbComLogoffAndX request = new SmbComLogoffAndX(null);
                request.uid = this.uid;
                try {
                    this.transport.send(request, null);
                } catch (SmbException e2) {
                }
                this.uid = 0;
            }
            this.connectionState = 0;
            this.transport.notifyAll();
        }
    }

    public String toString() {
        return "SmbSession[accountName=" + this.auth.username + ",primaryDomain=" + this.auth.domain + ",uid=" + this.uid + ",connectionState=" + this.connectionState + "]";
    }
}

package jcifs.smb;

import jcifs.util.LogStream;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/TransTransactNamedPipe.class */
class TransTransactNamedPipe extends SmbComTransaction {
    private byte[] pipeData;
    private int pipeFid;
    private int pipeDataOff;
    private int pipeDataLen;

    TransTransactNamedPipe(int fid, byte[] data, int off, int len) {
        this.pipeFid = fid;
        this.pipeData = data;
        this.pipeDataOff = off;
        this.pipeDataLen = len;
        this.command = (byte) 37;
        this.subCommand = (byte) 38;
        this.maxParameterCount = 0;
        this.maxDataCount = InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH;
        this.maxSetupCount = (byte) 0;
        this.setupCount = 2;
        this.name = "\\PIPE\\";
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        int dstIndex3 = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        writeInt2(this.pipeFid, dst, dstIndex3);
        int i = dstIndex3 + 2;
        return 4;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        if (dst.length - dstIndex < this.pipeDataLen) {
            LogStream logStream = log;
            if (LogStream.level >= 3) {
                log.println("TransTransactNamedPipe data too long for buffer");
                return 0;
            }
            return 0;
        }
        System.arraycopy(this.pipeData, this.pipeDataOff, dst, dstIndex, this.pipeDataLen);
        return this.pipeDataLen;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("TransTransactNamedPipe[" + super.toString() + ",pipeFid=" + this.pipeFid + "]");
    }
}

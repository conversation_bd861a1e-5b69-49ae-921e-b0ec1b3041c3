﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Plugin
{
	// Token: 0x0200003A RID: 58
	public class ResumeEnd : Base
	{
		// Token: 0x0600032E RID: 814 RVA: 0x000251F0 File Offset: 0x000233F0
		public ResumeEnd(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x0600032F RID: 815 RVA: 0x0002527C File Offset: 0x0002347C
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string resumeguid = this._entity_dextParam.RESUMEGUID;
			string text = base.FileLocationInfoReadWrite("R", resumeguid, "");
			string text2 = string.Empty;
			if (string.IsNullOrEmpty(text) || !File.Exists(text))
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				return null;
			}
			text2 = Path.GetFileName(text);
			string text3 = this._entity_dextParam.fileName;
			text3 = text3.Normalize(NormalizationForm.FormC);
			string tempFileFolder = base.GetTempFileFolder(this.tempPath, resumeguid);
			string.Concat(new object[]
			{
				tempFileFolder,
				this.m_PathChar,
				resumeguid,
				this.m_strHSTempSuffix
			});
			Directory.Delete(tempFileFolder, true);
			string text4 = string.Empty;
			string empty = string.Empty;
			if (!string.IsNullOrEmpty(this.physicalPath))
			{
				text4 = text;
			}
			else if (!string.IsNullOrEmpty(this.virtualPath))
			{
				string oldValue = HostingEnvironment.MapPath("~/");
				text4 = "/" + text.Replace(oldValue, "");
				text4 = text4.Replace(this.m_PathChar, '/');
			}
			try
			{
				pCompleteBeforeEvent(this.hContext, ref text, ref text4, ref text2, ref this._str_ResponseCustomValue);
			}
			catch
			{
			}
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.NewFileLocation = text;
				uploadEventEntity.ResponseFileServerPath = text4;
				uploadEventEntity.ResponseFileName = text2;
				uploadEventEntity.ResponseGroupId = this._entity_dextParam.fileGroupID;
				uploadEventEntity.FileIndex = this._entity_dextParam.fileIndex;
				pCompleteBeforeEventEx(uploadEventEntity);
				text = uploadEventEntity.NewFileLocation;
				text4 = uploadEventEntity.ResponseFileServerPath;
				text2 = uploadEventEntity.ResponseFileName;
				this._str_ResponseCustomValue = uploadEventEntity.ResponseCustomValue;
				this._str_ResponseGroupId = uploadEventEntity.ResponseGroupId;
			}
			catch
			{
			}
			string text5 = "";
			if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf('*') == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
			{
				text5 = text5 + "|" + this._str_ResponseCustomValue;
			}
			if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf('*') == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
			{
				text5 = text5 + "\b" + this._str_ResponseGroupId;
			}
			this.logMessage = text4 + "*" + text2 + text5;
			string str = Dext5Parameter.MakeParameter(text4 + "*" + text2 + text5);
			this.hContext.Response.Clear();
			this.hContext.Response.Write("[OK]" + str);
			if (pCustomError != null)
			{
				base.ClientResponse("[FAIL]", "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage, "");
			}
			if (pCustomError == null)
			{
				try
				{
					pCompleteEvent(this.hContext, text, text4, text2);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text,
						ResponseFileServerPath = text4,
						ResponseFileName = text2
					});
				}
				catch
				{
				}
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug(this.logMessage, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x040001BB RID: 443
		private string logMessage = string.Empty;

		// Token: 0x040001BC RID: 444
		private string physicalPath = string.Empty;

		// Token: 0x040001BD RID: 445
		private string virtualPath = string.Empty;

		// Token: 0x040001BE RID: 446
		private string fileWhiteList = string.Empty;

		// Token: 0x040001BF RID: 447
		private string fileBlackList = string.Empty;

		// Token: 0x040001C0 RID: 448
		private string[] fileBlackWordList;

		// Token: 0x040001C1 RID: 449
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

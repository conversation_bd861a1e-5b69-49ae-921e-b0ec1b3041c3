package jcifs.smb;

import jcifs.Config;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComWriteAndX.class */
class SmbComWriteAndX extends AndXServerMessageBlock {
    private static final int READ_ANDX_BATCH_LIMIT = Config.getInt("jcifs.smb.client.WriteAndX.ReadAndX", 1);
    private static final int CLOSE_BATCH_LIMIT = Config.getInt("jcifs.smb.client.WriteAndX.Close", 1);
    private int fid;
    private int remaining;
    private int dataLength;
    private int dataOffset;
    private int off;
    private byte[] b;
    private long offset;
    private int pad;
    int writeMode;

    SmbComWriteAndX() {
        super(null);
        this.command = (byte) 47;
    }

    SmbComWriteAndX(int fid, long offset, int remaining, byte[] b, int off, int len, ServerMessageBlock andx) {
        super(andx);
        this.fid = fid;
        this.offset = offset;
        this.remaining = remaining;
        this.b = b;
        this.off = off;
        this.dataLength = len;
        this.command = (byte) 47;
    }

    void setParam(int fid, long offset, int remaining, byte[] b, int off, int len) {
        this.fid = fid;
        this.offset = offset;
        this.remaining = remaining;
        this.b = b;
        this.off = off;
        this.dataLength = len;
        this.digest = null;
    }

    @Override // jcifs.smb.AndXServerMessageBlock
    int getBatchLimit(byte command) {
        if (command == 46) {
            return READ_ANDX_BATCH_LIMIT;
        }
        if (command == 4) {
            return CLOSE_BATCH_LIMIT;
        }
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        this.dataOffset = (dstIndex - this.headerStart) + 26;
        this.pad = (this.dataOffset - this.headerStart) % 4;
        this.pad = this.pad == 0 ? 0 : 4 - this.pad;
        this.dataOffset += this.pad;
        writeInt2(this.fid, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        writeInt4(this.offset, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 4;
        for (int i = 0; i < 4; i++) {
            int i2 = dstIndex3;
            dstIndex3++;
            dst[i2] = -1;
        }
        writeInt2(this.writeMode, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 2;
        writeInt2(this.remaining, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 2;
        int dstIndex6 = dstIndex5 + 1;
        dst[dstIndex5] = 0;
        int dstIndex7 = dstIndex6 + 1;
        dst[dstIndex6] = 0;
        writeInt2(this.dataLength, dst, dstIndex7);
        int dstIndex8 = dstIndex7 + 2;
        writeInt2(this.dataOffset, dst, dstIndex8);
        int dstIndex9 = dstIndex8 + 2;
        writeInt4(this.offset >> 32, dst, dstIndex9);
        return (dstIndex9 + 4) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        while (true) {
            int i = this.pad;
            this.pad = i - 1;
            if (i > 0) {
                int i2 = dstIndex;
                dstIndex++;
                dst[i2] = -18;
            } else {
                System.arraycopy(this.b, this.off, dst, dstIndex, this.dataLength);
                return (dstIndex + this.dataLength) - dstIndex;
            }
        }
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComWriteAndX[" + super.toString() + ",fid=" + this.fid + ",offset=" + this.offset + ",writeMode=" + this.writeMode + ",remaining=" + this.remaining + ",dataLength=" + this.dataLength + ",dataOffset=" + this.dataOffset + "]");
    }
}

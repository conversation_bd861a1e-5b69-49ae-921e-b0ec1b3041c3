importScripts("dext5upload.image.exif.js");function processFile(a){var b=ExifRestorer.restore2(a.imageOriginalData,a.imageNewData);0>b.indexOf("data:image/jpeg;base64,")&&(b="data:image/jpeg;base64,"+b);b=dataURItoBlob(b);a={workerIdx:a.workerIdx,fileIdx:a.fildIdx,newBlob:b};self.postMessage({type:"complete",sendData:a})}self.onmessage=function(a){a=a.data;switch(a.type){case "start":processFile(a.sendData)}};
function dataURItoBlob(a){var b=atob(a.split(",")[1]);a=a.split(",")[0].split(":")[1].split(";")[0];for(var d=new ArrayBuffer(b.length),e=new Uint8Array(d),c=0;c<b.length;c++)e[c]=b.charCodeAt(c);b=new DataView(d);return new Blob([b.buffer],{type:a})};

﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x0200002D RID: 45
	public class Upload : Base
	{
		// Token: 0x060002B8 RID: 696 RVA: 0x0001F880 File Offset: 0x0001DA80
		public Upload(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x060002B9 RID: 697 RVA: 0x0001F904 File Offset: 0x0001DB04
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string empty = string.Empty;
			string text = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string text4 = string.Empty;
			string text5 = string.Empty;
			if (!base.CheckCaller("html4"))
			{
				text2 = "error|013|Bad Request Type";
				text = text2;
			}
			else if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
			{
				text2 = "error|012|Not allowed file extension";
				text = text2;
			}
			else if (!base.CheckBlackWord(this.fileBlackWordList, null))
			{
				text2 = "error|018|There does not allow the string included in file name";
				text = text2;
			}
			else
			{
				this.tempPath = base.GetTempPath(this.tempPath);
				text2 = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
				string allowedZeroFileSize = this._entity_dextParam.allowedZeroFileSize;
				if (string.IsNullOrEmpty(text2))
				{
					text = "error|008|Error occured on the server side";
				}
				else if (text2.IndexOf("error") == 0)
				{
					text = text2;
				}
				else
				{
					string[] array = text2.Split(new char[]
					{
						'|'
					});
					text3 = array[0];
					text4 = array[1];
					if (this.hContext.Request.Files[0].ContentLength == 0 && allowedZeroFileSize == "0")
					{
						text = "error|017|0 byte files can not be transferred.";
					}
					else
					{
						bool flag = false;
						string text6 = text3;
						try
						{
							pBeforeInitializeEvent(this.hContext, ref text3, ref text4);
							if (!text6.Equals(text3))
							{
								flag = true;
							}
						}
						catch
						{
						}
						try
						{
							UploadEventEntity uploadEventEntity = new UploadEventEntity();
							uploadEventEntity.Context = this.hContext;
							uploadEventEntity.NewFileLocation = text3;
							uploadEventEntity.ResponseFileName = text4;
							pBeforeInitializeEventEx(uploadEventEntity);
							text3 = uploadEventEntity.NewFileLocation;
							text4 = uploadEventEntity.ResponseFileName;
							if (!text6.Equals(text3))
							{
								flag = true;
							}
						}
						catch
						{
						}
						if (flag)
						{
							string[] array2 = base.InitializeEventFileExec(text3, text4);
							text3 = array2[0];
							text4 = array2[1];
						}
						if (pCustomError == null)
						{
							this.hContext.Request.Files[0].SaveAs(text3);
							string limitOneFileSize = this._entity_dextParam.limitOneFileSize;
							FileInfo fileInfo = new FileInfo(text3);
							if (!string.IsNullOrEmpty(limitOneFileSize) && fileInfo.Length > Convert.ToInt64(limitOneFileSize))
							{
								File.Delete(text3);
								text = "error|026|Files larger than " + limitOneFileSize + " can not be uploaded.";
							}
							else
							{
								string fileExtensionDetector = this._entity_dextParam.fileExtensionDetector;
								if (!string.IsNullOrEmpty(fileExtensionDetector) && base.CheckFileExtensionDetector(text3) == 0)
								{
									File.Delete(text3);
									text = "error|019|This file is an extension of falsification has been detected.";
								}
								else
								{
									if (!string.IsNullOrEmpty(this.physicalPath))
									{
										text5 = text3;
									}
									else if (!string.IsNullOrEmpty(this.virtualPath))
									{
										string oldValue = HostingEnvironment.MapPath("~/");
										text5 = "/" + text3.Replace(oldValue, "");
										text5 = text5.Replace(this.m_PathChar, '/');
									}
									try
									{
										pCompleteBeforeEvent(this.hContext, ref text3, ref text5, ref text4, ref this._str_ResponseCustomValue);
									}
									catch
									{
									}
									try
									{
										UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
										uploadEventEntity2.Context = this.hContext;
										uploadEventEntity2.NewFileLocation = text3;
										uploadEventEntity2.ResponseFileServerPath = text5;
										uploadEventEntity2.ResponseFileName = text4;
										uploadEventEntity2.ResponseGroupId = this._entity_dextParam.fileGroupID;
										uploadEventEntity2.FileIndex = this._entity_dextParam.fileIndex;
										pCompleteBeforeEventEx(uploadEventEntity2);
										text3 = uploadEventEntity2.NewFileLocation;
										text5 = uploadEventEntity2.ResponseFileServerPath;
										text4 = uploadEventEntity2.ResponseFileName;
										this._str_ResponseCustomValue = uploadEventEntity2.ResponseCustomValue;
										this._str_ResponseGroupId = uploadEventEntity2.ResponseGroupId;
									}
									catch
									{
									}
									string text7 = "";
									if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
									{
										text7 = text7 + "|" + this._str_ResponseCustomValue;
									}
									if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
									{
										text7 = text7 + "\b" + this._str_ResponseGroupId;
									}
									string text8 = fileInfo.Length.ToString();
									string text9 = this._entity_dextParam.fileName;
									text9 = text9.Normalize(NormalizationForm.FormC);
									text = string.Concat(new string[]
									{
										"success|",
										text9,
										"::",
										text5,
										"|",
										text4,
										"|",
										text8,
										text7
									});
								}
							}
						}
					}
				}
			}
			if (pCustomError != null)
			{
				text = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			}
			string text10 = string.Empty;
			text10 += "<html><head>";
			text10 += "<script type=\"text/javascript\">";
			text10 += "if (window.postMessage) {";
			text10 += "if (window.addEventListener) {";
			text10 += "window.addEventListener('message', function (e) {";
			text10 += "var sendUrl = e.origin;";
			text10 += "var data = document.body.innerHTML;";
			text10 += "e.source.postMessage(data, sendUrl);";
			text10 += "}, false);";
			text10 += "}";
			text10 += "else if (window.attachEvent) {";
			text10 += "window.attachEvent('onmessage', function (e) {";
			text10 += "var sendUrl = e.origin;";
			text10 += "var data = document.body.innerHTML;";
			text10 += "e.source.postMessage(data, sendUrl);";
			text10 += "});";
			text10 += "}";
			text10 += "}";
			text10 += "</script>";
			text10 += "</head>";
			text10 += "<body>";
			text10 += Dext5Parameter.MakeParameter(text);
			text10 += "</body>";
			text10 += "</html>";
			this.hContext.Response.Write(text10);
			if (!string.IsNullOrEmpty(text2) && text2.IndexOf("error") != 0 && pCustomError == null)
			{
				try
				{
					pCompleteEvent(this.hContext, text3, text5, text4);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text3,
						ResponseFileServerPath = text5,
						ResponseFileName = text4
					});
				}
				catch
				{
				}
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html4 Upload, " + text, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x04000183 RID: 387
		private string physicalPath = string.Empty;

		// Token: 0x04000184 RID: 388
		private string virtualPath = string.Empty;

		// Token: 0x04000185 RID: 389
		private string fileWhiteList = string.Empty;

		// Token: 0x04000186 RID: 390
		private string fileBlackList = string.Empty;

		// Token: 0x04000187 RID: 391
		private string[] fileBlackWordList;

		// Token: 0x04000188 RID: 392
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

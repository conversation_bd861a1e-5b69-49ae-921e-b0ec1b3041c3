﻿using System;

namespace Ionic.Zlib
{
	// Token: 0x02000067 RID: 103
	internal static class InternalConstants
	{
		// Token: 0x04000388 RID: 904
		internal static readonly int MAX_BITS = 15;

		// Token: 0x04000389 RID: 905
		internal static readonly int BL_CODES = 19;

		// Token: 0x0400038A RID: 906
		internal static readonly int D_CODES = 30;

		// Token: 0x0400038B RID: 907
		internal static readonly int LITERALS = 256;

		// Token: 0x0400038C RID: 908
		internal static readonly int LENGTH_CODES = 29;

		// Token: 0x0400038D RID: 909
		internal static readonly int L_CODES = InternalConstants.LITERALS + 1 + InternalConstants.LENGTH_CODES;

		// Token: 0x0400038E RID: 910
		internal static readonly int MAX_BL_BITS = 7;

		// Token: 0x0400038F RID: 911
		internal static readonly int REP_3_6 = 16;

		// Token: 0x04000390 RID: 912
		internal static readonly int REPZ_3_10 = 17;

		// Token: 0x04000391 RID: 913
		internal static readonly int REPZ_11_138 = 18;
	}
}

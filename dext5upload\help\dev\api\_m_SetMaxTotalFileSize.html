﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con ">           
    <h3 class="title">DEXT5 Upload :: SetMaxTotalFileSize</h3>
    <p class="ttl">void SetMaxTotalFileSize(size, uploadID)</p>
    <p class="txt">
        업로드 할 전체 파일의 최대 크기를 제한 설정합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">size</span>&nbsp;&nbsp;전체 파일의 최대 사이즈를 의미합니다.<br />
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;설정할 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
       없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function setMaxOneFileSize(){
            DEXT5UPLOAD.SetMaxTotalFileSize('500MB', 'upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div border="0" style="width:800px;height:200px;"  
    &#60;script type="text/javascript"&#62;
        new Dext5Upload("upload1");
    &#60;/script&#62;       
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


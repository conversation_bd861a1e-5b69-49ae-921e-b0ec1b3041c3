package jcifs.dcerpc.msrpc;

import jcifs.dcerpc.msrpc.lsarpc;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/MsrpcLsarOpenPolicy2.class */
public class MsrpcLsarOpenPolicy2 extends lsarpc.LsarOpenPolicy2 {
    public MsrpcLsarOpenPolicy2(String server, int access, LsaPolicyHandle policyHandle) {
        super(server, new lsarpc.LsarObjectAttributes(), access, policyHandle);
        this.object_attributes.length = 24;
        lsarpc.LsarQosInfo qos = new lsarpc.LsarQosInfo();
        qos.length = 12;
        qos.impersonation_level = (short) 2;
        qos.context_mode = (byte) 1;
        qos.effective_only = (byte) 0;
        this.object_attributes.security_quality_of_service = qos;
        this.ptype = 0;
        this.flags = 3;
    }
}

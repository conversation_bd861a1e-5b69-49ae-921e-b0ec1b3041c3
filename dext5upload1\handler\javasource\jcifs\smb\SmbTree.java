package jcifs.smb;

import jcifs.util.LogStream;
import net.lingala.zip4j.util.InternalZipConstants;
import org.apache.commons.fileupload.MultipartStream;
import org.apache.commons.io.IOUtils;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbTree.class */
class SmbTree {
    private static int tree_conn_counter;
    int connectionState;
    int tid;
    String share;
    String service;
    String service0;
    SmbSession session;
    boolean inDfs;
    boolean inDomainDfs;
    int tree_num;

    SmbTree(SmbSession session, String share, String service) {
        this.service = "?????";
        this.session = session;
        this.share = share.toUpperCase();
        if (service != null && !service.startsWith("??")) {
            this.service = service;
        }
        this.service0 = this.service;
        this.connectionState = 0;
    }

    boolean matches(String share, String service) {
        return this.share.equalsIgnoreCase(share) && (service == null || service.startsWith("??") || this.service.equalsIgnoreCase(service));
    }

    public boolean equals(Object obj) {
        if (obj instanceof SmbTree) {
            SmbTree tree = (SmbTree) obj;
            return matches(tree.share, tree.service);
        }
        return false;
    }

    void send(ServerMessageBlock request, ServerMessageBlock response) throws SmbException {
        synchronized (this.session.transport()) {
            if (response != null) {
                response.received = false;
            }
            treeConnect(request, response);
            if (request == null || (response != null && response.received)) {
                return;
            }
            if (!this.service.equals("A:")) {
                switch (request.command) {
                    case -94:
                    case 4:
                    case MultipartStream.DASH /* 45 */:
                    case 46:
                    case IOUtils.DIR_SEPARATOR_UNIX /* 47 */:
                    case 113:
                        break;
                    case 37:
                    case InternalZipConstants.FOLDER_MODE_HIDDEN_ARCHIVE /* 50 */:
                        switch (((SmbComTransaction) request).subCommand & 255) {
                            case 0:
                            case 16:
                            case InternalZipConstants.FILE_MODE_READ_ONLY_HIDDEN_ARCHIVE /* 35 */:
                            case 38:
                            case 83:
                            case 84:
                            case 104:
                            case 215:
                                break;
                            default:
                                throw new SmbException("Invalid operation for " + this.service + " service");
                        }
                    default:
                        throw new SmbException("Invalid operation for " + this.service + " service" + request);
                }
            }
            request.tid = this.tid;
            if (this.inDfs && !this.service.equals("IPC") && request.path != null && request.path.length() > 0) {
                request.flags2 = 4096;
                request.path = '\\' + this.session.transport().tconHostName + '\\' + this.share + request.path;
            }
            try {
                this.session.send(request, response);
            } catch (SmbException se) {
                if (se.getNtStatus() == -1073741623) {
                    treeDisconnect(true);
                }
                throw se;
            }
        }
    }

    void treeConnect(ServerMessageBlock andx, ServerMessageBlock andxResponse) throws SmbException {
        synchronized (this.session.transport()) {
            while (this.connectionState != 0) {
                if (this.connectionState == 2 || this.connectionState == 3) {
                    return;
                }
                try {
                    this.session.transport.wait();
                } catch (InterruptedException ie) {
                    throw new SmbException(ie.getMessage(), ie);
                }
            }
            this.connectionState = 1;
            try {
                this.session.transport.connect();
                String unc = "\\\\" + this.session.transport.tconHostName + '\\' + this.share;
                this.service = this.service0;
                SmbTransport smbTransport = this.session.transport;
                LogStream logStream = SmbTransport.log;
                if (LogStream.level >= 4) {
                    SmbTransport smbTransport2 = this.session.transport;
                    SmbTransport.log.println("treeConnect: unc=" + unc + ",service=" + this.service);
                }
                SmbComTreeConnectAndXResponse response = new SmbComTreeConnectAndXResponse(andxResponse);
                SmbComTreeConnectAndX request = new SmbComTreeConnectAndX(this.session, unc, this.service, andx);
                this.session.send(request, response);
                this.tid = response.tid;
                this.service = response.service;
                this.inDfs = response.shareIsInDfs;
                int i = tree_conn_counter;
                tree_conn_counter = i + 1;
                this.tree_num = i;
                this.connectionState = 2;
            } catch (SmbException se) {
                treeDisconnect(true);
                this.connectionState = 0;
                throw se;
            }
        }
    }

    void treeDisconnect(boolean inError) {
        synchronized (this.session.transport()) {
            if (this.connectionState != 2) {
                return;
            }
            this.connectionState = 3;
            if (!inError && this.tid != 0) {
                try {
                    send(new SmbComTreeDisconnect(), null);
                } catch (SmbException se) {
                    SmbTransport smbTransport = this.session.transport;
                    LogStream logStream = SmbTransport.log;
                    if (LogStream.level > 1) {
                        SmbTransport smbTransport2 = this.session.transport;
                        se.printStackTrace(SmbTransport.log);
                    }
                }
            }
            this.inDfs = false;
            this.inDomainDfs = false;
            this.connectionState = 0;
            this.session.transport.notifyAll();
        }
    }

    public String toString() {
        return "SmbTree[share=" + this.share + ",service=" + this.service + ",tid=" + this.tid + ",inDfs=" + this.inDfs + ",inDomainDfs=" + this.inDomainDfs + ",connectionState=" + this.connectionState + "]";
    }
}

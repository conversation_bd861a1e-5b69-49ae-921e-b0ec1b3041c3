﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: ExtensionAllowOrLimit, ExtensionArr</h3>
    <p class="ttl">config.ExtensionAllowOrLimit, config.ExtensionArr</p>
    <p class="txt">
        업로드 할 파일 확장자를 허용 또는 제한 설정(ExtensionAllowOrLimit) 후
        제한 또는 허용할 파일 확장자를 설정(ExtensionArr)합니다. 
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        <span class="firebrick">ExtensionAllowOrLimit</span>&nbsp;&nbsp;설정값이 "0" 이면 설정한 파일 확장자명만 "제한"으로 설정되며, "1"로 설정시 "허용"으로 설정됩니다.<br/>
        <span class="firebrick">ExtensionArr</span>&nbsp;&nbsp;제한 및 허용할 확장자명을 ",(콤마)"로 구분하여 설정합니다.      
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 할 파일 확장자를 제한으로 설정합니다.
        DEXT5UPLOAD.config.ExtensionAllowOrLimit = '0';

        // 제한할 파일 확장자를 txt,gif로 설정합니다.
        DEXT5UPLOAD.config.ExtensionArr = 'txt,gif';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


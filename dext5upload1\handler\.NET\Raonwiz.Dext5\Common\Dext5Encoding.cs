﻿using System;
using System.Text;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x0200000D RID: 13
	internal class Dext5Encoding
	{
		// Token: 0x060000E4 RID: 228 RVA: 0x000104E4 File Offset: 0x0000E6E4
		public static string Base64Encoding(string data)
		{
			string result;
			try
			{
				byte[] inArray = new byte[data.Length];
				inArray = Encoding.UTF8.GetBytes(data);
				string text = Convert.ToBase64String(inArray);
				result = text;
			}
			catch (Exception ex)
			{
				throw new Exception("Error in base64Encode : " + ex.Message);
			}
			return result;
		}

		// Token: 0x060000E5 RID: 229 RVA: 0x0001053C File Offset: 0x0000E73C
		public static string Base64Decoding(string data)
		{
			string result;
			try
			{
				UTF8Encoding utf8Encoding = new UTF8Encoding();
				Decoder decoder = utf8Encoding.GetDecoder();
				byte[] array = Convert.FromBase64String(data);
				int charCount = decoder.GetCharCount(array, 0, array.Length);
				char[] array2 = new char[charCount];
				decoder.GetChars(array, 0, array.Length, array2, 0);
				string text = new string(array2);
				result = text;
			}
			catch (Exception ex)
			{
				throw new Exception("Error in base64Decode : " + ex.Message);
			}
			return result;
		}
	}
}

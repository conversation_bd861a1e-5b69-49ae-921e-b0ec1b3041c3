package jcifs.dcerpc.msrpc;

import javax.servlet.http.HttpServletResponse;
import jcifs.dcerpc.msrpc.netdfs;
import jcifs.dcerpc.ndr.NdrLong;
import jcifs.smb.FileEntry;
import jcifs.smb.SmbShareInfo;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/MsrpcDfsRootEnum.class */
public class MsrpcDfsRootEnum extends netdfs.NetrDfsEnumEx {
    public MsrpcDfsRootEnum(String server) {
        super(server, HttpServletResponse.SC_OK, InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH, new netdfs.DfsEnumStruct(), new NdrLong(0));
        this.info.level = this.level;
        this.info.e = new netdfs.DfsEnumArray200();
        this.ptype = 0;
        this.flags = 3;
    }

    public FileEntry[] getEntries() {
        netdfs.DfsEnumArray200 a200 = (netdfs.DfsEnumArray200) this.info.e;
        SmbShareInfo[] entries = new SmbShareInfo[a200.count];
        for (int i = 0; i < a200.count; i++) {
            entries[i] = new SmbShareInfo(a200.s[i].dfs_name, 0, null);
        }
        return entries;
    }
}

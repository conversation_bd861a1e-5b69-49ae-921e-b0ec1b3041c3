package jcifs.dcerpc.msrpc;

import java.io.IOException;
import jcifs.dcerpc.DcerpcHandle;
import jcifs.dcerpc.rpc;
import jcifs.smb.SmbException;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/SamrAliasHandle.class */
public class SamrAliasHandle extends rpc.policy_handle {
    public SamrAliasHandle(DcerpcHandle handle, SamrDomainHandle domainHandle, int access, int rid) throws IOException {
        MsrpcSamrOpenAlias rpc = new MsrpcSamrOpenAlias(domainHandle, access, rid, this);
        handle.sendrecv(rpc);
        if (rpc.retval != 0) {
            throw new SmbException(rpc.retval, false);
        }
    }

    public void close() throws IOException {
    }
}

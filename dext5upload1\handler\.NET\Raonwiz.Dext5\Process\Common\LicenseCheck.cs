﻿using System;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.License.Library.General.Security;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000020 RID: 32
	public class LicenseCheck : Base
	{
		// Token: 0x0600023C RID: 572 RVA: 0x00018FB4 File Offset: 0x000171B4
		public LicenseCheck(HttpContext context) : base(context)
		{
		}

		// Token: 0x0600023D RID: 573 RVA: 0x00018FC0 File Offset: 0x000171C0
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string text = string.Empty;
			string domain = this._entity_dextParam.domain;
			string productKey = this._entity_dextParam.productKey;
			string text2 = this._entity_dextParam.licenseKey;
			if (!string.IsNullOrEmpty(this._entity_dextParam.licenseBase64) && this._entity_dextParam.licenseBase64.ToLower().Equals("y"))
			{
				text2 = Dext5Encoding.Base64Decoding(text2);
			}
			string text3 = this.hContext.Request.UserAgent;
			if (text3.IndexOf("DEXT5") < 0)
			{
				text3 = this._entity_dextParam.productName;
			}
			if (string.IsNullOrEmpty(text3) || text3.ToLower() == "dext5")
			{
			}
			string text4 = text3.ToLower();
			bool flag = false;
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain) && this._entity_dextParam.crossDomain.Equals("1"))
			{
				flag = true;
			}
			if (flag)
			{
				text += "<html><head>";
				text += "<script type=\"text/javascript\">";
				text += "if (window.postMessage) {";
				text += "if (window.addEventListener) {";
				text += "window.addEventListener('message', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "}, false);";
				text += "}";
				text += "else if (window.attachEvent) {";
				text += "window.attachEvent('onmessage', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "});";
				text += "}";
				text += "}";
				text += "</script>";
				text += "</head>";
				text += "<body>";
				text += "{0}";
				text += "</body>";
				text += "</html>";
			}
			else
			{
				text = "{0}";
			}
			if (string.IsNullOrEmpty(domain) || string.IsNullOrEmpty(productKey) || string.IsNullOrEmpty(text2))
			{
				string a;
				if ((a = text4) != null)
				{
					if (!(a == "dext5e"))
					{
						if (!(a == "dext5u"))
						{
							if (a == "dext5p")
							{
								text = text.Replace("{0}", "Invalid licensekey.\v" + this._PhotoServerVersion);
							}
						}
						else
						{
							text = text.Replace("{0}", "Invalid licensekey.\v" + this._UploadServerVersion);
						}
					}
					else
					{
						text = text.Replace("{0}", "Invalid licensekey.\v" + this._EditorServerVersion);
					}
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return null;
			}
			LicenseInfo licenseInfo = LicenseAuthorization.CheckCommonLicense(text3, productKey, text2, domain);
			if (licenseInfo.Validate != "ok")
			{
				string a2;
				if ((a2 = text4) != null)
				{
					if (!(a2 == "dext5e"))
					{
						if (!(a2 == "dext5u"))
						{
							if (a2 == "dext5p")
							{
								text = text.Replace("{0}", "Licensekey is incorrect. Please contact us. " + licenseInfo.Validate + "\v" + this._PhotoServerVersion);
							}
						}
						else
						{
							text = text.Replace("{0}", "Licensekey is incorrect. Please contact us. " + licenseInfo.Validate + "\v" + this._UploadServerVersion);
						}
					}
					else
					{
						text = text.Replace("{0}", "Licensekey is incorrect. Please contact us. " + licenseInfo.Validate + "\v" + this._EditorServerVersion);
					}
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return null;
			}
			string a3;
			if ((a3 = text4) != null)
			{
				if (!(a3 == "dext5e"))
				{
					if (!(a3 == "dext5u"))
					{
						if (a3 == "dext5p")
						{
							text = text.Replace("{0}", string.Concat(new string[]
							{
								"Hi, DEXT5 Photo !!!-",
								this._PhotoServerVersion,
								"-",
								licenseInfo.LicenseCheckType,
								"-",
								licenseInfo.ProductName,
								"-",
								licenseInfo.InterworkingModule
							}));
						}
					}
					else
					{
						text = text.Replace("{0}", string.Concat(new string[]
						{
							"Hi, DEXT5 Upload !!!-",
							this._UploadServerVersion,
							"-",
							licenseInfo.LicenseCheckType,
							"-",
							licenseInfo.ProductName,
							"-",
							licenseInfo.InterworkingModule
						}));
					}
				}
				else
				{
					text = text.Replace("{0}", string.Concat(new string[]
					{
						"Hi, DEXT5 Editor !!!-",
						this._EditorServerVersion,
						"-",
						licenseInfo.LicenseCheckType,
						"-",
						licenseInfo.ProductName,
						"-",
						licenseInfo.InterworkingModule
					}));
				}
			}
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text);
			return null;
		}
	}
}

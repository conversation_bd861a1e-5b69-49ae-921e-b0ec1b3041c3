﻿using System;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Entity
{
	// Token: 0x02000008 RID: 8
	public class ParamEntity
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x06000047 RID: 71 RVA: 0x0000D2E4 File Offset: 0x0000B4E4
		// (set) Token: 0x06000046 RID: 70 RVA: 0x0000D2DB File Offset: 0x0000B4DB
		public string decryptParameter
		{
			get
			{
				return this._decryptParameter;
			}
			set
			{
				this._decryptParameter = value;
			}
		}

		// Token: 0x17000002 RID: 2
		// (get) Token: 0x06000048 RID: 72 RVA: 0x0000D2EC File Offset: 0x0000B4EC
		// (set) Token: 0x06000049 RID: 73 RVA: 0x0000D2F4 File Offset: 0x0000B4F4
		public string mode
		{
			get
			{
				return this._mode;
			}
			set
			{
				this._mode = value;
			}
		}

		// Token: 0x17000003 RID: 3
		// (get) Token: 0x0600004A RID: 74 RVA: 0x0000D2FD File Offset: 0x0000B4FD
		// (set) Token: 0x0600004B RID: 75 RVA: 0x0000D305 File Offset: 0x0000B505
		public string dext5CMD
		{
			get
			{
				return this._dext5CMD;
			}
			set
			{
				this._dext5CMD = value;
			}
		}

		// Token: 0x17000004 RID: 4
		// (get) Token: 0x0600004C RID: 76 RVA: 0x0000D30E File Offset: 0x0000B50E
		// (set) Token: 0x0600004D RID: 77 RVA: 0x0000D316 File Offset: 0x0000B516
		public string domain
		{
			get
			{
				return this._domain;
			}
			set
			{
				this._domain = value;
			}
		}

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x0600004E RID: 78 RVA: 0x0000D31F File Offset: 0x0000B51F
		// (set) Token: 0x0600004F RID: 79 RVA: 0x0000D327 File Offset: 0x0000B527
		public string productKey
		{
			get
			{
				return this._productKey;
			}
			set
			{
				this._productKey = value;
			}
		}

		// Token: 0x17000006 RID: 6
		// (get) Token: 0x06000050 RID: 80 RVA: 0x0000D330 File Offset: 0x0000B530
		// (set) Token: 0x06000051 RID: 81 RVA: 0x0000D338 File Offset: 0x0000B538
		public string licenseKey
		{
			get
			{
				return this._licenseKey;
			}
			set
			{
				this._licenseKey = value;
			}
		}

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x06000052 RID: 82 RVA: 0x0000D341 File Offset: 0x0000B541
		// (set) Token: 0x06000053 RID: 83 RVA: 0x0000D349 File Offset: 0x0000B549
		public string licenseBase64
		{
			get
			{
				return this._licenseBase64;
			}
			set
			{
				this._licenseBase64 = value;
			}
		}

		// Token: 0x17000008 RID: 8
		// (get) Token: 0x06000054 RID: 84 RVA: 0x0000D352 File Offset: 0x0000B552
		// (set) Token: 0x06000055 RID: 85 RVA: 0x0000D35A File Offset: 0x0000B55A
		public string productName
		{
			get
			{
				return this._productName;
			}
			set
			{
				this._productName = value;
			}
		}

		// Token: 0x17000009 RID: 9
		// (get) Token: 0x06000056 RID: 86 RVA: 0x0000D363 File Offset: 0x0000B563
		// (set) Token: 0x06000057 RID: 87 RVA: 0x0000D36B File Offset: 0x0000B56B
		public string crossDomain
		{
			get
			{
				return this._crossDomain;
			}
			set
			{
				this._crossDomain = value;
			}
		}

		// Token: 0x1700000A RID: 10
		// (get) Token: 0x06000058 RID: 88 RVA: 0x0000D374 File Offset: 0x0000B574
		// (set) Token: 0x06000059 RID: 89 RVA: 0x0000D37C File Offset: 0x0000B57C
		public string displayFileSize
		{
			get
			{
				return this._displayFileSize;
			}
			set
			{
				this._displayFileSize = value;
			}
		}

		// Token: 0x1700000B RID: 11
		// (get) Token: 0x0600005A RID: 90 RVA: 0x0000D385 File Offset: 0x0000B585
		// (set) Token: 0x0600005B RID: 91 RVA: 0x0000D38D File Offset: 0x0000B58D
		public string fileName
		{
			get
			{
				return this._fileName;
			}
			set
			{
				this._fileName = value;
			}
		}

		// Token: 0x1700000C RID: 12
		// (get) Token: 0x0600005C RID: 92 RVA: 0x0000D396 File Offset: 0x0000B596
		// (set) Token: 0x0600005D RID: 93 RVA: 0x0000D39E File Offset: 0x0000B59E
		public string folderNameRule
		{
			get
			{
				return this._folderNameRule;
			}
			set
			{
				this._folderNameRule = value;
			}
		}

		// Token: 0x1700000D RID: 13
		// (get) Token: 0x0600005E RID: 94 RVA: 0x0000D3A7 File Offset: 0x0000B5A7
		// (set) Token: 0x0600005F RID: 95 RVA: 0x0000D3AF File Offset: 0x0000B5AF
		public string fileNameRule
		{
			get
			{
				return this._fileNameRule;
			}
			set
			{
				this._fileNameRule = value;
			}
		}

		// Token: 0x1700000E RID: 14
		// (get) Token: 0x06000060 RID: 96 RVA: 0x0000D3B8 File Offset: 0x0000B5B8
		// (set) Token: 0x06000061 RID: 97 RVA: 0x0000D3C0 File Offset: 0x0000B5C0
		public string fileNameRuleEx
		{
			get
			{
				return this._fileNameRuleEx;
			}
			set
			{
				this._fileNameRuleEx = value;
			}
		}

		// Token: 0x1700000F RID: 15
		// (get) Token: 0x06000062 RID: 98 RVA: 0x0000D3C9 File Offset: 0x0000B5C9
		// (set) Token: 0x06000063 RID: 99 RVA: 0x0000D3D1 File Offset: 0x0000B5D1
		public string folderPath
		{
			get
			{
				return this._folderPath;
			}
			set
			{
				this._folderPath = value;
			}
		}

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x06000064 RID: 100 RVA: 0x0000D3DA File Offset: 0x0000B5DA
		// (set) Token: 0x06000065 RID: 101 RVA: 0x0000D3E2 File Offset: 0x0000B5E2
		public string filePrefix
		{
			get
			{
				return this._filePrefix;
			}
			set
			{
				this._filePrefix = value;
			}
		}

		// Token: 0x17000011 RID: 17
		// (get) Token: 0x06000066 RID: 102 RVA: 0x0000D3EB File Offset: 0x0000B5EB
		// (set) Token: 0x06000067 RID: 103 RVA: 0x0000D3F3 File Offset: 0x0000B5F3
		public string fileSubfix
		{
			get
			{
				return this._fileSubfix;
			}
			set
			{
				this._fileSubfix = value;
			}
		}

		// Token: 0x17000012 RID: 18
		// (get) Token: 0x06000068 RID: 104 RVA: 0x0000D3FC File Offset: 0x0000B5FC
		// (set) Token: 0x06000069 RID: 105 RVA: 0x0000D404 File Offset: 0x0000B604
		public string fileExtensionDetector
		{
			get
			{
				return this._fileExtensionDetector;
			}
			set
			{
				this._fileExtensionDetector = value;
			}
		}

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x0600006A RID: 106 RVA: 0x0000D40D File Offset: 0x0000B60D
		// (set) Token: 0x0600006B RID: 107 RVA: 0x0000D415 File Offset: 0x0000B615
		public string fileGroupID
		{
			get
			{
				return this._fileGroupID;
			}
			set
			{
				this._fileGroupID = value;
			}
		}

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x0600006C RID: 108 RVA: 0x0000D41E File Offset: 0x0000B61E
		// (set) Token: 0x0600006D RID: 109 RVA: 0x0000D426 File Offset: 0x0000B626
		public string fileIndex
		{
			get
			{
				return this._fileIndex;
			}
			set
			{
				this._fileIndex = value;
			}
		}

		// Token: 0x17000015 RID: 21
		// (get) Token: 0x0600006E RID: 110 RVA: 0x0000D42F File Offset: 0x0000B62F
		// (set) Token: 0x0600006F RID: 111 RVA: 0x0000D437 File Offset: 0x0000B637
		public string fileDataIntegrity
		{
			get
			{
				return this._fileDataIntegrity;
			}
			set
			{
				this._fileDataIntegrity = value;
			}
		}

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000070 RID: 112 RVA: 0x0000D440 File Offset: 0x0000B640
		// (set) Token: 0x06000071 RID: 113 RVA: 0x0000D448 File Offset: 0x0000B648
		public string GUID
		{
			get
			{
				return this._GUID;
			}
			set
			{
				this._GUID = value;
			}
		}

		// Token: 0x17000017 RID: 23
		// (get) Token: 0x06000072 RID: 114 RVA: 0x0000D451 File Offset: 0x0000B651
		// (set) Token: 0x06000073 RID: 115 RVA: 0x0000D459 File Offset: 0x0000B659
		public string chunkSize
		{
			get
			{
				return this._chunkSize;
			}
			set
			{
				this._chunkSize = value;
			}
		}

		// Token: 0x17000018 RID: 24
		// (get) Token: 0x06000074 RID: 116 RVA: 0x0000D462 File Offset: 0x0000B662
		// (set) Token: 0x06000075 RID: 117 RVA: 0x0000D46A File Offset: 0x0000B66A
		public string fileSize
		{
			get
			{
				return this._fileSize;
			}
			set
			{
				this._fileSize = value;
			}
		}

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x06000076 RID: 118 RVA: 0x0000D473 File Offset: 0x0000B673
		// (set) Token: 0x06000077 RID: 119 RVA: 0x0000D47B File Offset: 0x0000B67B
		public string chunkNumber
		{
			get
			{
				return this._chunkNumber;
			}
			set
			{
				this._chunkNumber = value;
			}
		}

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x06000078 RID: 120 RVA: 0x0000D484 File Offset: 0x0000B684
		// (set) Token: 0x06000079 RID: 121 RVA: 0x0000D48C File Offset: 0x0000B68C
		public string numberOfChunks
		{
			get
			{
				return this._numberOfChunks;
			}
			set
			{
				this._numberOfChunks = value;
			}
		}

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x0600007A RID: 122 RVA: 0x0000D495 File Offset: 0x0000B695
		// (set) Token: 0x0600007B RID: 123 RVA: 0x0000D49D File Offset: 0x0000B69D
		public string RESUMEUP
		{
			get
			{
				return this._RESUMEUP;
			}
			set
			{
				this._RESUMEUP = value;
			}
		}

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x0600007C RID: 124 RVA: 0x0000D4A6 File Offset: 0x0000B6A6
		// (set) Token: 0x0600007D RID: 125 RVA: 0x0000D4AE File Offset: 0x0000B6AE
		public string fileDataEncrypt
		{
			get
			{
				return this._fileDataEncrypt;
			}
			set
			{
				this._fileDataEncrypt = value;
			}
		}

		// Token: 0x1700001D RID: 29
		// (get) Token: 0x0600007E RID: 126 RVA: 0x0000D4B7 File Offset: 0x0000B6B7
		// (set) Token: 0x0600007F RID: 127 RVA: 0x0000D4BF File Offset: 0x0000B6BF
		public string RESUMEGUID
		{
			get
			{
				return this._RESUMEGUID;
			}
			set
			{
				this._RESUMEGUID = value;
			}
		}

		// Token: 0x1700001E RID: 30
		// (get) Token: 0x06000080 RID: 128 RVA: 0x0000D4C8 File Offset: 0x0000B6C8
		// (set) Token: 0x06000081 RID: 129 RVA: 0x0000D4D0 File Offset: 0x0000B6D0
		public string[] fiAry
		{
			get
			{
				return this._fiAry;
			}
			set
			{
				this._fiAry = value;
			}
		}

		// Token: 0x1700001F RID: 31
		// (get) Token: 0x06000082 RID: 130 RVA: 0x0000D4D9 File Offset: 0x0000B6D9
		// (set) Token: 0x06000083 RID: 131 RVA: 0x0000D4E1 File Offset: 0x0000B6E1
		public string g
		{
			get
			{
				return this._g;
			}
			set
			{
				this._g = value;
			}
		}

		// Token: 0x17000020 RID: 32
		// (get) Token: 0x06000084 RID: 132 RVA: 0x0000D4EA File Offset: 0x0000B6EA
		// (set) Token: 0x06000085 RID: 133 RVA: 0x0000D4F2 File Offset: 0x0000B6F2
		public string p
		{
			get
			{
				return this._p;
			}
			set
			{
				this._p = value;
			}
		}

		// Token: 0x17000021 RID: 33
		// (get) Token: 0x06000086 RID: 134 RVA: 0x0000D4FB File Offset: 0x0000B6FB
		// (set) Token: 0x06000087 RID: 135 RVA: 0x0000D503 File Offset: 0x0000B703
		public string zipFileName
		{
			get
			{
				return this._zipFileName;
			}
			set
			{
				this._zipFileName = value;
			}
		}

		// Token: 0x17000022 RID: 34
		// (get) Token: 0x06000088 RID: 136 RVA: 0x0000D50C File Offset: 0x0000B70C
		// (set) Token: 0x06000089 RID: 137 RVA: 0x0000D514 File Offset: 0x0000B714
		public string fileVirtualPath
		{
			get
			{
				return this._fileVirtualPath;
			}
			set
			{
				this._fileVirtualPath = value;
			}
		}

		// Token: 0x17000023 RID: 35
		// (get) Token: 0x0600008A RID: 138 RVA: 0x0000D51D File Offset: 0x0000B71D
		// (set) Token: 0x0600008B RID: 139 RVA: 0x0000D525 File Offset: 0x0000B725
		public string[] fileVirtualPathAry
		{
			get
			{
				return this._fileVirtualPathAry;
			}
			set
			{
				this._fileVirtualPathAry = value;
			}
		}

		// Token: 0x17000024 RID: 36
		// (get) Token: 0x0600008C RID: 140 RVA: 0x0000D52E File Offset: 0x0000B72E
		// (set) Token: 0x0600008D RID: 141 RVA: 0x0000D536 File Offset: 0x0000B736
		public string[] fileOrgNameAry
		{
			get
			{
				return this._fileOrgNameAry;
			}
			set
			{
				this._fileOrgNameAry = value;
			}
		}

		// Token: 0x17000025 RID: 37
		// (get) Token: 0x0600008E RID: 142 RVA: 0x0000D53F File Offset: 0x0000B73F
		// (set) Token: 0x0600008F RID: 143 RVA: 0x0000D547 File Offset: 0x0000B747
		public string resumeMode
		{
			get
			{
				return this._resumeMode;
			}
			set
			{
				this._resumeMode = value;
			}
		}

		// Token: 0x17000026 RID: 38
		// (get) Token: 0x06000090 RID: 144 RVA: 0x0000D550 File Offset: 0x0000B750
		// (set) Token: 0x06000091 RID: 145 RVA: 0x0000D558 File Offset: 0x0000B758
		public string crossDomainZipFileName
		{
			get
			{
				return this._crossDomainZipFileName;
			}
			set
			{
				this._crossDomainZipFileName = value;
			}
		}

		// Token: 0x17000027 RID: 39
		// (get) Token: 0x06000092 RID: 146 RVA: 0x0000D561 File Offset: 0x0000B761
		// (set) Token: 0x06000093 RID: 147 RVA: 0x0000D569 File Offset: 0x0000B769
		public string viewerGUID
		{
			get
			{
				return this._viewerGUID;
			}
			set
			{
				this._viewerGUID = value;
			}
		}

		// Token: 0x17000028 RID: 40
		// (get) Token: 0x06000094 RID: 148 RVA: 0x0000D572 File Offset: 0x0000B772
		// (set) Token: 0x06000095 RID: 149 RVA: 0x0000D57A File Offset: 0x0000B77A
		public string documentDomain
		{
			get
			{
				return this._documentDomain;
			}
			set
			{
				this._documentDomain = value;
			}
		}

		// Token: 0x17000029 RID: 41
		// (get) Token: 0x06000096 RID: 150 RVA: 0x0000D583 File Offset: 0x0000B783
		// (set) Token: 0x06000097 RID: 151 RVA: 0x0000D58B File Offset: 0x0000B78B
		public string viewerPopupInit
		{
			get
			{
				return this._viewerPopupInit;
			}
			set
			{
				this._viewerPopupInit = value;
			}
		}

		// Token: 0x1700002A RID: 42
		// (get) Token: 0x06000098 RID: 152 RVA: 0x0000D594 File Offset: 0x0000B794
		// (set) Token: 0x06000099 RID: 153 RVA: 0x0000D59C File Offset: 0x0000B79C
		public string dext5Release
		{
			get
			{
				return this._dext5Release;
			}
			set
			{
				this._dext5Release = value;
			}
		}

		// Token: 0x1700002B RID: 43
		// (get) Token: 0x0600009A RID: 154 RVA: 0x0000D5A5 File Offset: 0x0000B7A5
		// (set) Token: 0x0600009B RID: 155 RVA: 0x0000D5AD File Offset: 0x0000B7AD
		public string downloadUrl
		{
			get
			{
				return this._downloadUrl;
			}
			set
			{
				this._downloadUrl = value;
			}
		}

		// Token: 0x1700002C RID: 44
		// (get) Token: 0x0600009C RID: 156 RVA: 0x0000D5B6 File Offset: 0x0000B7B6
		// (set) Token: 0x0600009D RID: 157 RVA: 0x0000D5BE File Offset: 0x0000B7BE
		public string fileExt
		{
			get
			{
				return this._fileExt;
			}
			set
			{
				this._fileExt = value;
			}
		}

		// Token: 0x1700002D RID: 45
		// (get) Token: 0x0600009E RID: 158 RVA: 0x0000D5C7 File Offset: 0x0000B7C7
		// (set) Token: 0x0600009F RID: 159 RVA: 0x0000D5CF File Offset: 0x0000B7CF
		public string skinName
		{
			get
			{
				return this._skinName;
			}
			set
			{
				this._skinName = value;
			}
		}

		// Token: 0x1700002E RID: 46
		// (get) Token: 0x060000A0 RID: 160 RVA: 0x0000D5D8 File Offset: 0x0000B7D8
		// (set) Token: 0x060000A1 RID: 161 RVA: 0x0000D5E0 File Offset: 0x0000B7E0
		public string urlAddress
		{
			get
			{
				return this._urlAddress;
			}
			set
			{
				this._urlAddress = value;
			}
		}

		// Token: 0x1700002F RID: 47
		// (get) Token: 0x060000A2 RID: 162 RVA: 0x0000D5E9 File Offset: 0x0000B7E9
		// (set) Token: 0x060000A3 RID: 163 RVA: 0x0000D5F1 File Offset: 0x0000B7F1
		public string[] urlAddressAry
		{
			get
			{
				return this._urlAddressAry;
			}
			set
			{
				this._urlAddressAry = value;
			}
		}

		// Token: 0x17000030 RID: 48
		// (get) Token: 0x060000A4 RID: 164 RVA: 0x0000D5FA File Offset: 0x0000B7FA
		// (set) Token: 0x060000A5 RID: 165 RVA: 0x0000D602 File Offset: 0x0000B802
		public string configFileName
		{
			get
			{
				return this._configFileName;
			}
			set
			{
				this._configFileName = value;
			}
		}

		// Token: 0x17000031 RID: 49
		// (get) Token: 0x060000A6 RID: 166 RVA: 0x0000D60B File Offset: 0x0000B80B
		// (set) Token: 0x060000A7 RID: 167 RVA: 0x0000D613 File Offset: 0x0000B813
		public string fileOrgName
		{
			get
			{
				return this._fileOrgName;
			}
			set
			{
				this._fileOrgName = value;
			}
		}

		// Token: 0x17000032 RID: 50
		// (get) Token: 0x060000A8 RID: 168 RVA: 0x0000D61C File Offset: 0x0000B81C
		// (set) Token: 0x060000A9 RID: 169 RVA: 0x0000D624 File Offset: 0x0000B824
		public string h5pbeInfo
		{
			get
			{
				return this._h5pbeInfo;
			}
			set
			{
				this._h5pbeInfo = value;
			}
		}

		// Token: 0x17000033 RID: 51
		// (get) Token: 0x060000AA RID: 170 RVA: 0x0000D62D File Offset: 0x0000B82D
		// (set) Token: 0x060000AB RID: 171 RVA: 0x0000D635 File Offset: 0x0000B835
		public string allowedZeroFileSize
		{
			get
			{
				return this._allowedZeroFileSize;
			}
			set
			{
				this._allowedZeroFileSize = value;
			}
		}

		// Token: 0x17000034 RID: 52
		// (get) Token: 0x060000AC RID: 172 RVA: 0x0000D63E File Offset: 0x0000B83E
		// (set) Token: 0x060000AD RID: 173 RVA: 0x0000D646 File Offset: 0x0000B846
		public string checkFileExtension
		{
			get
			{
				return this._checkFileExtension;
			}
			set
			{
				this._checkFileExtension = value;
			}
		}

		// Token: 0x17000035 RID: 53
		// (get) Token: 0x060000AE RID: 174 RVA: 0x0000D64F File Offset: 0x0000B84F
		// (set) Token: 0x060000AF RID: 175 RVA: 0x0000D657 File Offset: 0x0000B857
		public string limitOneFileSize
		{
			get
			{
				return this._limitOneFileSize;
			}
			set
			{
				this._limitOneFileSize = value;
			}
		}

		// Token: 0x17000036 RID: 54
		// (get) Token: 0x060000B0 RID: 176 RVA: 0x0000D660 File Offset: 0x0000B860
		// (set) Token: 0x060000B1 RID: 177 RVA: 0x0000D668 File Offset: 0x0000B868
		public string kcmd
		{
			get
			{
				return this._kcmd;
			}
			set
			{
				this._kcmd = value;
			}
		}

		// Token: 0x17000037 RID: 55
		// (get) Token: 0x060000B2 RID: 178 RVA: 0x0000D671 File Offset: 0x0000B871
		// (set) Token: 0x060000B3 RID: 179 RVA: 0x0000D679 File Offset: 0x0000B879
		public string kFileStartPos
		{
			get
			{
				return this._kFileStartPos;
			}
			set
			{
				this._kFileStartPos = value;
			}
		}

		// Token: 0x17000038 RID: 56
		// (get) Token: 0x060000B4 RID: 180 RVA: 0x0000D682 File Offset: 0x0000B882
		// (set) Token: 0x060000B5 RID: 181 RVA: 0x0000D68A File Offset: 0x0000B88A
		public string viewerUrl
		{
			get
			{
				return this._viewerUrl;
			}
			set
			{
				this._viewerUrl = value;
			}
		}

		// Token: 0x060000B6 RID: 182 RVA: 0x0000D694 File Offset: 0x0000B894
		public ParamEntity(HttpContext hContext)
		{
			if (!string.IsNullOrEmpty(hContext.Request["d00"]))
			{
				this.decryptParameter = Dext5Parameter.GetParameter(hContext);
				return;
			}
			this.decryptParameter = "";
		}

		// Token: 0x04000031 RID: 49
		private string _decryptParameter = string.Empty;

		// Token: 0x04000032 RID: 50
		private string _mode = string.Empty;

		// Token: 0x04000033 RID: 51
		private string _dext5CMD = string.Empty;

		// Token: 0x04000034 RID: 52
		private string _domain = string.Empty;

		// Token: 0x04000035 RID: 53
		private string _productKey = string.Empty;

		// Token: 0x04000036 RID: 54
		private string _licenseKey = string.Empty;

		// Token: 0x04000037 RID: 55
		private string _licenseBase64 = string.Empty;

		// Token: 0x04000038 RID: 56
		private string _productName = string.Empty;

		// Token: 0x04000039 RID: 57
		private string _crossDomain = string.Empty;

		// Token: 0x0400003A RID: 58
		private string _displayFileSize = "0";

		// Token: 0x0400003B RID: 59
		private string _fileName = string.Empty;

		// Token: 0x0400003C RID: 60
		private string _folderNameRule = string.Empty;

		// Token: 0x0400003D RID: 61
		private string _fileNameRule = string.Empty;

		// Token: 0x0400003E RID: 62
		private string _fileNameRuleEx = string.Empty;

		// Token: 0x0400003F RID: 63
		private string _folderPath = string.Empty;

		// Token: 0x04000040 RID: 64
		private string _filePrefix = string.Empty;

		// Token: 0x04000041 RID: 65
		private string _fileSubfix = string.Empty;

		// Token: 0x04000042 RID: 66
		private string _fileExtensionDetector = string.Empty;

		// Token: 0x04000043 RID: 67
		private string _fileGroupID = string.Empty;

		// Token: 0x04000044 RID: 68
		private string _fileIndex = string.Empty;

		// Token: 0x04000045 RID: 69
		private string _fileDataIntegrity = string.Empty;

		// Token: 0x04000046 RID: 70
		private string _GUID = string.Empty;

		// Token: 0x04000047 RID: 71
		private string _chunkSize = "-1";

		// Token: 0x04000048 RID: 72
		private string _fileSize = "-1";

		// Token: 0x04000049 RID: 73
		private string _chunkNumber = "-1";

		// Token: 0x0400004A RID: 74
		private string _numberOfChunks = "-1";

		// Token: 0x0400004B RID: 75
		private string _RESUMEUP = string.Empty;

		// Token: 0x0400004C RID: 76
		private string _fileDataEncrypt = string.Empty;

		// Token: 0x0400004D RID: 77
		private string _RESUMEGUID = string.Empty;

		// Token: 0x0400004E RID: 78
		private string[] _fiAry;

		// Token: 0x0400004F RID: 79
		private string _g = string.Empty;

		// Token: 0x04000050 RID: 80
		private string _p = string.Empty;

		// Token: 0x04000051 RID: 81
		private string _zipFileName = string.Empty;

		// Token: 0x04000052 RID: 82
		private string _fileVirtualPath = string.Empty;

		// Token: 0x04000053 RID: 83
		private string[] _fileVirtualPathAry;

		// Token: 0x04000054 RID: 84
		private string[] _fileOrgNameAry;

		// Token: 0x04000055 RID: 85
		private string _resumeMode;

		// Token: 0x04000056 RID: 86
		private string _crossDomainZipFileName;

		// Token: 0x04000057 RID: 87
		private string _viewerGUID;

		// Token: 0x04000058 RID: 88
		private string _documentDomain;

		// Token: 0x04000059 RID: 89
		private string _viewerPopupInit;

		// Token: 0x0400005A RID: 90
		private string _dext5Release;

		// Token: 0x0400005B RID: 91
		private string _downloadUrl;

		// Token: 0x0400005C RID: 92
		private string _fileExt;

		// Token: 0x0400005D RID: 93
		private string _skinName;

		// Token: 0x0400005E RID: 94
		private string _urlAddress;

		// Token: 0x0400005F RID: 95
		private string[] _urlAddressAry;

		// Token: 0x04000060 RID: 96
		private string _configFileName;

		// Token: 0x04000061 RID: 97
		private string _fileOrgName;

		// Token: 0x04000062 RID: 98
		private string _h5pbeInfo;

		// Token: 0x04000063 RID: 99
		private string _allowedZeroFileSize;

		// Token: 0x04000064 RID: 100
		private string _checkFileExtension;

		// Token: 0x04000065 RID: 101
		private string _limitOneFileSize;

		// Token: 0x04000066 RID: 102
		private string _kcmd;

		// Token: 0x04000067 RID: 103
		private string _kFileStartPos;

		// Token: 0x04000068 RID: 104
		private string _viewerUrl;
	}
}

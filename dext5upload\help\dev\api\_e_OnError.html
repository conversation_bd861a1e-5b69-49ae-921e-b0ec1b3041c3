﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: OnError</h3>
    <p class="ttl">void DEXT5UPLOAD_OnError(uploadID, code, message, uploadedFileListObj)</p>
    <p class="txt">
        에러가 발생할 경우 발생합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p> 
    <p class="mttl01">parameters</p>
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;에러가 발생한 업로드의 id를 의미합니다.<br/>
        <span class="firebrick">code</span>&nbsp;&nbsp;에러가 발생할 경우 에러 코드를 의미합니다.<br/>
        <span class="firebrick">message</span>&nbsp;&nbsp;에러가 발생할 경우 에러 메세지를 의미합니다.<br />
        <span class="firebrick">uploadedFileListObj</span>&nbsp;&nbsp;이미 업로드 된 파일들의 Array 객체를 의미합니다. 
    </p>
    <p class="mttl01">remarks</p>
    <p class="txt">
        없음.
    </p>   
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;script type="text/javascript"&#62;
        DEXT5UPLOAD_OnError(uploadID, code, message, uploadedFileListObj){
            //에러 발생 후 경고창 띄어줌
            alert("Error Code : " + code + "\nError Message : " + message);
        }
&#60;/script&#62;

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


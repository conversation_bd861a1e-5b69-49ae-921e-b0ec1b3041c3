#!/usr/bin/env python3
"""
测试蜜罐检测功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from scanner import UniversalEditorScanner
except ImportError:
    # 如果在editor目录下
    sys.path.insert(0, r'e:\docs\untitled\editor')
    from scanner import UniversalEditorScanner

def test_honeypot_detection():
    """测试蜜罐检测功能"""
    print("测试蜜罐检测功能")
    print("=" * 50)
    
    scanner = UniversalEditorScanner()
    
    # 模拟正常情况 - 单个编辑器
    normal_results = [
        {
            'target': 'normal-site.com',
            'editor_type': 'ckeditor',
            'base_path': 'https://normal-site.com/ckeditor/',
            'core_files_found': [
                {'url': 'https://normal-site.com/ckeditor/ckeditor.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://normal-site.com/ckeditor/samples/sample.php', 'path_type': 'info'}
            ]
        }
    ]
    
    print("\n[*] 测试正常情况:")
    is_suspicious, reason = scanner.detect_honeypot_or_wildcard(normal_results)
    print(f"  结果: {'可疑' if is_suspicious else '正常'}")
    print(f"  原因: {reason}")
    
    # 模拟可疑情况1 - 多个编辑器
    suspicious_results_1 = [
        {
            'target': 'suspicious-site.com',
            'editor_type': 'smarteditor',
            'base_path': 'https://suspicious-site.com/',
            'core_files_found': [
                {'url': 'https://suspicious-site.com/HuskyEZCreator.js', 'status': 'valid'},
                {'url': 'https://suspicious-site.com/smart_editor2.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://suspicious-site.com/sample/photo_uploader/file_uploader.jsp', 'path_type': 'upload'},
                {'url': 'https://suspicious-site.com/sample/photo_uploader/file_uploader.php', 'path_type': 'upload'},
                {'url': 'https://suspicious-site.com/sample/photo_uploader/file_uploader_html5.jsp', 'path_type': 'upload'},
                {'url': 'https://suspicious-site.com/sample/photo_uploader/photo_uploader.html', 'path_type': 'info'},
                {'url': 'https://suspicious-site.com/photo_uploader/popup/file_uploader.jsp', 'path_type': 'upload'}
            ]
        },
        {
            'target': 'suspicious-site.com',
            'editor_type': 'cheditor',
            'base_path': 'https://suspicious-site.com/',
            'core_files_found': [
                {'url': 'https://suspicious-site.com/cheditor.js', 'status': 'valid'},
                {'url': 'https://suspicious-site.com/cheditor.css', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://suspicious-site.com/imageUpload/upload.jsp', 'path_type': 'upload'},
                {'url': 'https://suspicious-site.com/imageUpload/delete.jsp', 'path_type': 'delete'},
                {'url': 'https://suspicious-site.com/imageUpload/upload-simple.jsp', 'path_type': 'upload'},
                {'url': 'https://suspicious-site.com/imageUpload/config.jsp', 'path_type': 'info'}
            ]
        },
        {
            'target': 'suspicious-site.com',
            'editor_type': 'ckeditor',
            'base_path': 'https://suspicious-site.com/',
            'core_files_found': [
                {'url': 'https://suspicious-site.com/ckeditor.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://suspicious-site.com/_samples/sample_posteddata.php', 'path_type': 'info'},
                {'url': 'https://suspicious-site.com/_samples/assets/_posteddata.php', 'path_type': 'info'},
                {'url': 'https://suspicious-site.com/samples/sample_posteddata.php', 'path_type': 'info'}
            ]
        },
        {
            'target': 'suspicious-site.com',
            'editor_type': 'ckfinder',
            'base_path': 'https://suspicious-site.com/',
            'core_files_found': [
                {'url': 'https://suspicious-site.com/ckfinder.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://suspicious-site.com/core/connector/php/connector.php', 'path_type': 'rce'},
                {'url': 'https://suspicious-site.com/core/connector/connector', 'path_type': 'rce'},
                {'url': 'https://suspicious-site.com/plugins/fileeditor/plugin.js', 'path_type': 'rce'},
                {'url': 'https://suspicious-site.com/ckfinder.html', 'path_type': 'info'}
            ]
        }
    ]
    
    print("\n[*] 测试可疑情况1 - 多个编辑器:")
    is_suspicious, reason = scanner.detect_honeypot_or_wildcard(suspicious_results_1)
    print(f"  结果: {'可疑' if is_suspicious else '正常'}")
    print(f"  原因: {reason}")
    
    # 模拟可疑情况2 - 过多辅助路径
    suspicious_results_2 = [
        {
            'target': 'honeypot-site.com',
            'editor_type': 'smarteditor',
            'base_path': 'https://honeypot-site.com/',
            'core_files_found': [
                {'url': 'https://honeypot-site.com/HuskyEZCreator.js', 'status': 'valid'},
                {'url': 'https://honeypot-site.com/smart_editor2.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://honeypot-site.com/sample/photo_uploader/file_uploader.jsp', 'path_type': 'upload'},
                {'url': 'https://honeypot-site.com/sample/photo_uploader/file_uploader.php', 'path_type': 'upload'},
                {'url': 'https://honeypot-site.com/sample/photo_uploader/file_uploader_html5.jsp', 'path_type': 'upload'},
                {'url': 'https://honeypot-site.com/sample/photo_uploader/photo_uploader.html', 'path_type': 'info'},
                {'url': 'https://honeypot-site.com/photo_uploader/popup/file_uploader.jsp', 'path_type': 'upload'},
                {'url': 'https://honeypot-site.com/extra/path1.jsp', 'path_type': 'upload'},
                {'url': 'https://honeypot-site.com/extra/path2.jsp', 'path_type': 'upload'}
            ]
        }
    ]
    
    print("\n[*] 测试可疑情况2 - 过多辅助路径:")
    is_suspicious, reason = scanner.detect_honeypot_or_wildcard(suspicious_results_2)
    print(f"  结果: {'可疑' if is_suspicious else '正常'}")
    print(f"  原因: {reason}")

def test_target_legitimacy():
    """测试目标合法性验证"""
    print("\n\n测试目标合法性验证")
    print("=" * 50)
    
    scanner = UniversalEditorScanner()
    
    # 模拟你提到的可疑目标
    goldendew_results = [
        {
            'target': 'www.goldendewclub.com:443',
            'editor_type': 'smarteditor',
            'detection_method': 'directory_fuzzing',
            'base_path': 'https://www.goldendewclub.com:443',
            'core_files_found': [
                {'url': 'https://www.goldendewclub.com:443/HuskyEZCreator.js', 'status': 'valid'},
                {'url': 'https://www.goldendewclub.com:443/smart_editor2.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader.jsp', 'path_type': 'upload'},
                {'url': 'https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader.php', 'path_type': 'upload'},
                {'url': 'https://www.goldendewclub.com:443/sample/photo_uploader/file_uploader_html5.jsp', 'path_type': 'upload'},
                {'url': 'https://www.goldendewclub.com:443/sample/photo_uploader/photo_uploader.html', 'path_type': 'info'},
                {'url': 'https://www.goldendewclub.com:443/photo_uploader/popup/file_uploader.jsp', 'path_type': 'upload'}
            ]
        },
        {
            'target': 'www.goldendewclub.com:443',
            'editor_type': 'cheditor',
            'detection_method': 'directory_fuzzing',
            'base_path': 'https://www.goldendewclub.com:443',
            'core_files_found': [
                {'url': 'https://www.goldendewclub.com:443/cheditor.js', 'status': 'valid'},
                {'url': 'https://www.goldendewclub.com:443/cheditor.css', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://www.goldendewclub.com:443/imageUpload/upload.jsp', 'path_type': 'upload'},
                {'url': 'https://www.goldendewclub.com:443/imageUpload/delete.jsp', 'path_type': 'delete'},
                {'url': 'https://www.goldendewclub.com:443/imageUpload/upload-simple.jsp', 'path_type': 'upload'},
                {'url': 'https://www.goldendewclub.com:443/imageUpload/config.jsp', 'path_type': 'info'}
            ]
        },
        {
            'target': 'www.goldendewclub.com:443',
            'editor_type': 'ckeditor',
            'detection_method': 'directory_fuzzing',
            'base_path': 'https://www.goldendewclub.com:443',
            'core_files_found': [
                {'url': 'https://www.goldendewclub.com:443/ckeditor.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://www.goldendewclub.com:443/_samples/sample_posteddata.php', 'path_type': 'info'},
                {'url': 'https://www.goldendewclub.com:443/_samples/assets/_posteddata.php', 'path_type': 'info'},
                {'url': 'https://www.goldendewclub.com:443/samples/sample_posteddata.php', 'path_type': 'info'}
            ]
        },
        {
            'target': 'www.goldendewclub.com:443',
            'editor_type': 'ckfinder',
            'detection_method': 'directory_fuzzing',
            'base_path': 'https://www.goldendewclub.com:443',
            'core_files_found': [
                {'url': 'https://www.goldendewclub.com:443/ckfinder.js', 'status': 'valid'}
            ],
            'auxiliary_paths': [
                {'url': 'https://www.goldendewclub.com:443/core/connector/php/connector.php', 'path_type': 'rce'},
                {'url': 'https://www.goldendewclub.com:443/core/connector/connector', 'path_type': 'rce'},
                {'url': 'https://www.goldendewclub.com:443/plugins/fileeditor/plugin.js', 'path_type': 'rce'},
                {'url': 'https://www.goldendewclub.com:443/ckfinder.html', 'path_type': 'info'}
            ]
        }
    ]
    
    print("\n[*] 测试 goldendewclub.com 的合法性:")
    is_legitimate = scanner.verify_target_legitimacy('www.goldendewclub.com:443', goldendew_results)
    print(f"  结果: {'合法' if is_legitimate else '可疑'}")

def main():
    print("蜜罐检测功能测试")
    print("=" * 60)
    
    try:
        # 测试蜜罐检测
        test_honeypot_detection()
        
        # 测试目标合法性验证
        test_target_legitimacy()
        
        print("\n" + "=" * 60)
        print("测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

﻿using System;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x02000030 RID: 48
	public class UploadStatus : Base
	{
		// Token: 0x060002C2 RID: 706 RVA: 0x0002115F File Offset: 0x0001F35F
		public UploadStatus(HttpContext context) : base(context)
		{
		}

		// Token: 0x060002C3 RID: 707 RVA: 0x00021168 File Offset: 0x0001F368
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string text = this.hContext.Request["g"];
			if (!string.IsNullOrEmpty(text) && this.hContext.Application[text] != null)
			{
				UploadStatus uploadStatus = (UploadStatus)this.hContext.Application[text];
				string s = Dext5Parameter.MakeParameter(uploadStatus.Message);
				this.hContext.Response.Clear();
				this.hContext.Response.Write(s);
				if (uploadStatus.Message.IndexOf("error") > -1 || uploadStatus.Message.IndexOf("success") > -1)
				{
					this.hContext.Application.Remove(text);
				}
			}
			return null;
		}
	}
}

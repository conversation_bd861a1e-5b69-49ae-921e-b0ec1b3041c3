﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using Raonwiz.Dext5.Common.Entity;
using Raonwiz.Dext5.Common.Exif;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x0200000F RID: 15
	public class Dext5Image : IDisposable
	{
		// Token: 0x17000043 RID: 67
		// (get) Token: 0x060000E8 RID: 232 RVA: 0x000105CD File Offset: 0x0000E7CD
		// (set) Token: 0x060000E7 RID: 231 RVA: 0x000105C4 File Offset: 0x0000E7C4
		public long JpegQuality
		{
			get
			{
				return this._jpegQuality;
			}
			set
			{
				this._jpegQuality = value;
			}
		}

		// Token: 0x060000EA RID: 234 RVA: 0x000105F1 File Offset: 0x0000E7F1
		public void Dispose()
		{
			GC.SuppressFinalize(this);
		}

		// Token: 0x060000EB RID: 235 RVA: 0x000105FC File Offset: 0x0000E7FC
		public string MakeThumbnail(string pSourceFileFullPath, string pSuffix, int pNewWidth, int pNewHeight, bool pDeleteSourceFile)
		{
			string result = string.Empty;
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string text = string.Empty;
					if (pDeleteSourceFile && string.IsNullOrEmpty(pSuffix))
					{
						int num = pSourceFileFullPath.LastIndexOf('.');
						text = pSourceFileFullPath.Substring(0, num);
						text = text + "_" + Guid.NewGuid().ToString();
						text += pSourceFileFullPath.Substring(num);
					}
					else
					{
						int num2 = pSourceFileFullPath.LastIndexOf('.');
						text = pSourceFileFullPath.Substring(0, num2);
						text += pSuffix;
						text += pSourceFileFullPath.Substring(num2);
					}
					Size imageNewSize = this.GetImageNewSize(pSourceFileFullPath, pNewWidth, pNewHeight, false);
					string pConvertFormat = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					if (this.ImageResize(pSourceFileFullPath, text, imageNewSize, pConvertFormat))
					{
						if (pDeleteSourceFile)
						{
							File.Delete(pSourceFileFullPath);
						}
						if (pDeleteSourceFile && string.IsNullOrEmpty(pSuffix))
						{
							File.Move(text, pSourceFileFullPath);
						}
						result = text;
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
			return result;
		}

		// Token: 0x060000EC RID: 236 RVA: 0x00010714 File Offset: 0x0000E914
		public string MakeThumbnailEX(string pSourceFileFullPath, string pTargetFileFullPath, int pNewWidth, int pNewHeight, bool pDeleteSourceFile)
		{
			string result = string.Empty;
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				string pConvertFormat = Path.GetExtension(pTargetFileFullPath).Replace(".", "").ToLower();
				try
				{
					Size imageNewSize = this.GetImageNewSize(pSourceFileFullPath, pNewWidth, pNewHeight, true);
					if (this.ImageResize(pSourceFileFullPath, pTargetFileFullPath, imageNewSize, pConvertFormat))
					{
						if (pDeleteSourceFile)
						{
							File.Delete(pSourceFileFullPath);
						}
						result = pTargetFileFullPath;
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
			return result;
		}

		// Token: 0x060000ED RID: 237 RVA: 0x00010794 File Offset: 0x0000E994
		public string ConvertImageFormat(string pSourceFileFullPath, string pSuffix, Dext5ImageFormat pNewFormat, bool pSkipSameFormat, bool pDeleteSourceFile)
		{
			string result = string.Empty;
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string text = pNewFormat.ToString();
					string text2 = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					string text3 = string.Empty;
					if (pSkipSameFormat && text2.ToLower().Equals(text))
					{
						return pSourceFileFullPath;
					}
					if (pDeleteSourceFile && string.IsNullOrEmpty(pSuffix))
					{
						int length = pSourceFileFullPath.LastIndexOf('.');
						text3 = pSourceFileFullPath.Substring(0, length);
						text3 = text3 + "_" + Guid.NewGuid().ToString();
						text3 = text3 + "." + text;
					}
					else
					{
						int length2 = pSourceFileFullPath.LastIndexOf('.');
						text3 = pSourceFileFullPath.Substring(0, length2);
						text3 += pSuffix;
						text3 = text3 + "." + text;
					}
					Size imageNewSize = this.GetImageNewSize(pSourceFileFullPath, 0, 0, false);
					if (this.ImageResize(pSourceFileFullPath, text3, imageNewSize, text))
					{
						if (pDeleteSourceFile)
						{
							File.Delete(pSourceFileFullPath);
						}
						if (pDeleteSourceFile && string.IsNullOrEmpty(pSuffix))
						{
							File.Move(text3, pSourceFileFullPath);
						}
						result = text3;
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
				return result;
			}
			return result;
		}

		// Token: 0x060000EE RID: 238 RVA: 0x000108D4 File Offset: 0x0000EAD4
		public void ConvertImageSize(string pSourceFileFullPath, int pNewWidth, int pNewHeight)
		{
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string text = string.Empty;
					int num = pSourceFileFullPath.LastIndexOf('.');
					text = pSourceFileFullPath.Substring(0, num);
					text = text + "_" + Guid.NewGuid().ToString();
					text += pSourceFileFullPath.Substring(num);
					string pConvertFormat = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					Size imageNewSize = this.GetImageNewSize(pSourceFileFullPath, pNewWidth, pNewHeight, true);
					if (this.ImageResize(pSourceFileFullPath, text, imageNewSize, pConvertFormat))
					{
						File.Delete(pSourceFileFullPath);
						File.Move(text, pSourceFileFullPath);
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
		}

		// Token: 0x060000EF RID: 239 RVA: 0x00010998 File Offset: 0x0000EB98
		public void ConvertImageSizeByPercent(string pSourceFileFullPath, float pPercent)
		{
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string text = string.Empty;
					int num = pSourceFileFullPath.LastIndexOf('.');
					text = pSourceFileFullPath.Substring(0, num);
					text = text + "_" + Guid.NewGuid().ToString();
					text += pSourceFileFullPath.Substring(num);
					string pConvertFormat = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					Size imageNewSizeByPercent = this.GetImageNewSizeByPercent(pSourceFileFullPath, pPercent);
					if (this.ImageResize(pSourceFileFullPath, text, imageNewSizeByPercent, pConvertFormat))
					{
						File.Delete(pSourceFileFullPath);
						File.Move(text, pSourceFileFullPath);
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
		}

		// Token: 0x060000F0 RID: 240 RVA: 0x00010A5C File Offset: 0x0000EC5C
		public void Rotate(string pSourceFileFullPath, int pDegree)
		{
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					Bitmap bitmap = new Bitmap(pSourceFileFullPath);
					RotateFlipType rotateFlipType = RotateFlipType.RotateNoneFlipNone;
					if (bitmap == null)
					{
						throw new Exception(string.Format("[{0}] File Not Found", pSourceFileFullPath));
					}
					if (pDegree <= 90)
					{
						if (pDegree != 0)
						{
							if (pDegree != 90)
							{
								goto IL_52;
							}
							rotateFlipType = RotateFlipType.Rotate90FlipNone;
							goto IL_52;
						}
					}
					else
					{
						if (pDegree == 180)
						{
							rotateFlipType = RotateFlipType.Rotate180FlipNone;
							goto IL_52;
						}
						if (pDegree == 270)
						{
							rotateFlipType = RotateFlipType.Rotate270FlipNone;
							goto IL_52;
						}
						if (pDegree != 360)
						{
							goto IL_52;
						}
					}
					rotateFlipType = RotateFlipType.RotateNoneFlipNone;
					IL_52:
					bitmap.RotateFlip(rotateFlipType);
					this.ImageReSave(pSourceFileFullPath, ref bitmap);
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
		}

		// Token: 0x060000F1 RID: 241 RVA: 0x00010B00 File Offset: 0x0000ED00
		public void SetImageWaterMark(string pSourceFileFullPath, string pWaterMarkFileFullPath, string pVAlignFrom, int pVMargin, string pHAlignFrom, int pHMargin, float pTransparency)
		{
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					Size imageSize = this.GetImageSize(pSourceFileFullPath);
					Bitmap image = new Bitmap(pSourceFileFullPath);
					Image image2 = Image.FromFile(pWaterMarkFileFullPath);
					Graphics graphics = Graphics.FromImage(image);
					graphics.CompositingQuality = CompositingQuality.HighQuality;
					graphics.SmoothingMode = SmoothingMode.HighQuality;
					if (image2 != null)
					{
						graphics.DrawImage(image, 0, 0);
						if (pTransparency > 100f)
						{
							pTransparency = 100f;
						}
						pTransparency = 100f - pTransparency;
						ImageAttributes imageAttributes = new ImageAttributes();
						imageAttributes.SetColorMatrix(new ColorMatrix
						{
							Matrix00 = 1f,
							Matrix11 = 1f,
							Matrix22 = 1f,
							Matrix33 = pTransparency / 100f,
							Matrix44 = 1f
						});
						int width = image2.Width;
						int height = image2.Height;
						pVAlignFrom = pVAlignFrom.ToUpper();
						pHAlignFrom = pHAlignFrom.ToUpper();
						if (pVAlignFrom == "BOTTOM")
						{
							pVMargin = imageSize.Height - pVMargin - height;
						}
						else if (pVAlignFrom == "MIDDLE")
						{
							pVMargin = imageSize.Height / 2 - height / 2;
						}
						if (pHAlignFrom == "RIGHT")
						{
							pHMargin = imageSize.Width - pHMargin - width;
						}
						else if (pHAlignFrom == "CENTER")
						{
							pHMargin = imageSize.Width / 2 - width / 2;
						}
						Rectangle destRect = new Rectangle(pHMargin, pVMargin, width, height);
						graphics.DrawImage(image2, destRect, 0, 0, image2.Width, image2.Height, GraphicsUnit.Pixel, imageAttributes);
					}
					graphics.Dispose();
					image2.Dispose();
					this.ImageReSave(pSourceFileFullPath, ref image);
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
		}

		// Token: 0x060000F2 RID: 242 RVA: 0x00010CCC File Offset: 0x0000EECC
		public void SetTextWaterMark(string pSourceFileFullPath, TextWaterMark pTextWaterMark, string pVAlignFrom, int pVMargin, string pHAlignFrom, int pHMargin, int pTransparency, int pDegree)
		{
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					Size imageSize = this.GetImageSize(pSourceFileFullPath);
					Bitmap image = new Bitmap(pSourceFileFullPath);
					Graphics graphics = Graphics.FromImage(image);
					graphics.CompositingQuality = CompositingQuality.HighQuality;
					graphics.SmoothingMode = SmoothingMode.AntiAlias;
					if (pTextWaterMark.WaterMarkText.Length > 0)
					{
						SizeF sizeF = default(SizeF);
						if (pTextWaterMark.FontSize <= 0)
						{
							pTextWaterMark.FontSize = 12;
						}
						Font font = new Font(pTextWaterMark.FontName, (float)pTextWaterMark.FontSize, FontStyle.Bold);
						sizeF = graphics.MeasureString(pTextWaterMark.WaterMarkText, font);
						if (pTransparency > 100)
						{
							pTransparency = 100;
						}
						pTransparency = 255 - 255 * pTransparency / 100;
						int blue;
						int red;
						int green = red = (blue = 0);
						if (pTextWaterMark.FontColor.Length == 7 || pTextWaterMark.FontColor.IndexOf("#") == 0)
						{
							pTextWaterMark.FontColor = pTextWaterMark.FontColor.Substring(1);
						}
						if (pTextWaterMark.FontColor.Length == 6)
						{
							red = this.HexToInt(pTextWaterMark.FontColor.Substring(0, 2), 0);
							green = this.HexToInt(pTextWaterMark.FontColor.Substring(2, 2), 0);
							blue = this.HexToInt(pTextWaterMark.FontColor.Substring(4, 2), 0);
						}
						SolidBrush brush = new SolidBrush(Color.FromArgb(pTransparency, red, green, blue));
						if (pVAlignFrom == "BOTTOM")
						{
							pVMargin = imageSize.Height - pVMargin - (int)sizeF.Height;
						}
						else if (pVAlignFrom == "MIDDLE")
						{
							pVMargin = imageSize.Height / 2 - (int)sizeF.Height / 2;
						}
						if (pHAlignFrom == "RIGHT")
						{
							pHMargin = imageSize.Width - pHMargin - (int)sizeF.Width;
						}
						else if (pHAlignFrom == "CENTER")
						{
							pHMargin = imageSize.Width / 2 - (int)sizeF.Width / 2;
						}
						StringFormat stringFormat = new StringFormat();
						stringFormat.Alignment = StringAlignment.Near;
						if (pDegree != 0)
						{
							stringFormat.Alignment = StringAlignment.Center;
							stringFormat.LineAlignment = StringAlignment.Center;
							double num = (double)pDegree;
							int num2 = pDegree / 90;
							if (pDegree == 45 || pDegree == 45 + num2 * 90)
							{
								if (num2 % 2 == 0)
								{
									num = Math.Atan((double)imageSize.Height / (double)imageSize.Width) * 57.29577951308232 + (double)(num2 * 90);
								}
								else
								{
									num = (double)(num2 * 90 + 90) - Math.Atan((double)imageSize.Height / (double)imageSize.Width) * 57.29577951308232;
								}
							}
							graphics.SmoothingMode = SmoothingMode.AntiAlias;
							graphics.TranslateTransform((float)imageSize.Width / 2f, (float)imageSize.Height / 2f);
							graphics.RotateTransform((float)num);
							graphics.DrawString(pTextWaterMark.WaterMarkText, font, brush, new Point(0, 0), stringFormat);
							graphics.ResetTransform();
						}
						else
						{
							graphics.DrawString(pTextWaterMark.WaterMarkText, font, brush, new PointF((float)pHMargin, (float)pVMargin), stringFormat);
						}
					}
					graphics.Dispose();
					this.ImageReSave(pSourceFileFullPath, ref image);
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
		}

		// Token: 0x060000F3 RID: 243 RVA: 0x00010FFC File Offset: 0x0000F1FC
		public Size GetImageSize(string pSourceFileFullPath)
		{
			Size size = new Size(0, 0);
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					if (File.Exists(pSourceFileFullPath))
					{
						Bitmap bitmap = new Bitmap(pSourceFileFullPath);
						size = bitmap.Size;
						bitmap.Dispose();
						return size;
					}
					throw new Exception(string.Format("[{0}] File Not Found", pSourceFileFullPath));
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
				return size;
			}
			return size;
		}

		// Token: 0x060000F4 RID: 244 RVA: 0x00011074 File Offset: 0x0000F274
		private ExifTagCollection GetExifCollectionData(string pSourceFileFullPath)
		{
			ExifTagCollection result = null;
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string a = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					if (a == "jpg" || a == "jpeg")
					{
						result = new ExifTagCollection(pSourceFileFullPath);
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
			return result;
		}

		// Token: 0x060000F5 RID: 245 RVA: 0x000110F0 File Offset: 0x0000F2F0
		public ExifEntity GetExifEntityData(string pSourceFileFullPath)
		{
			ExifEntity exifEntity = new ExifEntity();
			if (this.CheckImageFormat(Path.GetExtension(pSourceFileFullPath)))
			{
				try
				{
					string a = Path.GetExtension(pSourceFileFullPath).Replace(".", "").ToLower();
					if (a == "jpg" || a == "jpeg")
					{
						ExifTagCollection exifTagCollection = new ExifTagCollection(pSourceFileFullPath);
						exifEntity.ImageWidth = exifTagCollection[256].Value;
						exifEntity.ImageHeight = exifTagCollection[257].Value;
						exifEntity.GPSVersionID = exifTagCollection[0].Value;
						exifEntity.GPSAltitudeRef = exifTagCollection[5].Value;
						exifEntity.StripOffsets = exifTagCollection[273].Value;
						exifEntity.RowsPerStrip = exifTagCollection[278].Value;
						exifEntity.StripByteCounts = exifTagCollection[279].Value;
						exifEntity.PixelXDimension = exifTagCollection[40962].Value;
						exifEntity.PixelYDimension = exifTagCollection[40963].Value;
						exifEntity.BitsPerSample = exifTagCollection[258].Value;
						exifEntity.Compression = exifTagCollection[259].Value;
						exifEntity.PhotometricInterpretation = exifTagCollection[262].Value;
						exifEntity.Orientation = exifTagCollection[274].Value;
						exifEntity.SamplesPerPixel = exifTagCollection[277].Value;
						exifEntity.PlanarConfiguration = exifTagCollection[284].Value;
						exifEntity.YCbCrSubSampling = exifTagCollection[530].Value;
						exifEntity.YCbCrPositioning = exifTagCollection[531].Value;
						exifEntity.ResolutionUnit = exifTagCollection[296].Value;
						exifEntity.TransferFunction = exifTagCollection[301].Value;
						exifEntity.ColorSpace = exifTagCollection[40961].Value;
						exifEntity.ExposureProgram = exifTagCollection[34850].Value;
						exifEntity.ISOSpeedRatings = exifTagCollection[34855].Value;
						exifEntity.MeteringMode = exifTagCollection[37383].Value;
						exifEntity.LightSource = exifTagCollection[37384].Value;
						exifEntity.Flash = exifTagCollection[37385].Value;
						exifEntity.SubjectArea = exifTagCollection[37396].Value;
						exifEntity.FocalPlaneResolutionUnit = exifTagCollection[41488].Value;
						exifEntity.SubjectLocation = exifTagCollection[41492].Value;
						exifEntity.SensingMethod = exifTagCollection[41495].Value;
						exifEntity.CustomRendered = exifTagCollection[41985].Value;
						exifEntity.ExposureMode = exifTagCollection[41986].Value;
						exifEntity.WhiteBalance = exifTagCollection[41987].Value;
						exifEntity.FocalLengthIn35mmFilm = exifTagCollection[41989].Value;
						exifEntity.SceneCaptureType = exifTagCollection[41990].Value;
						exifEntity.Contrast = exifTagCollection[41992].Value;
						exifEntity.Saturation = exifTagCollection[41993].Value;
						exifEntity.Sharpness = exifTagCollection[41994].Value;
						exifEntity.SubjectDistanceRange = exifTagCollection[41996].Value;
						exifEntity.GPSDifferential = exifTagCollection[30].Value;
						exifEntity.ShutterSpeedValue = exifTagCollection[37377].Value;
						exifEntity.BrightnessValue = exifTagCollection[37379].Value;
						exifEntity.ExposureBiasValue = exifTagCollection[37380].Value;
						exifEntity.JPEGInterchangeFormat = exifTagCollection[513].Value;
						exifEntity.JPEGInterchangeFormatLength = exifTagCollection[514].Value;
						exifEntity.XResolution = exifTagCollection[282].Value;
						exifEntity.YResolution = exifTagCollection[283].Value;
						exifEntity.WhitePoint = exifTagCollection[318].Value;
						exifEntity.PrimaryChromaticities = exifTagCollection[319].Value;
						exifEntity.YCbCrCoefficients = exifTagCollection[529].Value;
						exifEntity.ReferenceBlackWhite = exifTagCollection[532].Value;
						exifEntity.CompressedBitsPerPixel = exifTagCollection[37122].Value;
						exifEntity.ExposureTime = exifTagCollection[33434].Value;
						exifEntity.FNumber = exifTagCollection[33437].Value;
						exifEntity.ApertureValue = exifTagCollection[37378].Value;
						exifEntity.MaxApertureValue = exifTagCollection[37381].Value;
						exifEntity.SubjectDistance = exifTagCollection[37382].Value;
						exifEntity.FocalLength = exifTagCollection[37386].Value;
						exifEntity.FlashEnergy = exifTagCollection[41483].Value;
						exifEntity.FocalPlaneXResolution = exifTagCollection[41486].Value;
						exifEntity.FocalPlaneYResolution = exifTagCollection[41487].Value;
						exifEntity.ExposureIndex = exifTagCollection[41493].Value;
						exifEntity.DigitalZoomRatio = exifTagCollection[41988].Value;
						exifEntity.GainControl = exifTagCollection[41991].Value;
						exifEntity.GPSLatitude = exifTagCollection[2].Value;
						exifEntity.GPSLongitude = exifTagCollection[4].Value;
						exifEntity.GPSAltitude = exifTagCollection[6].Value;
						exifEntity.GPSTimeStamp = exifTagCollection[7].Value;
						exifEntity.GPSDOP = exifTagCollection[11].Value;
						exifEntity.GPSSpeed = exifTagCollection[13].Value;
						exifEntity.GPSTrack = exifTagCollection[15].Value;
						exifEntity.GPSImgDirection = exifTagCollection[17].Value;
						exifEntity.GPSDestLatitude = exifTagCollection[20].Value;
						exifEntity.GPSDestLongitude = exifTagCollection[22].Value;
						exifEntity.GPSDestBearing = exifTagCollection[24].Value;
						exifEntity.GPSDestDistance = exifTagCollection[26].Value;
						exifEntity.DateTime = exifTagCollection[306].Value;
						exifEntity.ImageDescription = exifTagCollection[270].Value;
						exifEntity.Make = exifTagCollection[271].Value;
						exifEntity.Model = exifTagCollection[272].Value;
						exifEntity.Software = exifTagCollection[305].Value;
						exifEntity.Artist = exifTagCollection[315].Value;
						exifEntity.Copyright = exifTagCollection[33432].Value;
						exifEntity.RelatedSoundFile = exifTagCollection[40964].Value;
						exifEntity.DateTimeOriginal = exifTagCollection[36867].Value;
						exifEntity.DateTimeDigitized = exifTagCollection[36868].Value;
						exifEntity.SubSecTime = exifTagCollection[37520].Value;
						exifEntity.SubSecTimeOriginal = exifTagCollection[37521].Value;
						exifEntity.SubSecTimeDigitized = exifTagCollection[37522].Value;
						exifEntity.ImageUniqueID = exifTagCollection[42016].Value;
						exifEntity.SpectralSensitivity = exifTagCollection[34852].Value;
						exifEntity.GPSLatitudeRef = exifTagCollection[1].Value;
						exifEntity.GPSLongitudeRef = exifTagCollection[3].Value;
						exifEntity.GPSSatellites = exifTagCollection[8].Value;
						exifEntity.GPSStatus = exifTagCollection[9].Value;
						exifEntity.GPSMeasureMode = exifTagCollection[10].Value;
						exifEntity.GPSSpeedRef = exifTagCollection[12].Value;
						exifEntity.GPSTrackRef = exifTagCollection[14].Value;
						exifEntity.GPSImgDirectionRef = exifTagCollection[16].Value;
						exifEntity.GPSMapDatum = exifTagCollection[18].Value;
						exifEntity.GPSDestLatitudeRef = exifTagCollection[19].Value;
						exifEntity.GPSDestLongitudeRef = exifTagCollection[21].Value;
						exifEntity.GPSDestBearingRef = exifTagCollection[23].Value;
						exifEntity.GPSDestDistanceRef = exifTagCollection[25].Value;
						exifEntity.GPSDateStamp = exifTagCollection[29].Value;
						exifEntity.OECF = exifTagCollection[34856].Value;
						exifEntity.SpatialFrequencyResponse = exifTagCollection[41484].Value;
						exifEntity.FileSource = exifTagCollection[41728].Value;
						exifEntity.SceneType = exifTagCollection[41729].Value;
						exifEntity.CFAPattern = exifTagCollection[41730].Value;
						exifEntity.DeviceSettingDescription = exifTagCollection[41995].Value;
						exifEntity.ExifVersion = exifTagCollection[36864].Value;
						exifEntity.FlashpixVersion = exifTagCollection[40960].Value;
						exifEntity.ComponentsConfiguration = exifTagCollection[37121].Value;
						exifEntity.MakerNote = exifTagCollection[37500].Value;
						exifEntity.UserComment = exifTagCollection[37510].Value;
						exifEntity.GPSProcessingMethod = exifTagCollection[27].Value;
						exifEntity.GPSAreaInformation = exifTagCollection[28].Value;
					}
				}
				catch (Exception ex)
				{
					throw new Exception(ex.Message);
				}
			}
			return exifEntity;
		}

		// Token: 0x060000F6 RID: 246 RVA: 0x00011B28 File Offset: 0x0000FD28
		private bool CheckImageFormat(string fileExt)
		{
			bool result = false;
			try
			{
				string[] names = Enum.GetNames(typeof(Dext5ImageFormat));
				fileExt = fileExt.ToLower().Replace(".", "");
				int num = Array.IndexOf<string>(names, fileExt);
				if (num > -1)
				{
					result = true;
				}
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
			return result;
		}

		// Token: 0x060000F7 RID: 247 RVA: 0x00011B8C File Offset: 0x0000FD8C
		private int HexToInt(string input_value, int _defalutValue)
		{
			int result = _defalutValue;
			if (input_value != null && input_value.Length > 0)
			{
				try
				{
					int num = Convert.ToInt32(input_value, 16);
					result = num;
				}
				catch (Exception)
				{
				}
			}
			return result;
		}

		// Token: 0x060000F8 RID: 248 RVA: 0x00011BC8 File Offset: 0x0000FDC8
		private void ImageReSave(string pSourceFileFullPath, ref Bitmap bmp)
		{
			try
			{
				Image image = Image.FromFile(pSourceFileFullPath);
				ImageFormat rawFormat = image.RawFormat;
				image.Dispose();
				int num = pSourceFileFullPath.LastIndexOf('.');
				string text = pSourceFileFullPath.Substring(0, num);
				text = text + "_" + Guid.NewGuid().ToString();
				text += pSourceFileFullPath.Substring(num);
				if (rawFormat.Equals(ImageFormat.Jpeg))
				{
					ImageCodecInfo encoderInfo = this.GetEncoderInfo("image/jpeg");
					EncoderParameters encoderParameters = new EncoderParameters(1);
					encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, this.JpegQuality);
					bmp.Save(text, encoderInfo, encoderParameters);
				}
				else
				{
					bmp.Save(text, rawFormat);
				}
				bmp.Dispose();
				File.Delete(pSourceFileFullPath);
				File.Move(text, pSourceFileFullPath);
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
		}

		// Token: 0x060000F9 RID: 249 RVA: 0x00011CB0 File Offset: 0x0000FEB0
		private Size GetImageNewSize(string pSourceFileFullPath, int pNewWidth, int pNewHeight, bool isSizeOver)
		{
			Size result = new Size(0, 0);
			try
			{
				using (Image image = Image.FromFile(pSourceFileFullPath))
				{
					if (image == null)
					{
						throw new Exception(string.Format("[{0}] is not image file", pSourceFileFullPath));
					}
					int width = image.Size.Width;
					int height = image.Size.Height;
					if (pNewWidth + pNewHeight == 0)
					{
						result.Width = width;
						result.Height = height;
						return result;
					}
					if (isSizeOver)
					{
						if (pNewWidth == 0)
						{
							pNewWidth = (int)((float)width * (float)((double)pNewHeight / ((double)height * 1.0)));
						}
						if (pNewHeight == 0)
						{
							pNewHeight = (int)((float)height * (float)((double)pNewWidth / ((double)width * 1.0)));
						}
					}
					else
					{
						if (pNewWidth == 0 || pNewWidth > width)
						{
							if (pNewWidth == 0 && pNewHeight > 0 && pNewHeight < width)
							{
								pNewWidth = (int)((float)width * (float)((double)pNewHeight / ((double)height * 1.0)));
							}
							else
							{
								pNewWidth = width;
							}
						}
						if (pNewHeight == 0 || pNewHeight > height)
						{
							if (pNewHeight == 0 && pNewWidth > 0 && pNewWidth < width)
							{
								pNewHeight = (int)((float)height * (float)((double)pNewWidth / ((double)width * 1.0)));
							}
							else
							{
								pNewHeight = height;
							}
						}
					}
					result.Width = pNewWidth;
					result.Height = pNewHeight;
				}
			}
			catch (FileNotFoundException)
			{
				throw new Exception(string.Format("[{0}] File Not Found", pSourceFileFullPath));
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
			return result;
		}

		// Token: 0x060000FA RID: 250 RVA: 0x00011E44 File Offset: 0x00010044
		private Size GetImageNewSizeByPercent(string pSourceFileFullPath, float pPercent)
		{
			Size result = new Size(0, 0);
			if ((double)pPercent < 0.1)
			{
				pPercent = 0.1f;
			}
			try
			{
				if (!File.Exists(pSourceFileFullPath))
				{
					throw new Exception(string.Format("[{0}] File Not Found", pSourceFileFullPath));
				}
				Bitmap bitmap = new Bitmap(pSourceFileFullPath);
				result.Width = (int)((float)bitmap.Size.Width * pPercent / 100f);
				result.Height = (int)((float)bitmap.Size.Height * pPercent / 100f);
				bitmap.Dispose();
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
			return result;
		}

		// Token: 0x060000FB RID: 251 RVA: 0x00011EF8 File Offset: 0x000100F8
		private bool ImageResize(string pSourceFileFullPath, string pTargetFileFullPath, Size pNewSize, string pConvertFormat)
		{
			bool result = false;
			if (pNewSize.Width == 0 || pNewSize.Height == 0)
			{
				return false;
			}
			try
			{
				int length = pTargetFileFullPath.LastIndexOf(this.m_PathChar);
				string path = pTargetFileFullPath.Substring(0, length);
				if (!Directory.Exists(path))
				{
					Directory.CreateDirectory(path);
				}
				using (Image image = Image.FromFile(pSourceFileFullPath))
				{
					if (image == null)
					{
						throw new Exception(string.Format("[0] is not image file", pSourceFileFullPath));
					}
					Bitmap bitmap = new Bitmap(image, pNewSize.Width, pNewSize.Height);
					if (pConvertFormat.ToLower().Equals("png"))
					{
						bitmap.Save(pTargetFileFullPath, ImageFormat.Png);
					}
					else
					{
						ImageCodecInfo encoderInfo = this.GetEncoderInfo("image/jpeg");
						EncoderParameters encoderParameters = new EncoderParameters(1);
						encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, this.JpegQuality);
						bitmap.Save(pTargetFileFullPath, encoderInfo, encoderParameters);
					}
					bitmap.Dispose();
					result = true;
				}
			}
			catch (FileNotFoundException)
			{
				throw new Exception(string.Format("[0] File not found.", pSourceFileFullPath));
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
			return result;
		}

		// Token: 0x060000FC RID: 252 RVA: 0x00012030 File Offset: 0x00010230
		private ImageCodecInfo GetEncoderInfo(string mimeType)
		{
			foreach (ImageCodecInfo imageCodecInfo in ImageCodecInfo.GetImageEncoders())
			{
				if (imageCodecInfo.MimeType == mimeType)
				{
					return imageCodecInfo;
				}
			}
			return null;
		}

		// Token: 0x0400008C RID: 140
		private char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x0400008D RID: 141
		private long _jpegQuality = 100L;
	}
}

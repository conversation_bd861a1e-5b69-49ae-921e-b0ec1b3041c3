package org.apache.struts.taglib.nested;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.BodyTagSupport;
import org.apache.struts.util.ResponseUtils;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/NestedWriteNestingTag.class */
public class NestedWriteNestingTag extends BodyTagSupport {
    private boolean filter = false;
    private String property = null;
    private String originalProperty = null;

    public String getProperty() {
        return this.property;
    }

    public void setProperty(String newProperty) {
        this.property = newProperty;
    }

    public boolean getFilter() {
        return this.filter;
    }

    public void setFilter(boolean newFilter) {
        this.filter = newFilter;
    }

    public int doStartTag() throws JspException {
        this.originalProperty = this.property;
        HttpServletRequest request = this.pageContext.getRequest();
        String nesting = NestedPropertyHelper.getAdjustedProperty(request, this.property);
        if (this.filter) {
            ResponseUtils.write(this.pageContext, ResponseUtils.filter(nesting));
            return 0;
        }
        ResponseUtils.write(this.pageContext, nesting);
        return 0;
    }

    public int doEndTag() throws JspException {
        int i = super.doEndTag();
        this.property = this.originalProperty;
        return i;
    }

    public void release() {
        super.release();
        this.filter = false;
        this.property = null;
        this.originalProperty = null;
    }
}

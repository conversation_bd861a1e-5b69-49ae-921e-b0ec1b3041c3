package jcifs.netbios;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.Socket;
import jcifs.Config;
import jcifs.util.LogStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NbtSocket.class */
public class NbtSocket extends Socket {
    private static final int SSN_SRVC_PORT = 139;
    private static final int BUFFER_SIZE = 512;
    private static final int DEFAULT_SO_TIMEOUT = 5000;
    private static LogStream log = LogStream.getInstance();
    private NbtAddress address;
    private Name calledName;
    private int soTimeout;

    public NbtSocket() {
    }

    public NbtSocket(NbtAddress address, int port) throws IOException {
        this(address, port, null, 0);
    }

    public NbtSocket(NbtAddress address, int port, InetAddress localAddr, int localPort) throws IOException {
        this(address, null, port, localAddr, localPort);
    }

    public NbtSocket(NbtAddress address, String calledName, int port, InetAddress localAddr, int localPort) throws IOException {
        super(address.getInetAddress(), port == 0 ? SSN_SRVC_PORT : port, localAddr, localPort);
        this.address = address;
        if (calledName == null) {
            this.calledName = address.hostName;
        } else {
            this.calledName = new Name(calledName, 32, null);
        }
        this.soTimeout = Config.getInt("jcifs.netbios.soTimeout", DEFAULT_SO_TIMEOUT);
        connect();
    }

    public NbtAddress getNbtAddress() {
        return this.address;
    }

    @Override // java.net.Socket
    public InputStream getInputStream() throws IOException {
        return new SocketInputStream(super.getInputStream());
    }

    @Override // java.net.Socket
    public OutputStream getOutputStream() throws IOException {
        return new SocketOutputStream(super.getOutputStream());
    }

    @Override // java.net.Socket
    public int getPort() {
        return super.getPort();
    }

    @Override // java.net.Socket
    public InetAddress getLocalAddress() {
        return super.getLocalAddress();
    }

    @Override // java.net.Socket
    public int getLocalPort() {
        return super.getLocalPort();
    }

    @Override // java.net.Socket
    public String toString() {
        return "NbtSocket[addr=" + this.address + ",port=" + super.getPort() + ",localport=" + super.getLocalPort() + "]";
    }

    private void connect() throws IOException {
        byte[] buffer = new byte[512];
        try {
            InputStream in = super.getInputStream();
            OutputStream out = super.getOutputStream();
            SessionServicePacket ssp0 = new SessionRequestPacket(this.calledName, NbtAddress.localhost.hostName);
            out.write(buffer, 0, ssp0.writeWireFormat(buffer, 0));
            setSoTimeout(this.soTimeout);
            int type = SessionServicePacket.readPacketType(in, buffer, 0);
            switch (type) {
                case -1:
                    throw new NbtException(2, -1);
                case 130:
                    LogStream logStream = log;
                    if (LogStream.level > 2) {
                        log.println("session established ok with " + this.address);
                        return;
                    }
                    return;
                case 131:
                    int errorCode = in.read() & 255;
                    close();
                    throw new NbtException(2, errorCode);
                default:
                    close();
                    throw new NbtException(2, 0);
            }
        } catch (IOException ioe) {
            close();
            throw ioe;
        }
    }

    @Override // java.net.Socket, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        LogStream logStream = log;
        if (LogStream.level > 3) {
            log.println("close: " + this);
        }
        super.close();
    }
}

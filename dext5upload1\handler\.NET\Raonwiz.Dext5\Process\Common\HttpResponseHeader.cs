﻿using System;
using System.Text;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000024 RID: 36
	internal class HttpResponseHeader
	{
		// Token: 0x170000C6 RID: 198
		// (get) Token: 0x0600024D RID: 589 RVA: 0x0001D633 File Offset: 0x0001B833
		// (set) Token: 0x0600024E RID: 590 RVA: 0x0001D63B File Offset: 0x0001B83B
		public string AcceptRanges { get; set; }

		// Token: 0x170000C7 RID: 199
		// (get) Token: 0x0600024F RID: 591 RVA: 0x0001D644 File Offset: 0x0001B844
		// (set) Token: 0x06000250 RID: 592 RVA: 0x0001D64C File Offset: 0x0001B84C
		public string Connection { get; set; }

		// Token: 0x170000C8 RID: 200
		// (get) Token: 0x06000251 RID: 593 RVA: 0x0001D655 File Offset: 0x0001B855
		// (set) Token: 0x06000252 RID: 594 RVA: 0x0001D65D File Offset: 0x0001B85D
		public string ContentDisposition { get; set; }

		// Token: 0x170000C9 RID: 201
		// (get) Token: 0x06000253 RID: 595 RVA: 0x0001D666 File Offset: 0x0001B866
		// (set) Token: 0x06000254 RID: 596 RVA: 0x0001D66E File Offset: 0x0001B86E
		public Encoding ContentEncoding { get; set; }

		// Token: 0x170000CA RID: 202
		// (get) Token: 0x06000255 RID: 597 RVA: 0x0001D677 File Offset: 0x0001B877
		// (set) Token: 0x06000256 RID: 598 RVA: 0x0001D67F File Offset: 0x0001B87F
		public string ContentLength { get; set; }

		// Token: 0x170000CB RID: 203
		// (get) Token: 0x06000257 RID: 599 RVA: 0x0001D688 File Offset: 0x0001B888
		// (set) Token: 0x06000258 RID: 600 RVA: 0x0001D690 File Offset: 0x0001B890
		public string ContentRange { get; set; }

		// Token: 0x170000CC RID: 204
		// (get) Token: 0x06000259 RID: 601 RVA: 0x0001D699 File Offset: 0x0001B899
		// (set) Token: 0x0600025A RID: 602 RVA: 0x0001D6A1 File Offset: 0x0001B8A1
		public string ContentType { get; set; }

		// Token: 0x170000CD RID: 205
		// (get) Token: 0x0600025B RID: 603 RVA: 0x0001D6AA File Offset: 0x0001B8AA
		// (set) Token: 0x0600025C RID: 604 RVA: 0x0001D6B2 File Offset: 0x0001B8B2
		public string Etag { get; set; }

		// Token: 0x170000CE RID: 206
		// (get) Token: 0x0600025D RID: 605 RVA: 0x0001D6BB File Offset: 0x0001B8BB
		// (set) Token: 0x0600025E RID: 606 RVA: 0x0001D6C3 File Offset: 0x0001B8C3
		public string LastModified { get; set; }
	}
}

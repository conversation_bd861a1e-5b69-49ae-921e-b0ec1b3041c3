package jcifs.smb;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NoRouteToHostException;
import java.net.Socket;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.ListIterator;
import jcifs.UniAddress;
import jcifs.util.Encdec;
import jcifs.util.Hexdump;
import jcifs.util.LogStream;
import jcifs.util.transport.Request;
import jcifs.util.transport.Response;
import jcifs.util.transport.Transport;
import jcifs.util.transport.TransportException;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbTransport.class */
public class SmbTransport extends Transport implements SmbConstants {
    static final byte[] BUF = new byte[InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH];
    static final SmbComNegotiate NEGOTIATE_REQUEST = new SmbComNegotiate();
    static LogStream log = LogStream.getInstance();
    static HashMap dfsRoots = null;
    InetAddress localAddr;
    int localPort;
    UniAddress address;
    Socket socket;
    int port;
    int mid;
    OutputStream out;
    InputStream in;
    byte[] sbuf = new byte[512];
    SmbComBlankResponse key = new SmbComBlankResponse();
    long sessionExpiration = System.currentTimeMillis() + SO_TIMEOUT;
    LinkedList referrals = new LinkedList();
    SigningDigest digest = null;
    LinkedList sessions = new LinkedList();
    ServerData server = new ServerData();
    int flags2 = FLAGS2;
    int maxMpxCount = MAX_MPX_COUNT;
    int snd_buf_size = SND_BUF_SIZE;
    int rcv_buf_size = RCV_BUF_SIZE;
    int capabilities = CAPABILITIES;
    int sessionKey = 0;
    boolean useUnicode = USE_UNICODE;
    String tconHostName = null;

    static synchronized SmbTransport getSmbTransport(UniAddress address, int port) {
        return getSmbTransport(address, port, LADDR, LPORT, null);
    }

    static synchronized SmbTransport getSmbTransport(UniAddress address, int port, InetAddress localAddr, int localPort, String hostName) {
        synchronized (CONNECTIONS) {
            if (SSN_LIMIT != 1) {
                ListIterator iter = CONNECTIONS.listIterator();
                while (iter.hasNext()) {
                    SmbTransport conn = (SmbTransport) iter.next();
                    if (conn.matches(address, port, localAddr, localPort, hostName) && (SSN_LIMIT == 0 || conn.sessions.size() < SSN_LIMIT)) {
                        return conn;
                    }
                }
            }
            SmbTransport conn2 = new SmbTransport(address, port, localAddr, localPort);
            CONNECTIONS.add(0, conn2);
            return conn2;
        }
    }

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbTransport$ServerData.class */
    class ServerData {
        byte flags;
        int flags2;
        int maxMpxCount;
        int maxBufferSize;
        int sessionKey;
        int capabilities;
        String oemDomainName;
        int securityMode;
        int security;
        boolean encryptedPasswords;
        boolean signaturesEnabled;
        boolean signaturesRequired;
        int maxNumberVcs;
        int maxRawSize;
        long serverTime;
        int serverTimeZone;
        int encryptionKeyLength;
        byte[] encryptionKey;
        byte[] guid;

        ServerData() {
        }
    }

    SmbTransport(UniAddress address, int port, InetAddress localAddr, int localPort) {
        this.address = address;
        this.port = port;
        this.localAddr = localAddr;
        this.localPort = localPort;
    }

    synchronized SmbSession getSmbSession() {
        return getSmbSession(new NtlmPasswordAuthentication(null, null, null));
    }

    synchronized SmbSession getSmbSession(NtlmPasswordAuthentication auth) {
        ListIterator iter = this.sessions.listIterator();
        while (iter.hasNext()) {
            SmbSession ssn = (SmbSession) iter.next();
            if (ssn.matches(auth)) {
                ssn.auth = auth;
                return ssn;
            }
        }
        if (SO_TIMEOUT > 0) {
            long j = this.sessionExpiration;
            long now = System.currentTimeMillis();
            if (j < now) {
                this.sessionExpiration = now + SO_TIMEOUT;
                ListIterator iter2 = this.sessions.listIterator();
                while (iter2.hasNext()) {
                    SmbSession ssn2 = (SmbSession) iter2.next();
                    if (ssn2.expiration < now) {
                        ssn2.logoff(false);
                    }
                }
            }
        }
        SmbSession ssn3 = new SmbSession(this.address, this.port, this.localAddr, this.localPort, auth);
        ssn3.transport = this;
        this.sessions.add(ssn3);
        return ssn3;
    }

    boolean matches(UniAddress address, int port, InetAddress localAddr, int localPort, String hostName) {
        if (hostName == null) {
            hostName = address.getHostName();
        }
        return (this.tconHostName == null || hostName.equalsIgnoreCase(this.tconHostName)) && address.equals(this.address) && (port == 0 || port == this.port || (port == 445 && this.port == 139)) && ((localAddr == this.localAddr || (localAddr != null && localAddr.equals(this.localAddr))) && localPort == this.localPort);
    }

    boolean hasCapability(int cap) throws SmbException {
        try {
            connect(RESPONSE_TIMEOUT);
            return (this.capabilities & cap) == cap;
        } catch (IOException ioe) {
            throw new SmbException(ioe.getMessage(), ioe);
        }
    }

    boolean isSignatureSetupRequired(NtlmPasswordAuthentication auth) {
        return ((this.flags2 & 4) == 0 || this.digest != null || auth == NtlmPasswordAuthentication.NULL || NtlmPasswordAuthentication.NULL.equals(auth)) ? false : true;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    void ssn139() throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 412
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: jcifs.smb.SmbTransport.ssn139():void");
    }

    private void negotiate(int port, ServerMessageBlock resp) throws IOException {
        synchronized (this.sbuf) {
            if (port == 139) {
                ssn139();
            } else {
                if (port == 0) {
                    port = 445;
                }
                this.socket = new Socket();
                if (this.localAddr != null) {
                    this.socket.bind(new InetSocketAddress(this.localAddr, this.localPort));
                }
                this.socket.connect(new InetSocketAddress(this.address.getHostAddress(), port), CONN_TIMEOUT);
                this.socket.setSoTimeout(SO_TIMEOUT);
                this.out = this.socket.getOutputStream();
                this.in = this.socket.getInputStream();
            }
            int i = this.mid + 1;
            this.mid = i;
            if (i == 32000) {
                this.mid = 1;
            }
            NEGOTIATE_REQUEST.mid = this.mid;
            int n = NEGOTIATE_REQUEST.encode(this.sbuf, 4);
            Encdec.enc_uint32be(n & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH, this.sbuf, 0);
            LogStream logStream = log;
            if (LogStream.level >= 4) {
                log.println(NEGOTIATE_REQUEST);
                LogStream logStream2 = log;
                if (LogStream.level >= 6) {
                    Hexdump.hexdump(log, this.sbuf, 4, n);
                }
            }
            this.out.write(this.sbuf, 0, 4 + n);
            this.out.flush();
            if (peekKey() == null) {
                throw new IOException("transport closed in negotiate");
            }
            int size = Encdec.dec_uint16be(this.sbuf, 2) & 65535;
            if (size < 33 || 4 + size > this.sbuf.length) {
                throw new IOException("Invalid payload size: " + size);
            }
            readn(this.in, this.sbuf, 36, size - 32);
            resp.decode(this.sbuf, 4);
            LogStream logStream3 = log;
            if (LogStream.level >= 4) {
                log.println(resp);
                LogStream logStream4 = log;
                if (LogStream.level >= 6) {
                    Hexdump.hexdump(log, this.sbuf, 4, n);
                }
            }
        }
    }

    public void connect() throws SmbException {
        try {
            super.connect(RESPONSE_TIMEOUT);
        } catch (TransportException te) {
            throw new SmbException("Failed to connect: " + this.address, te);
        }
    }

    @Override // jcifs.util.transport.Transport
    protected void doConnect() throws IOException {
        SmbComNegotiateResponse resp = new SmbComNegotiateResponse(this.server);
        try {
            negotiate(this.port, resp);
        } catch (ConnectException e) {
            this.port = (this.port == 0 || this.port == 445) ? 139 : SmbConstants.DEFAULT_PORT;
            negotiate(this.port, resp);
        } catch (NoRouteToHostException e2) {
            this.port = (this.port == 0 || this.port == 445) ? 139 : SmbConstants.DEFAULT_PORT;
            negotiate(this.port, resp);
        }
        if (resp.dialectIndex > 10) {
            throw new SmbException("This client does not support the negotiated dialect.");
        }
        if ((this.server.capabilities & Integer.MIN_VALUE) != Integer.MIN_VALUE && this.server.encryptionKeyLength != 8 && LM_COMPATIBILITY == 0) {
            throw new SmbException("Unexpected encryption key length: " + this.server.encryptionKeyLength);
        }
        this.tconHostName = this.address.getHostName();
        if (this.server.signaturesRequired || (this.server.signaturesEnabled && SIGNPREF)) {
            this.flags2 |= 4;
        } else {
            this.flags2 &= 65531;
        }
        this.maxMpxCount = Math.min(this.maxMpxCount, this.server.maxMpxCount);
        if (this.maxMpxCount < 1) {
            this.maxMpxCount = 1;
        }
        this.snd_buf_size = Math.min(this.snd_buf_size, this.server.maxBufferSize);
        this.capabilities &= this.server.capabilities;
        if ((this.server.capabilities & Integer.MIN_VALUE) == Integer.MIN_VALUE) {
            this.capabilities |= Integer.MIN_VALUE;
        }
        if ((this.capabilities & 4) == 0) {
            if (FORCE_UNICODE) {
                this.capabilities |= 4;
            } else {
                this.useUnicode = false;
                this.flags2 &= 32767;
            }
        }
    }

    @Override // jcifs.util.transport.Transport
    protected void doDisconnect(boolean hard) throws IOException {
        ListIterator iter = this.sessions.listIterator();
        while (iter.hasNext()) {
            try {
                SmbSession ssn = (SmbSession) iter.next();
                ssn.logoff(hard);
            } catch (Throwable th) {
                this.digest = null;
                this.socket = null;
                this.tconHostName = null;
                throw th;
            }
        }
        this.socket.shutdownOutput();
        this.out.close();
        this.in.close();
        this.socket.close();
        this.digest = null;
        this.socket = null;
        this.tconHostName = null;
    }

    @Override // jcifs.util.transport.Transport
    protected void makeKey(Request request) throws IOException {
        int i = this.mid + 1;
        this.mid = i;
        if (i == 32000) {
            this.mid = 1;
        }
        ((ServerMessageBlock) request).mid = this.mid;
    }

    @Override // jcifs.util.transport.Transport
    protected Request peekKey() throws IOException {
        while (readn(this.in, this.sbuf, 0, 4) >= 4) {
            if (this.sbuf[0] != -123) {
                if (readn(this.in, this.sbuf, 4, 32) < 32) {
                    return null;
                }
                LogStream logStream = log;
                if (LogStream.level >= 4) {
                    log.println("New data read: " + this);
                    Hexdump.hexdump(log, this.sbuf, 4, 32);
                }
                while (true) {
                    if (this.sbuf[0] != 0 || this.sbuf[1] != 0 || this.sbuf[4] != -1 || this.sbuf[5] != 83 || this.sbuf[6] != 77 || this.sbuf[7] != 66) {
                        for (int i = 0; i < 35; i++) {
                            this.sbuf[i] = this.sbuf[i + 1];
                        }
                        int b = this.in.read();
                        if (b == -1) {
                            return null;
                        }
                        this.sbuf[35] = (byte) b;
                    } else {
                        this.key.mid = Encdec.dec_uint16le(this.sbuf, 34) & 65535;
                        return this.key;
                    }
                }
            }
        }
        return null;
    }

    @Override // jcifs.util.transport.Transport
    protected void doSend(Request request) throws IOException {
        ServerMessageBlock serverMessageBlock;
        synchronized (BUF) {
            ServerMessageBlock smb = (ServerMessageBlock) request;
            int n = smb.encode(BUF, 4);
            Encdec.enc_uint32be(n & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH, BUF, 0);
            LogStream logStream = log;
            if (LogStream.level >= 4) {
                do {
                    log.println(smb);
                    if (!(smb instanceof AndXServerMessageBlock)) {
                        break;
                    }
                    serverMessageBlock = ((AndXServerMessageBlock) smb).andx;
                    smb = serverMessageBlock;
                } while (serverMessageBlock != null);
                LogStream logStream2 = log;
                if (LogStream.level >= 6) {
                    Hexdump.hexdump(log, BUF, 4, n);
                }
            }
            this.out.write(BUF, 0, 4 + n);
        }
    }

    protected void doSend0(Request request) throws IOException {
        try {
            doSend(request);
        } catch (IOException ioe) {
            LogStream logStream = log;
            if (LogStream.level > 2) {
                ioe.printStackTrace(log);
            }
            try {
                disconnect(true);
            } catch (IOException ioe2) {
                ioe2.printStackTrace(log);
            }
            throw ioe;
        }
    }

    @Override // jcifs.util.transport.Transport
    protected void doRecv(Response response) throws IOException {
        ServerMessageBlock resp = (ServerMessageBlock) response;
        resp.useUnicode = this.useUnicode;
        resp.extendedSecurity = (this.capabilities & Integer.MIN_VALUE) == Integer.MIN_VALUE;
        synchronized (BUF) {
            System.arraycopy(this.sbuf, 0, BUF, 0, 36);
            int size = Encdec.dec_uint16be(BUF, 2) & 65535;
            if (size < 33 || 4 + size > this.rcv_buf_size) {
                throw new IOException("Invalid payload size: " + size);
            }
            int errorCode = Encdec.dec_uint32le(BUF, 9) & (-1);
            if (resp.command == 46 && (errorCode == 0 || errorCode == -2147483643)) {
                SmbComReadAndXResponse r = (SmbComReadAndXResponse) resp;
                readn(this.in, BUF, 4 + 32, 27);
                int off = 32 + 27;
                resp.decode(BUF, 4);
                int pad = r.dataOffset - off;
                if (r.byteCount > 0 && pad > 0 && pad < 4) {
                    readn(this.in, BUF, 4 + off, pad);
                }
                if (r.dataLength > 0) {
                    readn(this.in, r.b, r.off, r.dataLength);
                }
            } else {
                readn(this.in, BUF, 36, size - 32);
                resp.decode(BUF, 4);
                if (resp instanceof SmbComTransactionResponse) {
                    ((SmbComTransactionResponse) resp).nextElement();
                }
            }
            if (this.digest != null && resp.errorCode == 0) {
                this.digest.verify(BUF, 4, resp);
            }
            LogStream logStream = log;
            if (LogStream.level >= 4) {
                log.println(response);
                LogStream logStream2 = log;
                if (LogStream.level >= 6) {
                    Hexdump.hexdump(log, BUF, 4, size);
                }
            }
        }
    }

    @Override // jcifs.util.transport.Transport
    protected void doSkip() throws IOException {
        int size = Encdec.dec_uint16be(this.sbuf, 2) & 65535;
        if (size < 33 || 4 + size > this.rcv_buf_size) {
            this.in.skip(this.in.available());
        } else {
            this.in.skip(size - 32);
        }
    }

    void checkStatus(ServerMessageBlock req, ServerMessageBlock resp) throws SmbException {
        resp.errorCode = SmbException.getStatusByCode(resp.errorCode);
        switch (resp.errorCode) {
            case -2147483643:
            case NtStatus.NT_STATUS_MORE_PROCESSING_REQUIRED /* -********** */:
            case 0:
                if (resp.verifyFailed) {
                    throw new SmbException("Signature verification failed.");
                }
                return;
            case NtStatus.NT_STATUS_ACCESS_DENIED /* -********** */:
            case NtStatus.NT_STATUS_WRONG_PASSWORD /* -********** */:
            case NtStatus.NT_STATUS_LOGON_FAILURE /* -********** */:
            case NtStatus.NT_STATUS_ACCOUNT_RESTRICTION /* -********** */:
            case NtStatus.NT_STATUS_INVALID_LOGON_HOURS /* -********** */:
            case NtStatus.NT_STATUS_INVALID_WORKSTATION /* -********** */:
            case NtStatus.NT_STATUS_PASSWORD_EXPIRED /* -********** */:
            case NtStatus.NT_STATUS_ACCOUNT_DISABLED /* -********** */:
            case NtStatus.NT_STATUS_TRUSTED_DOMAIN_FAILURE /* -********** */:
            case NtStatus.NT_STATUS_ACCOUNT_LOCKED_OUT /* -********** */:
                throw new SmbAuthException(resp.errorCode);
            case NtStatus.NT_STATUS_PATH_NOT_COVERED /* -********** */:
                if (req.auth == null) {
                    throw new SmbException(resp.errorCode, (Throwable) null);
                }
                DfsReferral dr = getDfsReferrals(req.auth, req.path, 1);
                if (dr == null) {
                    throw new SmbException(resp.errorCode, (Throwable) null);
                }
                SmbFile.dfs.insert(req.path, dr);
                throw dr;
            default:
                throw new SmbException(resp.errorCode, (Throwable) null);
        }
    }

    /* JADX WARN: Finally extract failed */
    void send(ServerMessageBlock request, ServerMessageBlock response) throws SmbException {
        connect();
        request.flags2 |= this.flags2;
        request.useUnicode = this.useUnicode;
        request.response = response;
        if (request.digest == null) {
            request.digest = this.digest;
        }
        try {
            if (response == null) {
                doSend0(request);
                return;
            }
            if (request instanceof SmbComTransaction) {
                response.command = request.command;
                SmbComTransaction req = (SmbComTransaction) request;
                SmbComTransactionResponse resp = (SmbComTransactionResponse) response;
                req.maxBufferSize = this.snd_buf_size;
                resp.reset();
                try {
                    BufferCache.getBuffers(req, resp);
                    req.nextElement();
                    if (req.hasMoreElements()) {
                        SmbComBlankResponse interim = new SmbComBlankResponse();
                        super.sendrecv(req, interim, RESPONSE_TIMEOUT);
                        if (interim.errorCode != 0) {
                            checkStatus(req, interim);
                        }
                        req.nextElement();
                    } else {
                        makeKey(req);
                    }
                    synchronized (this) {
                        response.received = false;
                        resp.isReceived = false;
                        try {
                            try {
                                this.response_map.put(req, resp);
                                do {
                                    doSend0(req);
                                    if (!req.hasMoreElements()) {
                                        break;
                                    }
                                } while (req.nextElement() != null);
                                long timeout = RESPONSE_TIMEOUT;
                                resp.expiration = System.currentTimeMillis() + timeout;
                                while (resp.hasMoreElements()) {
                                    wait(timeout);
                                    timeout = resp.expiration - System.currentTimeMillis();
                                    if (timeout <= 0) {
                                        throw new TransportException(this + " timedout waiting for response to " + req);
                                    }
                                }
                                if (response.errorCode != 0) {
                                    checkStatus(req, resp);
                                }
                                this.response_map.remove(req);
                            } catch (InterruptedException ie) {
                                throw new TransportException(ie);
                            }
                        } catch (Throwable th) {
                            this.response_map.remove(req);
                            throw th;
                        }
                    }
                    BufferCache.releaseBuffer(req.txn_buf);
                    BufferCache.releaseBuffer(resp.txn_buf);
                } catch (Throwable th2) {
                    BufferCache.releaseBuffer(req.txn_buf);
                    BufferCache.releaseBuffer(resp.txn_buf);
                    throw th2;
                }
            } else {
                response.command = request.command;
                super.sendrecv(request, response, RESPONSE_TIMEOUT);
            }
            checkStatus(request, response);
        } catch (SmbException se) {
            throw se;
        } catch (IOException ioe) {
            throw new SmbException(ioe.getMessage(), ioe);
        }
    }

    @Override // jcifs.util.transport.Transport
    public String toString() {
        return super.toString() + "[" + this.address + ":" + this.port + "]";
    }

    void dfsPathSplit(String path, String[] result) {
        int ri = 0;
        int rlast = result.length - 1;
        int i = 0;
        int b = 0;
        int len = path.length();
        while (ri != rlast) {
            if (i == len || path.charAt(i) == '\\') {
                int i2 = ri;
                ri++;
                result[i2] = path.substring(b, i);
                b = i + 1;
            }
            int i3 = i;
            i++;
            if (i3 >= len) {
                while (ri < result.length) {
                    int i4 = ri;
                    ri++;
                    result[i4] = "";
                }
                return;
            }
        }
        result[rlast] = path.substring(b);
    }

    DfsReferral getDfsReferrals(NtlmPasswordAuthentication auth, String path, int rn) throws SmbException {
        SmbTree ipc = getSmbSession(auth).getSmbTree("IPC$", null);
        Trans2GetDfsReferralResponse resp = new Trans2GetDfsReferralResponse();
        ipc.send(new Trans2GetDfsReferral(path), resp);
        if (resp.numReferrals == 0) {
            return null;
        }
        if (rn == 0 || resp.numReferrals < rn) {
            rn = resp.numReferrals;
        }
        DfsReferral dr = new DfsReferral();
        String[] arr = new String[4];
        long expiration = System.currentTimeMillis() + (Dfs.TTL * 1000);
        int di = 0;
        while (true) {
            dr.resolveHashes = auth.hashesExternal;
            dr.ttl = resp.referrals[di].ttl;
            dr.expiration = expiration;
            if (path.equals("")) {
                dr.server = resp.referrals[di].path.substring(1).toLowerCase();
            } else {
                dfsPathSplit(resp.referrals[di].node, arr);
                dr.server = arr[1];
                dr.share = arr[2];
                dr.path = arr[3];
            }
            dr.pathConsumed = resp.pathConsumed;
            di++;
            if (di != rn) {
                dr.append(new DfsReferral());
                dr = dr.next;
            } else {
                return dr.next;
            }
        }
    }

    DfsReferral[] __getDfsReferrals(NtlmPasswordAuthentication auth, String path, int rn) throws SmbException {
        SmbTree ipc = getSmbSession(auth).getSmbTree("IPC$", null);
        Trans2GetDfsReferralResponse resp = new Trans2GetDfsReferralResponse();
        ipc.send(new Trans2GetDfsReferral(path), resp);
        if (rn == 0 || resp.numReferrals < rn) {
            rn = resp.numReferrals;
        }
        DfsReferral[] drs = new DfsReferral[rn];
        String[] arr = new String[4];
        long expiration = System.currentTimeMillis() + (Dfs.TTL * 1000);
        for (int di = 0; di < drs.length; di++) {
            DfsReferral dr = new DfsReferral();
            dr.resolveHashes = auth.hashesExternal;
            dr.ttl = resp.referrals[di].ttl;
            dr.expiration = expiration;
            if (path.equals("")) {
                dr.server = resp.referrals[di].path.substring(1).toLowerCase();
            } else {
                dfsPathSplit(resp.referrals[di].node, arr);
                dr.server = arr[1];
                dr.share = arr[2];
                dr.path = arr[3];
            }
            dr.pathConsumed = resp.pathConsumed;
            drs[di] = dr;
        }
        return drs;
    }
}

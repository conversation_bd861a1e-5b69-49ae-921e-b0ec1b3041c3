package jcifs.dcerpc;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFileInputStream;
import jcifs.smb.SmbFileOutputStream;
import jcifs.smb.SmbNamedPipe;
import jcifs.util.Encdec;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/DcerpcPipeHandle.class */
public class DcerpcPipeHandle extends DcerpcHandle {
    SmbNamedPipe pipe;
    SmbFileInputStream in = null;
    SmbFileOutputStream out = null;
    boolean isStart = true;

    public DcerpcPipeHandle(String url, NtlmPasswordAuthentication auth) throws UnknownHostException, MalformedURLException, DcerpcException {
        String params;
        this.binding = DcerpcHandle.parseBinding(url);
        String url2 = "smb://" + this.binding.server + "/IPC$/" + this.binding.endpoint.substring(6);
        params = "";
        String server = (String) this.binding.getOption("server");
        params = server != null ? params + "&server=" + server : "";
        String address = (String) this.binding.getOption("address");
        params = server != null ? params + "&address=" + address : params;
        this.pipe = new SmbNamedPipe(params.length() > 0 ? url2 + "?" + params.substring(1) : url2, 27198979, auth);
    }

    @Override // jcifs.dcerpc.DcerpcHandle
    protected void doSendFragment(byte[] buf, int off, int length, boolean isDirect) throws IOException {
        if (this.out != null && !this.out.isOpen()) {
            throw new IOException("DCERPC pipe is no longer open");
        }
        if (this.in == null) {
            this.in = (SmbFileInputStream) this.pipe.getNamedPipeInputStream();
        }
        if (this.out == null) {
            this.out = (SmbFileOutputStream) this.pipe.getNamedPipeOutputStream();
        }
        if (isDirect) {
            this.out.writeDirect(buf, off, length, 1);
        } else {
            this.out.write(buf, off, length);
        }
    }

    @Override // jcifs.dcerpc.DcerpcHandle
    protected void doReceiveFragment(byte[] buf, boolean isDirect) throws IOException {
        int off;
        if (buf.length < this.max_recv) {
            throw new IllegalArgumentException("buffer too small");
        }
        if (this.isStart && !isDirect) {
            off = this.in.read(buf, 0, 1024);
        } else {
            off = this.in.readDirect(buf, 0, buf.length);
        }
        if (buf[0] != 5 && buf[1] != 0) {
            throw new IOException("Unexpected DCERPC PDU header");
        }
        int flags = buf[3] & 255;
        this.isStart = (flags & 2) == 2;
        int length = Encdec.dec_uint16le(buf, 8);
        if (length > this.max_recv) {
            throw new IOException("Unexpected fragment length: " + length);
        }
        while (off < length) {
            off += this.in.readDirect(buf, off, length - off);
        }
    }

    @Override // jcifs.dcerpc.DcerpcHandle
    public void close() throws IOException {
        this.state = 0;
        if (this.out != null) {
            this.out.close();
        }
    }
}

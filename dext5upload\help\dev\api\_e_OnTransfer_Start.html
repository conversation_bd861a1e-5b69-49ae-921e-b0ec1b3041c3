﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: OnTransfer_Start</h3>
    <p class="ttl">boolean DEXT5UPLOAD_OnTransfer_Start(uploadID)</p>
    <p class="txt">
        파일 전송 시작 전 발생합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        <span class="firebrick">boolean</span>&nbsp;&nbsp;true일 경우 전송 시작하고, false일 경우 전송하지 않습니다.
    </p> 
    <p class="mttl01">parameters</p>
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;전송이 시작된 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>
    <p class="txt">
        없음.
    </p>   
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;script type="text/javascript"&#62;
        DEXT5UPLOAD_OnTransfer_Start(uploadID) {
            // 업로드 시작 후 처리할 내용

            return true or false;
        }
&#60;/script&#62;

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


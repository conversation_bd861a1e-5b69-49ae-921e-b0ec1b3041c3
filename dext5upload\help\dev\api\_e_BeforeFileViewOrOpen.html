﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: BeforeFileViewOrOpen</h3>
    <p class="ttl">boolean DEXT5UPLOAD_BeforeFileViewOrOpen(uploadID, strWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strLargeFile)</p>
    <p class="txt">
        파일 열기 전 발생합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        <span class="firebrick">boolean</span>&nbsp;&nbsp;true일 경우 파일을 열기하고, false일 경우 열기하지 않습니다. 
    </p> 
    <p class="mttl01">parameters</p>
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;파일 열기 요청한 업로드의 id를 의미합니다.<br/>
        <span class="firebrick">strWebFile</span>&nbsp;&nbsp;Web파일과 Local 파일의 구분 값을 의미합니다. (0:local, 1:web)<br />
        <span class="firebrick">strItemKey</span>&nbsp;&nbsp;파일의 Unique Key를 의미합니다.<br />
        <span class="firebrick">strItemOrgName</span>&nbsp;&nbsp;파일의 원본 파일명을 의미합니다.<br />
        <span class="firebrick">strItemUrlOrPath</span>&nbsp;&nbsp;파일의 저장위치를 의미합니다.<br />
        <span class="firebrick">strLargeFile</span>&nbsp;&nbsp;파일 대용량를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>
    <p class="txt">
        DEXT5 Upload 설정 config.xml에서 use_view_or_open_event 값이 1일 경우 발생합니다. 
    </p>   
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;script type="text/javascript"&#62;
        DEXT5UPLOAD_BeforeFileViewOrOpen(uploadID, strWebFile, strItemKey, strItemOrgName, strItemUrlOrPath, strLargeFile) {
            // 파일 열기전 처리할 내용

            return true or false;
        }
&#60;/script&#62;

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿using System;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x0200000B RID: 11
	[Serializable]
	public class Dext5CustomError
	{
		// Token: 0x17000040 RID: 64
		// (get) Token: 0x060000DA RID: 218 RVA: 0x0000E04E File Offset: 0x0000C24E
		public string ErrorCode
		{
			get
			{
				return this._code;
			}
		}

		// Token: 0x17000041 RID: 65
		// (get) Token: 0x060000DB RID: 219 RVA: 0x0000E056 File Offset: 0x0000C256
		public string ErrorMessage
		{
			get
			{
				return this._message.Replace("|", "");
			}
		}

		// Token: 0x17000042 RID: 66
		// (get) Token: 0x060000DC RID: 220 RVA: 0x0000E06D File Offset: 0x0000C26D
		public bool ClientConfirmMessage
		{
			get
			{
				return this._clientConfirm;
			}
		}

		// Token: 0x060000DD RID: 221 RVA: 0x0000E075 File Offset: 0x0000C275
		public Dext5CustomError()
		{
			this._code = "-1";
			this._message = "사용자 에러입니다.";
			this._clientConfirm = false;
		}

		// Token: 0x060000DE RID: 222 RVA: 0x0000E0B0 File Offset: 0x0000C2B0
		public Dext5CustomError(string message)
		{
			this._code = "999";
			this._message = message;
			this._clientConfirm = false;
		}

		// Token: 0x060000DF RID: 223 RVA: 0x0000E0E7 File Offset: 0x0000C2E7
		public Dext5CustomError(string code, string message)
		{
			this._code = code;
			this._message = message;
			this._clientConfirm = false;
		}

		// Token: 0x04000081 RID: 129
		private string _code = string.Empty;

		// Token: 0x04000082 RID: 130
		private string _message = string.Empty;

		// Token: 0x04000083 RID: 131
		private bool _clientConfirm;
	}
}

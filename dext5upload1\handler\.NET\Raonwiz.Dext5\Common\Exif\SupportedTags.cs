﻿using System;
using System.Collections;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000017 RID: 23
	internal sealed class SupportedTags : Hashtable
	{
		// Token: 0x06000211 RID: 529 RVA: 0x000146B4 File Offset: 0x000128B4
		public SupportedTags()
		{
			this.Add(256, new ExifTag(256, "ImageWidth", "Image width"));
			this.Add(257, new ExifTag(257, "ImageHeight", "Image height"));
			this.Add(0, new ExifTag(0, "GPSVersionID", "GPS tag version"));
			this.Add(5, new ExifTag(5, "GPSAltitudeRef", "Altitude reference"));
			this.Add(273, new ExifTag(273, "StripOffsets", "Image data location"));
			this.Add(278, new ExifTag(278, "RowsPerStrip", "Number of rows per strip"));
			this.Add(279, new ExifTag(279, "StripByteCounts", "Bytes per compressed strip"));
			this.Add(40962, new ExifTag(40962, "PixelXDimension", "Valid image width"));
			this.Add(40963, new ExifTag(40963, "PixelYDimension", "Valid image height"));
			this.Add(258, new ExifTag(258, "BitsPerSample", "Number of bits per component"));
			this.Add(259, new ExifTag(259, "Compression", "Compression scheme"));
			this.Add(262, new ExifTag(262, "PhotometricInterpretation", "Pixel composition"));
			this.Add(274, new ExifTag(274, "Orientation", "Orientation of image"));
			this.Add(277, new ExifTag(277, "SamplesPerPixel", "Number of components"));
			this.Add(284, new ExifTag(284, "PlanarConfiguration", "Image data arrangement"));
			this.Add(530, new ExifTag(530, "YCbCrSubSampling", "Subsampling ratio of Y to C"));
			this.Add(531, new ExifTag(531, "YCbCrPositioning", "Y and C positioning"));
			this.Add(296, new ExifTag(296, "ResolutionUnit", "Unit of X and Y resolution"));
			this.Add(301, new ExifTag(301, "TransferFunction", "Transfer function"));
			this.Add(40961, new ExifTag(40961, "ColorSpace", "Color space information"));
			this.Add(34850, new ExifTag(34850, "ExposureProgram", "Exposure program"));
			this.Add(34855, new ExifTag(34855, "ISOSpeedRatings", "ISO speed rating"));
			this.Add(37383, new ExifTag(37383, "MeteringMode", "Metering mode"));
			this.Add(37384, new ExifTag(37384, "LightSource", "Light source"));
			this.Add(37385, new ExifTag(37385, "Flash", "Flash"));
			this.Add(37396, new ExifTag(37396, "SubjectArea", "Subject area"));
			this.Add(41488, new ExifTag(41488, "FocalPlaneResolutionUnit", "Focal plane resolution unit"));
			this.Add(41492, new ExifTag(41492, "SubjectLocation", "Subject location"));
			this.Add(41495, new ExifTag(41495, "SensingMethod", "Sensing method"));
			this.Add(41985, new ExifTag(41985, "CustomRendered", "Custom image processing"));
			this.Add(41986, new ExifTag(41986, "ExposureMode", "Exposure mode"));
			this.Add(41987, new ExifTag(41987, "WhiteBalance", "White balance"));
			this.Add(41989, new ExifTag(41989, "FocalLengthIn35mmFilm", "Focal length in 35 mm film"));
			this.Add(41990, new ExifTag(41990, "SceneCaptureType", "Scene capture type"));
			this.Add(41992, new ExifTag(41992, "Contrast", "Contrast"));
			this.Add(41993, new ExifTag(41993, "Saturation", "Saturation"));
			this.Add(41994, new ExifTag(41994, "Sharpness", "Sharpness"));
			this.Add(41996, new ExifTag(41996, "SubjectDistanceRange", "Subject distance range"));
			this.Add(30, new ExifTag(30, "GPSDifferential", "GPS differential correction"));
			this.Add(37377, new ExifTag(37377, "ShutterSpeedValue", "Shutter speed"));
			this.Add(37379, new ExifTag(37379, "BrightnessValue", "Brightness"));
			this.Add(37380, new ExifTag(37380, "ExposureBiasValue", "Exposure bias"));
			this.Add(513, new ExifTag(513, "JPEGInterchangeFormat", "Offset to JPEG SOI"));
			this.Add(514, new ExifTag(514, "JPEGInterchangeFormatLength", "Bytes of JPEG data"));
			this.Add(282, new ExifTag(282, "XResolution", "Image resolution in width direction"));
			this.Add(283, new ExifTag(283, "YResolution", "Image resolution in height direction"));
			this.Add(318, new ExifTag(318, "WhitePoint", "White point chromaticity"));
			this.Add(319, new ExifTag(319, "PrimaryChromaticities", "Chromaticities of primaries"));
			this.Add(529, new ExifTag(529, "YCbCrCoefficients", "Color space transformation matrix coefficients"));
			this.Add(532, new ExifTag(532, "ReferenceBlackWhite", "Pair of black and white reference values"));
			this.Add(37122, new ExifTag(37122, "CompressedBitsPerPixel", "Image compression mode"));
			this.Add(33434, new ExifTag(33434, "ExposureTime", "Exposure time"));
			this.Add(33437, new ExifTag(33437, "FNumber", "F number"));
			this.Add(37378, new ExifTag(37378, "ApertureValue", "Aperture"));
			this.Add(37381, new ExifTag(37381, "MaxApertureValue", "Maximum lens aperture"));
			this.Add(37382, new ExifTag(37382, "SubjectDistance", "Subject distance"));
			this.Add(37386, new ExifTag(37386, "FocalLength", "Lens focal length"));
			this.Add(41483, new ExifTag(41483, "FlashEnergy", "Flash energy"));
			this.Add(41486, new ExifTag(41486, "FocalPlaneXResolution", "Focal plane X resolution"));
			this.Add(41487, new ExifTag(41487, "FocalPlaneYResolution", "Focal plane Y resolution"));
			this.Add(41493, new ExifTag(41493, "ExposureIndex", "Exposure index"));
			this.Add(41988, new ExifTag(41988, "DigitalZoomRatio", "Digital zoom ratio"));
			this.Add(41991, new ExifTag(41991, "GainControl", "Gain control"));
			this.Add(2, new ExifTag(2, "GPSLatitude", "Latitude"));
			this.Add(4, new ExifTag(4, "GPSLongitude", "Longitude"));
			this.Add(6, new ExifTag(6, "GPSAltitude", "Altitude"));
			this.Add(7, new ExifTag(7, "GPSTimeStamp", "GPS time (atomic clock)"));
			this.Add(11, new ExifTag(11, "GPSDOP", "Measurement precision"));
			this.Add(13, new ExifTag(13, "GPSSpeed", "Speed of GPS receiver"));
			this.Add(15, new ExifTag(15, "GPSTrack", "Direction of movement"));
			this.Add(17, new ExifTag(17, "GPSImgDirection", "Direction of image"));
			this.Add(20, new ExifTag(20, "GPSDestLatitude", "Latitude of destination"));
			this.Add(22, new ExifTag(22, "GPSDestLongitude", "Longitude of destination"));
			this.Add(24, new ExifTag(24, "GPSDestBearing", "Bearing of destination"));
			this.Add(26, new ExifTag(26, "GPSDestDistance", "Distance to destination"));
			this.Add(306, new ExifTag(306, "DateTime", "File change date and time"));
			this.Add(270, new ExifTag(270, "ImageDescription", "Image title"));
			this.Add(271, new ExifTag(271, "Make", "Image input equipment manufacturer"));
			this.Add(272, new ExifTag(272, "Model", "Image input equipment model"));
			this.Add(305, new ExifTag(305, "Software", "Software used"));
			this.Add(315, new ExifTag(315, "Artist", "Person who created the image"));
			this.Add(33432, new ExifTag(33432, "Copyright", "Copyright holder"));
			this.Add(40964, new ExifTag(40964, "RelatedSoundFile", "Related audio file"));
			this.Add(36867, new ExifTag(36867, "DateTimeOriginal", "Date and time of original data generation"));
			this.Add(36868, new ExifTag(36868, "DateTimeDigitized", "Date and time of digital data generation"));
			this.Add(37520, new ExifTag(37520, "SubSecTime", "DateTime subseconds"));
			this.Add(37521, new ExifTag(37521, "SubSecTimeOriginal", "DateTimeOriginal subseconds"));
			this.Add(37522, new ExifTag(37522, "SubSecTimeDigitized", "DateTimeDigitized subseconds"));
			this.Add(42016, new ExifTag(42016, "ImageUniqueID", "Unique image ID"));
			this.Add(34852, new ExifTag(34852, "SpectralSensitivity", "Spectral sensitivity"));
			this.Add(1, new ExifTag(1, "GPSLatitudeRef", "North or South Latitude"));
			this.Add(3, new ExifTag(3, "GPSLongitudeRef", "East or West Longitude"));
			this.Add(8, new ExifTag(8, "GPSSatellites", "GPS satellites used for measurement"));
			this.Add(9, new ExifTag(9, "GPSStatus", "GPS receiver status"));
			this.Add(10, new ExifTag(10, "GPSMeasureMode", "GPS measurement mode"));
			this.Add(12, new ExifTag(12, "GPSSpeedRef", "Speed unit"));
			this.Add(14, new ExifTag(14, "GPSTrackRef", "Reference for direction of movement"));
			this.Add(16, new ExifTag(16, "GPSImgDirectionRef", "Reference for direction of image"));
			this.Add(18, new ExifTag(18, "GPSMapDatum", "Geodetic survey data used"));
			this.Add(19, new ExifTag(19, "GPSDestLatitudeRef", "Reference for latitude of destination"));
			this.Add(21, new ExifTag(21, "GPSDestLongitudeRef", "Reference for longitude of destination"));
			this.Add(23, new ExifTag(23, "GPSDestBearingRef", "Reference for bearing of destination"));
			this.Add(25, new ExifTag(25, "GPSDestDistanceRef", "Reference for distance to destination"));
			this.Add(29, new ExifTag(29, "GPSDateStamp", "GPS date"));
			this.Add(34856, new ExifTag(34856, "OECF", "Optoelectric conversion factor"));
			this.Add(41484, new ExifTag(41484, "SpatialFrequencyResponse", "Spatial frequency response"));
			this.Add(41728, new ExifTag(41728, "FileSource", "File source"));
			this.Add(41729, new ExifTag(41729, "SceneType", "Scene type"));
			this.Add(41730, new ExifTag(41730, "CFAPattern", "CFA pattern"));
			this.Add(41995, new ExifTag(41995, "DeviceSettingDescription", "Device settings description"));
			this.Add(36864, new ExifTag(36864, "ExifVersion", "Exif version"));
			this.Add(40960, new ExifTag(40960, "FlashpixVersion", "Supported Flashpix version"));
			this.Add(37121, new ExifTag(37121, "ComponentsConfiguration", "Meaning of each component"));
			this.Add(37500, new ExifTag(37500, "MakerNote", "Manufacturer notes"));
			this.Add(37510, new ExifTag(37510, "UserComment", "User comments"));
			this.Add(27, new ExifTag(27, "GPSProcessingMethod", "Name of GPS processing method"));
			this.Add(28, new ExifTag(28, "GPSAreaInformation", "Name of GPS area"));
		}
	}
}

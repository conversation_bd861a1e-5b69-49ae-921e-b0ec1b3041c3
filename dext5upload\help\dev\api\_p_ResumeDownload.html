﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">          
    <h3 class="title">DEXT5 Upload :: Config :: ResumeDownload</h3>
    <p class="ttl">config.ResumeDownload</p>
    <p class="txt">
        업로드 이어받기를 설정합니다.<br/>
        파일 다운로드 중에 네트워크가 단절되거나 사용자 요청에 의해 중지 되어도 이어받기가 가능합니다.<br />
        웹표준모드, 플러그인모드 모두 지원합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0" 이고, "1"로 설정시 이어받기가 가능합니다.    
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 이어 받기를 설정합니다.
        DEXT5UPLOAD.config.ResumeDownload = '1';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


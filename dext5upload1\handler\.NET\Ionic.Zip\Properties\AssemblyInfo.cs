﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security;

[assembly: AssemblyVersion("*******")]
[assembly: Guid("dfd2b1f6-e3be-43d1-9b43-11aae1e901d8")]
[assembly: CLSCompliant(true)]
[assembly: ComVisible(true)]
[assembly: AssemblyCopyright("Copyright © Dino Chiesa 2006 - 2011")]
[assembly: AssemblyDescription("a library for handling zip archives. http://www.codeplex.com/DotNetZip (Flavor=Retail)")]
[assembly: AssemblyCompany("Dino Chiesa")]
[assembly: AssemblyProduct("DotNetZip Library")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AllowPartiallyTrustedCallers]
[assembly: AssemblyTitle("Ionic's Zip Library")]
[assembly: AssemblyConfiguration("Retail")]

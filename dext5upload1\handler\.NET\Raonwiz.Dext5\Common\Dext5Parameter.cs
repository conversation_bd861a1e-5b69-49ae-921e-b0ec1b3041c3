﻿using System;
using System.Collections;
using System.Reflection;
using System.Web;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x02000002 RID: 2
	public class Dext5Parameter
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
		public static string GetParameter(HttpContext hContext)
		{
			string text = Dext5Encoding.Base64Decoding(hContext.Request["d00"]);
			text = text.Substring(1);
			return Dext5Encoding.Base64Decoding(text);
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002084 File Offset: 0x00000284
		public static string MakeParameter(string str)
		{
			string text = Dext5Encoding.Base64Encoding(str);
			text = "R" + text;
			text = Dext5Encoding.Base64Encoding(text);
			return text.Replace("+", "%2B");
		}

		// Token: 0x06000003 RID: 3 RVA: 0x000020C0 File Offset: 0x000002C0
		public static void SetParameter(HttpContext hContext, ParamEntity dextParam)
		{
			Hashtable hashtable = new Hashtable();
			Dext5Parameter.SetDextContextTable(hContext, hashtable);
			try
			{
				if (!string.IsNullOrEmpty(hContext.Request["d00"]))
				{
					Dext5Parameter.SetPropertyValue(dextParam, "mode", "secure");
					dextParam.fileDataIntegrity = hContext.Request["fdi"];
					string[] array = dextParam.decryptParameter.Split(new string[]
					{
						Dext5Parameter.trans_unitDelimiter
					}, StringSplitOptions.RemoveEmptyEntries);
					foreach (string text in array)
					{
						string[] array3 = text.Split(new string[]
						{
							Dext5Parameter.trans_unitAttributeDelimiter
						}, StringSplitOptions.None);
						string text2 = array3[0];
						string text3 = array3[1];
						if (text2 == "d40")
						{
							text3 = (string.IsNullOrEmpty(text3) ? hContext.Request.Url.Host : text3);
						}
						else if (text2 == "d25")
						{
							if (!string.IsNullOrEmpty(text3))
							{
								string[] values = text3.Split(new string[]
								{
									"|"
								}, StringSplitOptions.RemoveEmptyEntries);
								Dext5Parameter.SetPropertyValue(dextParam, "fileVirtualPathAry", values);
							}
						}
						else if (text2 == "d26")
						{
							if (!string.IsNullOrEmpty(text3))
							{
								string[] values2 = text3.Split(new string[]
								{
									"|"
								}, StringSplitOptions.RemoveEmptyEntries);
								Dext5Parameter.SetPropertyValue(dextParam, "fileOrgNameAry", values2);
							}
						}
						else if (text2 == "d32" && !string.IsNullOrEmpty(text3))
						{
							string[] values3 = text3.Split(new string[]
							{
								"|"
							}, StringSplitOptions.RemoveEmptyEntries);
							Dext5Parameter.SetPropertyValue(dextParam, "urlAddressAry", values3);
						}
						try
						{
							Dext5Parameter.SetPropertyValue(dextParam, hashtable[text2].ToString(), text3);
						}
						catch (Exception)
						{
						}
					}
				}
				else
				{
					Dext5Parameter.SetPropertyValue(dextParam, "mode", "normal");
					string[] allKeys = hContext.Request.Form.AllKeys;
					string[] allKeys2 = hContext.Request.QueryString.AllKeys;
					string[] array4 = new string[allKeys.Length + allKeys2.Length];
					if (allKeys.Length != 0)
					{
						Array.Copy(allKeys, array4, allKeys.Length);
					}
					if (allKeys2.Length != 0)
					{
						Array.Copy(allKeys2, 0, array4, allKeys.Length, allKeys2.Length);
					}
					string text4 = string.Empty;
					foreach (string text5 in array4)
					{
						text4 = hContext.Request[text5];
						if (text5 == "domain")
						{
							text4 = (string.IsNullOrEmpty(text4) ? hContext.Request.Url.Host : text4);
						}
						else if (text5 == "fileVirtualPath")
						{
							if (!string.IsNullOrEmpty(text4))
							{
								string[] values4 = hContext.Request.Form.GetValues(text5);
								if (values4 == null)
								{
									values4 = hContext.Request.QueryString.GetValues(text5);
								}
								Dext5Parameter.SetPropertyValue(dextParam, "fileVirtualPathAry", values4);
							}
						}
						else if (text5 == "fileOrgName")
						{
							if (!string.IsNullOrEmpty(text4))
							{
								string[] values5 = hContext.Request.Form.GetValues(text5);
								if (values5 == null)
								{
									values5 = hContext.Request.QueryString.GetValues(text5);
								}
								Dext5Parameter.SetPropertyValue(dextParam, "fileOrgNameAry", values5);
							}
						}
						else if (text5 == "urlAddress" && !string.IsNullOrEmpty(text4))
						{
							string[] values6 = hContext.Request.Form.GetValues(text5);
							if (values6 == null)
							{
								values6 = hContext.Request.QueryString.GetValues(text5);
							}
							Dext5Parameter.SetPropertyValue(dextParam, "urlAddressAry", values6);
						}
						try
						{
							Dext5Parameter.SetPropertyValue(dextParam, hashtable[text5].ToString(), text4);
						}
						catch (Exception)
						{
						}
					}
				}
			}
			catch (Exception)
			{
				Dext5Parameter.ParameterErrorHandle(hContext, dextParam);
			}
		}

		// Token: 0x06000004 RID: 4 RVA: 0x000024EC File Offset: 0x000006EC
		private static void SetDextContextTable(HttpContext hContext, Hashtable secureParamTable)
		{
			if (!string.IsNullOrEmpty(hContext.Request["d00"]))
			{
				secureParamTable.Add("mode", "secure");
				secureParamTable.Add("d01", "dext5CMD");
				secureParamTable.Add("d02", "productKey");
				secureParamTable.Add("d03", "licenseKey");
				secureParamTable.Add("d04", "crossDomain");
				secureParamTable.Add("d05", "licenseBase64");
				secureParamTable.Add("d06", "productName");
				secureParamTable.Add("d07", "GUID");
				secureParamTable.Add("d08", "fileName");
				secureParamTable.Add("d09", "fileNameRule");
				secureParamTable.Add("d10", "fileNameRuleEx");
				secureParamTable.Add("d11", "folderNameRule");
				secureParamTable.Add("d12", "fileGroupID");
				secureParamTable.Add("d13", "filePrefix");
				secureParamTable.Add("d14", "fileSubfix");
				secureParamTable.Add("d15", "fileExtensionDetector");
				secureParamTable.Add("d17", "fileDataEncrypt");
				secureParamTable.Add("d18", "chunkNumber");
				secureParamTable.Add("d19", "numberOfChunks");
				secureParamTable.Add("d20", "RESUMEUP");
				secureParamTable.Add("d21", "RESUMEGUID");
				secureParamTable.Add("d22", "p");
				secureParamTable.Add("d23", "fi");
				secureParamTable.Add("d24", "zipFileName");
				secureParamTable.Add("d25", "fileVirtualPath");
				secureParamTable.Add("d26", "fileOrgName");
				secureParamTable.Add("d27", "resumeMode");
				secureParamTable.Add("d28", "crossDomainZipFileName");
				secureParamTable.Add("d29", "viewerGUID");
				secureParamTable.Add("d30", "documentDomain");
				secureParamTable.Add("d31", "skinName");
				secureParamTable.Add("d32", "urlAddress");
				secureParamTable.Add("d33", "configFileName");
				secureParamTable.Add("d34", "h5pbeInfo");
				secureParamTable.Add("d35", "checkFileExtension");
				secureParamTable.Add("d36", "allowedZeroFileSize");
				secureParamTable.Add("d37", "chunkSize");
				secureParamTable.Add("d38", "fileIndex");
				secureParamTable.Add("d39", "displayFileSize");
				secureParamTable.Add("d40", "domain");
				secureParamTable.Add("d41", "dext5Release");
				secureParamTable.Add("d42", "downloadUrl");
				secureParamTable.Add("d43", "fileExt");
				secureParamTable.Add("d44", "viewerPopupInit");
				secureParamTable.Add("d45", "g");
				secureParamTable.Add("d46", "folderPath");
				secureParamTable.Add("d47", "fileSize");
				secureParamTable.Add("d48", "limitOneFileSize");
				secureParamTable.Add("d49", "viewerUrl");
				secureParamTable.Add("d70", "kcmd");
				secureParamTable.Add("d71", "kFileStartPos");
				return;
			}
			secureParamTable.Add("mode", "normal");
			secureParamTable.Add("dext5CMD", "dext5CMD");
			secureParamTable.Add("productKey", "productKey");
			secureParamTable.Add("licenseKey", "licenseKey");
			secureParamTable.Add("cd", "crossDomain");
			secureParamTable.Add("Se", "licenseBase64");
			secureParamTable.Add("pn", "productName");
			secureParamTable.Add("GUID", "GUID");
			secureParamTable.Add("fileName", "fileName");
			secureParamTable.Add("fileNameRule", "fileNameRule");
			secureParamTable.Add("fileNameRuleEx", "fileNameRuleEx");
			secureParamTable.Add("folderNameRule", "folderNameRule");
			secureParamTable.Add("gpid", "fileGroupID");
			secureParamTable.Add("d5_prefix", "filePrefix");
			secureParamTable.Add("d5_subfix", "fileSubfix");
			secureParamTable.Add("fed", "fileExtensionDetector");
			secureParamTable.Add("fdi", "fileDataIntegrity");
			secureParamTable.Add("fde", "fileDataEncrypt");
			secureParamTable.Add("chunkNumber", "chunkNumber");
			secureParamTable.Add("numberOfChunks", "numberOfChunks");
			secureParamTable.Add("RESUMEUP", "RESUMEUP");
			secureParamTable.Add("RESUMEGUID", "RESUMEGUID");
			secureParamTable.Add("p", "p");
			secureParamTable.Add("fi", "fi");
			secureParamTable.Add("zfn", "zipFileName");
			secureParamTable.Add("fileVirtualPath", "fileVirtualPath");
			secureParamTable.Add("fileOrgName", "fileOrgName");
			secureParamTable.Add("resumeMode", "resumeMode");
			secureParamTable.Add("c2", "crossDomainZipFileName");
			secureParamTable.Add("viewerguid", "viewerGUID");
			secureParamTable.Add("documentDomain", "documentDomain");
			secureParamTable.Add("skinName", "skinName");
			secureParamTable.Add("urlAddress", "urlAddress");
			secureParamTable.Add("f", "configFileName");
			secureParamTable.Add("info", "h5pbeInfo");
			secureParamTable.Add("cfe", "checkFileExtension");
			secureParamTable.Add("allowedZeroFileSize", "allowedZeroFileSize");
			secureParamTable.Add("cs", "chunkSize");
			secureParamTable.Add("fidx", "fileIndex");
			secureParamTable.Add("ds", "displayFileSize");
			secureParamTable.Add("domain", "domain");
			secureParamTable.Add("dext5Release", "dext5Release");
			secureParamTable.Add("downloadUrl", "downloadUrl");
			secureParamTable.Add("fileExt", "fileExt");
			secureParamTable.Add("PopupInit", "viewerPopupInit");
			secureParamTable.Add("g", "g");
			secureParamTable.Add("folderPath", "folderPath");
			secureParamTable.Add("fs", "fileSize");
			secureParamTable.Add("fileNameEx", "fileName");
			secureParamTable.Add("lofs", "limitOneFileSize");
			secureParamTable.Add("kcmd", "kcmd");
			secureParamTable.Add("k71", "kFileStartPos");
			secureParamTable.Add("vu", "viewerUrl");
		}

		// Token: 0x06000005 RID: 5 RVA: 0x00002B94 File Offset: 0x00000D94
		private static void SetPropertyValue(object obj, string propertyName, object values)
		{
			Type type = obj.GetType();
			PropertyInfo property = type.GetProperty(propertyName);
			property.SetValue(obj, values, null);
		}

		// Token: 0x06000006 RID: 6 RVA: 0x00002BBC File Offset: 0x00000DBC
		private static void ParameterErrorHandle(HttpContext hContext, ParamEntity dextParam)
		{
			string text = "";
			if (!string.IsNullOrEmpty(dextParam.crossDomain))
			{
				text += "<html><head>";
				text += "<script type=\"text/javascript\">";
				text += "if (window.postMessage) {";
				text += "if (window.addEventListener) {";
				text += "window.addEventListener('message', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "}, false);";
				text += "}";
				text += "else if (window.attachEvent) {";
				text += "window.attachEvent('onmessage', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "});";
				text += "}";
				text += "}";
				text += "</script>";
				text += "</head>";
				text += "<body>";
				text += "{0}";
				text += "</body>";
				text += "</html>";
			}
			else
			{
				text = "{0}";
			}
			if (!string.IsNullOrEmpty(dextParam.dext5CMD))
			{
				string dext5CMD;
				if ((dext5CMD = dextParam.dext5CMD) != null)
				{
					if (dext5CMD == "downloadRequest")
					{
						text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
						goto IL_218;
					}
					if (dext5CMD == "mdf")
					{
						text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
						goto IL_218;
					}
					if (dext5CMD == "mzf")
					{
						text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
						goto IL_218;
					}
				}
				text = text.Replace("{0}", Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
			}
			else
			{
				text = text.Replace("{0}", Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
			}
			IL_218:
			hContext.Response.Clear();
			hContext.Response.Write(text);
		}

		// Token: 0x04000001 RID: 1
		public static string trans_unitDelimiter = "\v";

		// Token: 0x04000002 RID: 2
		public static string trans_unitAttributeDelimiter = "\f";
	}
}

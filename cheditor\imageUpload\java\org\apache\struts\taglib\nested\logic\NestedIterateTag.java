package org.apache.struts.taglib.nested.logic;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import org.apache.struts.taglib.logic.IterateTag;
import org.apache.struts.taglib.nested.NestedNameSupport;
import org.apache.struts.taglib.nested.NestedPropertyHelper;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/logic/NestedIterateTag.class */
public class NestedIterateTag extends IterateTag implements NestedNameSupport {
    private String nesting = null;
    private String originalName = null;
    private String originalProperty = null;
    private String originalNesting = null;
    private String originalNestingName = null;

    @Override // org.apache.struts.taglib.logic.IterateTag
    public int doStartTag() throws JspException {
        this.originalName = getName();
        this.originalProperty = getProperty();
        if (this.id == null || this.id.trim().length() == 0) {
            this.id = this.property;
        }
        HttpServletRequest request = this.pageContext.getRequest();
        this.originalNesting = NestedPropertyHelper.getCurrentProperty(request);
        this.originalNestingName = NestedPropertyHelper.getCurrentName(request, this);
        if (getName() == null) {
            this.nesting = NestedPropertyHelper.getAdjustedProperty(request, getProperty());
        } else {
            this.nesting = getProperty();
        }
        NestedPropertyHelper.setNestedProperties(request, this);
        int temp = super.doStartTag();
        NestedPropertyHelper.setName(request, getName());
        NestedPropertyHelper.setProperty(request, deriveNestedProperty());
        return temp;
    }

    private String deriveNestedProperty() {
        Object idObj = this.pageContext.getAttribute(this.id);
        if (idObj instanceof Map.Entry) {
            return new StringBuffer().append(this.nesting).append("(").append(((Map.Entry) idObj).getKey()).append(")").toString();
        }
        return new StringBuffer().append(this.nesting).append("[").append(getIndex()).append("]").toString();
    }

    @Override // org.apache.struts.taglib.logic.IterateTag
    public int doAfterBody() throws JspException {
        int temp = super.doAfterBody();
        HttpServletRequest request = this.pageContext.getRequest();
        if (temp != 0) {
            NestedPropertyHelper.setProperty(request, deriveNestedProperty());
        }
        return temp;
    }

    @Override // org.apache.struts.taglib.logic.IterateTag
    public int doEndTag() throws JspException {
        int i = super.doEndTag();
        HttpServletRequest request = this.pageContext.getRequest();
        super.setName(this.originalName);
        super.setProperty(this.originalProperty);
        if (this.originalNesting == null) {
            NestedPropertyHelper.deleteReference(request);
        } else {
            NestedPropertyHelper.setProperty(request, this.originalNesting);
            NestedPropertyHelper.setName(request, this.originalNestingName);
        }
        return i;
    }

    @Override // org.apache.struts.taglib.logic.IterateTag
    public void release() {
        super.release();
        this.originalName = null;
        this.originalProperty = null;
        this.originalNesting = null;
        this.originalNestingName = null;
    }
}

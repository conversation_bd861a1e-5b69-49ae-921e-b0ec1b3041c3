﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con ">           
    <h3 class="title">DEXT5 Upload :: SetFolderTransfer</h3>
    <p class="ttl">void SetFolderTransfer(useFolderTransfer, uploadID);</p>
    <p class="txt">
        폴더 구조로 업로드 할수 있도록 설정합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
       없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">useFolderTransfer</span>&nbsp;&nbsp;0 : 폴더구조 업로드 사용안함<br/>
        <span style="padding-left:105px"> 1 : 폴더구조 업로드 사용함(일반파일 업로드 사용)</span><br/><span style="padding-left:105px"> 2 : 폴더구조 업로드 사용함(일반파일 업로드 미사용)</span><br/>
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;다운로드 경로를 설정할 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="upload1/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function setFolderTransfer(){
            // 폴더 구조 업로드 사용안함으로 설정합니다.
            DEXT5UPLOAD.SetFolderTransfer(0, 'upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


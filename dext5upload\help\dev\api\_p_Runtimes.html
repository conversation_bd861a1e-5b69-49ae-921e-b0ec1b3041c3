﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: Runtimes</h3>
    <p class="ttl">config.Runtimes</p>
    <p class="txt">
        업로드 모드를 설정합니다.<br/>
        (웹표준모드, 플러그인모드 중 선택) 
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        html5 설정시 html5 모드가 우선 적용됩니다.<br/>
        ieplugin 설정시 ieplugin 모드가 우선 적용되며, 지원되지 않는 브라우저는 html5 모드가 적용됩니다.(ie 전용 모드)<br/>
        versionieplugin 설정시 적용한 버전 이하에서만 플러그인 모드로 적용됩니다.<br/>
        9ieplugin 으로 적용하면 ie9 이하 버전에서만 플러그인 모드로 적용됩니다. <br/>예) "html5" or "ieplugin" or versionieplugin
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드를 ieplugin 모드로 설정합니다.
        DEXT5UPLOAD.config.Runtimes = 'ieplugin';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


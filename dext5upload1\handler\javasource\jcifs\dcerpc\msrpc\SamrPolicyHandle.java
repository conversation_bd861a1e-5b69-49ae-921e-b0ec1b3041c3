package jcifs.dcerpc.msrpc;

import java.io.IOException;
import jcifs.dcerpc.DcerpcException;
import jcifs.dcerpc.DcerpcHandle;
import jcifs.dcerpc.rpc;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/SamrPolicyHandle.class */
public class SamrPolicyHandle extends rpc.policy_handle {
    public SamrPolicyHandle(DcerpcHandle handle, String server, int access) throws IOException {
        server = server == null ? "\\\\" : server;
        MsrpcSamrConnect4 rpc = new MsrpcSamrConnect4(server, access, this);
        try {
            handle.sendrecv(rpc);
        } catch (DcerpcException de) {
            if (de.getErrorCode() != 469827586) {
                throw de;
            }
            MsrpcSamrConnect2 rpc2 = new MsrpcSamrConnect2(server, access, this);
            handle.sendrecv(rpc2);
        }
    }

    public void close() throws IOException {
    }
}

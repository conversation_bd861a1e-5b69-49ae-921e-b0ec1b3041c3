﻿using System;
using System.Text;

namespace Raonwiz.Dext5.Process
{
	// Token: 0x02000038 RID: 56
	internal class HttpResponseHeader
	{
		// Token: 0x170000F3 RID: 243
		// (get) Token: 0x06000319 RID: 793 RVA: 0x00024ACF File Offset: 0x00022CCF
		// (set) Token: 0x0600031A RID: 794 RVA: 0x00024AD7 File Offset: 0x00022CD7
		public string AcceptRanges { get; set; }

		// Token: 0x170000F4 RID: 244
		// (get) Token: 0x0600031B RID: 795 RVA: 0x00024AE0 File Offset: 0x00022CE0
		// (set) Token: 0x0600031C RID: 796 RVA: 0x00024AE8 File Offset: 0x00022CE8
		public string Connection { get; set; }

		// Token: 0x170000F5 RID: 245
		// (get) Token: 0x0600031D RID: 797 RVA: 0x00024AF1 File Offset: 0x00022CF1
		// (set) Token: 0x0600031E RID: 798 RVA: 0x00024AF9 File Offset: 0x00022CF9
		public string ContentDisposition { get; set; }

		// Token: 0x170000F6 RID: 246
		// (get) Token: 0x0600031F RID: 799 RVA: 0x00024B02 File Offset: 0x00022D02
		// (set) Token: 0x06000320 RID: 800 RVA: 0x00024B0A File Offset: 0x00022D0A
		public Encoding ContentEncoding { get; set; }

		// Token: 0x170000F7 RID: 247
		// (get) Token: 0x06000321 RID: 801 RVA: 0x00024B13 File Offset: 0x00022D13
		// (set) Token: 0x06000322 RID: 802 RVA: 0x00024B1B File Offset: 0x00022D1B
		public string ContentLength { get; set; }

		// Token: 0x170000F8 RID: 248
		// (get) Token: 0x06000323 RID: 803 RVA: 0x00024B24 File Offset: 0x00022D24
		// (set) Token: 0x06000324 RID: 804 RVA: 0x00024B2C File Offset: 0x00022D2C
		public string ContentRange { get; set; }

		// Token: 0x170000F9 RID: 249
		// (get) Token: 0x06000325 RID: 805 RVA: 0x00024B35 File Offset: 0x00022D35
		// (set) Token: 0x06000326 RID: 806 RVA: 0x00024B3D File Offset: 0x00022D3D
		public string ContentType { get; set; }

		// Token: 0x170000FA RID: 250
		// (get) Token: 0x06000327 RID: 807 RVA: 0x00024B46 File Offset: 0x00022D46
		// (set) Token: 0x06000328 RID: 808 RVA: 0x00024B4E File Offset: 0x00022D4E
		public string Etag { get; set; }

		// Token: 0x170000FB RID: 251
		// (get) Token: 0x06000329 RID: 809 RVA: 0x00024B57 File Offset: 0x00022D57
		// (set) Token: 0x0600032A RID: 810 RVA: 0x00024B5F File Offset: 0x00022D5F
		public string LastModified { get; set; }
	}
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: CustomHeaderColor, CustomFooterColor<br />DEXT5 Upload :: Config :: CustomProgressBarColor, CustomTextColor</h3>
    <p class="ttl">config.CustomHeaderColor, config.CustomFooterColor<br/> config.CustomProgressBarColor, config.CustomTextColor<br/></p>
    <p class="txt">
        업로드 색상을 사용자가 원하는 색상으로 변경합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        <span class="txt"><span class="firebrick">CustomHeaderColor</span>&nbsp;&nbsp;업로드창 (상단/ 하단 / 하단 버튼 바탕색)을 설정합니다.</span><br/>
        <span class="txt"><span class="firebrick">CustomFooterColor </span>&nbsp;&nbsp;업로드창 (상단 / 하단 버튼 텍스트색)을 설정합니다.</span><br />
        <span class="txt"><span class="firebrick">CustomProgressBarColor</span>&nbsp;&nbsp;전송창(상단/ 전체 전송률/ 전송 용량률/ 취소 버튼 바탕색)을 설정합니다.</span><br />
        <span class="txt"><span class="firebrick">CustomTextColor</span>&nbsp;&nbsp;전송창 (상단 / 하단 취소 버튼 텍스트 색)을 설정합니다.</span><br /><br />
        <span class="txt"> #을 포함한 hex값으로 입력합니다.</span><br />
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 사용자가 원하는 색상으로 설정합니다.
        DEXT5UPLOAD.config.CustomHeaderColor = '#ff0000';
        DEXT5UPLOAD.config.CustomFooterColor = '#f7f140';
        DEXT5UPLOAD.config.CustomProgressBarColor = '#33e8f5';
        DEXT5UPLOAD.config.CustomTextColor = '#c2ffd0';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


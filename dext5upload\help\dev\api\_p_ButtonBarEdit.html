﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: ButtonBarEdit</h3>
    <p class="ttl">config.ButtonBarEdit</p>
    <p class="txt">
        업로드가 편집모드일 때 버튼바 영역에 노출 할 버튼을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        "0"으로 설정시 버튼 영역을 표시하지 않습니다.<br />
        (버튼영역 미표시는 IE 브라우저 모드 10 이상부터 가능합니다. 플러그인 모드는 IE브라우저 모드 상관 없이 사용 가능합니다.) <br/><br />
        버튼 종류는<br/>add : 파일추가<br/> send : 전송하기<br /> remove : 항목제거<br /> remove_all : 전체 항목제거<br /> move_first : 맨 앞으로<br /> move_forward : 앞으로<br /> move_back : 뒤로<br /> move_end : 맨 뒤로 <br/>
        <br/>버튼 종류는 ",(콤마)"로 구분하여 설정합니다.            
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드가 편집모드일 때 버튼 바 영역에 파일추가,전송하기 버튼을 설정합니다.
        DEXT5UPLOAD.config.ButtonBarEdit = 'add,send';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


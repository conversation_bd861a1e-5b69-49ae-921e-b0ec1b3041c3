﻿using System;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x0200002A RID: 42
	public class FileCheck : Base
	{
		// Token: 0x06000298 RID: 664 RVA: 0x0001E7D3 File Offset: 0x0001C9D3
		public FileCheck(HttpContext context) : base(context)
		{
		}

		// Token: 0x06000299 RID: 665 RVA: 0x0001E7DC File Offset: 0x0001C9DC
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string value = this.hContext.Request.QueryString["s"];
			string value2 = this.hContext.Request.QueryString["c"];
			string name = this.hContext.Request.QueryString["g"];
			string text = this.hContext.Request.QueryString["cd"];
			if (!string.IsNullOrEmpty(value))
			{
				if (this.hContext.Application[name] != null)
				{
					UploadStatus uploadStatus = (UploadStatus)this.hContext.Application[name];
					string text2 = string.Empty;
					if (text != null && text.Equals("1"))
					{
						text2 += "<html><head>";
						text2 += "<script type=\"text/javascript\">";
						text2 += "if (window.postMessage) {";
						text2 += "if (window.addEventListener) {";
						text2 += "window.addEventListener('message', function (e) {";
						text2 += "var sendUrl = e.origin;";
						text2 += "var data = document.body.innerHTML;";
						text2 += "e.source.postMessage(data, sendUrl);";
						text2 += "}, false);";
						text2 += "}";
						text2 += "else if (window.attachEvent) {";
						text2 += "window.attachEvent('onmessage', function (e) {";
						text2 += "var sendUrl = e.origin;";
						text2 += "var data = document.body.innerHTML;";
						text2 += "e.source.postMessage(data, sendUrl);";
						text2 += "});";
						text2 += "}";
						text2 += "}";
						text2 += "</script>";
						text2 += "</head>";
						text2 += "<body>";
						text2 += "{0}";
						text2 += "</body>";
						text2 += "</html>";
					}
					else
					{
						text2 = "{0}";
					}
					string newValue = Dext5Parameter.MakeParameter(uploadStatus.Message);
					text2 = text2.Replace("{0}", newValue);
					this.hContext.Response.Write(text2);
					this.hContext.Application.Remove(name);
				}
			}
			else if (!string.IsNullOrEmpty(value2) && this.hContext.Application[name] != null)
			{
				UploadStatus uploadStatus2 = (UploadStatus)this.hContext.Application[name];
				uploadStatus2.CloseConnection = true;
			}
			return null;
		}
	}
}

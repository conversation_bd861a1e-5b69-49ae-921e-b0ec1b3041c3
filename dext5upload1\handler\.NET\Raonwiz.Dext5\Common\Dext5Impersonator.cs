﻿using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Security.Principal;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x02000010 RID: 16
	public class Dext5Impersonator : IDisposable
	{
		// Token: 0x060000FD RID: 253 RVA: 0x0001206A File Offset: 0x0001026A
		public Dext5Impersonator(string userName, string domainName, string password)
		{
			this.ImpersonateValidUser(userName, domainName, password);
		}

		// Token: 0x060000FE RID: 254 RVA: 0x0001207B File Offset: 0x0001027B
		public void Dispose()
		{
			this.UndoImpersonation();
		}

		// Token: 0x060000FF RID: 255
		[DllImport("advapi32.dll", SetLastError = true)]
		private static extern int LogonUser(string lpszUserName, string lpszDomain, string lpszPassword, int dwLogonType, int dwLogonProvider, ref IntPtr phToken);

		// Token: 0x06000100 RID: 256
		[DllImport("advapi32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		private static extern int DuplicateToken(IntPtr hToken, int impersonationLevel, ref IntPtr hNewToken);

		// Token: 0x06000101 RID: 257
		[DllImport("advapi32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		private static extern bool RevertToSelf();

		// Token: 0x06000102 RID: 258
		[DllImport("kernel32.dll", CharSet = CharSet.Auto)]
		private static extern bool CloseHandle(IntPtr handle);

		// Token: 0x06000103 RID: 259 RVA: 0x00012084 File Offset: 0x00010284
		private void ImpersonateValidUser(string userName, string domain, string password)
		{
			IntPtr zero = IntPtr.Zero;
			IntPtr zero2 = IntPtr.Zero;
			try
			{
				if (!Dext5Impersonator.RevertToSelf())
				{
					throw new Win32Exception(Marshal.GetLastWin32Error());
				}
				if (Dext5Impersonator.LogonUser(userName, domain, password, 2, 0, ref zero) != 0)
				{
					if (Dext5Impersonator.DuplicateToken(zero, 2, ref zero2) == 0)
					{
						throw new Win32Exception(Marshal.GetLastWin32Error());
					}
					WindowsIdentity windowsIdentity = new WindowsIdentity(zero2);
					this.impersonationContext = windowsIdentity.Impersonate();
				}
				else if (Dext5Impersonator.LogonUser(userName, domain, password, 9, 0, ref zero) != 0)
				{
					if (Dext5Impersonator.DuplicateToken(zero, 2, ref zero2) == 0)
					{
						throw new Win32Exception(Marshal.GetLastWin32Error());
					}
					WindowsIdentity windowsIdentity = new WindowsIdentity(zero2);
					this.impersonationContext = windowsIdentity.Impersonate();
				}
			}
			finally
			{
				if (zero != IntPtr.Zero)
				{
					Dext5Impersonator.CloseHandle(zero);
				}
				if (zero2 != IntPtr.Zero)
				{
					Dext5Impersonator.CloseHandle(zero2);
				}
			}
		}

		// Token: 0x06000104 RID: 260 RVA: 0x00012160 File Offset: 0x00010360
		private void UndoImpersonation()
		{
			if (this.impersonationContext != null)
			{
				this.impersonationContext.Undo();
			}
		}

		// Token: 0x0400008E RID: 142
		internal const int LOGON32_LOGON_INTERACTIVE = 2;

		// Token: 0x0400008F RID: 143
		internal const int LOGON32_PROVIDER_DEFAULT = 0;

		// Token: 0x04000090 RID: 144
		internal const int LOGON32_LOGON_NEW_CREDENTIALS = 9;

		// Token: 0x04000091 RID: 145
		private WindowsImpersonationContext impersonationContext;
	}
}

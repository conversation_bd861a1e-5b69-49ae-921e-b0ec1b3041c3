[可疑目标检测时间: 2025-06-21 12:08:32]
目标: memory.ddm.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/dext5upload.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: memory.ddm.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/HuskyEZCreator.js (valid) - 验证成功
  - https://memory.ddm.go.kr/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: memory.ddm.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/cheditor.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://memory.ddm.go.kr/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: memory.ddm.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: memory.ddm.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 12:10:29]
目标: 3.37.80.124
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/dext5upload.js (valid) - 基础验证通过
  - https://3.37.80.124/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: 3.37.80.124
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/HuskyEZCreator.js (valid) - 验证成功
  - https://3.37.80.124/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://3.37.80.124/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 3.37.80.124
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/cheditor.js (valid) - 基础验证通过
  - https://3.37.80.124/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://3.37.80.124/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 3.37.80.124
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: 3.37.80.124
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 13:57:35]
目标: chairman.gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:09:04]
目标: 211.34.96.55
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/dext5upload.js (valid) - 基础验证通过
  - https://211.34.96.55/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://211.34.96.55/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: 211.34.96.55
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/HuskyEZCreator.js (valid) - 基础验证通过
  - https://211.34.96.55/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://211.34.96.55/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: 211.34.96.55
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/cheditor.js (valid) - 基础验证通过
  - https://211.34.96.55/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://211.34.96.55/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://211.34.96.55/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://211.34.96.55/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 211.34.96.55
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: 211.34.96.55
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/core/connector/php/connector.php (rce) - 基础验证通过
  - https://211.34.96.55/core/connector/connector (rce) - 基础验证通过
  - https://211.34.96.55/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://211.34.96.55/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:09:43]
目标: gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr/common/
发现的核心文件:
  - https://gangbuk.go.kr/common/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/common/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/common/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/common/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:21:01]
目标: 210.122.100.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.122.100.38/xplugin/SmartEdit/js/service/
主页发现的资源: https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 210.122.100.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.122.100.38/xplugin/SmartEdit/js/service/
主页发现的资源: https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:22:46]
目标: 211.43.13.115
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.43.13.115/editor/static/js/service/
主页发现的资源: http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.43.13.115
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.43.13.115/editor/static/js/service/
主页发现的资源: http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:38:19]
目标: 61.107.76.98
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.98/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 61.107.76.98
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.98/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:43:04]
目标: 210.116.109.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.116.109.166/admin/naverEditor/js/service/
主页发现的资源: https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 210.116.109.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.116.109.166/admin/naverEditor/js/service/
主页发现的资源: https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:43:08]
目标: 59.9.253.137
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://59.9.253.137/assets/js/se/js/service/
主页发现的资源: https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 59.9.253.137
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://59.9.253.137/assets/js/se/js/service/
主页发现的资源: https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:10:05]
目标: 211.188.57.30:8443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.188.57.30:8443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:19:58]
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.gcomija.co.kr/js/
主页发现的资源: https://www.gcomija.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.gcomija.co.kr/js/
主页发现的资源: https://www.gcomija.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.gcomija.co.kr/js/
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:24:58]
目标: mydatasafe.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/dext5upload.js (valid) - 基础验证通过
  - https://mydatasafe.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://mydatasafe.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mydatasafe.kr/smarteditor/
主页发现的资源: https://mydatasafe.kr/smarteditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://mydatasafe.kr/smarteditor/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smarteditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/smarteditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mydatasafe.kr/smarteditor/
主页发现的资源: https://mydatasafe.kr/smarteditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://mydatasafe.kr/smarteditor/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smarteditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/smarteditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/cheditor.js (valid) - 基础验证通过
  - https://mydatasafe.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://mydatasafe.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: mydatasafe.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: mydatasafe.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr/editors/
发现的核心文件:
  - https://mydatasafe.kr/editors/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/editors/core/connector/php/connector.php (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/core/connector/connector (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:45:04]
目标: www.jaintextile.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/
主页发现的资源: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.jaintextile.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/
主页发现的资源: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:49:01]
目标: 14.32.191.9
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://14.32.191.9/boardEditor/js/service/
主页发现的资源: http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 14.32.191.9
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://14.32.191.9/boardEditor/js/service/
主页发现的资源: http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:57:12]
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
主页发现的资源: https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
主页发现的资源: https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:57:26]
目标: www.dsliquid.com
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/dext5upload.js (valid) - 验证成功
  - https://www.dsliquid.com/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://www.dsliquid.com/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.dsliquid.com/editor/
主页发现的资源: https://www.dsliquid.com/editor/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.dsliquid.com/editor/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/editor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/editor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.dsliquid.com/editor/
主页发现的资源: https://www.dsliquid.com/editor/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.dsliquid.com/editor/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/editor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/editor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/cheditor.js (valid) - 验证成功
  - https://www.dsliquid.com/cheditor.css (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://www.dsliquid.com/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: www.dsliquid.com
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/ckeditor.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.dsliquid.com
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/core/connector/connector (rce) - 包含RCE相关内容
  - https://www.dsliquid.com/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://www.dsliquid.com/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:04:20]
目标: 3.39.136.151
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.39.136.151/js/service/
主页发现的资源: https://3.39.136.151/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.39.136.151/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.136.151
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.39.136.151/js/service/
主页发现的资源: https://3.39.136.151/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.39.136.151/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:07:45]
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.38.251.48/js/
主页发现的资源: https://3.38.251.48/js/HuskyEZCreator.js
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.38.251.48/js/
主页发现的资源: https://3.38.251.48/js/HuskyEZCreator.js
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://3.38.251.48/js/
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:14:31]
目标: ************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://************/xplugin/SmartEdit/js/service/
主页发现的资源: http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: ************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://************/xplugin/SmartEdit/js/service/
主页发现的资源: http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:25:04]
目标: *************:3201
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://*************:3201/Scripts/js/choose/
主页发现的资源: http://*************:3201/Scripts/js/choose/HuskyEZCreator.js
发现的核心文件:
  - http://*************:3201/Scripts/js/choose/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: *************:3201
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://*************:3201/Scripts/js/choose/
主页发现的资源: http://*************:3201/Scripts/js/choose/HuskyEZCreator.js
发现的核心文件:
  - http://*************:3201/Scripts/js/choose/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:39:11]
目标: mosim.seesaw.land
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mosim.seesaw.land/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: mosim.seesaw.land
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mosim.seesaw.land/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:42:08]
目标: scat.co.kr:7711
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://scat.co.kr:7711/resources/newEditor/js/service/
主页发现的资源: http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: scat.co.kr:7711
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://scat.co.kr:7711/resources/newEditor/js/service/
主页发现的资源: http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:44:46]
目标: 52.79.184.60
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://52.79.184.60/adm/webedit/js/service/
主页发现的资源: https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 52.79.184.60
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://52.79.184.60/adm/webedit/js/service/
主页发现的资源: https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:51:17]
目标: 211.188.57.30
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.188.57.30
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:57:55]
目标: 175.45.212.17
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://175.45.212.17/xplugin/SmartEdit/js/service/
主页发现的资源: https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 175.45.212.17
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://175.45.212.17/xplugin/SmartEdit/js/service/
主页发现的资源: https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:05:22]
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: homepage_analysis
基础路径: https://3.39.35.14/dext5upload/
主页发现的资源: https://3.39.35.14/dext5upload/js/dext5upload.js?t=20250519
发现的核心文件:
  - https://3.39.35.14/dext5upload/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: homepage_analysis
基础路径: https://3.39.35.14/dext5upload/
主页发现的资源: https://3.39.35.14/dext5upload/js/dext5upload.js?t=20250519
发现的核心文件:
  - https://3.39.35.14/dext5upload/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://3.39.35.14
发现的核心文件:
  - https://3.39.35.14/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://3.39.35.14
发现的核心文件:
  - https://3.39.35.14/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://3.39.35.14/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://3.39.35.14/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:18:21]
目标: ***************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://***************/admin/plugin/editor/js/service/
主页发现的资源: https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: ***************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://***************/admin/plugin/editor/js/service/
主页发现的资源: https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:20:42]
目标: m.mss1.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/
主页发现的资源: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js
发现的核心文件:
  - http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: m.mss1.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/
主页发现的资源: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js
发现的核心文件:
  - http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:29:03]
目标: **************:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://**************:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: **************:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://**************:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:31:44]
目标: 112.175.61.220
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://112.175.61.220/js/service/
主页发现的资源: https://112.175.61.220/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://112.175.61.220/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 112.175.61.220
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://112.175.61.220/js/service/
主页发现的资源: https://112.175.61.220/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://112.175.61.220/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:28:04]
目标: daemyungrental.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/dext5upload.js (valid) - 基础验证通过
  - http://daemyungrental.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - http://daemyungrental.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: daemyungrental.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/HuskyEZCreator.js (valid) - 验证成功
  - http://daemyungrental.kr/smart_editor2.js (valid) - 验证成功
辅助路径:
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - http://daemyungrental.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: daemyungrental.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/cheditor.js (valid) - 基础验证通过
  - http://daemyungrental.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - http://daemyungrental.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: daemyungrental.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - http://daemyungrental.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - http://daemyungrental.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: daemyungrental.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - http://daemyungrental.kr/core/connector/connector (rce) - 基础验证通过
  - http://daemyungrental.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - http://daemyungrental.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:52:38]
目标: 211.252.81.75
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.75/editor/js/service/
主页发现的资源: https://211.252.81.75/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.75/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.252.81.75
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.75/editor/js/service/
主页发现的资源: https://211.252.81.75/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.75/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:53:11]
目标: 211.252.81.72
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.72/editor/js/service/
主页发现的资源: https://211.252.81.72/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.72/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.252.81.72
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.72/editor/js/service/
主页发现的资源: https://211.252.81.72/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.72/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:55:19]
目标: 43.200.78.125
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/dext5upload.js (valid) - 基础验证通过
  - https://43.200.78.125/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://43.200.78.125/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.78.125/wm_engine_SW/_engine/smartEditor/
主页发现的资源: https://new.dev.bringko.com/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.78.125/wm_engine_SW/_engine/smartEditor/
主页发现的资源: https://new.dev.bringko.com/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/cheditor.js (valid) - 基础验证通过
  - https://43.200.78.125/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://43.200.78.125/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 43.200.78.125
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/ckeditor.js (valid) - 基础验证通过
辅助路径:

------------------------------
目标: 43.200.78.125
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/core/connector/connector (rce) - 基础验证通过
  - https://43.200.78.125/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://43.200.78.125/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:02:55]
目标: 58.120.165.167
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://58.120.165.167/xplugin/SmartEdit/js/service/
主页发现的资源: https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 58.120.165.167
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://58.120.165.167/xplugin/SmartEdit/js/service/
主页发现的资源: https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:07:08]
目标: 61.77.53.187
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.77.53.187/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 61.77.53.187
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.77.53.187/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:10:31]
目标: 211.251.238.188
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.251.238.188/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.251.238.188
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.251.238.188/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:11:06]
目标: 121.170.221.61:3000
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://121.170.221.61:3000/smart-editor/js/service/
主页发现的资源: http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 121.170.221.61:3000
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://121.170.221.61:3000/smart-editor/js/service/
主页发现的资源: http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:12:26]
目标: 211.251.238.36
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.251.238.36/xplugin/SmartEdit/js/service/
主页发现的资源: http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.251.238.36
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.251.238.36/xplugin/SmartEdit/js/service/
主页发现的资源: http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================


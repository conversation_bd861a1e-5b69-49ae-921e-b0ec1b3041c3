[可疑目标检测时间: 2025-06-21 12:08:32]
目标: memory.ddm.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/dext5upload.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: memory.ddm.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/HuskyEZCreator.js (valid) - 验证成功
  - https://memory.ddm.go.kr/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: memory.ddm.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/cheditor.js (valid) - 基础验证通过
  - https://memory.ddm.go.kr/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://memory.ddm.go.kr/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://memory.ddm.go.kr/files/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: memory.ddm.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://memory.ddm.go.kr/files/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: memory.ddm.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://memory.ddm.go.kr/files/
发现的核心文件:
  - https://memory.ddm.go.kr/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://memory.ddm.go.kr/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://memory.ddm.go.kr/files/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 12:10:29]
目标: 3.37.80.124
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/dext5upload.js (valid) - 基础验证通过
  - https://3.37.80.124/files/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: 3.37.80.124
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/HuskyEZCreator.js (valid) - 验证成功
  - https://3.37.80.124/files/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://3.37.80.124/files/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 3.37.80.124
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/cheditor.js (valid) - 基础验证通过
  - https://3.37.80.124/files/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://3.37.80.124/files/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://3.37.80.124/files/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 3.37.80.124
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://3.37.80.124/files/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: 3.37.80.124
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://3.37.80.124/files/
发现的核心文件:
  - https://3.37.80.124/files/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://3.37.80.124/files/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/core/connector/connector (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://3.37.80.124/files/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 13:57:35]
目标: chairman.gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://chairman.gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://chairman.gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://chairman.gangbuk.go.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: chairman.gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://chairman.gangbuk.go.kr
发现的核心文件:
  - https://chairman.gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://chairman.gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://chairman.gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:09:04]
目标: 211.34.96.55
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/dext5upload.js (valid) - 基础验证通过
  - https://211.34.96.55/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://211.34.96.55/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: 211.34.96.55
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/HuskyEZCreator.js (valid) - 基础验证通过
  - https://211.34.96.55/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://211.34.96.55/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://211.34.96.55/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: 211.34.96.55
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/cheditor.js (valid) - 基础验证通过
  - https://211.34.96.55/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://211.34.96.55/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://211.34.96.55/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://211.34.96.55/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 211.34.96.55
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://211.34.96.55/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: 211.34.96.55
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://211.34.96.55
发现的核心文件:
  - https://211.34.96.55/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://211.34.96.55/core/connector/php/connector.php (rce) - 基础验证通过
  - https://211.34.96.55/core/connector/connector (rce) - 基础验证通过
  - https://211.34.96.55/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://211.34.96.55/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:09:43]
目标: gangbuk.go.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/dext5upload.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/handler/dext5handler.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/dext5upload/handler/dext5handler.jsp (upload) - 基础验证通过

------------------------------
目标: gangbuk.go.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/HuskyEZCreator.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://gangbuk.go.kr/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: gangbuk.go.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/cheditor.js (valid) - 基础验证通过
  - https://gangbuk.go.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/imageUpload/upload.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/upload-simple.jsp (upload) - 基础验证通过
  - https://gangbuk.go.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: gangbuk.go.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr/common/
发现的核心文件:
  - https://gangbuk.go.kr/common/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/common/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/common/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://gangbuk.go.kr/common/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: gangbuk.go.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://gangbuk.go.kr
发现的核心文件:
  - https://gangbuk.go.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://gangbuk.go.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - https://gangbuk.go.kr/core/connector/connector (rce) - 基础验证通过
  - https://gangbuk.go.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://gangbuk.go.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:21:01]
目标: 210.122.100.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.122.100.38/xplugin/SmartEdit/js/service/
主页发现的资源: https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 210.122.100.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.122.100.38/xplugin/SmartEdit/js/service/
主页发现的资源: https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.122.100.38/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:22:46]
目标: 211.43.13.115
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.43.13.115/editor/static/js/service/
主页发现的资源: http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.43.13.115
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.43.13.115/editor/static/js/service/
主页发现的资源: http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.43.13.115/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:38:19]
目标: 61.107.76.98
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.98/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 61.107.76.98
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.98/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.98/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:43:04]
目标: 210.116.109.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.116.109.166/admin/naverEditor/js/service/
主页发现的资源: https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 210.116.109.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://210.116.109.166/admin/naverEditor/js/service/
主页发现的资源: https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://210.116.109.166/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 14:43:08]
目标: 59.9.253.137
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://59.9.253.137/assets/js/se/js/service/
主页发现的资源: https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 59.9.253.137
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://59.9.253.137/assets/js/se/js/service/
主页发现的资源: https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://59.9.253.137/assets/js/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:10:05]
目标: 211.188.57.30:8443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.188.57.30:8443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30:8443/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:19:58]
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.gcomija.co.kr/js/
主页发现的资源: https://www.gcomija.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.gcomija.co.kr/js/
主页发现的资源: https://www.gcomija.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.gcomija.co.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.gcomija.co.kr/js/
发现的核心文件:
  - https://www.gcomija.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:24:58]
目标: mydatasafe.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/dext5upload.js (valid) - 基础验证通过
  - https://mydatasafe.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://mydatasafe.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mydatasafe.kr/smarteditor/
主页发现的资源: https://mydatasafe.kr/smarteditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://mydatasafe.kr/smarteditor/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smarteditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/smarteditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mydatasafe.kr/smarteditor/
主页发现的资源: https://mydatasafe.kr/smarteditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://mydatasafe.kr/smarteditor/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smarteditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/smarteditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/smarteditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/HuskyEZCreator.js (valid) - 验证成功
  - https://mydatasafe.kr/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://mydatasafe.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: mydatasafe.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/cheditor.js (valid) - 基础验证通过
  - https://mydatasafe.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://mydatasafe.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://mydatasafe.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: mydatasafe.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr
发现的核心文件:
  - https://mydatasafe.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://mydatasafe.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: mydatasafe.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr/editors/
发现的核心文件:
  - https://mydatasafe.kr/editors/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://mydatasafe.kr/editors/core/connector/php/connector.php (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/core/connector/connector (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://mydatasafe.kr/editors/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:45:04]
目标: www.jaintextile.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/
主页发现的资源: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.jaintextile.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/
主页发现的资源: https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.jaintextile.co.kr/gs/out_module/smart_editor_2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:49:01]
目标: 14.32.191.9
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://14.32.191.9/boardEditor/js/service/
主页发现的资源: http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 14.32.191.9
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://14.32.191.9/boardEditor/js/service/
主页发现的资源: http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://14.32.191.9/boardEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:57:12]
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
主页发现的资源: https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
主页发现的资源: https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.touch-displays.co.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.touch-displays.co.kr/js/smarteditor/
发现的核心文件:
  - https://www.touch-displays.co.kr/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 15:57:26]
目标: www.dsliquid.com
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/dext5upload.js (valid) - 验证成功
  - https://www.dsliquid.com/dext5upload.css (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://www.dsliquid.com/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.dsliquid.com/editor/
主页发现的资源: https://www.dsliquid.com/editor/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.dsliquid.com/editor/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/editor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/editor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.dsliquid.com/editor/
主页发现的资源: https://www.dsliquid.com/editor/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.dsliquid.com/editor/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/editor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/editor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/editor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/HuskyEZCreator.js (valid) - 验证成功
  - https://www.dsliquid.com/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.dsliquid.com/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.dsliquid.com
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/cheditor.js (valid) - 验证成功
  - https://www.dsliquid.com/cheditor.css (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://www.dsliquid.com/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://www.dsliquid.com/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: www.dsliquid.com
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/ckeditor.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.dsliquid.com
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com
发现的核心文件:
  - https://www.dsliquid.com/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://www.dsliquid.com/core/connector/connector (rce) - 包含RCE相关内容
  - https://www.dsliquid.com/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://www.dsliquid.com/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:04:20]
目标: 3.39.136.151
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.39.136.151/js/service/
主页发现的资源: https://3.39.136.151/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.39.136.151/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.136.151
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.39.136.151/js/service/
主页发现的资源: https://3.39.136.151/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.39.136.151/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:07:45]
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.38.251.48/js/
主页发现的资源: https://3.38.251.48/js/HuskyEZCreator.js
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.38.251.48/js/
主页发现的资源: https://3.38.251.48/js/HuskyEZCreator.js
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.38.251.48
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://3.38.251.48/js/
发现的核心文件:
  - https://3.38.251.48/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:14:31]
目标: ************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://************/xplugin/SmartEdit/js/service/
主页发现的资源: http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: ************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://************/xplugin/SmartEdit/js/service/
主页发现的资源: http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:25:04]
目标: *************:3201
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://*************:3201/Scripts/js/choose/
主页发现的资源: http://*************:3201/Scripts/js/choose/HuskyEZCreator.js
发现的核心文件:
  - http://*************:3201/Scripts/js/choose/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: *************:3201
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://*************:3201/Scripts/js/choose/
主页发现的资源: http://*************:3201/Scripts/js/choose/HuskyEZCreator.js
发现的核心文件:
  - http://*************:3201/Scripts/js/choose/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:39:11]
目标: mosim.seesaw.land
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mosim.seesaw.land/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: mosim.seesaw.land
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://mosim.seesaw.land/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://mosim.seesaw.land/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:42:08]
目标: scat.co.kr:7711
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://scat.co.kr:7711/resources/newEditor/js/service/
主页发现的资源: http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: scat.co.kr:7711
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://scat.co.kr:7711/resources/newEditor/js/service/
主页发现的资源: http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://scat.co.kr:7711/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:44:46]
目标: 52.79.184.60
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://52.79.184.60/adm/webedit/js/service/
主页发现的资源: https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 52.79.184.60
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://52.79.184.60/adm/webedit/js/service/
主页发现的资源: https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://52.79.184.60/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:51:17]
目标: 211.188.57.30
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.188.57.30
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.188.57.30/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.188.57.30/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 16:57:55]
目标: 175.45.212.17
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://175.45.212.17/xplugin/SmartEdit/js/service/
主页发现的资源: https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 175.45.212.17
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://175.45.212.17/xplugin/SmartEdit/js/service/
主页发现的资源: https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://175.45.212.17/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:05:22]
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: homepage_analysis
基础路径: https://3.39.35.14/dext5upload/
主页发现的资源: https://3.39.35.14/dext5upload/js/dext5upload.js?t=20250519
发现的核心文件:
  - https://3.39.35.14/dext5upload/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: homepage_analysis
基础路径: https://3.39.35.14/dext5upload/
主页发现的资源: https://3.39.35.14/dext5upload/js/dext5upload.js?t=20250519
发现的核心文件:
  - https://3.39.35.14/dext5upload/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://3.39.35.14
发现的核心文件:
  - https://3.39.35.14/dext5upload.css (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.39.35.14
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://3.39.35.14
发现的核心文件:
  - https://3.39.35.14/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://3.39.35.14/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://3.39.35.14/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:18:21]
目标: ***************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://***************/admin/plugin/editor/js/service/
主页发现的资源: https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: ***************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://***************/admin/plugin/editor/js/service/
主页发现的资源: https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://***************/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:20:42]
目标: m.mss1.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/
主页发现的资源: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js
发现的核心文件:
  - http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: m.mss1.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/
主页发现的资源: http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js
发现的核心文件:
  - http://m.mss1.net/mvwizFramework7.2/mvwizWebCom/javascript/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:29:03]
目标: **************:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://**************:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: **************:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://**************:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://**************:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 17:31:44]
目标: 112.175.61.220
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://112.175.61.220/js/service/
主页发现的资源: https://112.175.61.220/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://112.175.61.220/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 112.175.61.220
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://112.175.61.220/js/service/
主页发现的资源: https://112.175.61.220/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://112.175.61.220/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:28:04]
目标: daemyungrental.kr
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/dext5upload.js (valid) - 基础验证通过
  - http://daemyungrental.kr/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - http://daemyungrental.kr/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: daemyungrental.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/HuskyEZCreator.js (valid) - 验证成功
  - http://daemyungrental.kr/smart_editor2.js (valid) - 验证成功
辅助路径:
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - http://daemyungrental.kr/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: daemyungrental.kr
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/cheditor.js (valid) - 基础验证通过
  - http://daemyungrental.kr/cheditor.css (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/imageUpload/delete.jsp (delete) - 基础验证通过
  - http://daemyungrental.kr/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - http://daemyungrental.kr/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: daemyungrental.kr
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/ckeditor.js (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - http://daemyungrental.kr/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - http://daemyungrental.kr/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: daemyungrental.kr
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: http://daemyungrental.kr
发现的核心文件:
  - http://daemyungrental.kr/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - http://daemyungrental.kr/core/connector/php/connector.php (rce) - 基础验证通过
  - http://daemyungrental.kr/core/connector/connector (rce) - 基础验证通过
  - http://daemyungrental.kr/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - http://daemyungrental.kr/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:52:38]
目标: 211.252.81.75
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.75/editor/js/service/
主页发现的资源: https://211.252.81.75/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.75/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.252.81.75
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.75/editor/js/service/
主页发现的资源: https://211.252.81.75/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.75/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:53:11]
目标: 211.252.81.72
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.72/editor/js/service/
主页发现的资源: https://211.252.81.72/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.72/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.252.81.72
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.252.81.72/editor/js/service/
主页发现的资源: https://211.252.81.72/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.252.81.72/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 18:55:19]
目标: 43.200.78.125
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/dext5upload.js (valid) - 基础验证通过
  - https://43.200.78.125/dext5upload.css (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/handler/dext5handler.ashx (upload) - 包含上传相关内容
  - https://43.200.78.125/dext5upload/handler/dext5handler.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/dext5upload/handler/dext5handler.ashx (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.78.125/wm_engine_SW/_engine/smartEditor/
主页发现的资源: https://new.dev.bringko.com/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.78.125/wm_engine_SW/_engine/smartEditor/
主页发现的资源: https://new.dev.bringko.com/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/HuskyEZCreator.js (valid) - 验证成功
  - https://43.200.78.125/smart_editor2.js (valid) - 验证成功
辅助路径:
  - https://43.200.78.125/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.78.125/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: 43.200.78.125
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/cheditor.js (valid) - 基础验证通过
  - https://43.200.78.125/cheditor.css (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://43.200.78.125/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://43.200.78.125/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: 43.200.78.125
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/ckeditor.js (valid) - 基础验证通过
辅助路径:

------------------------------
目标: 43.200.78.125
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://43.200.78.125
发现的核心文件:
  - https://43.200.78.125/ckfinder.js (valid) - 基础验证通过
辅助路径:
  - https://43.200.78.125/core/connector/connector (rce) - 基础验证通过
  - https://43.200.78.125/plugins/fileeditor/plugin.js (rce) - 基础验证通过
  - https://43.200.78.125/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:02:55]
目标: 58.120.165.167
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://58.120.165.167/xplugin/SmartEdit/js/service/
主页发现的资源: https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 58.120.165.167
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://58.120.165.167/xplugin/SmartEdit/js/service/
主页发现的资源: https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://58.120.165.167/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:07:08]
目标: 61.77.53.187
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.77.53.187/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 61.77.53.187
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.77.53.187/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.77.53.187/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:10:31]
目标: 211.251.238.188
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.251.238.188/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.251.238.188
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.251.238.188/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.251.238.188/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:11:06]
目标: 121.170.221.61:3000
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://121.170.221.61:3000/smart-editor/js/service/
主页发现的资源: http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 121.170.221.61:3000
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://121.170.221.61:3000/smart-editor/js/service/
主页发现的资源: http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://121.170.221.61:3000/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:12:26]
目标: 211.251.238.36
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.251.238.36/xplugin/SmartEdit/js/service/
主页发现的资源: http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.251.238.36
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://211.251.238.36/xplugin/SmartEdit/js/service/
主页发现的资源: http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://211.251.238.36/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:15:20]
目标: 223.130.161.165:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://223.130.161.165:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://223.130.161.165:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://223.130.161.165:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 223.130.161.165:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://223.130.161.165:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://223.130.161.165:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://223.130.161.165:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:21:08]
目标: 43.200.108.70
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.108.70/adm/webedit/js/service/
主页发现的资源: https://43.200.108.70/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.108.70/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 43.200.108.70
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.108.70/adm/webedit/js/service/
主页发现的资源: https://43.200.108.70/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://43.200.108.70/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:33:41]
目标: 211.192.0.34
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.192.0.34/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.192.0.34/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.192.0.34/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.192.0.34
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.192.0.34/xplugin/SmartEdit/js/service/
主页发现的资源: https://211.192.0.34/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.192.0.34/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:33:48]
目标: 125.142.60.112
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://125.142.60.112/resources/newEditor/js/service/
主页发现的资源: http://125.142.60.112/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://125.142.60.112/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 125.142.60.112
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://125.142.60.112/resources/newEditor/js/service/
主页发现的资源: http://125.142.60.112/resources/newEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://125.142.60.112/resources/newEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:52:55]
目标: 3.37.5.165
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.37.5.165/smart_editor2/js/service/
主页发现的资源: https://3.37.5.165/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.37.5.165/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 3.37.5.165
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://3.37.5.165/smart_editor2/js/service/
主页发现的资源: https://3.37.5.165/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://3.37.5.165/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 19:53:24]
目标: unv.or.kr:42224
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://unv.or.kr:42224/editor/static/js/service/
主页发现的资源: https://unv.or.kr:42224/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://unv.or.kr:42224/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: unv.or.kr:42224
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://unv.or.kr:42224/editor/static/js/service/
主页发现的资源: https://unv.or.kr:42224/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://unv.or.kr:42224/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 20:02:27]
目标: 211.110.139.175
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.110.139.175/editor/static/js/service/
主页发现的资源: https://211.110.139.175/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.110.139.175/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 211.110.139.175
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://211.110.139.175/editor/static/js/service/
主页发现的资源: https://211.110.139.175/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://211.110.139.175/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 21:15:06]
目标: youth.gyeongnam.go.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/?v=20250401/
主页发现的资源: https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/HuskyEZCreator.js?v=20250401
发现的核心文件:
  - https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: youth.gyeongnam.go.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/?v=20250401/
主页发现的资源: https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/HuskyEZCreator.js?v=20250401
发现的核心文件:
  - https://youth.gyeongnam.go.kr/youth/ease_src/sEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 21:22:27]
目标: **************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://**************/xplugin/SmartEdit/js/service/
主页发现的资源: https://**************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://**************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: **************
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://**************/xplugin/SmartEdit/js/service/
主页发现的资源: https://**************/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://**************/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 21:24:59]
目标: 13.209.133.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://13.209.133.38/assets/js/editer/js/service/
主页发现的资源: https://13.209.133.38/assets/js/editer/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://13.209.133.38/assets/js/editer/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 13.209.133.38
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://13.209.133.38/assets/js/editer/js/service/
主页发现的资源: https://13.209.133.38/assets/js/editer/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://13.209.133.38/assets/js/editer/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 21:25:49]
目标: www.unv.or.kr:47771
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.unv.or.kr:47771/editor/static/js/service/
主页发现的资源: https://www.unv.or.kr:47771/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.unv.or.kr:47771/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.unv.or.kr:47771
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.unv.or.kr:47771/editor/static/js/service/
主页发现的资源: https://www.unv.or.kr:47771/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.unv.or.kr:47771/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 22:10:05]
目标: xn--h50bs1si7dipht3j6rd.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/
主页发现的资源: https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: xn--h50bs1si7dipht3j6rd.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/
主页发现的资源: https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/HuskyEZCreator.js
发现的核心文件:
  - https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: xn--h50bs1si7dipht3j6rd.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/
发现的核心文件:
  - https://xn--h50bs1si7dipht3j6rd.com/js/smarteditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 22:27:19]
目标: isaac-s.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://isaac-s.com/plugins/webedit/js/service/
主页发现的资源: https://isaac-s.com/plugins/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://isaac-s.com/plugins/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: isaac-s.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://isaac-s.com/plugins/webedit/js/service/
主页发现的资源: https://isaac-s.com/plugins/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://isaac-s.com/plugins/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 22:31:26]
目标: 27.101.224.50
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://27.101.224.50/rsc/;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10/
主页发现的资源: https://27.101.224.50/rsc/HuskyEZCreator.js;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10
发现的核心文件:
  - https://27.101.224.50/rsc/;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 27.101.224.50
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://27.101.224.50/rsc/;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10/
主页发现的资源: https://27.101.224.50/rsc/HuskyEZCreator.js;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10
发现的核心文件:
  - https://27.101.224.50/rsc/;jsessionid=SL5xZ4rK-kAXDM3u2q2hpCyaxert0OK-oyE-Q5a1.ap10/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 22:41:05]
目标: gs.comeet.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://gs.comeet.co.kr/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://gs.comeet.co.kr/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://gs.comeet.co.kr/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: gs.comeet.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://gs.comeet.co.kr/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://gs.comeet.co.kr/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://gs.comeet.co.kr/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 23:16:11]
目标: www.chatocha.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.chatocha.co.kr/module/editor/js/?v=20250622001110/
主页发现的资源: https://www.chatocha.co.kr/module/editor/js/HuskyEZCreator.js?v=20250622001110
发现的核心文件:
  - https://www.chatocha.co.kr/module/editor/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.chatocha.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.chatocha.co.kr/module/editor/js/?v=20250622001110/
主页发现的资源: https://www.chatocha.co.kr/module/editor/js/HuskyEZCreator.js?v=20250622001110
发现的核心文件:
  - https://www.chatocha.co.kr/module/editor/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-21 23:16:19]
目标: 103.59.156.50
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://103.59.156.50/js/smartEditor/
主页发现的资源: https://103.59.156.50/js/smartEditor/HuskyEZCreator.js?v=********
发现的核心文件:
  - https://103.59.156.50/js/smartEditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 103.59.156.50
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://103.59.156.50/js/smartEditor/
主页发现的资源: https://103.59.156.50/js/smartEditor/HuskyEZCreator.js?v=********
发现的核心文件:
  - https://103.59.156.50/js/smartEditor/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 00:08:57]
目标: itsm.busanbank.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/?v=1/
主页发现的资源: https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js?v=1
发现的核心文件:
  - https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: itsm.busanbank.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/?v=1/
主页发现的资源: https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js?v=1
发现的核心文件:
  - https://itsm.busanbank.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 00:15:53]
目标: 43.200.131.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.131.166/se/js/service/?v=2506201/
主页发现的资源: https://43.200.131.166/se/js/service/HuskyEZCreator.js?v=2506201
发现的核心文件:
  - https://43.200.131.166/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.131.166/se/js/service/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
目标: 43.200.131.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://43.200.131.166/se/js/service/?v=2506201/
主页发现的资源: https://43.200.131.166/se/js/service/HuskyEZCreator.js?v=2506201
发现的核心文件:
  - https://43.200.131.166/se/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader.jsp (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader.php (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/file_uploader_html5.jsp (upload) - 基础验证通过
  - https://43.200.131.166/se/js/service/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://43.200.131.166/se/js/service/photo_uploader/popup/file_uploader.jsp (upload) - 基础验证通过

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 00:26:03]
目标: kfoodit.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://kfoodit.com/static/api/editor/dist/js/service/
主页发现的资源: http://kfoodit.com/static/api/editor/dist/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://kfoodit.com/static/api/editor/dist/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: kfoodit.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://kfoodit.com/static/api/editor/dist/js/service/
主页发现的资源: http://kfoodit.com/static/api/editor/dist/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://kfoodit.com/static/api/editor/dist/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 00:33:40]
目标: gymg.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://gymg.co.kr/assets/js/edit/demo/js/service/
主页发现的资源: http://gymg.co.kr/assets/js/edit/demo/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://gymg.co.kr/assets/js/edit/demo/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: gymg.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://gymg.co.kr/assets/js/edit/demo/js/service/
主页发现的资源: http://gymg.co.kr/assets/js/edit/demo/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://gymg.co.kr/assets/js/edit/demo/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 01:24:00]
目标: www.total-impact.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.total-impact.net/plugins/webedit/js/service/
主页发现的资源: https://www.total-impact.net/plugins/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.total-impact.net/plugins/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.total-impact.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.total-impact.net/plugins/webedit/js/service/
主页发现的资源: https://www.total-impact.net/plugins/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.total-impact.net/plugins/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 01:54:07]
目标: 101.79.8.195
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://101.79.8.195/xplugin/SmartEdit/js/service/
主页发现的资源: http://101.79.8.195/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://101.79.8.195/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 101.79.8.195
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://101.79.8.195/xplugin/SmartEdit/js/service/
主页发现的资源: http://101.79.8.195/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://101.79.8.195/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 02:07:01]
目标: www.seoro.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.seoro.com/js/
主页发现的资源: https://www.seoro.com/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.seoro.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.seoro.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.seoro.com/js/
主页发现的资源: https://www.seoro.com/js/HuskyEZCreator.js
发现的核心文件:
  - https://www.seoro.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.seoro.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.seoro.com/js/
发现的核心文件:
  - https://www.seoro.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 02:16:32]
目标: www.petcarecard.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.petcarecard.net/adm/webedit/js/service/
主页发现的资源: https://www.petcarecard.net/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.petcarecard.net/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.petcarecard.net
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.petcarecard.net/adm/webedit/js/service/
主页发现的资源: https://www.petcarecard.net/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.petcarecard.net/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 02:25:02]
目标: admin.happycall365.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://admin.happycall365.com/assets/js/editer/js/service/
主页发现的资源: https://admin.happycall365.com/assets/js/editer/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://admin.happycall365.com/assets/js/editer/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: admin.happycall365.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://admin.happycall365.com/assets/js/editer/js/service/
主页发现的资源: https://admin.happycall365.com/assets/js/editer/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://admin.happycall365.com/assets/js/editer/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 02:41:36]
目标: 223.130.138.117:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://223.130.138.117:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://223.130.138.117:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://223.130.138.117:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 223.130.138.117:8080
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://223.130.138.117:8080/xplugin/SmartEdit/js/service/
主页发现的资源: http://223.130.138.117:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://223.130.138.117:8080/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 02:43:25]
目标: songgwangsamuseum.org
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://songgwangsamuseum.org/editor/static/js/service/
主页发现的资源: http://songgwangsamuseum.org/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://songgwangsamuseum.org/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: songgwangsamuseum.org
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://songgwangsamuseum.org/editor/static/js/service/
主页发现的资源: http://songgwangsamuseum.org/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://songgwangsamuseum.org/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 03:06:53]
目标: dongwonapt.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dongwonapt.co.kr/js/
主页发现的资源: https://dongwonapt.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://dongwonapt.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dongwonapt.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dongwonapt.co.kr/js/
主页发现的资源: https://dongwonapt.co.kr/js/HuskyEZCreator.js
发现的核心文件:
  - https://dongwonapt.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dongwonapt.co.kr
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://dongwonapt.co.kr/js/
发现的核心文件:
  - https://dongwonapt.co.kr/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 03:25:22]
目标: food-it.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://food-it.co.kr/static/api/editor/dist/js/service/
主页发现的资源: https://food-it.co.kr/static/api/editor/dist/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://food-it.co.kr/static/api/editor/dist/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: food-it.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://food-it.co.kr/static/api/editor/dist/js/service/
主页发现的资源: https://food-it.co.kr/static/api/editor/dist/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://food-it.co.kr/static/api/editor/dist/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 04:30:11]
目标: 118.37.44.67:8480
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://118.37.44.67:8480/editor/js/service/
主页发现的资源: http://118.37.44.67:8480/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://118.37.44.67:8480/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 118.37.44.67:8480
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://118.37.44.67:8480/editor/js/service/
主页发现的资源: http://118.37.44.67:8480/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - http://118.37.44.67:8480/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 04:58:39]
目标: 61.107.76.82
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.82/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.82/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.82/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 61.107.76.82
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://61.107.76.82/xplugin/SmartEdit/js/service/
主页发现的资源: https://61.107.76.82/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://61.107.76.82/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 05:18:46]
目标: dev.tscloud.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev.tscloud.co.kr/js/service/
主页发现的资源: https://dev.tscloud.co.kr/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://dev.tscloud.co.kr/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dev.tscloud.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev.tscloud.co.kr/js/service/
主页发现的资源: https://dev.tscloud.co.kr/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://dev.tscloud.co.kr/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 05:31:15]
目标: www.zerosquare.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/
主页发现的资源: https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.zerosquare.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/
主页发现的资源: https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.zerosquare.co.kr/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 05:51:55]
目标: dronesoccer.or.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dronesoccer.or.kr/smart_editor2/js/service/
主页发现的资源: https://dronesoccer.or.kr/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://dronesoccer.or.kr/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dronesoccer.or.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dronesoccer.or.kr/smart_editor2/js/service/
主页发现的资源: https://dronesoccer.or.kr/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://dronesoccer.or.kr/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 06:01:44]
目标: 15.165.159.191
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://15.165.159.191/admin/plugin/editor/js/service/
主页发现的资源: https://15.165.159.191/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://15.165.159.191/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 15.165.159.191
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://15.165.159.191/admin/plugin/editor/js/service/
主页发现的资源: https://15.165.159.191/admin/plugin/editor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://15.165.159.191/admin/plugin/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 06:07:46]
目标: i-sim.cloud
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://i-sim.cloud/editor/js/?version=1/
主页发现的资源: https://i-sim.cloud/editor/js/HuskyEZCreator.js?version=1
发现的核心文件:
  - https://i-sim.cloud/editor/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: i-sim.cloud
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://i-sim.cloud/editor/js/?version=1/
主页发现的资源: https://i-sim.cloud/editor/js/HuskyEZCreator.js?version=1
发现的核心文件:
  - https://i-sim.cloud/editor/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 06:34:49]
目标: m.bestklife.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://m.bestklife.com/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://m.bestklife.com/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://m.bestklife.com/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: m.bestklife.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://m.bestklife.com/lib/smart_editor/js/service/?ver=70/
主页发现的资源: https://m.bestklife.com/lib/smart_editor/js/service/HuskyEZCreator.js?ver=70
发现的核心文件:
  - https://m.bestklife.com/lib/smart_editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 06:35:08]
目标: www.todac.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.todac.co.kr/editor/static/js/service/
主页发现的资源: https://www.todac.co.kr/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.todac.co.kr/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.todac.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.todac.co.kr/editor/static/js/service/
主页发现的资源: https://www.todac.co.kr/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.todac.co.kr/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 07:07:53]
目标: xn--910bm6oi7ah8vb8bz5e34o.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/?20200830131631/
主页发现的资源: http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/HuskyEZCreator.js?20200830131631
发现的核心文件:
  - http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: xn--910bm6oi7ah8vb8bz5e34o.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/?20200830131631/
主页发现的资源: http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/HuskyEZCreator.js?20200830131631
发现的核心文件:
  - http://xn--910bm6oi7ah8vb8bz5e34o.com/layouts/ks_02/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 07:34:17]
目标: www.trimble.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.trimble.kr/adm/webedit/js/service/
主页发现的资源: https://www.trimble.kr/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.trimble.kr/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.trimble.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.trimble.kr/adm/webedit/js/service/
主页发现的资源: https://www.trimble.kr/adm/webedit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.trimble.kr/adm/webedit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 07:40:38]
目标: 218.236.125.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://218.236.125.166/xplugin/SmartEdit/js/service/
主页发现的资源: https://218.236.125.166/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://218.236.125.166/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 218.236.125.166
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://218.236.125.166/xplugin/SmartEdit/js/service/
主页发现的资源: https://218.236.125.166/xplugin/SmartEdit/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://218.236.125.166/xplugin/SmartEdit/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 07:46:03]
目标: 35.216.27.52
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://35.216.27.52/assets/js_new/
主页发现的资源: https://35.216.27.52/assets/js_new/HuskyEZCreator.js
发现的核心文件:
  - https://35.216.27.52/assets/js_new/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: 35.216.27.52
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://35.216.27.52/assets/js_new/
主页发现的资源: https://35.216.27.52/assets/js_new/HuskyEZCreator.js
发现的核心文件:
  - https://35.216.27.52/assets/js_new/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 08:05:59]
目标: dev-member.payappcafe.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev-member.payappcafe.com/statics/editor/js/service/?ver=20250622090155/
主页发现的资源: https://dev-member.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js?ver=20250622090155
发现的核心文件:
  - https://dev-member.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dev-member.payappcafe.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev-member.payappcafe.com/statics/editor/js/service/?ver=20250622090155/
主页发现的资源: https://dev-member.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js?ver=20250622090155
发现的核心文件:
  - https://dev-member.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 08:14:02]
目标: www.p-tech2000.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://www.p-tech2000.com/js/
主页发现的资源: http://www.p-tech2000.com/js/HuskyEZCreator.js
发现的核心文件:
  - http://www.p-tech2000.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.p-tech2000.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://www.p-tech2000.com/js/
主页发现的资源: http://www.p-tech2000.com/js/HuskyEZCreator.js
发现的核心文件:
  - http://www.p-tech2000.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.p-tech2000.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://www.p-tech2000.com/js/
发现的核心文件:
  - http://www.p-tech2000.com/js/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 08:25:08]
目标: gifted.hanyang.ac.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/
主页发现的资源: https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: gifted.hanyang.ac.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/
主页发现的资源: https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://gifted.hanyang.ac.kr/admin/naverEditor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 09:38:48]
目标: highedu.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://highedu.co.kr/application/third_party/smart_editor2/js/service/
主页发现的资源: https://highedu.co.kr/application/third_party/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://highedu.co.kr/application/third_party/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: highedu.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://highedu.co.kr/application/third_party/smart_editor2/js/service/
主页发现的资源: https://highedu.co.kr/application/third_party/smart_editor2/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://highedu.co.kr/application/third_party/smart_editor2/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 09:53:24]
目标: baro.gyeongnam.go.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/
主页发现的资源: https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: baro.gyeongnam.go.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/
主页发现的资源: https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://baro.gyeongnam.go.kr/baro/source/sEditorNew/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 10:02:26]
目标: tnc.or.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://tnc.or.kr/resources/core/js/vendor/smartEditor2/
主页发现的资源: https://tnc.or.kr/resources/core/js/vendor/smartEditor2/HuskyEZCreator.js?6154868
发现的核心文件:
  - https://tnc.or.kr/resources/core/js/vendor/smartEditor2/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: tnc.or.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://tnc.or.kr/resources/core/js/vendor/smartEditor2/
主页发现的资源: https://tnc.or.kr/resources/core/js/vendor/smartEditor2/HuskyEZCreator.js?6154868
发现的核心文件:
  - https://tnc.or.kr/resources/core/js/vendor/smartEditor2/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 10:44:33]
目标: mudo.sg.ac.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://mudo.sg.ac.kr/js_mudo/
主页发现的资源: http://mudo.sg.ac.kr/js_mudo/HuskyEZCreator.js
发现的核心文件:
  - http://mudo.sg.ac.kr/js_mudo/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: mudo.sg.ac.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: http://mudo.sg.ac.kr/js_mudo/
主页发现的资源: http://mudo.sg.ac.kr/js_mudo/HuskyEZCreator.js
发现的核心文件:
  - http://mudo.sg.ac.kr/js_mudo/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 10:48:09]
目标: www.goyangdestinationweek.com
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.goyangdestinationweek.com/assets/
发现的核心文件:
  - https://www.goyangdestinationweek.com/assets/HuskyEZCreator.js (valid) - 验证成功
  - https://www.goyangdestinationweek.com/assets/smart_editor2.js (valid) - 基础验证通过
辅助路径:
  - https://www.goyangdestinationweek.com/assets/sample/photo_uploader/file_uploader.jsp (upload) - 包含上传相关内容
  - https://www.goyangdestinationweek.com/assets/sample/photo_uploader/file_uploader.php (upload) - 包含上传相关内容
  - https://www.goyangdestinationweek.com/assets/sample/photo_uploader/file_uploader_html5.jsp (upload) - 包含上传相关内容
  - https://www.goyangdestinationweek.com/assets/sample/photo_uploader/photo_uploader.html (info) - 信息页面内容充实
  - https://www.goyangdestinationweek.com/assets/photo_uploader/popup/file_uploader.jsp (upload) - 包含上传相关内容

------------------------------
目标: www.goyangdestinationweek.com
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://www.goyangdestinationweek.com
发现的核心文件:
  - https://www.goyangdestinationweek.com/cheditor.js (valid) - 验证成功
  - https://www.goyangdestinationweek.com/cheditor.css (valid) - 验证成功
辅助路径:
  - https://www.goyangdestinationweek.com/imageUpload/upload.jsp (upload) - 包含上传相关内容
  - https://www.goyangdestinationweek.com/imageUpload/delete.jsp (delete) - 基础验证通过
  - https://www.goyangdestinationweek.com/imageUpload/upload-simple.jsp (upload) - 包含上传相关内容
  - https://www.goyangdestinationweek.com/imageUpload/config.jsp (info) - 信息页面内容充实

------------------------------
目标: www.goyangdestinationweek.com
编辑器类型: ckeditor
检测方法: directory_fuzzing
基础路径: https://www.goyangdestinationweek.com
发现的核心文件:
  - https://www.goyangdestinationweek.com/ckeditor.js (valid) - 验证成功
辅助路径:
  - https://www.goyangdestinationweek.com/_samples/sample_posteddata.php (info) - 信息页面内容充实
  - https://www.goyangdestinationweek.com/_samples/assets/_posteddata.php (info) - 信息页面内容充实
  - https://www.goyangdestinationweek.com/samples/sample_posteddata.php (info) - 信息页面内容充实

------------------------------
目标: www.goyangdestinationweek.com
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.goyangdestinationweek.com
发现的核心文件:
  - https://www.goyangdestinationweek.com/ckfinder.js (valid) - 验证成功
辅助路径:
  - https://www.goyangdestinationweek.com/core/connector/php/connector.php (rce) - 包含RCE相关内容
  - https://www.goyangdestinationweek.com/core/connector/connector (rce) - 包含RCE相关内容
  - https://www.goyangdestinationweek.com/plugins/fileeditor/plugin.js (rce) - 包含RCE相关内容
  - https://www.goyangdestinationweek.com/ckfinder.html (info) - 信息页面内容充实

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 11:06:18]
目标: manager.caretbio.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://manager.caretbio.com/assets/plugins/smart-editor/js/service/?v=2022080704342/
主页发现的资源: https://manager.caretbio.com/assets/plugins/smart-editor/js/service/HuskyEZCreator.js?v=2022080704342
发现的核心文件:
  - https://manager.caretbio.com/assets/plugins/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: manager.caretbio.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://manager.caretbio.com/assets/plugins/smart-editor/js/service/?v=2022080704342/
主页发现的资源: https://manager.caretbio.com/assets/plugins/smart-editor/js/service/HuskyEZCreator.js?v=2022080704342
发现的核心文件:
  - https://manager.caretbio.com/assets/plugins/smart-editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 12:08:47]
目标: www.concon.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.concon.co.kr/editor/static/js/service/
主页发现的资源: https://www.concon.co.kr/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.concon.co.kr/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: www.concon.co.kr
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://www.concon.co.kr/editor/static/js/service/
主页发现的资源: https://www.concon.co.kr/editor/static/js/service/HuskyEZCreator.js
发现的核心文件:
  - https://www.concon.co.kr/editor/static/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================

[可疑目标检测时间: 2025-06-22 12:21:33]
目标: dev-admin.payappcafe.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev-admin.payappcafe.com/statics/editor/js/service/?ver=20250622131408/
主页发现的资源: https://dev-admin.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js?ver=20250622131408
发现的核心文件:
  - https://dev-admin.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
目标: dev-admin.payappcafe.com
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://dev-admin.payappcafe.com/statics/editor/js/service/?ver=20250622131408/
主页发现的资源: https://dev-admin.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js?ver=20250622131408
发现的核心文件:
  - https://dev-admin.payappcafe.com/statics/editor/js/service/HuskyEZCreator.js (valid) - 验证成功
辅助路径:

------------------------------
==================================================


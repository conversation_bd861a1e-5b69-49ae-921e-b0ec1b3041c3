﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: ListViewDbclick</h3>
    <p class="ttl">config.ListViewDbclick</p>
    <p class="txt">
        업로드 영역을 더블클릭 이벤트에 의해 파일선택 창이 열리도록 설정합니다.(편집 모드)
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "1"이고 더블클릭 이벤트를 적용, "0" 으로 설정시 더블클릭 이벤트가 적용되지 않습니다.<br/>
        (IE 브라우저 모드는 IE10이상부터 가능합니다. 플러그인 모드는 IE브라우저 모드 상관 없이 사용 가능합니다.) 
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 영역에 더블클릭 이벤트를 미 설정합니다.
        DEXT5UPLOAD.config.ListViewDbclick = '0';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


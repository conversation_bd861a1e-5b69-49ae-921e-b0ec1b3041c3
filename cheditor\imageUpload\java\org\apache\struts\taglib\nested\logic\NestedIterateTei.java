package org.apache.struts.taglib.nested.logic;

import javax.servlet.jsp.tagext.TagData;
import javax.servlet.jsp.tagext.VariableInfo;
import org.apache.struts.taglib.logic.IterateTei;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/logic/NestedIterateTei.class */
public class NestedIterateTei extends IterateTei {
    @Override // org.apache.struts.taglib.logic.IterateTei
    public VariableInfo[] getVariableInfo(TagData data) {
        return super.getVariableInfo(data);
    }
}

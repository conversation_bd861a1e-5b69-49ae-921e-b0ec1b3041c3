package jcifs.http;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandler;
import java.net.URLStreamHandlerFactory;
import java.util.HashMap;
import java.util.Map;
import java.util.StringTokenizer;

/* loaded from: jcifs-1.3.18.jar:jcifs/http/Handler.class */
public class Hand<PERSON> extends URLStreamHandler {
    public static final int DEFAULT_HTTP_PORT = 80;
    private static final String HANDLER_PKGS_PROPERTY = "java.protocol.handler.pkgs";
    private static URLStreamHandlerFactory factory;
    private static final Map PROTOCOL_HANDLERS = new HashMap();
    private static final String[] JVM_VENDOR_DEFAULT_PKGS = {"sun.net.www.protocol"};

    public static void setURLStreamHandlerFactory(URLStreamHandlerFactory factory2) {
        synchronized (PROTOCOL_HANDLERS) {
            if (factory != null) {
                throw new IllegalStateException("URLStreamHandlerFactory already set.");
            }
            PROTOCOL_HANDLERS.clear();
            factory = factory2;
        }
    }

    @Override // java.net.URLStreamHandler
    protected int getDefaultPort() {
        return 80;
    }

    @Override // java.net.URLStreamHandler
    protected URLConnection openConnection(URL url) throws IOException {
        return new NtlmHttpURLConnection((HttpURLConnection) new URL(url, url.toExternalForm(), getDefaultStreamHandler(url.getProtocol())).openConnection());
    }

    private static URLStreamHandler getDefaultStreamHandler(String protocol) throws IOException {
        synchronized (PROTOCOL_HANDLERS) {
            URLStreamHandler handler = (URLStreamHandler) PROTOCOL_HANDLERS.get(protocol);
            if (handler != null) {
                return handler;
            }
            if (factory != null) {
                handler = factory.createURLStreamHandler(protocol);
            }
            if (handler == null) {
                String path = System.getProperty(HANDLER_PKGS_PROPERTY);
                StringTokenizer tokenizer = new StringTokenizer(path, "|");
                while (tokenizer.hasMoreTokens()) {
                    String provider = tokenizer.nextToken().trim();
                    if (!provider.equals("jcifs")) {
                        String className = provider + "." + protocol + ".Handler";
                        Class handlerClass = null;
                        try {
                            try {
                                handlerClass = Class.forName(className);
                            } catch (Exception e) {
                            }
                        } catch (Exception e2) {
                        }
                        if (handlerClass == null) {
                            handlerClass = ClassLoader.getSystemClassLoader().loadClass(className);
                        }
                        handler = (URLStreamHandler) handlerClass.newInstance();
                        break;
                    }
                }
            }
            if (handler == null) {
                for (int i = 0; i < JVM_VENDOR_DEFAULT_PKGS.length; i++) {
                    String className2 = JVM_VENDOR_DEFAULT_PKGS[i] + "." + protocol + ".Handler";
                    Class handlerClass2 = null;
                    try {
                        try {
                            handlerClass2 = Class.forName(className2);
                        } catch (Exception e3) {
                        }
                    } catch (Exception e4) {
                    }
                    if (handlerClass2 == null) {
                        handlerClass2 = ClassLoader.getSystemClassLoader().loadClass(className2);
                    }
                    handler = (URLStreamHandler) handlerClass2.newInstance();
                    if (handler != null) {
                        break;
                    }
                }
            }
            if (handler == null) {
                throw new IOException("Unable to find default handler for protocol: " + protocol);
            }
            PROTOCOL_HANDLERS.put(protocol, handler);
            return handler;
        }
    }
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: CssRootPath</h3>
    <p class="ttl">config.CssRootPath</p>
    <p class="txt">
        사용자가 만든 css 폴더 경로를 업로드 영역에 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        dext5upload &gt; css 폴더 안에 존재 하는 파일명은 유지 시켜주셔야 합니다. <span class="firebrick">(파일명 변경 불가)</span> 
    <br />
    http://를 포함하는 전체 url을 입력해야 합니다.
</p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 사용자가 만든 css_admin 폴더 경로를 설정합니다.
        DEXT5UPLOAD.config.CssRootPath = 'http://localhost/css_admin/';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/TransWaitNamedPipe.class */
class TransWaitNamedPipe extends SmbComTransaction {
    TransWaitNamedPipe(String pipeName) {
        this.name = pipeName;
        this.command = (byte) 37;
        this.subCommand = (byte) 83;
        this.timeout = -1;
        this.maxParameterCount = 0;
        this.maxDataCount = 0;
        this.maxSetupCount = (byte) 0;
        this.setupCount = 2;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        int dstIndex3 = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        int dstIndex4 = dstIndex3 + 1;
        dst[dstIndex3] = 0;
        int i = dstIndex4 + 1;
        dst[dstIndex4] = 0;
        return 4;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("TransWaitNamedPipe[" + super.toString() + ",pipeName=" + this.name + "]");
    }
}

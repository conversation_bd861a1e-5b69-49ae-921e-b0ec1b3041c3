package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryFSInformationResponse.class */
class Trans2QueryFSInformationResponse extends SmbComTransactionResponse {
    static final int SMB_INFO_ALLOCATION = 1;
    static final int SMB_QUERY_FS_SIZE_INFO = 259;
    static final int SMB_FS_FULL_SIZE_INFORMATION = 1007;
    private int informationLevel;
    AllocInfo info;

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2QueryFSInformationResponse$SmbInfoAllocation.class */
    class SmbInfoAllocation implements AllocInfo {
        long alloc;
        long free;
        int sectPerAlloc;
        int bytesPerSect;

        SmbInfoAllocation() {
        }

        @Override // jcifs.smb.AllocInfo
        public long getCapacity() {
            return this.alloc * this.sectPerAlloc * this.bytesPerSect;
        }

        @Override // jcifs.smb.AllocInfo
        public long getFree() {
            return this.free * this.sectPerAlloc * this.bytesPerSect;
        }

        public String toString() {
            return new String("SmbInfoAllocation[alloc=" + this.alloc + ",free=" + this.free + ",sectPerAlloc=" + this.sectPerAlloc + ",bytesPerSect=" + this.bytesPerSect + "]");
        }
    }

    Trans2QueryFSInformationResponse(int informationLevel) {
        this.informationLevel = informationLevel;
        this.command = (byte) 50;
        this.subCommand = (byte) 3;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        switch (this.informationLevel) {
            case 1:
                return readSmbInfoAllocationWireFormat(buffer, bufferIndex);
            case SMB_QUERY_FS_SIZE_INFO /* 259 */:
                return readSmbQueryFSSizeInfoWireFormat(buffer, bufferIndex);
            case SMB_FS_FULL_SIZE_INFORMATION /* 1007 */:
                return readFsFullSizeInformationWireFormat(buffer, bufferIndex);
            default:
                return 0;
        }
    }

    int readSmbInfoAllocationWireFormat(byte[] buffer, int bufferIndex) {
        SmbInfoAllocation info = new SmbInfoAllocation();
        int bufferIndex2 = bufferIndex + 4;
        info.sectPerAlloc = readInt4(buffer, bufferIndex2);
        info.alloc = readInt4(buffer, r6);
        info.free = readInt4(buffer, r6);
        int bufferIndex3 = bufferIndex2 + 4 + 4 + 4;
        info.bytesPerSect = readInt2(buffer, bufferIndex3);
        this.info = info;
        return (bufferIndex3 + 4) - bufferIndex;
    }

    int readSmbQueryFSSizeInfoWireFormat(byte[] buffer, int bufferIndex) {
        SmbInfoAllocation info = new SmbInfoAllocation();
        info.alloc = readInt8(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 8;
        info.free = readInt8(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 8;
        info.sectPerAlloc = readInt4(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 4;
        info.bytesPerSect = readInt4(buffer, bufferIndex4);
        this.info = info;
        return (bufferIndex4 + 4) - bufferIndex;
    }

    int readFsFullSizeInformationWireFormat(byte[] buffer, int bufferIndex) {
        SmbInfoAllocation info = new SmbInfoAllocation();
        info.alloc = readInt8(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 8;
        info.free = readInt8(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 8 + 8;
        info.sectPerAlloc = readInt4(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 4;
        info.bytesPerSect = readInt4(buffer, bufferIndex4);
        this.info = info;
        return (bufferIndex4 + 4) - bufferIndex;
    }

    @Override // jcifs.smb.SmbComTransactionResponse, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2QueryFSInformationResponse[" + super.toString() + "]");
    }
}

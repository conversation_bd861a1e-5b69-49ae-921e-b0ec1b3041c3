package jcifs.dcerpc.msrpc;

import jcifs.dcerpc.msrpc.lsarpc;
import jcifs.smb.SID;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/MsrpcLookupSids.class */
public class MsrpcLookupSids extends lsarpc.LsarLookupSids {
    SID[] sids;

    public MsrpcLookupSids(LsaPolicyHandle policyHandle, SID[] sids) {
        super(policyHandle, new LsarSidArrayX(sids), new lsarpc.LsarRefDomainList(), new lsarpc.LsarTransNameArray(), (short) 1, sids.length);
        this.sids = sids;
        this.ptype = 0;
        this.flags = 3;
    }
}

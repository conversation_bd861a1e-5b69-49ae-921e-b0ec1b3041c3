package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComSessionSetupAndXResponse.class */
class SmbComSessionSetupAndXResponse extends AndXServerMessageBlock {
    private String nativeOs;
    private String nativeLanMan;
    private String primaryDomain;
    boolean isLoggedInAsGuest;
    byte[] blob;

    SmbComSessionSetupAndXResponse(ServerMessageBlock andx) {
        super(andx);
        this.nativeOs = "";
        this.nativeLanMan = "";
        this.primaryDomain = "";
        this.blob = null;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        this.isLoggedInAsGuest = (buffer[bufferIndex] & 1) == 1;
        int bufferIndex2 = bufferIndex + 2;
        if (this.extendedSecurity) {
            int blobLength = readInt2(buffer, bufferIndex2);
            bufferIndex2 += 2;
            this.blob = new byte[blobLength];
        }
        return bufferIndex2 - bufferIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        if (this.extendedSecurity) {
            System.arraycopy(buffer, bufferIndex, this.blob, 0, this.blob.length);
            bufferIndex += this.blob.length;
        }
        this.nativeOs = readString(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + stringWireLength(this.nativeOs, bufferIndex);
        this.nativeLanMan = readString(buffer, bufferIndex2, bufferIndex + this.byteCount, 255, this.useUnicode);
        int bufferIndex3 = bufferIndex2 + stringWireLength(this.nativeLanMan, bufferIndex2);
        if (!this.extendedSecurity) {
            this.primaryDomain = readString(buffer, bufferIndex3, bufferIndex + this.byteCount, 255, this.useUnicode);
            bufferIndex3 += stringWireLength(this.primaryDomain, bufferIndex3);
        }
        return bufferIndex3 - bufferIndex;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        String result = new String("SmbComSessionSetupAndXResponse[" + super.toString() + ",isLoggedInAsGuest=" + this.isLoggedInAsGuest + ",nativeOs=" + this.nativeOs + ",nativeLanMan=" + this.nativeLanMan + ",primaryDomain=" + this.primaryDomain + "]");
        return result;
    }
}

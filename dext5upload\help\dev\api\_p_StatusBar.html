﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: StatusBar</h3>
    <p class="ttl">config.StatusBar</p>
    <p class="txt">
        업로드의 상태바를 표시하거나 숨기는 기능을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "1" 이고, "0" 으로 설정시 상태바를 표시하지 않습니다.<br /><br/>

        <span class="txt">
            <span class="firebrick">StatusBarShowLimit</span>&nbsp;&nbsp;"파일 제한" 문구를 표시하거나 숨기는 기능을 설정합니다.<br />
            <span style="padding-left:118px">기본값은 "1" 이고, "0" 으로 설정시 "파일 제한" 문구를 표시하지 않습니다. </span>
        </span><br /><br />
        <span class="txt">
            <span class="firebrick">StatusBarShowStatus</span>&nbsp;&nbsp;"현재 파일 상태" 문구를 표시하거나 숨기는 기능을 설정합니다.<br />
            <span style="padding-left:126px">기본값은 "1" 이고, "0" 으로 설정시 현재 파일 상태 문구를 표시하지 않습니다.</span>
        </span>
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드의 상태바를 미사용으로 설정합니다.
        DEXT5UPLOAD.config.StatusBar = "0";

        // 업로드 파일 제한 문구를 미표시 합니다.
        DEXT5UPLOAD.config.StatusBarShowLimit = "0";

        // 업로드 현재 파일 상태 문구를 미표시 합니다.
        DEXT5UPLOAD.config.StatusBarShowStatus = "0";

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


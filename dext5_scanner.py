#!/usr/bin/env python3
import requests
import re
import urllib3
import sys
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import time,base64
import os
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_parameter(str_input):
    # 将 "%2B" 替换回 "+" 并进行第一次 Base64 解码
    param_encrypt = base64.b64decode(str_input.replace("%2B", "+").encode('utf-8')).decode('utf-8')
    # 检查并去掉前缀 "R"，进行第二次 Base64 解码
    if param_encrypt.startswith("R"):
        original = base64.b64decode(param_encrypt[1:].encode('utf-8')).decode('utf-8')
        return original
    else:
        raise ValueError("无效的加密字符串：缺少 'R' 前缀")

class DEXT5Scanner:
    def __init__(self, timeout=10, max_workers=10):
        self.timeout = timeout
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # DEXT5相关文件的正则表达式
        self.dext5_patterns = [
            re.compile(r'["\']([^"\']*dext5upload\.js[^"\']*)["\']', re.IGNORECASE),
            re.compile(r'["\']([^"\']*dext5upload\.css[^"\']*)["\']', re.IGNORECASE),
            re.compile(r'src=[\'"]*([^"\']*dext5upload\.js[^"\']*)', re.IGNORECASE),
            re.compile(r'href=[\'"]*([^"\']*dext5upload\.css[^"\']*)', re.IGNORECASE),
        ]
        
        # handler文件列表
        self.handler_files = [
            'handler/dext5handler.ashx',
            'handler/dext5handler.jsp',
            'dext5upload/handler/dext5handler.ashx',
            'dext5upload/handler/dext5handler.jsp'
        ]
        
        # 常见的DEXT5部署目录
        self.common_directories = [
            'js/',
            'resources/',
            'common/',
            'static/lib/',
            'Dext5Upload/',
            'dext5upload/',
            'static/',
            'assets/',
            'lib/',
            'resource/upload/',
            'js/component/dext5upload_new/'
            'scripts/',
            'upload/',
            'upload1/',
            'file/',
            ''  # 根目录
        ]
        
        self.results = []
        self.file_lock = threading.Lock()
    
    def normalize_url(self, url):
        """标准化URL，确保有协议"""
        if not url.startswith(('http://', 'https://')):
            # 先尝试https，再尝试http
            return f"https://{url}", f"http://{url}"
        return url, None
    
    def test_url_accessibility(self, url):
        """测试URL是否可访问，返回(可访问的URL, 响应对象)"""
        primary_url, fallback_url = self.normalize_url(url)
        
        # 先测试主URL
        try:
            response = self.session.get(primary_url, allow_redirects=True)
            if response.status_code == 200:
                return primary_url, response
        except:
            pass
        
        # 如果有备用URL，测试备用URL
        if fallback_url:
            try:
                response = self.session.get(fallback_url, allow_redirects=True)
                if response.status_code == 200:
                    return fallback_url, response
            except:
                pass
        
        return None, None
    
    def extract_dext5_paths(self, html_content, base_url):
        """从HTML内容中提取DEXT5相关文件路径"""
        found_paths = []
        seen_urls = set()  # 用于去重
        
        for pattern in self.dext5_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 构造完整URL
                full_url = urljoin(base_url, match)
                
                # 去重检查
                if full_url not in seen_urls:
                    seen_urls.add(full_url)
                    found_paths.append({
                        'resource_path': match,
                        'full_url': full_url,
                        'type': 'js' if 'dext5upload.js' in match.lower() else 'css'
                    })
        
        return found_paths
    
    def extract_base_path_from_resource(self, resource_path):
        """从资源路径中提取基础路径，去掉js/css子目录"""
        # 找到dext5upload目录的位置
        parts = resource_path.split('/')
        
        for i, part in enumerate(parts):
            if 'dext5upload' in part.lower():
                # 找到dext5upload目录，只保留到这个目录为止
                base_parts = parts[:i+1]
                base_path = '/'.join(base_parts) + '/'
                return base_path
        
        # 如果没找到dext5upload目录名，作为后备方案
        if 'dext5upload.js' in resource_path:
            base_path = resource_path.replace('dext5upload.js', '').rstrip('/')
            # 移除可能的js/css目录
            base_path = re.sub(r'/(js|css)/?$', '', base_path) + '/'
        elif 'dext5upload.css' in resource_path:
            base_path = resource_path.replace('dext5upload.css', '').rstrip('/')
            # 移除可能的js/css目录
            base_path = re.sub(r'/(js|css)/?$', '', base_path) + '/'
        else:
            # 最后的后备方案
            base_path = '/'.join(resource_path.split('/')[:-1]) + '/'
        
        return base_path
    
    def test_handler_path(self, base_url, base_path, handler_file):
        """测试handler路径是否有效"""
        # 构造handler URL
        handler_url = urljoin(base_url, base_path + handler_file)
        
        try:
            response = self.session.get(handler_url)
            
            # 检查有效性标准
            if (response.status_code == 200 and len(response.text)>30 and
                len(response.text) <= 200 and 
                '<title>' not in response.text.lower()):
                return True, handler_url, len(response.text)
        except:
            pass
        
        return False, handler_url, 0
    
    def learn_and_save_new_directory(self, path_prefix):
        """
        从发现的DEXT5基础路径中学习新的前缀目录，
        如果它不是已知的公共目录，就将其保存到文件中。
        """
        try:
            # 组件已经是相对路径，只需清理
            dir_to_save = path_prefix.strip()
            if not dir_to_save or dir_to_save == '/':
                return

            # 规范化以便比较
            dir_to_save_normalized = dir_to_save.strip('/')
            if not dir_to_save_normalized:
                return

            # 检查它是否是内置的公共目录之一
            normalized_common_dirs = {d.strip().strip('/') for d in self.common_directories if d.strip()}
            if dir_to_save_normalized in normalized_common_dirs:
                return

            with self.file_lock:
                # 检查目录是否已在文件中
                existing_dirs = set()
                if os.path.exists('commdir_dext5.txt'):
                    with open('commdir_dext5.txt', 'r', encoding='utf-8') as f:
                        existing_dirs = {line.strip().strip('/') for line in f if line.strip()}

                if dir_to_save_normalized not in existing_dirs:
                    print(f"[*] 发现新的 DEXT5 非标准目录: {dir_to_save}，已保存到 commdir_dext5.txt")
                    # 为保持一致性，保存时附带斜杠
                    with open('commdir_dext5.txt', 'a', encoding='utf-8') as f:
                        f.write(dir_to_save_normalized + '/\n')
        except Exception as e:
            print(f"[!] 保存新目录时出错: {e}")

    def scan_single_target(self, target):
        """扫描单个目标"""
        print(f"[*] 扫描目标: {target}")
        
        # 1. 测试URL可访问性
        accessible_url, response = self.test_url_accessibility(target)
        if not accessible_url:
            print(f"[-] {target} - 无法访问")
            return None
        
        print(f"[+] {target} - 可访问 ({accessible_url})")
        
        # 2. 查找DEXT5相关文件
        dext5_paths = self.extract_dext5_paths(response.text, accessible_url)
        
        result = {
            'target': target,
            'accessible_url': accessible_url,
            'dext5_resources': dext5_paths,
            'valid_handlers': []
        }

        if not dext5_paths:
            print(f"[-] {target} - 未发现DEXT5相关文件，尝试根目录fuzz...")
        else:
            # 3. 对每个发现的DEXT5路径测试handler
            tested_base_paths = set()  # 避免重复测试相同的基础路径
            
            for resource_info in dext5_paths:
                print(f"[+] {target} - 发现DEXT5资源: {resource_info['full_url']}")
                
                base_path = self.extract_base_path_from_resource(resource_info['resource_path'])
                
                # 从发现的路径中学习新的非标准目录
                if 'dext5upload' in base_path.lower():
                    # 分割路径以找到'dext5upload'组件之前的前缀
                    # 例如: 'new/path/dext5upload/' -> 'new/path/'
                    path_prefix = base_path.lower().split('dext5upload', 1)[0]
                    if path_prefix and path_prefix.strip('/'):
                        self.learn_and_save_new_directory(path_prefix)
                
                # 如果这个基础路径已经测试过了，跳过
                if base_path in tested_base_paths:
                    continue
                
                tested_base_paths.add(base_path)
                print(f"[*] {target} - 测试基础路径: {base_path}")
                
                # 测试每个可能的handler文件
                for handler_file in self.handler_files:
                    is_valid, handler_url, content_length = self.test_handler_path(
                        accessible_url, base_path, handler_file
                    )
                    
                    if is_valid:
                        handler_info = {
                            'resource_base': base_path,
                            'handler_file': handler_file,
                            'handler_url': handler_url,
                            'content_length': content_length
                        }
                        result['valid_handlers'].append(handler_info)
                        print(f"[+] {target} - 发现有效Handler: {handler_url} (长度: {content_length})")

        # 如果没有找到有效的handler，或者没有找到DEXT5资源文件，尝试目录fuzz
        if not result['valid_handlers']:
            print(f"[*] {target} - 开始目录fuzz...")
            
            # 基本的handler文件
            basic_handlers = ['dext5handler.ashx', 'dext5handler.jsp']
            
            # 生成所有可能的路径组合
            fuzz_paths = []
            
            for directory in self.common_directories:
                for handler in basic_handlers:
                    if directory == '':
                        # 根目录情况
                        fuzz_paths.extend([
                            handler,  # 直接在根目录
                            f"dext5upload/handler/{handler}",  # 标准路径
                            f"handler/{handler}"  # 简化路径
                        ])
                    else:
                        # 各种目录组合
                        fuzz_paths.extend([
                            f"{directory}dext5upload/handler/{handler}",  # 标准组合
                            f"{directory}handler/{handler}",  # 简化组合
                            f"{directory}{handler}",  # 直接组合
                            f"{directory}Dext5Upload/handler/{handler}",  # 大小写变体
                        ])
            
            # 去重
            fuzz_paths = list(set(fuzz_paths))
            
            print(f"[*] {target} - 将测试 {len(fuzz_paths)} 个可能的路径...")
            
            found_count = 0
            for handler_path in fuzz_paths:
                is_valid, handler_url, content_length = self.test_handler_path(
                    accessible_url, '/', handler_path
                )
                
                if is_valid:
                    handler_info = {
                        'resource_base': '/',
                        'handler_file': handler_path,
                        'handler_url': handler_url,
                        'content_length': content_length
                    }
                    result['valid_handlers'].append(handler_info)
                    found_count += 1
                    print(f"[+] {target} - 目录fuzz发现Handler [{found_count}]: {handler_url} (长度: {content_length})")
            
            if found_count > 0:
                print(f"[+] {target} - 目录fuzz总共发现 {found_count} 个有效Handler")
            else:
                print(f"[-] {target} - 目录fuzz未发现有效Handler")

        if result['valid_handlers']:
            self.save_single_result(result)  # 立即保存结果
            return result
        else:
            print(f"[-] {target} - 未发现有效的Handler")
            return None
    
    def save_single_result(self, result):
        """立即保存单个结果到文件（追加模式）"""
        with open('res.txt', 'a', encoding='utf-8') as f:
            f.write(f"目标: {result['target']}\n")
            f.write(f"访问URL: {result['accessible_url']}\n")
            
            f.write(f"发现的DEXT5资源:\n")
            for resource in result['dext5_resources']:
                f.write(f"  - {resource['type'].upper()}: {resource['full_url']}\n")
            
            f.write(f"有效的Handler:\n")
            for handler in result['valid_handlers']:
                f.write(f"  - {handler['handler_url']} (长度: {handler['content_length']})\n")
            
            # 尝试对handler响应内容进行get_parameter解密
            decryption_results = []
            for handler in result['valid_handlers']:
                handler_url = handler['handler_url']
                # 尝试获取handler响应内容并解密
                try:
                    response = self.session.get(handler_url)
                    response_content = response.text.strip()
                    if response_content and len(response_content) > 10:
                        # 尝试解密完整响应内容
                        try:
                            decrypted_response = get_parameter(response_content)
                            decryption_results.append(f"  - 响应内容解密: {response_content} -> {decrypted_response}")
                            print(f"[+] {result['target']} - 响应内容解密成功: {decrypted_response}")
                        except:
                            pass  # 静默忽略解密失败
                except:
                    pass  # 静默忽略获取响应失败
            
            # 只有在有解密成功的结果时才写入解密部分
            if decryption_results:
                f.write(f"解密尝试结果:\n")
                for result_line in decryption_results:
                    f.write(result_line + "\n")
            
            f.write("\n" + "-"*50 + "\n\n")
        
        print(f"[+] {result['target']} - 结果已保存到 res.txt")
    
    def scan_targets(self, targets):
        """批量扫描目标"""    
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_target = {
                executor.submit(self.scan_single_target, target.strip()): target.strip() 
                for target in targets if target.strip()
            }
            
            for future in as_completed(future_to_target):
                target = future_to_target[future]
                try:
                    result = future.result()
                    if result:
                        self.results.append(result)
                except Exception as e:
                    print(f"[!] {target} - 扫描出错: {e}")
        
        self.print_summary()
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "="*80)
        print("扫描结果摘要:")
        print("="*80)
        
        if not self.results:
            print("[-] 未发现任何DEXT5 Upload组件")
            return
        
        for result in self.results:
            print(f"\n[+] 目标: {result['target']}")
            print(f"    访问URL: {result['accessible_url']}")
            
            print(f"    发现的DEXT5资源:")
            for resource in result['dext5_resources']:
                print(f"      - {resource['type'].upper()}: {resource['full_url']}")
            
            print(f"    有效的Handler ({len(result['valid_handlers'])}):")
            for handler in result['valid_handlers']:
                print(f"      - {handler['handler_url']} (长度: {handler['content_length']})")

def main():
    print("DEXT5 Upload组件扫描器")
    print("="*40)
    
    if len(sys.argv) != 2:
        print("用法:")
        print("  python3 dext5_scanner.py <目标文件>")
        print("  python3 dext5_scanner.py <单个目标>")
        print("\n示例:")
        print("  python3 dext5_scanner.py targets.txt")
        print("  python3 dext5_scanner.py iss.target.com")
        sys.exit(1)
    
    target_input = sys.argv[1]
    
    # 判断是文件还是单个目标
    try:
        # 尝试作为文件读取
        with open(target_input, 'r', encoding='utf-8') as f:
            targets = [line.strip() for line in f if line.strip()]
        print(f"[*] 从文件读取到 {len(targets)} 个目标")
    except FileNotFoundError:
        # 作为单个目标处理
        targets = [target_input]
        print(f"[*] 单个目标模式: {target_input}")
    
    if not targets:
        print("[-] 没有找到有效目标")
        sys.exit(1)
    
    # 创建扫描器并开始扫描
    scanner = DEXT5Scanner(timeout=10, max_workers=10)
    scanner.scan_targets(targets)

if __name__ == "__main__":
    main() 
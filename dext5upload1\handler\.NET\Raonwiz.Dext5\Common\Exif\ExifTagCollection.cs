﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Text;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000016 RID: 22
	public sealed class ExifTagCollection : IEnumerable<ExifTag>, IEnumerable
	{
		// Token: 0x06000209 RID: 521 RVA: 0x000130AE File Offset: 0x000112AE
		public ExifTagCollection(string fileName) : this(fileName, true, false)
		{
		}

		// Token: 0x0600020A RID: 522 RVA: 0x000130BC File Offset: 0x000112BC
		public ExifTagCollection(string fileName, bool useEmbeddedColorManagement, bool validateImageData)
		{
			using (FileStream fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read))
			{
				Image image = Image.FromStream(fileStream, useEmbeddedColorManagement, validateImageData);
				this.ReadTags(image.PropertyItems);
			}
		}

		// Token: 0x0600020B RID: 523 RVA: 0x0001310C File Offset: 0x0001130C
		public ExifTagCollection(Image image)
		{
			this.ReadTags(image.PropertyItems);
		}

		// Token: 0x0600020C RID: 524 RVA: 0x00013120 File Offset: 0x00011320
		private void ReadTags(PropertyItem[] pitems)
		{
			Encoding ascii = Encoding.ASCII;
			SupportedTags supportedTags = new SupportedTags();
			this._tags = new Dictionary<int, ExifTag>();
			foreach (PropertyItem propertyItem in pitems)
			{
				ExifTag exifTag = (ExifTag)supportedTags[propertyItem.Id];
				if (exifTag != null)
				{
					string text = "";
					if (propertyItem.Type == 1)
					{
						if (propertyItem.Value.Length == 4)
						{
							text = "Version " + propertyItem.Value[0].ToString() + "." + propertyItem.Value[1].ToString();
						}
						else if (propertyItem.Id == 5 && propertyItem.Value[0] == 0)
						{
							text = "Sea level";
						}
						else
						{
							text = propertyItem.Value[0].ToString();
						}
					}
					else if (propertyItem.Type == 2)
					{
						string @string = ascii.GetString(propertyItem.Value);
						char[] trimChars = new char[1];
						text = @string.Trim(trimChars);
						if (propertyItem.Id == 1 || propertyItem.Id == 19)
						{
							if (text == "N")
							{
								text = "North latitude";
							}
							else if (text == "S")
							{
								text = "South latitude";
							}
							else
							{
								text = "reserved";
							}
						}
						if (propertyItem.Id == 3 || propertyItem.Id == 21)
						{
							if (text == "E")
							{
								text = "East longitude";
							}
							else if (text == "W")
							{
								text = "West longitude";
							}
							else
							{
								text = "reserved";
							}
						}
						if (propertyItem.Id == 9)
						{
							if (text == "A")
							{
								text = "Measurement in progress";
							}
							else if (text == "V")
							{
								text = "Measurement Interoperability";
							}
							else
							{
								text = "reserved";
							}
						}
						if (propertyItem.Id == 10)
						{
							if (text == "2")
							{
								text = "2-dimensional measurement";
							}
							else if (text == "3")
							{
								text = "3-dimensional measurement";
							}
							else
							{
								text = "reserved";
							}
						}
						if (propertyItem.Id == 12 || propertyItem.Id == 25)
						{
							if (text == "K")
							{
								text = "Kilometers per hour";
							}
							else if (text == "M")
							{
								text = "Miles per hour";
							}
							else if (text == "N")
							{
								text = "Knots";
							}
							else
							{
								text = "reserved";
							}
						}
						if (propertyItem.Id == 14 || propertyItem.Id == 16 || propertyItem.Id == 23)
						{
							if (text == "T")
							{
								text = "True direction";
							}
							else if (text == "M")
							{
								text = "Magnetic direction";
							}
							else
							{
								text = "reserved";
							}
						}
					}
					else if (propertyItem.Type == 3)
					{
						ushort num = BitConverter.ToUInt16(propertyItem.Value, 0);
						int id = propertyItem.Id;
						if (id <= 531)
						{
							if (id <= 262)
							{
								if (id != 30)
								{
									if (id != 259)
									{
										if (id == 262)
										{
											ushort num2 = num;
											if (num2 == 2)
											{
												text = "RGB";
												goto IL_140F;
											}
											if (num2 != 6)
											{
												text = "Reserved";
												goto IL_140F;
											}
											text = "YCbCr";
											goto IL_140F;
										}
									}
									else
									{
										ushort num3 = num;
										if (num3 == 1)
										{
											text = "Uncompressed";
											goto IL_140F;
										}
										if (num3 != 6)
										{
											text = "Reserved";
											goto IL_140F;
										}
										text = "JPEG compression (thumbnails only)";
										goto IL_140F;
									}
								}
								else
								{
									switch (num)
									{
									case 0:
										text = "Measurement without differential correction";
										goto IL_140F;
									case 1:
										text = "Differential correction applied";
										goto IL_140F;
									default:
										text = "Reserved";
										goto IL_140F;
									}
								}
							}
							else if (id != 274)
							{
								if (id != 296)
								{
									if (id == 531)
									{
										ushort num4 = num;
										if (num4 == 1)
										{
											text = "centered";
											goto IL_140F;
										}
										if (num4 != 6)
										{
											text = "Reserved";
											goto IL_140F;
										}
										text = "co-sited";
										goto IL_140F;
									}
								}
								else
								{
									switch (num)
									{
									case 2:
										text = "Inch";
										goto IL_140F;
									case 3:
										text = "Centimeter";
										goto IL_140F;
									default:
										text = "No Unit";
										goto IL_140F;
									}
								}
							}
							else
							{
								switch (num)
								{
								case 1:
									text = "The 0th row is at the visual top of the image, and the 0th column is the visual left-hand side.";
									goto IL_140F;
								case 2:
									text = "The 0th row is at the visual top of the image, and the 0th column is the visual right-hand side.";
									goto IL_140F;
								case 3:
									text = "The 0th row is at the visual bottom of the image, and the 0th column is the visual right-hand side.";
									goto IL_140F;
								case 4:
									text = "The 0th row is at the visual bottom of the image, and the 0th column is the visual left-hand side.";
									goto IL_140F;
								case 5:
									text = "The 0th row is the visual left-hand side of the image, and the 0th column is the visual top.";
									goto IL_140F;
								case 6:
									text = "The 0th row is the visual right-hand side of the image, and the 0th column is the visual top.";
									goto IL_140F;
								case 7:
									text = "The 0th row is the visual right-hand side of the image, and the 0th column is the visual bottom.";
									goto IL_140F;
								case 8:
									text = "The 0th row is the visual left-hand side of the image, and the 0th column is the visual bottom.";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
							}
						}
						else if (id <= 37385)
						{
							if (id != 34850)
							{
								if (id == 34855)
								{
									text = "ISO-" + num.ToString();
									goto IL_140F;
								}
								switch (id)
								{
								case 37383:
								{
									ushort num5 = num;
									switch (num5)
									{
									case 0:
										text = "unknown";
										goto IL_140F;
									case 1:
										text = "Average";
										goto IL_140F;
									case 2:
										text = "Center Weighted Average";
										goto IL_140F;
									case 3:
										text = "Spot";
										goto IL_140F;
									case 4:
										text = "MultiSpot";
										goto IL_140F;
									case 5:
										text = "Pattern";
										goto IL_140F;
									case 6:
										text = "Partial";
										goto IL_140F;
									default:
										if (num5 != 255)
										{
											text = "reserved";
											goto IL_140F;
										}
										text = "Other";
										goto IL_140F;
									}
									break;
								}
								case 37384:
								{
									ushort num6 = num;
									switch (num6)
									{
									case 0:
										text = "unknown";
										goto IL_140F;
									case 1:
										text = "Daylight";
										goto IL_140F;
									case 2:
										text = "Fluorescent";
										goto IL_140F;
									case 3:
										text = "Tungsten (incandescent light)";
										goto IL_140F;
									case 4:
										text = "Flash";
										goto IL_140F;
									case 5:
									case 6:
									case 7:
									case 8:
									case 16:
										break;
									case 9:
										text = "Fine weather";
										goto IL_140F;
									case 10:
										text = "Cloudy weather";
										goto IL_140F;
									case 11:
										text = "Shade";
										goto IL_140F;
									case 12:
										text = "Daylight fluorescent (D 5700 – 7100K)";
										goto IL_140F;
									case 13:
										text = "Day white fluorescent (N 4600 – 5400K)";
										goto IL_140F;
									case 14:
										text = "Cool white fluorescent (W 3900 – 4500K)";
										goto IL_140F;
									case 15:
										text = "White fluorescent (WW 3200 – 3700K)";
										goto IL_140F;
									case 17:
										text = "Standard light A";
										goto IL_140F;
									case 18:
										text = "Standard light B";
										goto IL_140F;
									case 19:
										text = "Standard light C";
										goto IL_140F;
									case 20:
										text = "D55";
										goto IL_140F;
									case 21:
										text = "D65";
										goto IL_140F;
									case 22:
										text = "D75";
										goto IL_140F;
									case 23:
										text = "D50";
										goto IL_140F;
									case 24:
										text = "ISO studio tungsten";
										goto IL_140F;
									default:
										if (num6 == 255)
										{
											text = "ISO studio tungsten";
											goto IL_140F;
										}
										break;
									}
									text = "other light source";
									goto IL_140F;
								}
								case 37385:
								{
									ushort num7 = num;
									if (num7 <= 32)
									{
										if (num7 <= 9)
										{
											switch (num7)
											{
											case 0:
												text = "Flash did not fire";
												goto IL_140F;
											case 1:
												text = "Flash fired";
												goto IL_140F;
											default:
												switch (num7)
												{
												case 5:
													text = "Strobe return light not detected";
													goto IL_140F;
												case 7:
													text = "Strobe return light detected";
													goto IL_140F;
												case 9:
													text = "Flash fired, compulsory flash mode";
													goto IL_140F;
												}
												break;
											}
										}
										else
										{
											switch (num7)
											{
											case 13:
												text = "Flash fired, compulsory flash mode, return light not detected";
												goto IL_140F;
											case 14:
												break;
											case 15:
												text = "Flash fired, compulsory flash mode, return light detected";
												goto IL_140F;
											case 16:
												text = "Flash did not fire, compulsory flash mode";
												goto IL_140F;
											default:
												switch (num7)
												{
												case 24:
													text = "Flash did not fire, auto mode";
													goto IL_140F;
												case 25:
													text = "Flash fired, auto mode";
													goto IL_140F;
												case 29:
													text = "Flash fired, auto mode, return light not detected";
													goto IL_140F;
												case 31:
													text = "Flash fired, auto mode, return light detected";
													goto IL_140F;
												case 32:
													text = "No flash function";
													goto IL_140F;
												}
												break;
											}
										}
									}
									else if (num7 <= 73)
									{
										if (num7 == 65)
										{
											text = "Flash fired, red-eye reduction mode";
											goto IL_140F;
										}
										switch (num7)
										{
										case 69:
											text = "Flash fired, red-eye reduction mode, return light not detected";
											goto IL_140F;
										case 71:
											text = "Flash fired, red-eye reduction mode, return light detected";
											goto IL_140F;
										case 73:
											text = "Flash fired, compulsory flash mode, red-eye reduction mode";
											goto IL_140F;
										}
									}
									else
									{
										switch (num7)
										{
										case 77:
											text = "Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected";
											goto IL_140F;
										case 78:
											break;
										case 79:
											text = "Flash fired, compulsory flash mode, red-eye reduction mode, return light detected";
											goto IL_140F;
										default:
											if (num7 == 89)
											{
												text = "Flash fired, auto mode, red-eye reduction mode";
												goto IL_140F;
											}
											switch (num7)
											{
											case 93:
												text = "Flash fired, auto mode, return light not detected, red-eye reduction mode";
												goto IL_140F;
											case 95:
												text = "Flash fired, auto mode, return light detected, red-eye reduction mode";
												goto IL_140F;
											}
											break;
										}
									}
									text = "reserved";
									goto IL_140F;
								}
								}
							}
							else
							{
								switch (num)
								{
								case 0:
									text = "Not defined";
									goto IL_140F;
								case 1:
									text = "Manual";
									goto IL_140F;
								case 2:
									text = "Normal program";
									goto IL_140F;
								case 3:
									text = "Aperture priority";
									goto IL_140F;
								case 4:
									text = "Shutter priority";
									goto IL_140F;
								case 5:
									text = "Creative program (biased toward depth of field)";
									goto IL_140F;
								case 6:
									text = "Action program (biased toward fast shutter speed)";
									goto IL_140F;
								case 7:
									text = "Portrait mode (for closeup photos with the background out of focus)";
									goto IL_140F;
								case 8:
									text = "Landscape mode (for landscape photos with the background in focus)";
									goto IL_140F;
								default:
									text = "reserved";
									goto IL_140F;
								}
							}
						}
						else if (id != 40961)
						{
							if (id == 41495)
							{
								switch (num)
								{
								case 1:
									text = "Not defined";
									goto IL_140F;
								case 2:
									text = "One-chip color area sensor";
									goto IL_140F;
								case 3:
									text = "Two-chip color area sensor";
									goto IL_140F;
								case 4:
									text = "Three-chip color area sensor";
									goto IL_140F;
								case 5:
									text = "Color sequential area sensor";
									goto IL_140F;
								case 7:
									text = "Trilinear sensor";
									goto IL_140F;
								case 8:
									text = "Color sequential linear sensor";
									goto IL_140F;
								}
								text = " reserved";
								goto IL_140F;
							}
							switch (id)
							{
							case 41985:
								switch (num)
								{
								case 0:
									text = "Normal process";
									goto IL_140F;
								case 1:
									text = "Custom process";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41986:
								switch (num)
								{
								case 0:
									text = "Auto exposure";
									goto IL_140F;
								case 1:
									text = "Manual exposure";
									goto IL_140F;
								case 2:
									text = "Auto bracket";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41987:
								switch (num)
								{
								case 0:
									text = "Auto white balance";
									goto IL_140F;
								case 1:
									text = "Manual white balance";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41989:
								text = num.ToString() + " mm";
								goto IL_140F;
							case 41990:
								switch (num)
								{
								case 0:
									text = "Standard";
									goto IL_140F;
								case 1:
									text = "Landscape";
									goto IL_140F;
								case 2:
									text = "Portrait";
									goto IL_140F;
								case 3:
									text = "Night scene";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41992:
								switch (num)
								{
								case 0:
									text = "Normal";
									goto IL_140F;
								case 1:
									text = "Soft";
									goto IL_140F;
								case 2:
									text = "Hard";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41993:
								switch (num)
								{
								case 0:
									text = "Normal";
									goto IL_140F;
								case 1:
									text = "Low saturation";
									goto IL_140F;
								case 2:
									text = "High saturation";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41994:
								switch (num)
								{
								case 0:
									text = "Normal";
									goto IL_140F;
								case 1:
									text = "Soft";
									goto IL_140F;
								case 2:
									text = "Hard";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							case 41996:
								switch (num)
								{
								case 0:
									text = "unknown";
									goto IL_140F;
								case 1:
									text = "Macro";
									goto IL_140F;
								case 2:
									text = "Close view";
									goto IL_140F;
								case 3:
									text = "Distant view";
									goto IL_140F;
								default:
									text = "Reserved";
									goto IL_140F;
								}
								break;
							}
						}
						else
						{
							ushort num8 = num;
							if (num8 == 1)
							{
								text = "sRGB";
								goto IL_140F;
							}
							if (num8 != 65535)
							{
								text = "Reserved";
								goto IL_140F;
							}
							text = "Uncalibrated";
							goto IL_140F;
						}
						text = num.ToString();
					}
					else if (propertyItem.Type == 4)
					{
						text = BitConverter.ToUInt32(propertyItem.Value, 0).ToString();
					}
					else if (propertyItem.Type == 5)
					{
						URational urational = new URational(propertyItem.Value);
						int id2 = propertyItem.Id;
						if (id2 <= 33434)
						{
							if (id2 <= 17)
							{
								switch (id2)
								{
								case 2:
									text = new GPSRational(propertyItem.Value).ToString();
									goto IL_140F;
								case 3:
								case 5:
									break;
								case 4:
									text = new GPSRational(propertyItem.Value).ToString();
									goto IL_140F;
								case 6:
									text = urational.ToDouble() + " meters";
									goto IL_140F;
								case 7:
									text = new GPSRational(propertyItem.Value).ToString(":");
									goto IL_140F;
								default:
									switch (id2)
									{
									case 11:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									case 13:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									case 15:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									case 17:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									}
									break;
								}
							}
							else
							{
								switch (id2)
								{
								case 20:
									text = new GPSRational(propertyItem.Value).ToString();
									goto IL_140F;
								case 21:
								case 23:
								case 25:
									break;
								case 22:
									text = new GPSRational(propertyItem.Value).ToString();
									goto IL_140F;
								case 24:
									text = urational.ToDouble().ToString();
									goto IL_140F;
								case 26:
									text = urational.ToDouble().ToString();
									goto IL_140F;
								default:
									switch (id2)
									{
									case 282:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									case 283:
										text = urational.ToDouble().ToString();
										goto IL_140F;
									default:
										if (id2 == 33434)
										{
											text = urational.ToString() + " sec";
											goto IL_140F;
										}
										break;
									}
									break;
								}
							}
						}
						else if (id2 <= 37378)
						{
							if (id2 == 33437)
							{
								text = "F/" + urational.ToDouble().ToString();
								goto IL_140F;
							}
							if (id2 == 37378)
							{
								text = "F/" + Math.Round(Math.Pow(Math.Sqrt(2.0), urational.ToDouble()), 2).ToString();
								goto IL_140F;
							}
						}
						else
						{
							if (id2 == 37381)
							{
								text = "F/" + Math.Round(Math.Pow(Math.Sqrt(2.0), urational.ToDouble()), 2).ToString();
								goto IL_140F;
							}
							if (id2 == 37386)
							{
								text = urational.ToDouble().ToString() + " mm";
								goto IL_140F;
							}
							if (id2 == 41988)
							{
								text = urational.ToDouble().ToString();
								if (text == "0")
								{
									text = "none";
									goto IL_140F;
								}
								goto IL_140F;
							}
						}
						text = urational.ToString();
					}
					else if (propertyItem.Type == 7)
					{
						int id3 = propertyItem.Id;
						if (id3 <= 37121)
						{
							switch (id3)
							{
							case 27:
							{
								string string2 = ascii.GetString(propertyItem.Value);
								char[] trimChars2 = new char[1];
								text = string2.Trim(trimChars2);
								goto IL_140F;
							}
							case 28:
							{
								string string3 = ascii.GetString(propertyItem.Value);
								char[] trimChars3 = new char[1];
								text = string3.Trim(trimChars3);
								goto IL_140F;
							}
							default:
								if (id3 == 36864)
								{
									string string4 = ascii.GetString(propertyItem.Value);
									char[] trimChars4 = new char[1];
									text = string4.Trim(trimChars4);
									goto IL_140F;
								}
								if (id3 == 37121)
								{
									text = ExifTagCollection.GetComponentsConfig(propertyItem.Value);
									goto IL_140F;
								}
								break;
							}
						}
						else if (id3 <= 37510)
						{
							if (id3 == 37500)
							{
								string string5 = ascii.GetString(propertyItem.Value);
								char[] trimChars5 = new char[1];
								text = string5.Trim(trimChars5);
								goto IL_140F;
							}
							if (id3 == 37510)
							{
								string string6 = ascii.GetString(propertyItem.Value);
								char[] trimChars6 = new char[1];
								text = string6.Trim(trimChars6);
								goto IL_140F;
							}
						}
						else if (id3 != 40960)
						{
							switch (id3)
							{
							case 41728:
								if (propertyItem.Value[0] == 3)
								{
									text = "DSC";
									goto IL_140F;
								}
								text = "reserved";
								goto IL_140F;
							case 41729:
								if (propertyItem.Value[0] == 1)
								{
									text = "A directly photographed image";
									goto IL_140F;
								}
								text = "reserved";
								goto IL_140F;
							}
						}
						else
						{
							string string7 = ascii.GetString(propertyItem.Value);
							char[] trimChars7 = new char[1];
							text = string7.Trim(trimChars7);
							if (text == "0100")
							{
								text = "Flashpix Format Version 1.0";
								goto IL_140F;
							}
							text = "reserved";
							goto IL_140F;
						}
						text = "-";
					}
					else if (propertyItem.Type == 9)
					{
						text = BitConverter.ToInt32(propertyItem.Value, 0).ToString();
					}
					else if (propertyItem.Type == 10)
					{
						Rational rational = new Rational(propertyItem.Value);
						switch (propertyItem.Id)
						{
						case 37377:
							text = "1/" + Math.Round(Math.Pow(2.0, rational.ToDouble()), 2).ToString();
							goto IL_140F;
						case 37379:
							text = Math.Round(rational.ToDouble(), 4).ToString();
							goto IL_140F;
						case 37380:
							text = Math.Round(rational.ToDouble(), 2).ToString() + " eV";
							goto IL_140F;
						}
						text = rational.ToString();
					}
					IL_140F:
					exifTag.Value = text;
					this._tags.Add(exifTag.Id, exifTag);
				}
			}
			foreach (object obj in supportedTags)
			{
				int num9 = Convert.ToInt32(((DictionaryEntry)obj).Key);
				if (!this._tags.ContainsKey(num9))
				{
					ExifTag exifTag2 = (ExifTag)supportedTags[num9];
					exifTag2.Value = "";
					this._tags.Add(num9, exifTag2);
				}
			}
		}

		// Token: 0x0600020D RID: 525 RVA: 0x000145FC File Offset: 0x000127FC
		private static string GetComponentsConfig(byte[] bytes)
		{
			string text = "";
			string[] array = new string[]
			{
				"",
				"Y",
				"Cb",
				"Cr",
				"R",
				"G",
				"B"
			};
			foreach (byte b in bytes)
			{
				text += array[(int)b];
			}
			return text;
		}

		// Token: 0x0600020E RID: 526 RVA: 0x00014677 File Offset: 0x00012877
		public IEnumerator<ExifTag> GetEnumerator()
		{
			return this._tags.Values.GetEnumerator();
		}

		// Token: 0x0600020F RID: 527 RVA: 0x0001468E File Offset: 0x0001288E
		IEnumerator IEnumerable.GetEnumerator()
		{
			return this._tags.Values.GetEnumerator();
		}

		// Token: 0x170000C2 RID: 194
		public ExifTag this[int id]
		{
			get
			{
				return this._tags[id];
			}
		}

		// Token: 0x0400011E RID: 286
		private Dictionary<int, ExifTag> _tags;
	}
}

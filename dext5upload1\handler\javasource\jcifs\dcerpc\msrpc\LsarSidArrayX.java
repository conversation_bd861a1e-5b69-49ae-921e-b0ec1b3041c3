package jcifs.dcerpc.msrpc;

import jcifs.dcerpc.msrpc.lsarpc;
import jcifs.smb.SID;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/msrpc/LsarSidArrayX.class */
class LsarSidArrayX extends lsarpc.LsarSidArray {
    LsarSidArrayX(SID[] sids) {
        this.num_sids = sids.length;
        this.sids = new lsarpc.LsarSidPtr[sids.length];
        for (int si = 0; si < sids.length; si++) {
            this.sids[si] = new lsarpc.LsarSidPtr();
            this.sids[si].sid = sids[si];
        }
    }
}

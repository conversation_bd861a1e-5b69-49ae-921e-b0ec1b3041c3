﻿==============================================================================================
2.3.10_임시
----------------------------------------------------------------------------------------------
1. 버그 수정 
   - 크롬 > 밑줄 선택 글작성하다 취소선 선택하고 밑줄 선택을 취소한 경우 툴바에 반영되지 않는 문제
   - 굵게/밑줄/기울림/취소선이 있는 상태에서 엔터치고 폰트크기 수정하면 이전 폰트크기로 줄간격이 유지되는 문제
   - 외부프로그램 테이블 복사 붙여넣기 관련 오류 수정
   - IE8이하 > 글자크기 지정 후 엔터를 치면 커서위치가 위로 올라감
   - IE9이상 > 글꼴 효과를 미리 지정 한 후에 텍스트 입력 시, 색상 변경은 적용되나 굵게 기울임 밑줄 취소선 등의 효과는 적용안됨
   - [FF]밑줄 선택> 내용입력 후 엔터>밑줄 취소 후 내용 입력>마우스로 커서 클릭 후 내용 계속 입력 시 밑줄이 있는 글로 노출됨
   - [FF] 메모장에서 작성한 내용을 붙여넣기 후 엔터 > 내용입력 > 엔터 했을 때 줄바꿈이 되지 않는 현상
   - HTML5 > 글자를 선택하여 폰트크기 지정시 굵게/밑줄/기울림/취소선이 있으면 이전에 적용한 폰트크기 기준으로 줄간격이 유지되는 문제
   
2. 기능 개선 
   - IE에서 자동으로 공백이 삽입되는 문제
   - MacOS > 사파리 > 외부프로그램 테이블 붙여넣기 개선

3. 보안 패치
   - 사진첨부 샘플의 null byte injection 취약점 보완

==============================================================================================
2.3.10
----------------------------------------------------------------------------------------------
1. 버그 수정 
   - 크롬 > 브라우저 확대축소시 폰트크기가 잘못 나오는 이슈
   - IE > 표삽입>임의로 두개 칸 선택하여 셀 병합>행삽입 클릭 시 JS 오류 발생
   - IE11 > 호환성 보기를 설정하지 않을 경우 글꼴목록이 선택되지 않는 문제 수정
   
2. 기능 개선 
   - 외부프로그램 테이블 복사 붙여넣기 개선
   - 입력창 조절 안내 레이어를 주석처리하면 스크립트 오류 발생

==============================================================================================
2.3.9
----------------------------------------------------------------------------------------------
1. 버그 수정 
   - 파이어폭스에서 에디팅시 스타일깨짐 등 오작동
   - Chrome > 찾기/바꾸기 > 모두바꾸기 버튼 클릭시 찾을단어가 지워지지 않고 남아있음
   
2. 기능 개선 
   - 링크 > 자동링크 설정/해제 옵션 추가
   - [IE11] WYSIWYG 모드와 HTML 모드를 오갈 때마다 문서의 마지막에 비정상적인 <BR>이 첨가됩니다.
   - [웹접근성] 빠져나가기 단축키 기능 개선

==============================================================================================
2.3.8
----------------------------------------------------------------------------------------------
1. 버그 수정 
   - 테이블 내부 영역을 Shift + 클릭으로 선택 후 정렬하고 HTML 로 전환하면 더미 P 태그가 생성되는 문제 수정
   - 테이블 내부 영역 선택 혹은 에디터 내용 전체 선택 후 정렬 시 동작안함
   - [IE10, IE11] 표의 셀을 드래그했을 때 블럭 지정이 되지 않는 현상
   - HTML 모드 변환시 태그 자동 정렬에 의한 버그
   
2. 기능 개선 
   - [MacOS 대응] 폰트변경이슈 

==============================================================================================
2.3.7
----------------------------------------------------------------------------------------------
1. 버그 수정 
   - 에디터에 표 생성 후 일부 셀 선택하여 배경색 설정> 배경색 설정된 셀 선택 후 셀 삽입 시 색상이 삽입되지 않습니다. 
   - [IE9특정] 글 작성 중 번호매기기 또는 글머리 적용 후 정렬방식을 변경하면 엔터키 누를 시 커서가 한줄 떨어져서 노출됩니다. 
   - [IE10] 표 생성 후 표 드래그 시 셀의 너비/높이가 늘어나는 현상 

2. 기능 개선 
   - IE11 대응 
   - 특수기호 삽입시 커서 위치가 뒤쪽으로 나오도록 개선 
   - 커서에 활성화된 글꼴 확인 로직 개선 
   
==============================================================================================
2.3.6
----------------------------------------------------------------------------------------------
1. 버그 수정
   - 글 작성 후 번호매기기 적용하고 엔터키 수행하는 경우 JS 오류가 발생하는 현상 수정

==============================================================================================
2.3.5
----------------------------------------------------------------------------------------------
1. 기능 개선
   - 줄간격 설정 시 값을 직접 입력하는 경우 줄간격의 최소값 적용
   
==============================================================================================
2.3.4
----------------------------------------------------------------------------------------------
1. 버그 수정
   - [IE9/10] pre 태그의 바로 다음에 \n이 존재하는 경우 개행이 되지 않는 이슈 해결
   - 입력창 크기 조절바 사용 여부 오류 해결
   - 사진 퀵 업로더 모듈 오타 수정 ($newPath -> $new_path)
   
2. 기능 개선
   - 글꼴 목록에 글꼴 종류 추가하기 기능 (SmartEditor2.html 참조)
   - 사진 퀵 업로더 모듈에 이미지 파일 확장자 체크 추가

==============================================================================================
2.3.3
----------------------------------------------------------------------------------------------
1. 버그 수정
   - IE9 에서 템플릿을 적용한 표 생성 후 일부의 셀을 드래그하는 경우 셀의 높이가 늘어나는 현상 수정 
   
2. 기능 개선
   - MAC OS의 CMD 키로 Ctrl 단축키 기능 적용 확장 
   - 기본 글꼴 종류 추가 (Courier New, 나눔고딕 코딩) 

==============================================================================================
2.3.1
----------------------------------------------------------------------------------------------
1. 기능 개선
   - [웹접근성] 글쓰기 영역의 iframe의 title속성에 단축키 설명 제공 
   - [웹접근성] 제목 input영역에서 제목 입력 후 TAB하면 스마트에디터 편집 영역으로 포커스 이동하는 기능 추가 
   - [웹접근성] 툴바 영역의 이전/다음 아이템 이동을 TAB, SHIFT+TAB으로 이동할 수 있도록 추가 

==============================================================================================
2.3.0
----------------------------------------------------------------------------------------------
1. 기능 개선
   - [웹접근성] 키보드로만 메뉴를 이동할 수 있도록 단축키 적용  
   - [웹접근성] 웹접근성 도움말 제공 
   - 편집모드와 사이즈 조절바 사용 옵션 추가  
   - 사진 첨부 팝업 데모 파일 구조 개선  

==============================================================================================
2.2.1
----------------------------------------------------------------------------------------------
1. 버그 수정
   - 사진 퀵 업로더 추가 시, 가이드 대로 수행했을 때 사진 첨부가 2번 실행되는 문제 해결
	: loader-min.js 파일 내에 사진 퀵 업로더 소스가 포함되어 있던 부분 제거하여 소스 분리

2. 기능 개선
   - 툴바의 기능 제거/순서 변경이 쉽도록 마크업 구조 개선
	※ 툴바의 기능 제거/순서 변경은 가이드 문서를 참고하세요.


3. 폴더/파일 변경
   - /js_src 폴더 제거
   - /js/smarteditor2.js 추가
	: /js_src 폴더를 /js/smarteditor2.js 로 대체했습니다.
	: /js_src 폴더 구조에서 사용자가 소스를 검색하여 수정하기 어렵던 부분을 보완하기 위하여 
	: /js_src 폴더 내의 플러그인 소스를 통합한 /js/smarteditor2.js 를 추가했습니다.
   - /js/loader-min.js 제거
   - /js/smarteditor2.min.js 추가
	: /js/loader-min.js 파일을 /js/smarteditor2.min.js로 대체했습니다.
   - /quick_photo_uploader 폴더 추가
   - /popup 폴더 이동
	: /popup 폴더	- 사진 퀵 업로더의 팝업과 관련된 소스
	: /plugin 폴더	- 사진 퀵 업로더의 사진첨부를 처리하는 플러그인 js 소스
   - /img/ko_KR 폴더 추가 
	: 이후의 다국어 버전 지원을 위하여 이미지 폴더 내 디렉토리가 추가되었습니다.
	: 언어 별 구분이 필요없는 이미지는 /img 바로 하위에 두었고,
	: 언어 별로 구분되어야 하는 이미지는 /img/ko_KR 과 같이 언어 별 디렉토리로 구분했습니다.
	: 버전 업그레이드를 하는 경우 이미지 경로가 변경된 점에 주의하시기 바랍니다.
   - /js/SE2B_Configuration.js 제거
   - /js/SE2B_Configuration_Service.js 추가
   - /js/SE2B_Configuration_General.js 추가
	: /js/SE2B_Configuration_Service.js 와 /js/SE2B_Configuration_General.js로 파일 분리했습니다.
	: /js/SE2B_Configuration_Service.js 는 적용을 할 때 사용자가 변경할 가능성이 높은 플러그인 설정을 갖고,
	: /js/SE2B_Configuration_General.js 는 서비스에 적용할 때 변경할 가능성이 거의 없는 설정입니다.

==============================================================================================
2.1.3
----------------------------------------------------------------------------------------------
1. 버그 수정
   - [Chrome] 보기 페이지에 글자색이 설정되어 있는 경우 글 작성 시 내용에 적용한 글자색으로 노출되지 않는 문제 해결
   - 엔터 처리가 <BR>로 설정된 경우에도 텍스트 모드에서 모드변경 혹은 글 저장할 때 개행이 <P>로 표시되는 문제 해결
   - [IE9] 각주 삽입 시, 하단으로 떨어지는 이슈 해결
   - [Chrome] 인용구 밖에 글머리기호/번호매기기가 있을 때 인용구 안에서 글머리기호/번호매기기 시 내용이 인용구 밖으로 나가는 문제 해결
   - [IE] IE에서 특정 블로그 글을 복사하여 붙여넣기 했을 때 개행이 제거되는 문제 해결
   - 사진을 드래그해서 사이즈를 변경한 후 저장 혹은 HTML모드로 변경하면, 사진 사이즈가 원복되는 현상 해결
   - [Chrome/FF/Safari] 스크롤바가 생성되도록 문자입력 후 엔터 클릭하지 않은 상태에서 이미지 하나 삽입 시 이미지에 포커싱이 놓이지 않는 문제 해결
   - [IE9 표준] 사진을 스크롤로 일부 가린 상태에서 재편집하여 적용했을 때 계속 가려진 상태인 문제 해결
   - FF에서 사진을 여러장 첨부 시 스크롤이 가장 마지막 추가한 사진으로 내려가지 않음 해결
   - 호환 모드를 제거하고 사진 첨부 시 에디팅 영역의 커서 주위에 <sub><sup> 태그가 붙어서 글자가 매우 작게 되는 현상 해결
   - [IE9] 에디터에 각주 연속으로 입력 시 커서가 각주사이로 이동되는 현상 해결
   - 글꼴색/글꼴배경색 더보기에서 글꼴색 선택>다시 다른 색상 선택 후 처음 선택되었던 색상 선택 시 처음 선택색상이 원래 자리에서 삭제되지 않는 현상 해결
   - 제공하지 않는 기능인 이모티콘 플러그인 소스 제거
   - 플러그인 태그 코드 추가 시 <li> 태그와 <button> 태그 사이에 개행이 있으면 이벤트가 등록되지 않는 현상 해결
   
2. 기능 개선
   - 표 삽입 시 본문 작성 영역 안에 너비 100%로 생성되도록 개선
   - 호환모드 설정이 설정 파일 정보에 따라 처리되도록 변경
   
   
==============================================================================================
2.1.2
----------------------------------------------------------------------------------------------
1. 버그 수정
   - [IE9]Shift+Enter를 여러번 하고 글의 중간의 마지막 글자 다음에서 엔터를 쳤을 때 엔터 위치가 달라지는 현상 수정
   - [IE9]메모장에서 붙여 넣기 후 내용 중간의 마지막 글자 다음에서 엔터를 쳤을 때 엔터 위치가 달라지는 현상 수정
   - 한 줄 입력 후 색상을 적용하고 내용 중간에서 엔터를 쳤을 때 적용되었던 색상이 풀리던 현상 수정
   - 글꼴 레이어를 열었을 때, 샘플 텍스트가 잘못 나오던 현상 수정 
   - 인용구를 14개까지 중첩하고, 15개부터 경고 창이 나오도록 수정

2. 기능 개선
   - 찾기/바꾸기 레이어를 닫았다가 다시 열 때, [바꿀 단어] 입력란이 초기화 되도록 개선
   - 찾기/바꾸기 레이어 오픈 시 툴바 버튼 inactive 처리
   - 표 추가 레이어의 테이블 색상, 배경 색상의 기본 값을 SmartEditor2Skin.html에서 변경할 수 있도록 함 
   ※주의 : 기존의 html파일에 덮어 씌우게 되면 기본 배경 색상이 다르게 표시됨
           따라서 반드시 새로 업데이트 된 html 파일을 사용하기를 권장
           임의로 수정하려면 위 파일의 아래 부분의 value를 아래와 같이 변경해야 함
	   <input id="se2_b_color" name="" type="text" maxlength="7" value="#cccccc" class="input_ty3">	
           <input id="se2_cellbg" name="" type="text" maxlength="7" value="#ffffff" class="input_ty3">


==============================================================================================
2.1.1
----------------------------------------------------------------------------------------------
1. 기능 추가
   - 에디터 로딩 완료 시점에 실행되는 함수 (fOnAppLoad) 정의
   
2. 버그 수정
   - 에디터 초기 Width에 100%가 설정될 수 있도록 수정, minWidth 설정 추가
   - 마크업에서 나눔 글꼴을 제외하면 JS 에러가 나는 문제 수정
   - [IE9] 글자 색상 적용 후 내용 중간에서 계속 Enter할 때 Enter가 되지 않는 오류 수정
   - [Chrome/Safari] 표 간단편집기 위에서 text를 drag하면 JS 에러가 발생하는 문제 수정

3. 기능 개선
   - 사진 퀵 업로더 : 쉽게 사용할 수 있도록 소스 수정 및 예제 보강


==============================================================================================
2.1.0
----------------------------------------------------------------------------------------------
1. 기능 추가
   - 사진 퀵 업로더 : 사진 첨부 팝업 UI 제공 (HTML5 지원)
   - 에디터 본문에 글 작성 후 창을 닫을 때 발생하는 alert 메세지를 사용자가 설정할 수 있도록 옵션을 추가함
   - Jindo 모듈을 패키지에 포함하도록 빌드를 수정함
   - document.domain을 제거함
   - 에디터 초기 Width를 설정할 수 있도록 수정함
   - 툴바의 접힘/펼침 기능을 제공하는 SE_ToolbarToggler 플러그인 추가함
   
2. 버그 수정
   - 에디터 리사이즈 시 북마크 태그가 본문에 추가되는 이슈 확인 및 수정함


==============================================================================================
2.0.0
----------------------------------------------------------------------------------------------
1. 기능 강화
   - 글꼴과 글자 크기
      : 기존의 Selectbox 형태의 글꼴 목록을 깔끔한 디자인의 레이어로 제공한다.
   - 글자색과 글자 배경색
      : 기존의 기본 색상표 이외에 다양한 색상을 선택할 수 있는 컬러 팔레트를 확장 지원한다.
   - 줄간격
      : 기존의 Selectbox 형태의 줄간격 목록을 깔끔한 디자인의 레이어로 제공한다.
        또한, 줄간격을 직접 설정할 수 있도록 직접 입력 기능도 확장 지원한다.
   - 인용구
      : 기존의 7가지에서 10가지로 인용구 디자인을 확장 지원한다.
   - 표
      : 표 생성 시 기존의 테두리 색상과 두께를 설정할 수 있는 기능 이외에 테두리 스타일을 설정할 수 있는 기능을 확장 지원한다.
        또한, 표 템플릿을 제공하여 보다 쉽게 표 스타일을 생성할 수 있도록 하였다.
 
2. 기능 추가
   - 표 간단편집기
      : 표 생성 후 스타일을 편집할 수 있도록 표 편집 기능을 추가 제공한다.
   - TEXT 모드
      : WYSIWYG와 HTML 모드 이외에 TEXT 모드를 제공하여 텍스트만으로 본문의 내용을 작성할 수 있도록 편집 모드를 추가 제공한다.
package jcifs.smb;

import jcifs.Config;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComSessionSetupAndX.class */
class SmbComSessionSetupAndX extends AndXServerMessageBlock {
    private static final int BATCH_LIMIT = Config.getInt("jcifs.smb.client.SessionSetupAndX.TreeConnectAndX", 1);
    private static final boolean DISABLE_PLAIN_TEXT_PASSWORDS = Config.getBoolean("jcifs.smb.client.disablePlainTextPasswords", true);
    private byte[] lmHash;
    private byte[] ntHash;
    private byte[] blob;
    private int sessionKey;
    private int capabilities;
    private String accountName;
    private String primaryDomain;
    SmbSession session;
    Object cred;

    SmbComSessionSetupAndX(SmbSession session, ServerMessageBlock andx, Object cred) throws SmbException {
        super(andx);
        this.blob = null;
        this.command = (byte) 115;
        this.session = session;
        this.cred = cred;
        this.sessionKey = session.transport.sessionKey;
        this.capabilities = session.transport.capabilities;
        if (session.transport.server.security == 1) {
            if (!(cred instanceof NtlmPasswordAuthentication)) {
                if (cred instanceof byte[]) {
                    this.blob = (byte[]) cred;
                    return;
                }
                throw new SmbException("Unsupported credential type");
            }
            NtlmPasswordAuthentication auth = (NtlmPasswordAuthentication) cred;
            if (auth == NtlmPasswordAuthentication.ANONYMOUS) {
                this.lmHash = new byte[0];
                this.ntHash = new byte[0];
                this.capabilities &= Integer.MAX_VALUE;
            } else if (session.transport.server.encryptedPasswords) {
                this.lmHash = auth.getAnsiHash(session.transport.server.encryptionKey);
                this.ntHash = auth.getUnicodeHash(session.transport.server.encryptionKey);
                if (this.lmHash.length == 0 && this.ntHash.length == 0) {
                    throw new RuntimeException("Null setup prohibited.");
                }
            } else {
                if (DISABLE_PLAIN_TEXT_PASSWORDS) {
                    throw new RuntimeException("Plain text passwords are disabled");
                }
                if (this.useUnicode) {
                    String password = auth.getPassword();
                    this.lmHash = new byte[0];
                    this.ntHash = new byte[(password.length() + 1) * 2];
                    writeString(password, this.ntHash, 0);
                } else {
                    String password2 = auth.getPassword();
                    this.lmHash = new byte[(password2.length() + 1) * 2];
                    this.ntHash = new byte[0];
                    writeString(password2, this.lmHash, 0);
                }
            }
            this.accountName = auth.username;
            if (this.useUnicode) {
                this.accountName = this.accountName.toUpperCase();
            }
            this.primaryDomain = auth.domain.toUpperCase();
            return;
        }
        if (session.transport.server.security == 0) {
            if (cred instanceof NtlmPasswordAuthentication) {
                NtlmPasswordAuthentication auth2 = (NtlmPasswordAuthentication) cred;
                this.lmHash = new byte[0];
                this.ntHash = new byte[0];
                this.accountName = auth2.username;
                if (this.useUnicode) {
                    this.accountName = this.accountName.toUpperCase();
                }
                this.primaryDomain = auth2.domain.toUpperCase();
                return;
            }
            throw new SmbException("Unsupported credential type");
        }
        throw new SmbException("Unsupported");
    }

    @Override // jcifs.smb.AndXServerMessageBlock
    int getBatchLimit(byte command) {
        if (command == 117) {
            return BATCH_LIMIT;
        }
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2;
        writeInt2(this.session.transport.snd_buf_size, dst, dstIndex);
        int dstIndex3 = dstIndex + 2;
        writeInt2(this.session.transport.maxMpxCount, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 2;
        SmbTransport smbTransport = this.session.transport;
        writeInt2(1L, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 2;
        writeInt4(this.sessionKey, dst, dstIndex5);
        int dstIndex6 = dstIndex5 + 4;
        if (this.blob != null) {
            writeInt2(this.blob.length, dst, dstIndex6);
            dstIndex2 = dstIndex6 + 2;
        } else {
            writeInt2(this.lmHash.length, dst, dstIndex6);
            int dstIndex7 = dstIndex6 + 2;
            writeInt2(this.ntHash.length, dst, dstIndex7);
            dstIndex2 = dstIndex7 + 2;
        }
        int i = dstIndex2;
        int dstIndex8 = dstIndex2 + 1;
        dst[i] = 0;
        int dstIndex9 = dstIndex8 + 1;
        dst[dstIndex8] = 0;
        int dstIndex10 = dstIndex9 + 1;
        dst[dstIndex9] = 0;
        int dstIndex11 = dstIndex10 + 1;
        dst[dstIndex10] = 0;
        writeInt4(this.capabilities, dst, dstIndex11);
        return (dstIndex11 + 4) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2;
        if (this.blob != null) {
            System.arraycopy(this.blob, 0, dst, dstIndex, this.blob.length);
            dstIndex2 = dstIndex + this.blob.length;
        } else {
            System.arraycopy(this.lmHash, 0, dst, dstIndex, this.lmHash.length);
            int dstIndex3 = dstIndex + this.lmHash.length;
            System.arraycopy(this.ntHash, 0, dst, dstIndex3, this.ntHash.length);
            int dstIndex4 = dstIndex3 + this.ntHash.length;
            int dstIndex5 = dstIndex4 + writeString(this.accountName, dst, dstIndex4);
            dstIndex2 = dstIndex5 + writeString(this.primaryDomain, dst, dstIndex5);
        }
        SmbTransport smbTransport = this.session.transport;
        int dstIndex6 = dstIndex2 + writeString(SmbTransport.NATIVE_OS, dst, dstIndex2);
        SmbTransport smbTransport2 = this.session.transport;
        return (dstIndex6 + writeString(SmbTransport.NATIVE_LANMAN, dst, dstIndex6)) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        StringBuilder append = new StringBuilder().append("SmbComSessionSetupAndX[").append(super.toString()).append(",snd_buf_size=").append(this.session.transport.snd_buf_size).append(",maxMpxCount=").append(this.session.transport.maxMpxCount).append(",VC_NUMBER=");
        SmbTransport smbTransport = this.session.transport;
        StringBuilder append2 = append.append(1).append(",sessionKey=").append(this.sessionKey).append(",lmHash.length=").append(this.lmHash == null ? 0 : this.lmHash.length).append(",ntHash.length=").append(this.ntHash == null ? 0 : this.ntHash.length).append(",capabilities=").append(this.capabilities).append(",accountName=").append(this.accountName).append(",primaryDomain=").append(this.primaryDomain).append(",NATIVE_OS=");
        SmbTransport smbTransport2 = this.session.transport;
        StringBuilder append3 = append2.append(SmbTransport.NATIVE_OS).append(",NATIVE_LANMAN=");
        SmbTransport smbTransport3 = this.session.transport;
        String result = new String(append3.append(SmbTransport.NATIVE_LANMAN).append("]").toString());
        return result;
    }
}

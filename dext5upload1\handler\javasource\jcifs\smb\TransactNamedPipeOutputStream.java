package jcifs.smb;

import java.io.IOException;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/TransactNamedPipeOutputStream.class */
class TransactNamedPipeOutputStream extends SmbFileOutputStream {
    private String path;
    private SmbNamedPipe pipe;
    private byte[] tmp;
    private boolean dcePipe;

    TransactNamedPipeOutputStream(SmbNamedPipe pipe) throws IOException {
        super(pipe, false, (pipe.pipeType & (-65281)) | 32);
        this.tmp = new byte[1];
        this.pipe = pipe;
        this.dcePipe = (pipe.pipeType & SmbNamedPipe.PIPE_TYPE_DCE_TRANSACT) == 1536;
        this.path = pipe.unc;
    }

    @Override // jcifs.smb.SmbFileOutputStream, java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        this.pipe.close();
    }

    @Override // jcifs.smb.SmbFileOutputStream, java.io.OutputStream
    public void write(int b) throws IOException {
        this.tmp[0] = (byte) b;
        write(this.tmp, 0, 1);
    }

    @Override // jcifs.smb.SmbFileOutputStream, java.io.OutputStream
    public void write(byte[] b) throws IOException {
        write(b, 0, b.length);
    }

    @Override // jcifs.smb.SmbFileOutputStream, java.io.OutputStream
    public void write(byte[] b, int off, int len) throws IOException {
        if (len < 0) {
            len = 0;
        }
        if ((this.pipe.pipeType & 256) == 256) {
            this.pipe.send(new TransWaitNamedPipe(this.path), new TransWaitNamedPipeResponse());
            this.pipe.send(new TransCallNamedPipe(this.path, b, off, len), new TransCallNamedPipeResponse(this.pipe));
        } else if ((this.pipe.pipeType & 512) == 512) {
            ensureOpen();
            TransTransactNamedPipe req = new TransTransactNamedPipe(this.pipe.fid, b, off, len);
            if (this.dcePipe) {
                req.maxDataCount = 1024;
            }
            this.pipe.send(req, new TransTransactNamedPipeResponse(this.pipe));
        }
    }
}

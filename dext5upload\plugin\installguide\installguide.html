﻿<!doctype html>
<html lang="ko">
<head>
    <title>DEXT5 Plugin Install</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link rel="stylesheet" type="text/css" href="common.css" />
    <script type="text/javascript">
        function set_page() {
            var contentDiv = document.getElementById('DEXT_fiVe_UP_Popup_content');
            var lang = opener.window.UPLOADTOP.G_CURRUPLOADER.frameWin.Dext5Upload_Lang;
            var _html = '<div class="guide_box">';
            _html += '<h2>' + lang.plugin_install_guide.header + '</h2>';
            _html += '<h3>' + lang.plugin_install_guide.desc1 + '</h3>';
            _html += '<ul class="guide_lst">';
            _html += '<li>' + lang.plugin_install_guide.desc2.replace('{0}', getInstallFileName()) + '</li>';
            _html += '<li>' + lang.plugin_install_guide.desc3 + '</li>';
            _html += '<li>' + lang.plugin_install_guide.desc4 + '</li>';
            _html += '</ul>';
            _html += '</div>';
            _html += '<div class="info_os_chk">';
            _html += '<p>' + lang.plugin_install_guide.agent_info + '</p>';
            _html += '</div>';
            contentDiv.innerHTML = _html;
            install_ready();
        }

        function install_ready() {
            try {
                var downloadLink = document.getElementById('download_link');

                var downUrl = '';
                if (opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl) {
                    downUrl = opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl;
                } else {
                    downUrl = opener.window.UPLOADTOP.G_CURRUPLOADER._config.webPath.plugin + opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallFileName;
                }
                downloadLink.href = downUrl;

                var fileName = document.getElementById('file_name');
                var insta
                fileName.innerHTML = getInstallFileName();

                var osInfo = document.getElementById('os_info');
                var browserInfo = document.getElementById('browser_info');

                var agentInfo = opener.window.UPLOADTOP.DEXT5UPLOAD.UserAgent;
                osInfo.innerHTML = agentInfo.os.name + ' ' + agentInfo.os.version;
                browserInfo.innerHTML = agentInfo.browser.name + agentInfo.browser.major;

                opener.window.requestPluginInstall(opener.window, document);
            } catch (e) {
                self.focus();
            }
        }

        function getInstallFileName() {
            var installFileName = '';
            if (opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl != '') {
                var tempIdx = opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl.lastIndexOf('/');
                installFileName = opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallUrl.substring(tempIdx + 1);
            } else {
                installFileName = opener.window.UPLOADTOP.G_CURRUPLOADER._config.pluginInstallFileName;
            }
            return installFileName;
        }
    </script>
</head>
<body onload="set_page();">
    <div id="DEXT_fiVe_UP_Popup_wrap">
        <div id="DEXT_fiVe_UP_Popup_head"><h1>DEXT5</h1></div>
        <div id="DEXT_fiVe_UP_Popup_content" class="txt_install_guide"></div>
        <div id="DEXT_fiVe_UP_Popup_foot"><img src="img_dext5_txt.png" alt="DEXT5" title="DEXT5" /></div>
    </div>
</body>
</html>
﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con ">           
    <h3 class="title">DEXT5 Upload :: Hidden</h3>
    <p class="ttl">void Hidden(uploadID)</p>
    <p class="txt">
        DEXT5UPLOAD를 화면에서 숨김니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음. 
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;숨길 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        예) id가 upload1인 업로드를 생성했을 경우<br/>
        &nbsp;&nbsp;&nbsp;DEXT5UPLOAD.Hidden("upload1") upload1 업로드를 화면에서 숨김니다.<br /><br />

        예) id가 upload1, upload2인 업로드를 생성했을 경우<br />
        &nbsp;&nbsp;&nbsp;DEXT5UPLOAD.Hidden("upload1")  upload1 업로드를 화면에서 숨김니다.<br />
        &nbsp;&nbsp;&nbsp;DEXT5UPLOAD.Hidden("upload2")  upload2 업로드를 화면에서 숨김니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function dext_upload_loaded_event() {
            DEXT5UPLOAD.Hidden('upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: MaxTotalFileCount</h3>
    <p class="ttl">config.MaxTotalFileCount</p>
    <p class="txt">
        업로드 할 최대 파일 개수 제한값을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0" 제한없음이고, "0 보다 큰 수" 설정시 해당 개수 초과로 첨부 할 수 없습니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 할 최대 파일 개수를 5개로 제한합니다.
        DEXT5UPLOAD.config.MaxTotalFileCount = '5';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿using System;
using System.IO;
using System.Text;
using System.Xml.Serialization;

namespace Raonwiz.Dext5.License.Library.General.IO
{
	// Token: 0x02000002 RID: 2
	public static class Serializer
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
		private static XmlSerializer getSerializer<T>()
		{
			return new XmlSerializer(typeof(T));
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002064 File Offset: 0x00000264
		public static void Save<T>(T o, string fileName)
		{
			using (FileStream fileStream = File.Open(fileName, FileMode.Create))
			{
				Serializer.Save<T>(o, fileStream);
			}
		}

		// Token: 0x06000003 RID: 3 RVA: 0x0000209C File Offset: 0x0000029C
		public static void Save<T>(T o, Stream stream)
		{
			Serializer.getSerializer<T>().Serialize(stream, o);
		}

		// Token: 0x06000004 RID: 4 RVA: 0x000020B0 File Offset: 0x000002B0
		public static byte[] Save<T>(T o)
		{
			byte[] buffer;
			using (MemoryStream memoryStream = new MemoryStream())
			{
				Serializer.Save<T>(o, memoryStream);
				buffer = memoryStream.GetBuffer();
			}
			return buffer;
		}

		// Token: 0x06000005 RID: 5 RVA: 0x000020F0 File Offset: 0x000002F0
		public static string Save<T>(T o, Encoding encoding)
		{
			return encoding.GetString(Serializer.Save<T>(o));
		}

		// Token: 0x06000006 RID: 6 RVA: 0x000020FE File Offset: 0x000002FE
		public static T Load<T>(Stream stream)
		{
			return (T)((object)Serializer.getSerializer<T>().Deserialize(stream));
		}

		// Token: 0x06000007 RID: 7 RVA: 0x00002110 File Offset: 0x00000310
		public static T Load<T>(FileInfo file)
		{
			T result;
			using (FileStream fileStream = file.Open(FileMode.Open))
			{
				result = Serializer.Load<T>(fileStream);
			}
			return result;
		}

		// Token: 0x06000008 RID: 8 RVA: 0x0000214C File Offset: 0x0000034C
		public static T Load<T>(byte[] data)
		{
			T result;
			using (MemoryStream memoryStream = new MemoryStream(data))
			{
				result = Serializer.Load<T>(memoryStream);
			}
			return result;
		}

		// Token: 0x06000009 RID: 9 RVA: 0x00002184 File Offset: 0x00000384
		public static T Load<T>(string data)
		{
			return Serializer.Load<T>(Encoding.ASCII.GetBytes(data));
		}
	}
}

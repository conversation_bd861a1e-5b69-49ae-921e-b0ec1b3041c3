/*
Copyright (C) NAVER corp.  

This library is free software; you can redistribute it and/or  
modify it under the terms of the GNU Lesser General Public  
License as published by the Free Software Foundation; either  
version 2.1 of the License, or (at your option) any later version.  

This library is distributed in the hope that it will be useful,  
but WITHOUT ANY WARRANTY; without even the implied warranty of  
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU  
Lesser General Public License for more details.  

You should have received a copy of the GNU Lesser General Public  
License along with this library; if not, write to the Free Software  
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA  
*/
if(typeof window.nhn=='undefined'){window.nhn = {};}
/**
 * @fileOverview This file contains a message mapping(Korean), which is used to map the message code to the actual message
 * @name husky_SE2B_Lang_en_US.js
 * @ unescape
 */
var oMessageMap_en_US = {
	'SE_EditingAreaManager.onExit' : 'Contents have been changed.',
	'SE_Color.invalidColorCode' : 'Enter the correct color code. \n\n ex) #000000, #FF0000, #FFFFFF, #ffffff, ffffff',
	'SE_Hyperlink.invalidURL' : 'You have entered an incorrect URL.',
	'SE_FindReplace.keywordMissing' : 'Enter the word you wish to find.',
	'SE_FindReplace.keywordNotFound' : 'The word does not exist.',
	'SE_FindReplace.replaceAllResultP1' : 'A total of ',
	'SE_FindReplace.replaceAllResultP2' : ' matching contents have been changed.',
	'SE_FindReplace.notSupportedBrowser' : 'Function cannot be used in the browser you are currently using. \n\nSorry for the inconvenience.',
	'SE_FindReplace.replaceKeywordNotFound' : 'No word to change.',
	'SE_LineHeight.invalidLineHeight' : 'Incorrect value.',
	'SE_Footnote.defaultText' : 'Enter footnote details.',
	'SE.failedToLoadFlash' : 'The function cannot be used because flash has been blocked.',
	'SE2M_EditingModeChanger.confirmTextMode' : 'The contents remain, but editing effects, including fonts, and attachments, \n\nsuch as images, will disappear when changed to text mode. \n\n Make changes?',
	'SE2M_FontNameWithLayerUI.sSampleText' : 'ABCD'
};
﻿using System;
using System.IO;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Plugin
{
	// Token: 0x0200003B RID: 59
	public class Upload : Base
	{
		// Token: 0x06000330 RID: 816 RVA: 0x00025638 File Offset: 0x00023838
		public Upload(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000331 RID: 817 RVA: 0x000256C4 File Offset: 0x000238C4
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string resumeup = this._entity_dextParam.RESUMEUP;
			string kcmd = this._entity_dextParam.kcmd;
			if (!base.CheckCaller("ieplugin"))
			{
				base.ClientResponse("[FAIL]", "error|001|Bad Request Type", "");
				return null;
			}
			if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
			{
				base.ClientResponse("[FAIL]", "error|012|Not allowed file extension", "");
				return null;
			}
			if (!base.CheckBlackWord(this.fileBlackWordList, null))
			{
				base.ClientResponse("[FAIL]", "error|018|There does not allow the string included in file name", "");
				return null;
			}
			this.tempPath = base.GetTempPath(this.tempPath);
			if (string.IsNullOrEmpty(kcmd))
			{
				if (this.hContext.Request.Files.Count == 0)
				{
					base.ClientResponse("[FAIL]", "error|" + (string.IsNullOrEmpty(resumeup) ? "027" : "727") + "|Not exists File Stream", "");
					return null;
				}
				if (string.IsNullOrEmpty(resumeup))
				{
					this.PluginFileSave(pBeforeInitializeEvent, pBeforeInitializeEventEx, pCompleteBeforeEvent, pCompleteBeforeEventEx, pCompleteEvent, pCompleteEventEx, ref pCustomError);
				}
				else
				{
					this.PluginFileResumeSave(pBeforeInitializeEvent, pBeforeInitializeEventEx, ref pCustomError);
				}
			}
			else if (kcmd == "kc01")
			{
				this.HS_CreateDummyFile(pBeforeInitializeEvent, pBeforeInitializeEventEx, ref pCustomError);
			}
			else if (kcmd == "kc02")
			{
				this.HS_WriteChunkedFile();
			}
			else if (kcmd == "kc03")
			{
				this.HS_Fininshed(pCompleteBeforeEvent, pCompleteBeforeEventEx, pCompleteEvent, pCompleteEventEx, ref pCustomError);
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug(this.logMessage, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x06000332 RID: 818 RVA: 0x00025858 File Offset: 0x00023A58
		private void PluginFileSave(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, ref Dext5CustomError pCustomError)
		{
			string text = string.Empty;
			string text2 = string.Empty;
			string fileLocationInfo = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, true, null, null, null, null, null, null, null);
			if (string.IsNullOrEmpty(fileLocationInfo) || fileLocationInfo.IndexOf("error") == 0)
			{
				return;
			}
			string[] array = fileLocationInfo.Split(new char[]
			{
				'|'
			});
			text = array[0];
			text2 = array[1];
			bool flag = false;
			string text3 = text;
			try
			{
				pBeforeInitializeEvent(this.hContext, ref text, ref text2);
				if (!text3.Equals(text))
				{
					flag = true;
				}
			}
			catch
			{
			}
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.NewFileLocation = text;
				uploadEventEntity.ResponseFileName = text2;
				pBeforeInitializeEventEx(uploadEventEntity);
				text = uploadEventEntity.NewFileLocation;
				text2 = uploadEventEntity.ResponseFileName;
				if (!text3.Equals(text))
				{
					flag = true;
				}
			}
			catch
			{
			}
			if (flag)
			{
				string[] array2 = base.InitializeEventFileExec(text, text2);
				text = array2[0];
				text2 = array2[1];
			}
			string text4 = string.Empty;
			if (pCustomError == null)
			{
				bool flag2 = true;
				if (!string.IsNullOrEmpty(this._entity_dextParam.fileDataEncrypt) && this._entity_dextParam.fileDataEncrypt == "1")
				{
					base.decryptFile(text, this.hContext.Request.Files[0].InputStream, FileMode.Create, null);
				}
				else
				{
					this.hContext.Request.Files[0].SaveAs(text);
				}
				string fileDataIntegrity = this._entity_dextParam.fileDataIntegrity;
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("Upload PluginFileSave :: sFormDataIntegrity => " + (!string.IsNullOrEmpty(fileDataIntegrity)).ToString(), this._str_DebugFilePath);
				}
				if (!string.IsNullOrEmpty(fileDataIntegrity))
				{
					string integrityHashValue = base.getIntegrityHashValue(text);
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("Upload PluginFileSave :: newfilelocation => " + text, this._str_DebugFilePath);
						LogUtil.DextDebug("Upload PluginFileSave :: hashString => " + integrityHashValue, this._str_DebugFilePath);
						LogUtil.DextDebug("Upload PluginFileSave :: sFormDataIntegrity => " + fileDataIntegrity, this._str_DebugFilePath);
					}
					if (integrityHashValue != fileDataIntegrity)
					{
						flag2 = false;
					}
				}
				if (!flag2)
				{
					File.Delete(text);
					base.ClientResponse("[FAIL]", "error|024|After the completion of file data transfer and file data before transmitting do not match", "");
					return;
				}
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text4 = text;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					string oldValue = HostingEnvironment.MapPath("~/");
					text4 = "/" + text.Replace(oldValue, "");
					text4 = text4.Replace(this.m_PathChar, '/');
				}
				try
				{
					pCompleteBeforeEvent(this.hContext, ref text, ref text4, ref text2, ref this._str_ResponseCustomValue);
				}
				catch
				{
				}
				try
				{
					UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
					uploadEventEntity2.Context = this.hContext;
					uploadEventEntity2.NewFileLocation = text;
					uploadEventEntity2.ResponseFileServerPath = text4;
					uploadEventEntity2.ResponseFileName = text2;
					uploadEventEntity2.ResponseGroupId = this._entity_dextParam.fileGroupID;
					uploadEventEntity2.FileIndex = this._entity_dextParam.fileIndex;
					pCompleteBeforeEventEx(uploadEventEntity2);
					text = uploadEventEntity2.NewFileLocation;
					text4 = uploadEventEntity2.ResponseFileServerPath;
					text2 = uploadEventEntity2.ResponseFileName;
					this._str_ResponseCustomValue = uploadEventEntity2.ResponseCustomValue;
					this._str_ResponseGroupId = uploadEventEntity2.ResponseGroupId;
				}
				catch
				{
				}
				string text5 = "";
				if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf('*') == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
				{
					text5 = text5 + "|" + this._str_ResponseCustomValue;
				}
				if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf('*') == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
				{
					text5 = text5 + "\b" + this._str_ResponseGroupId;
				}
				this.logMessage = text4 + "*" + text2 + text5;
				string s = Dext5Parameter.MakeParameter(text4 + "*" + text2 + text5);
				this.hContext.Response.Write(s);
			}
			if (pCustomError != null)
			{
				string responseMessage = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
				base.ClientResponse("[FAIL]", responseMessage, "");
				return;
			}
			if (pCustomError == null)
			{
				try
				{
					pCompleteEvent(this.hContext, text, text4, text2);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text,
						ResponseFileServerPath = text4,
						ResponseFileName = text2
					});
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000333 RID: 819 RVA: 0x00025D60 File Offset: 0x00023F60
		private void PluginFileResumeSave(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
			{
				base.ClientResponse("[FAIL]", "error|012|Not allowed file extension", "");
				return;
			}
			if (!base.CheckBlackWord(this.fileBlackWordList, null))
			{
				base.ClientResponse("[FAIL]", "error|018|There does not allow the string included in file name", "");
				return;
			}
			string text = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string resumeguid = this._entity_dextParam.RESUMEGUID;
			string tempFileFolder = base.GetTempFileFolder(this.tempPath, resumeguid);
			string path = string.Concat(new object[]
			{
				tempFileFolder,
				this.m_PathChar,
				resumeguid,
				this.m_strHSTempSuffix
			});
			if (File.Exists(path))
			{
				text2 = base.FileLocationInfoReadWrite("R", resumeguid, "");
				if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
					return;
				}
				text3 = Path.GetFileName(text2);
			}
			else
			{
				text = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
				if (string.IsNullOrEmpty(text))
				{
					throw new Exception("Error occured on the server side");
				}
				if (text.IndexOf("error") == 0)
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter(text));
					return;
				}
				string[] array = text.Split(new char[]
				{
					'|'
				});
				text2 = array[0];
				text3 = array[1];
				bool flag = false;
				string text4 = text2;
				try
				{
					pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
					if (!text4.Equals(text2))
					{
						flag = true;
					}
				}
				catch
				{
				}
				try
				{
					UploadEventEntity uploadEventEntity = new UploadEventEntity();
					uploadEventEntity.Context = this.hContext;
					uploadEventEntity.NewFileLocation = text2;
					uploadEventEntity.ResponseFileName = text3;
					pBeforeInitializeEventEx(uploadEventEntity);
					text2 = uploadEventEntity.NewFileLocation;
					text3 = uploadEventEntity.ResponseFileName;
					if (!text4.Equals(text2))
					{
						flag = true;
					}
				}
				catch
				{
				}
				if (flag)
				{
					string[] array2 = base.InitializeEventFileExec(text2, text3);
					text2 = array2[0];
					text3 = array2[1];
				}
				base.FileLocationInfoReadWrite("W", resumeguid, text2);
			}
			if (pCustomError == null)
			{
				try
				{
					string text5 = this.hContext.Request.Headers["X-Raon-Ps"];
					if (!string.IsNullOrEmpty(this._entity_dextParam.fileDataEncrypt) && Convert.ToInt32(this._entity_dextParam.fileDataEncrypt) > 0)
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Upload PluginFileResumeSave :: 암호화 => " + text2, this._str_DebugFilePath);
						}
						if (File.Exists(text2))
						{
							base.decryptFile(text2, this.hContext.Request.Files[0].InputStream, FileMode.OpenOrCreate, text5);
						}
						else
						{
							base.decryptFile(text2, this.hContext.Request.Files[0].InputStream, FileMode.Create, null);
						}
					}
					else
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Upload PluginFileResumeSave :: 비 암호화 => " + text2, this._str_DebugFilePath);
						}
						if (this.hContext.Request.Files[0].ContentLength > this.iResumeCheckSize)
						{
							using (Stream inputStream = this.hContext.Request.Files[0].InputStream)
							{
								int num = 65536;
								byte[] array3 = new byte[num];
								int num2;
								if (File.Exists(text2))
								{
									num2 = 1;
								}
								else
								{
									num2 = 0;
								}
								using (FileStream fileStream = new FileStream(text2, (num2 == 0) ? FileMode.Create : FileMode.OpenOrCreate))
								{
									if (!string.IsNullOrEmpty(text5))
									{
										if (this._b_IsDebug)
										{
											LogUtil.DextDebug("Upload PluginFileResumeSave :: X-Raon-Ps => " + text5, this._str_DebugFilePath);
										}
										fileStream.Seek(Convert.ToInt64(text5), SeekOrigin.Begin);
									}
									int count;
									while ((count = inputStream.Read(array3, 0, array3.Length)) > 0)
									{
										fileStream.Write(array3, 0, count);
									}
									fileStream.Close();
								}
								inputStream.Close();
								goto IL_514;
							}
						}
						using (BinaryReader binaryReader = new BinaryReader(this.hContext.Request.Files[0].InputStream))
						{
							byte[] buffer = binaryReader.ReadBytes(this.hContext.Request.Files[0].ContentLength);
							binaryReader.Close();
							int num3;
							if (File.Exists(text2))
							{
								num3 = 1;
							}
							else
							{
								num3 = 0;
							}
							using (FileStream fileStream2 = new FileStream(text2, (num3 == 0) ? FileMode.Create : FileMode.OpenOrCreate))
							{
								if (!string.IsNullOrEmpty(text5))
								{
									if (this._b_IsDebug)
									{
										LogUtil.DextDebug("Upload PluginFileResumeSave :: X-Raon-Ps => " + text5, this._str_DebugFilePath);
									}
									fileStream2.Seek(Convert.ToInt64(text5), SeekOrigin.Begin);
								}
								using (BinaryWriter binaryWriter = new BinaryWriter(fileStream2))
								{
									binaryWriter.Write(buffer);
									binaryWriter.Close();
									fileStream2.Close();
								}
							}
						}
					}
					IL_514:
					string fileDataIntegrity = this._entity_dextParam.fileDataIntegrity;
					if (!string.IsNullOrEmpty(fileDataIntegrity))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Upload PluginFileResumeSave :: Data Integrity Check => True", this._str_DebugFilePath);
						}
						bool flag2 = true;
						string integrityHashValue = base.getIntegrityHashValue(text2);
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Upload PluginFileResumeSave :: newfilelocation => " + text2, this._str_DebugFilePath);
							LogUtil.DextDebug("Upload PluginFileResumeSave :: Client Hash => " + fileDataIntegrity, this._str_DebugFilePath);
							LogUtil.DextDebug("Upload PluginFileResumeSave :: Server Hash => " + integrityHashValue, this._str_DebugFilePath);
						}
						if (integrityHashValue != fileDataIntegrity)
						{
							flag2 = false;
						}
						if (!flag2)
						{
							FileInfo fileInfo = new FileInfo(text2);
							File.Delete(text2);
							Directory.Delete(fileInfo.DirectoryName);
							base.ClientResponse("[FAIL]", "error|024|After the completion of file data transfer and file data before transmitting do not match", "");
						}
						else
						{
							this.logMessage = "[OK]PluginFileResumeSave file integrity check";
							base.ClientResponse("[OK]", "", "");
						}
					}
					else
					{
						this.logMessage = "[OK]PluginFileResumeSave";
						base.ClientResponse("[OK]", "", "");
					}
					return;
				}
				catch (Exception ex)
				{
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("Upload PluginFileResumeSave Error => " + ex.ToString(), this._str_DebugFilePath);
					}
					throw new Exception(ex.Message);
				}
			}
			string responseMessage = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			base.ClientResponse("[FAIL]", responseMessage, "");
		}

		// Token: 0x06000334 RID: 820 RVA: 0x000264BC File Offset: 0x000246BC
		private void HS_CreateDummyFile(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string guid = this._entity_dextParam.GUID;
			string fileName = this._entity_dextParam.fileName;
			long num = Convert.ToInt64(this._entity_dextParam.fileSize);
			if (string.IsNullOrEmpty(fileName))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			try
			{
				string text = string.Empty;
				string text2 = string.Empty;
				string fileLocationInfo = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, true, null, null, null, null, null, null, null);
				if (!string.IsNullOrEmpty(fileLocationInfo) && fileLocationInfo.IndexOf("error") != 0)
				{
					string[] array = fileLocationInfo.Split(new char[]
					{
						'|'
					});
					text = array[0];
					text2 = array[1];
					bool flag = false;
					string text3 = text;
					try
					{
						pBeforeInitializeEvent(this.hContext, ref text, ref text2);
						if (!text3.Equals(text))
						{
							flag = true;
						}
					}
					catch
					{
					}
					try
					{
						UploadEventEntity uploadEventEntity = new UploadEventEntity();
						uploadEventEntity.Context = this.hContext;
						uploadEventEntity.NewFileLocation = text;
						uploadEventEntity.ResponseFileName = text2;
						pBeforeInitializeEventEx(uploadEventEntity);
						text = uploadEventEntity.NewFileLocation;
						text2 = uploadEventEntity.ResponseFileName;
						if (!text3.Equals(text))
						{
							flag = true;
						}
					}
					catch
					{
					}
					if (flag)
					{
						string[] array2 = base.InitializeEventFileExec(text, text2);
						text = array2[0];
						text2 = array2[1];
					}
					if (pCustomError == null)
					{
						using (FileStream fileStream = new FileStream(text, FileMode.Create, FileAccess.Write, FileShare.None))
						{
							fileStream.SetLength(num);
							fileStream.Close();
						}
						if (File.Exists(text))
						{
							FileInfo fileInfo = new FileInfo(text);
							if (fileInfo.Length == num)
							{
								base.FileLocationInfoReadWrite("W", guid, text);
								string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
								string path = string.Concat(new object[]
								{
									tempFileFolder,
									this.m_PathChar,
									guid,
									this.m_strHSTempSuffix
								});
								if (File.Exists(path))
								{
									base.ClientResponse("[OK]", "", "");
								}
								else
								{
									base.ClientResponse("[FAIL]", "error|777|Can't create a file(temp).", "");
								}
							}
							else
							{
								base.ClientResponse("[FAIL]", "error|777|Can't create a file(size).", "");
							}
						}
						else
						{
							base.ClientResponse("[FAIL]", "error|777|Can't create a file.", "");
						}
					}
					else
					{
						string responseMessage = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
						base.ClientResponse("[FAIL]", responseMessage, "");
					}
				}
			}
			catch (Exception ex)
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("Upload HS Error => " + ex.ToString(), this._str_DebugFilePath);
				}
				throw new Exception(ex.Message);
			}
		}

		// Token: 0x06000335 RID: 821 RVA: 0x000267E4 File Offset: 0x000249E4
		private void HS_WriteChunkedFile()
		{
			string guid = this._entity_dextParam.GUID;
			string fileName = this._entity_dextParam.fileName;
			long offset = Convert.ToInt64(this._entity_dextParam.kFileStartPos);
			if (string.IsNullOrEmpty(fileName))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			try
			{
				string text = string.Empty;
				string empty = string.Empty;
				text = base.FileLocationInfoReadWrite("R", guid, "");
				if (string.IsNullOrEmpty(text) || !File.Exists(text))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				}
				else
				{
					Path.GetFileName(text);
					if (this.hContext.Request.Files.Count == 0)
					{
						base.ClientResponse("[FAIL]", "error|027|Not exists File Stream", "");
					}
					else
					{
						using (FileStream fileStream = File.Open(text, FileMode.Open, FileAccess.Write, FileShare.ReadWrite))
						{
							fileStream.Seek(offset, SeekOrigin.Begin);
							byte[] array = new byte[131072];
							int count;
							while ((count = this.hContext.Request.Files[0].InputStream.Read(array, 0, array.Length)) > 0)
							{
								fileStream.Write(array, 0, count);
							}
							fileStream.Close();
						}
					}
				}
			}
			catch (Exception ex)
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("Upload HS Error => " + ex.ToString(), this._str_DebugFilePath);
				}
				throw new Exception(ex.Message);
			}
		}

		// Token: 0x06000336 RID: 822 RVA: 0x000269AC File Offset: 0x00024BAC
		private void HS_Fininshed(UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, ref Dext5CustomError pCustomError)
		{
			string guid = this._entity_dextParam.GUID;
			string fileName = this._entity_dextParam.fileName;
			if (string.IsNullOrEmpty(fileName))
			{
				base.ClientResponse("[FAIL]", "error|009|Invalid parameter", "");
				return;
			}
			try
			{
				string text = string.Empty;
				string text2 = string.Empty;
				text = base.FileLocationInfoReadWrite("R", guid, "");
				if (string.IsNullOrEmpty(text) || !File.Exists(text))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
				}
				else
				{
					text2 = Path.GetFileName(text);
					string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
					string.Concat(new object[]
					{
						tempFileFolder,
						this.m_PathChar,
						guid,
						this.m_strHSTempSuffix
					});
					Directory.Delete(tempFileFolder, true);
					string text3 = string.Empty;
					if (!string.IsNullOrEmpty(this.physicalPath))
					{
						text3 = text;
					}
					else if (!string.IsNullOrEmpty(this.virtualPath))
					{
						string oldValue = HostingEnvironment.MapPath("~/");
						text3 = "/" + text.Replace(oldValue, "");
						text3 = text3.Replace(this.m_PathChar, '/');
					}
					try
					{
						pCompleteBeforeEvent(this.hContext, ref text, ref text3, ref text2, ref this._str_ResponseCustomValue);
					}
					catch
					{
					}
					try
					{
						UploadEventEntity uploadEventEntity = new UploadEventEntity();
						uploadEventEntity.Context = this.hContext;
						uploadEventEntity.NewFileLocation = text;
						uploadEventEntity.ResponseFileServerPath = text3;
						uploadEventEntity.ResponseFileName = text2;
						uploadEventEntity.ResponseGroupId = this._entity_dextParam.fileGroupID;
						uploadEventEntity.FileIndex = this._entity_dextParam.fileIndex;
						pCompleteBeforeEventEx(uploadEventEntity);
						text = uploadEventEntity.NewFileLocation;
						text3 = uploadEventEntity.ResponseFileServerPath;
						text2 = uploadEventEntity.ResponseFileName;
						this._str_ResponseCustomValue = uploadEventEntity.ResponseCustomValue;
						this._str_ResponseGroupId = uploadEventEntity.ResponseGroupId;
					}
					catch
					{
					}
					string text4 = "";
					if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf('*') == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
					{
						text4 = text4 + "|" + this._str_ResponseCustomValue;
					}
					if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf('*') == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
					{
						text4 = text4 + "\b" + this._str_ResponseGroupId;
					}
					this.logMessage = text3 + "*" + text2 + text4;
					string s = Dext5Parameter.MakeParameter(text3 + "*" + text2 + text4);
					this.hContext.Response.Write(s);
					if (pCustomError != null)
					{
						string responseMessage = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
						base.ClientResponse("[FAIL]", responseMessage, "");
					}
					else if (pCustomError == null)
					{
						try
						{
							pCompleteEvent(this.hContext, text, text3, text2);
						}
						catch
						{
						}
						try
						{
							pCompleteEventEx(new UploadEventEntity
							{
								Context = this.hContext,
								NewFileLocation = text,
								ResponseFileServerPath = text3,
								ResponseFileName = text2
							});
						}
						catch
						{
						}
					}
				}
			}
			catch (Exception ex)
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("Upload HS Error => " + ex.ToString(), this._str_DebugFilePath);
				}
				throw new Exception(ex.Message);
			}
		}

		// Token: 0x040001C2 RID: 450
		private string logMessage = string.Empty;

		// Token: 0x040001C3 RID: 451
		private string physicalPath = string.Empty;

		// Token: 0x040001C4 RID: 452
		private string virtualPath = string.Empty;

		// Token: 0x040001C5 RID: 453
		private string fileWhiteList = string.Empty;

		// Token: 0x040001C6 RID: 454
		private string fileBlackList = string.Empty;

		// Token: 0x040001C7 RID: 455
		private string[] fileBlackWordList;

		// Token: 0x040001C8 RID: 456
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

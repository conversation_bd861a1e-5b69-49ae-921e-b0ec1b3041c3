﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using Ionic.Zip;
using Ionic.Zlib;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000006 RID: 6
	public class Download : Base
	{
		// Token: 0x06000037 RID: 55 RVA: 0x000091C8 File Offset: 0x000073C8
		public Download(HttpContext context, string pTempPath, string pZipFileName) : base(context)
		{
			this.tempPath = pTempPath;
			this.zipFileName = pZipFileName;
		}

		// Token: 0x06000038 RID: 56 RVA: 0x00009224 File Offset: 0x00007424
		public Download(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pDownloadRootPath, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.zipFileName = pZipFileName;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.downloadRootPath = pDownloadRootPath;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000039 RID: 57 RVA: 0x000092A8 File Offset: 0x000074A8
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			if (!string.IsNullOrEmpty(this._entity_dextParam.zipFileName))
			{
				this.ZipFileDownload();
				return null;
			}
			string fileVirtualPath = this._entity_dextParam.fileVirtualPath;
			string empty = string.Empty;
			string empty2 = string.Empty;
			if (string.IsNullOrEmpty(fileVirtualPath))
			{
				string text = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text += "<html><head>";
					text += "<script type=\"text/javascript\">";
					text += "if (window.postMessage) {";
					text += "if (window.addEventListener) {";
					text += "window.addEventListener('message', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "}, false);";
					text += "}";
					text += "else if (window.attachEvent) {";
					text += "window.attachEvent('onmessage', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "});";
					text += "}";
					text += "}";
					text += "</script>";
					text += "</head>";
					text += "<body>";
					text += "{0}";
					text += "</body>";
					text += "</html>";
				}
				else
				{
					text = "{0}";
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return "error|009|Invalid parameter on server";
			}
			string[] fileVirtualPathAry = this._entity_dextParam.fileVirtualPathAry;
			string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
			if (this._entity_dextParam.mode == "normal")
			{
				for (int i = 0; i < fileVirtualPathAry.Length; i++)
				{
					fileVirtualPathAry[i] = HttpUtility.UrlDecode(fileVirtualPathAry[i], Encoding.UTF8);
					fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
				}
			}
			List<string> list = new List<string>();
			List<string> list2 = new List<string>();
			string text2 = string.Empty;
			string empty3 = string.Empty;
			string str = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Authority;
			for (int j = 0; j < fileVirtualPathAry.Length; j++)
			{
				text2 = fileVirtualPathAry[j];
				if (!string.IsNullOrEmpty(this.downloadRootPath) && File.Exists(this.downloadRootPath + text2))
				{
					list.Add(this.downloadRootPath + text2);
					list2.Add(fileOrgNameAry[j]);
				}
				else if (File.Exists(text2))
				{
					list.Add(text2);
					list2.Add(fileOrgNameAry[j]);
				}
				else
				{
					string text3 = text2.Replace("http://", "");
					text3 = text3.Replace("https://", "");
					text3 = text3.Substring(text3.IndexOf("/"));
					if (!string.IsNullOrEmpty(this.downloadRootPath))
					{
						text3 = this.hContext.Request.MapPath(this.downloadRootPath + text3);
					}
					else
					{
						text3 = this.hContext.Request.MapPath(text3);
					}
					if (File.Exists(text3))
					{
						list.Add(text3);
						list2.Add(fileOrgNameAry[j]);
					}
					else
					{
						if (this._b_useExternalDownload)
						{
							if (text2.IndexOf("http://") == -1 && text2.IndexOf("https://") == -1)
							{
								text2 = str + text2.Substring(text2.IndexOf("/"));
							}
							text2 = base.CheckExternalWebFile(text2);
						}
						else
						{
							text2 = "";
						}
						if (string.IsNullOrEmpty(text2))
						{
							string text4 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text4 += "<html><head>";
								text4 += "<script type=\"text/javascript\">";
								text4 += "if (window.postMessage) {";
								text4 += "if (window.addEventListener) {";
								text4 += "window.addEventListener('message', function (e) {";
								text4 += "var sendUrl = e.origin;";
								text4 += "var data = document.body.innerHTML;";
								text4 += "e.source.postMessage(data, sendUrl);";
								text4 += "}, false);";
								text4 += "}";
								text4 += "else if (window.attachEvent) {";
								text4 += "window.attachEvent('onmessage', function (e) {";
								text4 += "var sendUrl = e.origin;";
								text4 += "var data = document.body.innerHTML;";
								text4 += "e.source.postMessage(data, sendUrl);";
								text4 += "});";
								text4 += "}";
								text4 += "}";
								text4 += "</script>";
								text4 += "</head>";
								text4 += "<body>";
								text4 += "{0}";
								text4 += "</body>";
								text4 += "</html>";
							}
							else
							{
								text4 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|010|Not found chunk file on server", this._str_DebugFilePath);
							}
							text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text4);
							return "error|010|Not found chunk file on server";
						}
						list.Add(text2);
						list2.Add(fileOrgNameAry[j]);
					}
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list2[j]))
				{
					string text5 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text5 += "<html><head>";
						text5 += "<script type=\"text/javascript\">";
						text5 += "if (window.postMessage) {";
						text5 += "if (window.addEventListener) {";
						text5 += "window.addEventListener('message', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "}, false);";
						text5 += "}";
						text5 += "else if (window.attachEvent) {";
						text5 += "window.attachEvent('onmessage', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "});";
						text5 += "}";
						text5 += "}";
						text5 += "</script>";
						text5 += "</head>";
						text5 += "<body>";
						text5 += "{0}";
						text5 += "</body>";
						text5 += "</html>";
					}
					else
					{
						text5 = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
					}
					text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text5);
					return "error|012|Not allowed file extension";
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, list2[j]))
				{
					string text6 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text6 += "<html><head>";
						text6 += "<script type=\"text/javascript\">";
						text6 += "if (window.postMessage) {";
						text6 += "if (window.addEventListener) {";
						text6 += "window.addEventListener('message', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "}, false);";
						text6 += "}";
						text6 += "else if (window.attachEvent) {";
						text6 += "window.attachEvent('onmessage', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "});";
						text6 += "}";
						text6 += "}";
						text6 += "</script>";
						text6 += "</head>";
						text6 += "<body>";
						text6 += "{0}";
						text6 += "</body>";
						text6 += "</html>";
					}
					else
					{
						text6 = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
					}
					text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text6);
					return "error|018|There does not allow the string included in file name";
				}
			}
			if (!string.IsNullOrEmpty(empty2))
			{
				return null;
			}
			if (list.Count <= 0)
			{
				string text7 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text7 += "<html><head>";
					text7 += "<script type=\"text/javascript\">";
					text7 += "if (window.postMessage) {";
					text7 += "if (window.addEventListener) {";
					text7 += "window.addEventListener('message', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "}, false);";
					text7 += "}";
					text7 += "else if (window.attachEvent) {";
					text7 += "window.attachEvent('onmessage', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "});";
					text7 += "}";
					text7 += "}";
					text7 += "</script>";
					text7 += "</head>";
					text7 += "<body>";
					text7 += "{0}";
					text7 += "</body>";
					text7 += "</html>";
				}
				else
				{
					text7 = "{0}";
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
				}
				text7 = text7.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text7);
				return "error|010|Not found file on server";
			}
			bool resumeMode = false;
			if (!string.IsNullOrEmpty(this._entity_dextParam.resumeMode) && this._entity_dextParam.resumeMode == "1")
			{
				resumeMode = true;
			}
			string[] array = new string[list.Count];
			string[] array2 = new string[list2.Count];
			for (int k = 0; k < list.Count; k++)
			{
				array[k] = list[k];
			}
			for (int l = 0; l < list2.Count; l++)
			{
				array2[l] = list2[l];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			bool bUseDownloadServerFileName = false;
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array;
				uploadEventEntity.DownloadFileName = array2;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				array = uploadEventEntity.DownloadFilePath;
				array2 = uploadEventEntity.DownloadFileName;
				for (int m = 0; m < array.Length; m++)
				{
					list[m] = array[m];
				}
				for (int n = 0; n < array2.Length; n++)
				{
					list2[n] = array2[n];
				}
				bUseDownloadServerFileName = uploadEventEntity.UseDownloadServerFileName;
			}
			catch
			{
			}
			if (pCustomError == null)
			{
				if (list.Count > 1)
				{
					string text8 = this.CompressionFiles(list, list2);
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomainZipFileName))
					{
						string[] array3 = text8.Split(new char[]
						{
							this.m_PathChar
						});
						string text9 = string.Empty;
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text9 += "<html><head>";
							text9 += "<script type=\"text/javascript\">";
							text9 += "if (window.postMessage) {";
							text9 += "if (window.addEventListener) {";
							text9 += "window.addEventListener('message', function (e) {";
							text9 += "var sendUrl = e.origin;";
							text9 += "var data = document.body.innerHTML;";
							text9 += "e.source.postMessage(data, sendUrl);";
							text9 += "}, false);";
							text9 += "}";
							text9 += "else if (window.attachEvent) {";
							text9 += "window.attachEvent('onmessage', function (e) {";
							text9 += "var sendUrl = e.origin;";
							text9 += "var data = document.body.innerHTML;";
							text9 += "e.source.postMessage(data, sendUrl);";
							text9 += "});";
							text9 += "}";
							text9 += "}";
							text9 += "</script>";
							text9 += "</head>";
							text9 += "<body>";
							text9 += "{0}";
							text9 += "</body>";
							text9 += "</html>";
						}
						else
						{
							text9 = "{0}";
						}
						text9 = text9.Replace("{0}", "[OK]" + Dext5Parameter.MakeParameter(array3[array3.Length - 1]));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text9);
						return null;
					}
					if (string.IsNullOrEmpty(this.zipFileName))
					{
						this.zipFileName = Path.GetFileName(text8);
					}
					this.SendFileToClient("attachment", text8, this.zipFileName, this.zipFileName, resumeMode, bUseDownloadServerFileName);
				}
				else
				{
					string text10 = list[0].ToLower();
					string text11 = string.Empty;
					if (text10.StartsWith("http"))
					{
						text11 = base.DownloadOutsideUrlFile(list[0], list2[0], "attachment", bUseDownloadServerFileName);
					}
					else
					{
						text11 = list[0];
					}
					if (!string.IsNullOrEmpty(text11))
					{
						string headerFileName = base.GetHeaderFileName(list2[0]);
						this.SendFileToClient("attachment", text11, list2[0], headerFileName, resumeMode, bUseDownloadServerFileName);
					}
				}
				for (int num = 0; num < list.Count; num++)
				{
					list[num] = list[num] + "\f" + list2[num];
				}
				return list;
			}
			string text12 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text12 += "<html><head>";
				text12 += "<script type=\"text/javascript\">";
				text12 += "if (window.postMessage) {";
				text12 += "if (window.addEventListener) {";
				text12 += "window.addEventListener('message', function (e) {";
				text12 += "var sendUrl = e.origin;";
				text12 += "var data = document.body.innerHTML;";
				text12 += "e.source.postMessage(data, sendUrl);";
				text12 += "}, false);";
				text12 += "}";
				text12 += "else if (window.attachEvent) {";
				text12 += "window.attachEvent('onmessage', function (e) {";
				text12 += "var sendUrl = e.origin;";
				text12 += "var data = document.body.innerHTML;";
				text12 += "e.source.postMessage(data, sendUrl);";
				text12 += "});";
				text12 += "}";
				text12 += "}";
				text12 += "</script>";
				text12 += "</head>";
				text12 += "<body>";
				text12 += "{0}";
				text12 += "</body>";
				text12 += "</html>";
			}
			else
			{
				text12 = "{0}";
			}
			string text13 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("[FAIL]" + text13, this._str_DebugFilePath);
			}
			text12 = text12.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text13));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text12);
			return text13;
		}

		// Token: 0x0600003A RID: 58 RVA: 0x0000A540 File Offset: 0x00008740
		protected void ZipFileDownload()
		{
			string arg = this._entity_dextParam.zipFileName;
			string text = this.tempPath + this.m_PathChar + arg;
			if (File.Exists(text))
			{
				bool resumeMode = false;
				if (!string.IsNullOrEmpty(this._entity_dextParam.resumeMode) && this._entity_dextParam.resumeMode == "1")
				{
					resumeMode = true;
				}
				string headerFileName = base.GetHeaderFileName(this.zipFileName);
				this.SendFileToClient("attachment", text, this.zipFileName, headerFileName, resumeMode, false);
				return;
			}
			string text2 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text2 += "<html><head>";
				text2 += "<script type=\"text/javascript\">";
				text2 += "if (window.postMessage) {";
				text2 += "if (window.addEventListener) {";
				text2 += "window.addEventListener('message', function (e) {";
				text2 += "var sendUrl = e.origin;";
				text2 += "var data = document.body.innerHTML;";
				text2 += "e.source.postMessage(data, sendUrl);";
				text2 += "}, false);";
				text2 += "}";
				text2 += "else if (window.attachEvent) {";
				text2 += "window.attachEvent('onmessage', function (e) {";
				text2 += "var sendUrl = e.origin;";
				text2 += "var data = document.body.innerHTML;";
				text2 += "e.source.postMessage(data, sendUrl);";
				text2 += "});";
				text2 += "}";
				text2 += "}";
				text2 += "</script>";
				text2 += "</head>";
				text2 += "<body>";
				text2 += "{0}";
				text2 += "</body>";
				text2 += "</html>";
			}
			else
			{
				text2 = "{0}";
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
			}
			text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text2);
		}

		// Token: 0x0600003B RID: 59 RVA: 0x0000A79C File Offset: 0x0000899C
		protected void SendFileToClient(string contentDispositionType, string filePath, string fileName, string headerFileName, bool resumeMode, bool bUseDownloadServerFileName)
		{
			string text = this.hContext.Request.Headers["X-Raon-Fde"];
			if (contentDispositionType == "attachment" && resumeMode)
			{
				if (!string.IsNullOrEmpty(text) && text == "securedAes")
				{
					string text2 = this.hContext.Request.Headers["X-Raon-Guid"];
					if (!string.IsNullOrEmpty(text2))
					{
						base.GetTempFileFolder(this.tempPath, text2);
					}
				}
				ResumeDownload.DownloadFile(this.hContext, filePath, fileName, headerFileName, this.tempPath, this._UploadServerVersion, bUseDownloadServerFileName, this.iBufferSize);
				return;
			}
			HttpResponse response = this.hContext.Response;
			response.Clear();
			response.BufferOutput = false;
			FileInfo fileInfo = new FileInfo(filePath);
			response.AddHeader("Content-Disposition", contentDispositionType + "; filename=\"" + headerFileName + "\"");
			if (bUseDownloadServerFileName)
			{
				response.AddHeader("X-Raon-FName", HttpUtility.UrlEncode(fileName).Replace("+", "%20"));
			}
			string mimeType = Dext5Mime.GetMimeType(headerFileName);
			response.ContentType = mimeType;
			if (contentDispositionType == "inline" && (mimeType == "text/plain" || mimeType == "text/html"))
			{
				Encoding textFileEncoding = this.GetTextFileEncoding(filePath);
				response.Charset = textFileEncoding.WebName;
			}
			string text3 = this.hContext.Request.Headers["X-Raon-Fdi"];
			if (!string.IsNullOrEmpty(text3) && text3 == "hmc-sha256")
			{
				string integrityHashValue = base.getIntegrityHashValue(filePath);
				response.AddHeader("X-Raon-Fdi", integrityHashValue);
			}
			long num = 0L;
			long num2 = -1L;
			byte[] array = new byte[this.iBufferSize];
			if (this.hContext.Request.Headers["Range"] != null)
			{
				string[] array2 = this.hContext.Request.Headers["Range"].Split(new char[]
				{
					'=',
					'-'
				});
				num = Convert.ToInt64(array2[1]);
				if (num < 0L || num >= fileInfo.Length)
				{
					num = 0L;
				}
				if (array2.Length == 3 && !string.IsNullOrEmpty(array2[2]))
				{
					num2 = Convert.ToInt64(array2[2]);
					if (num2 <= 0L || num2 >= fileInfo.Length)
					{
						if (num == 0L)
						{
							num2 = fileInfo.Length - 1L;
						}
						else
						{
							num2 = -1L;
						}
					}
				}
			}
			long num3;
			if (num2 == -1L)
			{
				num3 = fileInfo.Length - num;
			}
			else
			{
				num3 = num2 - num + 1L;
			}
			if (!string.IsNullOrEmpty(text) && text == "securedAes")
			{
				CryptoStream cryptoStream = null;
				FileStream fileStream = null;
				try
				{
					try
					{
						long num4 = (long)this._cipherRoanKeySize - fileInfo.Length % (long)this._cipherRoanKeySize;
						response.AddHeader("Content-Length", (num3 + num4).ToString());
						response.AddHeader("X-Raon-Cl", (num3 + num4).ToString());
						long num5 = num3 + num4;
						string s = this._UploadServerVersion.Substring(0, 11);
						byte[] array3 = new byte[this._cipherRoanKeySize];
						byte[] bytes = Encoding.UTF8.GetBytes(s);
						Array.Copy(bytes, array3, Math.Min(array3.Length, bytes.Length));
						ICryptoTransform transform = new RijndaelManaged
						{
							KeySize = 128,
							BlockSize = 128,
							Mode = CipherMode.CBC,
							Padding = PaddingMode.PKCS7,
							Key = array3,
							IV = array3
						}.CreateEncryptor();
						CryptoStream cryptoStream2;
						cryptoStream = (cryptoStream2 = new CryptoStream(response.OutputStream, transform, CryptoStreamMode.Write));
						try
						{
							FileStream fileStream2;
							fileStream = (fileStream2 = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
							try
							{
								if (this.hContext.Request.Headers["Range"] != null)
								{
									response.AppendHeader("Accept-Ranges", "bytes");
									response.StatusCode = 206;
									response.ContentType = "application/octet-stream";
									if (num2 == -1L)
									{
										response.AddHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num, fileStream.Length, fileStream.Length));
									}
									else
									{
										response.AddHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num, num2, fileStream.Length));
									}
								}
								if (num != 0L)
								{
									long num6 = fileStream.Seek(num, SeekOrigin.Begin);
									if (num6 == -1L)
									{
									}
								}
								if (num5 > 0L)
								{
									while (num5 > 0L)
									{
										if (num5 < (long)this.iBufferSize)
										{
											array = new byte[num5];
										}
										int num7 = fileStream.Read(array, 0, array.Length);
										if (num7 <= 0)
										{
											break;
										}
										cryptoStream.Write(array, 0, num7);
										num5 -= (long)num7;
									}
								}
								fileStream.Close();
							}
							finally
							{
								if (fileStream2 != null)
								{
									((IDisposable)fileStream2).Dispose();
								}
							}
							cryptoStream.FlushFinalBlock();
							cryptoStream.Close();
							response.Flush();
						}
						finally
						{
							if (cryptoStream2 != null)
							{
								((IDisposable)cryptoStream2).Dispose();
							}
						}
					}
					catch
					{
					}
					return;
				}
				finally
				{
					if (cryptoStream != null)
					{
						cryptoStream.Close();
					}
					if (fileStream != null)
					{
						fileStream.Close();
					}
				}
			}
			try
			{
				response.AddHeader("Content-Length", num3.ToString());
				response.AddHeader("X-Raon-Cl", num3.ToString());
				long num5 = num3;
				if (this.hContext.Request.Headers["Range"] != null)
				{
					response.AppendHeader("Accept-Ranges", "bytes");
					response.StatusCode = 206;
					response.ContentType = "application/octet-stream";
					if (num2 == -1L)
					{
						response.AddHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num, fileInfo.Length - 1L, fileInfo.Length));
					}
					else
					{
						response.AddHeader("Content-Range", string.Format("bytes {0}-{1}/{2}", num, num2, fileInfo.Length));
					}
				}
				BinaryReader binaryReader = null;
				BinaryWriter binaryWriter = null;
				try
				{
					binaryReader = new BinaryReader(File.OpenRead(filePath));
					binaryWriter = new BinaryWriter(response.OutputStream);
					if (num != 0L)
					{
						long num8 = binaryReader.BaseStream.Seek(num, SeekOrigin.Begin);
						if (num8 == -1L)
						{
						}
					}
					if (num5 > 0L)
					{
						while (num5 > 0L)
						{
							if (num5 < (long)this.iBufferSize)
							{
								array = new byte[num5];
							}
							int num7 = binaryReader.BaseStream.Read(array, 0, array.Length);
							if (num7 <= 0)
							{
								break;
							}
							binaryWriter.BaseStream.Write(array, 0, num7);
							num5 -= (long)num7;
						}
					}
				}
				catch
				{
				}
				finally
				{
					if (binaryWriter != null)
					{
						binaryWriter.BaseStream.Flush();
						binaryWriter.BaseStream.Close();
					}
					if (binaryReader != null)
					{
						binaryReader.BaseStream.Close();
					}
					response.Flush();
				}
			}
			catch
			{
			}
		}

		// Token: 0x0600003C RID: 60 RVA: 0x0000AF18 File Offset: 0x00009118
		protected void SendFileToClient(string contentDispositionType, Stream sFile, string sFileName, string headerFileName, bool resumeMode, bool bUseDownloadServerFileName)
		{
			HttpResponse response = this.hContext.Response;
			response.Clear();
			response.BufferOutput = false;
			response.AddHeader("Content-Disposition", contentDispositionType + "; filename=\"" + headerFileName + "\"");
			if (bUseDownloadServerFileName)
			{
				response.AddHeader("X-Raon-FName", HttpUtility.UrlEncode(sFileName).Replace("+", "%20"));
			}
			string mimeType = Dext5Mime.GetMimeType(headerFileName);
			response.ContentType = mimeType;
			byte[] array = new byte[this.iBufferSize];
			int count;
			while (response.IsClientConnected && (count = sFile.Read(array, 0, array.Length)) > 0)
			{
				response.OutputStream.Write(array, 0, count);
			}
			response.Flush();
		}

		// Token: 0x0600003D RID: 61 RVA: 0x0000AFC8 File Offset: 0x000091C8
		private long GetStartBytePosition()
		{
			Regex regex = new Regex("^bytes=(d+)-$", RegexOptions.IgnoreCase | RegexOptions.Compiled | RegexOptions.CultureInvariant);
			string text = this.hContext.Request.Headers["Range"];
			Match match;
			long result;
			if (string.IsNullOrEmpty(text) || !(match = regex.Match(text)).Success || !long.TryParse(match.Groups[0].Value, NumberStyles.Any, CultureInfo.InvariantCulture, out result))
			{
				return 0L;
			}
			return result;
		}

		// Token: 0x0600003E RID: 62 RVA: 0x0000B040 File Offset: 0x00009240
		protected string CompressionFiles(List<string> lPhysicalPath, List<string> lFileOrgName)
		{
			string text = base.GenerateUniqueKey();
			string empty = string.Empty;
			string text2 = string.Empty;
			for (;;)
			{
				text2 = string.Concat(new object[]
				{
					this.tempPath,
					this.m_PathChar,
					text,
					".zip"
				});
				if (!File.Exists(text2))
				{
					break;
				}
				text = base.GenerateUniqueKey();
			}
			int count = lFileOrgName.Count;
			string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
			for (int i = count - 1; i >= 0; i--)
			{
				for (int j = i - 1; j >= 0; j--)
				{
					if (lFileOrgName[i].ToLower() == lFileOrgName[j].ToLower())
					{
						lFileOrgName[i] = base.GenerateUniqueKey() + "_" + lFileOrgName[i];
					}
				}
			}
			using (ZipFile zipFile = new ZipFile())
			{
				zipFile.AlternateEncodingUsage = ZipOption.AsNecessary;
				zipFile.AlternateEncoding = Encoding.GetEncoding(949);
				zipFile.CompressionLevel = CompressionLevel.None;
				for (int k = 0; k < lPhysicalPath.Count; k++)
				{
					zipFile.AddFile(lPhysicalPath[k], "").FileName = lFileOrgName[k];
				}
				zipFile.Comment = "This zip was created from DEXT5 Upload.";
				zipFile.Save(text2);
			}
			return text2;
		}

		// Token: 0x0600003F RID: 63 RVA: 0x0000B1AC File Offset: 0x000093AC
		protected string CompressionFiles(List<Stream> lStream, List<string> lFileOrgName)
		{
			string text = base.GenerateUniqueKey();
			string empty = string.Empty;
			string text2 = string.Empty;
			for (;;)
			{
				text2 = string.Concat(new object[]
				{
					this.tempPath,
					this.m_PathChar,
					text,
					".zip"
				});
				if (!File.Exists(text2))
				{
					break;
				}
				text = base.GenerateUniqueKey();
			}
			int count = lFileOrgName.Count;
			string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
			for (int i = count - 1; i >= 0; i--)
			{
				for (int j = i - 1; j >= 0; j--)
				{
					if (lFileOrgName[i].ToLower() == lFileOrgName[j].ToLower())
					{
						lFileOrgName[i] = base.GenerateUniqueKey() + "_" + lFileOrgName[i];
					}
				}
			}
			using (ZipFile zipFile = new ZipFile())
			{
				zipFile.AlternateEncodingUsage = ZipOption.AsNecessary;
				zipFile.AlternateEncoding = Encoding.GetEncoding(949);
				zipFile.CompressionLevel = CompressionLevel.None;
				for (int k = 0; k < lStream.Count; k++)
				{
					zipFile.AddEntry(lFileOrgName[k], lStream[k]).FileName = lFileOrgName[k];
				}
				zipFile.Comment = "This zip was created from DEXT5 Upload.";
				zipFile.Save(text2);
			}
			return text2;
		}

		// Token: 0x06000040 RID: 64 RVA: 0x0000B31C File Offset: 0x0000951C
		private Encoding GetTextFileEncoding(string fileName)
		{
			Encoding result = Encoding.Default;
			try
			{
				Stream stream = File.OpenRead(fileName);
				byte[] array = new byte[4];
				stream.Read(array, 0, array.Length);
				if ((array[0] & 255) == 239 && (array[1] & 255) == 187 && (array[2] & 255) == 191)
				{
					result = Encoding.UTF8;
				}
				else if ((array[0] & 255) == 254 && (array[1] & 255) == 255)
				{
					result = Encoding.GetEncoding(1201);
				}
				else if ((array[0] & 255) == 255 && (array[1] & 255) == 254)
				{
					result = Encoding.GetEncoding(1200);
				}
				else if ((array[0] & 255) == 0 && (array[1] & 255) == 0 && (array[0] & 255) == 254 && (array[1] & 255) == 255)
				{
					result = Encoding.GetEncoding(12001);
				}
				else if ((array[0] & 255) == 255 && (array[1] & 255) == 254 && (array[0] & 255) == 0 && (array[1] & 255) == 0)
				{
					result = Encoding.GetEncoding(12000);
				}
				else
				{
					stream.Close();
					stream = File.OpenRead(fileName);
					if (!this.getIsUtf8(stream))
					{
						result = Encoding.Default;
					}
					else
					{
						result = Encoding.UTF8;
					}
				}
				if (stream != null)
				{
					stream.Close();
				}
			}
			catch
			{
			}
			return result;
		}

		// Token: 0x06000041 RID: 65 RVA: 0x0000B4C0 File Offset: 0x000096C0
		private bool getIsUtf8(Stream fs)
		{
			bool result = true;
			byte[] array = new byte[this.iBufferSize];
			while (fs.Read(array, 0, array.Length) > 0)
			{
				bool flag = true;
				int num = 0;
				for (int i = 0; i < array.Length; i++)
				{
					int num2 = (int)(array[i] & byte.MaxValue);
					if ((num2 & 128) != 0)
					{
						flag = false;
					}
					if (num == 0)
					{
						if (num2 >= 128)
						{
							do
							{
								num2 = (num2 << 1 & 255);
								num++;
							}
							while ((num2 & 128) != 0);
							num--;
							if (num == 0)
							{
								result = false;
								break;
							}
						}
					}
					else
					{
						if ((num2 & 192) != 128)
						{
							result = false;
							break;
						}
						num--;
					}
				}
				if (num > 0)
				{
					result = false;
				}
				if (flag)
				{
					result = false;
				}
			}
			return result;
		}

		// Token: 0x04000027 RID: 39
		protected const string RexRangeValuePattern = "^bytes=(d+)-$";

		// Token: 0x04000028 RID: 40
		protected string zipFileName = string.Empty;

		// Token: 0x04000029 RID: 41
		protected string fileWhiteList = string.Empty;

		// Token: 0x0400002A RID: 42
		protected string fileBlackList = string.Empty;

		// Token: 0x0400002B RID: 43
		protected string[] fileBlackWordList;

		// Token: 0x0400002C RID: 44
		protected string downloadRootPath = string.Empty;

		// Token: 0x0400002D RID: 45
		protected string allowExtensionSpecialSymbol = string.Empty;
	}
}

﻿using System;
using System.IO;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process;
using Raonwiz.Dext5.Process.Base64;
using Raonwiz.Dext5.Process.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5
{
	// Token: 0x0200000A RID: 10
	public class PhotoHandler : IDisposable
	{
		// Token: 0x14000001 RID: 1
		// (add) Token: 0x060000BC RID: 188 RVA: 0x0000DA28 File Offset: 0x0000BC28
		// (remove) Token: 0x060000BD RID: 189 RVA: 0x0000DA60 File Offset: 0x0000BC60
		public event UploadHandlerBeforeInitializeDelegate PhotoBeforeInitializeEvent;

		// Token: 0x14000002 RID: 2
		// (add) Token: 0x060000BE RID: 190 RVA: 0x0000DA98 File Offset: 0x0000BC98
		// (remove) Token: 0x060000BF RID: 191 RVA: 0x0000DAD0 File Offset: 0x0000BCD0
		public event UploadHandlerBeforeInitializeDelegateEx PhotoBeforeInitializeEventEx;

		// Token: 0x14000003 RID: 3
		// (add) Token: 0x060000C0 RID: 192 RVA: 0x0000DB08 File Offset: 0x0000BD08
		// (remove) Token: 0x060000C1 RID: 193 RVA: 0x0000DB40 File Offset: 0x0000BD40
		public event UploadHandlerBeforeCompleteDelegate PhotoCompleteBeforeEvent;

		// Token: 0x14000004 RID: 4
		// (add) Token: 0x060000C2 RID: 194 RVA: 0x0000DB78 File Offset: 0x0000BD78
		// (remove) Token: 0x060000C3 RID: 195 RVA: 0x0000DBB0 File Offset: 0x0000BDB0
		public event UploadHandlerBeforeCompleteDelegateEx PhotoCompleteBeforeEventEx;

		// Token: 0x14000005 RID: 5
		// (add) Token: 0x060000C4 RID: 196 RVA: 0x0000DBE8 File Offset: 0x0000BDE8
		// (remove) Token: 0x060000C5 RID: 197 RVA: 0x0000DC20 File Offset: 0x0000BE20
		public event UploadHandlerDelegate PhotoCompleteEvent;

		// Token: 0x14000006 RID: 6
		// (add) Token: 0x060000C6 RID: 198 RVA: 0x0000DC58 File Offset: 0x0000BE58
		// (remove) Token: 0x060000C7 RID: 199 RVA: 0x0000DC90 File Offset: 0x0000BE90
		public event UploadHandlerDelegateEx PhotoCompleteEventEx;

		// Token: 0x14000007 RID: 7
		// (add) Token: 0x060000C8 RID: 200 RVA: 0x0000DCC8 File Offset: 0x0000BEC8
		// (remove) Token: 0x060000C9 RID: 201 RVA: 0x0000DD00 File Offset: 0x0000BF00
		public event OpenDownloadBeforeInitializeDelegateEx OpenDownloadBeforeInitializeEventEx;

		// Token: 0x17000039 RID: 57
		// (set) Token: 0x060000CA RID: 202 RVA: 0x0000DD35 File Offset: 0x0000BF35
		public string SetLicenseKeyPath
		{
			set
			{
				this._str_licenseKeyPath = value;
			}
		}

		// Token: 0x1700003A RID: 58
		// (set) Token: 0x060000CB RID: 203 RVA: 0x0000DD3E File Offset: 0x0000BF3E
		public string SetTempPath
		{
			set
			{
				this._str_tempPath = value;
			}
		}

		// Token: 0x1700003B RID: 59
		// (set) Token: 0x060000CC RID: 204 RVA: 0x0000DD47 File Offset: 0x0000BF47
		public string SetConfigPhysicalPath
		{
			set
			{
				this._str_ConfigPhysicalPath = value;
			}
		}

		// Token: 0x1700003C RID: 60
		// (set) Token: 0x060000CD RID: 205 RVA: 0x0000DD50 File Offset: 0x0000BF50
		public string SetVirtualPath
		{
			set
			{
				this._str_virtualPath = value;
			}
		}

		// Token: 0x1700003D RID: 61
		// (set) Token: 0x060000CE RID: 206 RVA: 0x0000DD59 File Offset: 0x0000BF59
		public string SetPhysicalPath
		{
			set
			{
				this._str_physicalPath = value;
			}
		}

		// Token: 0x1700003E RID: 62
		// (set) Token: 0x060000CF RID: 207 RVA: 0x0000DD62 File Offset: 0x0000BF62
		public string SetFileBlackList
		{
			set
			{
				this._str_fileBlackList = value;
			}
		}

		// Token: 0x1700003F RID: 63
		// (set) Token: 0x060000D0 RID: 208 RVA: 0x0000DD6B File Offset: 0x0000BF6B
		public string SetFileWhiteList
		{
			set
			{
				this._str_fileWhiteList = value;
			}
		}

		// Token: 0x060000D1 RID: 209 RVA: 0x0000DD74 File Offset: 0x0000BF74
		public void SetCustomError(string errorMessage)
		{
			this._CustomError = new Dext5CustomError(errorMessage);
		}

		// Token: 0x060000D2 RID: 210 RVA: 0x0000DD82 File Offset: 0x0000BF82
		public void SetCustomError(string errorCode, string errorMessage)
		{
			this._CustomError = new Dext5CustomError(errorCode, errorMessage);
		}

		// Token: 0x060000D3 RID: 211 RVA: 0x0000DD91 File Offset: 0x0000BF91
		public void SetNetworkCredentials(string userID, string userPassword, string domain)
		{
			this._str_Credentials_UserID = userID;
			this._str_Credentials_UserPassword = userPassword;
			this._str_Credentials_Domain = domain;
		}

		// Token: 0x060000D4 RID: 212 RVA: 0x0000DDA8 File Offset: 0x0000BFA8
		public void SetDebugMode(bool isDebug, string debugFilePath)
		{
			this._b_IsDebug = isDebug;
			this._str_DebugFilePath = debugFilePath;
		}

		// Token: 0x060000D5 RID: 213 RVA: 0x0000DDB8 File Offset: 0x0000BFB8
		public void Process(HttpContext context)
		{
			ParamEntity dextParam = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, dextParam);
			}
			catch (Exception)
			{
				return;
			}
			if (context.Request["dext5CMD"] == null)
			{
				context.Response.Clear();
				context.Response.Write(Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
				return;
			}
			string text = context.Request["dext5CMD"];
			string a;
			if ((a = text) != null)
			{
				Base @base;
				if (!(a == "ir"))
				{
					if (!(a == "cr"))
					{
						if (!(a == "ub"))
						{
							goto IL_CC;
						}
						@base = new Upload(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					}
					else
					{
						@base = new ConfigXML(context, this._str_ConfigPhysicalPath);
					}
				}
				else
				{
					@base = new LicenseCheck(context);
				}
				@base.SetDextParam(dextParam);
				@base.SetDebugMode(this._b_IsDebug, this._str_DebugFilePath);
				@base.Run(this.PhotoBeforeInitializeEvent, this.PhotoBeforeInitializeEventEx, this.PhotoCompleteBeforeEvent, this.PhotoCompleteBeforeEventEx, this.PhotoCompleteEvent, this.PhotoCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
				@base.ImpersonatorDispose();
				return;
			}
			IL_CC:
			context.Response.Clear();
			context.Response.Write(Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
		}

		// Token: 0x060000D6 RID: 214 RVA: 0x0000DF3C File Offset: 0x0000C13C
		public string[] GetRequestValue(HttpContext context, string keyName)
		{
			string[] array = null;
			string value = context.Request[keyName];
			if (!string.IsNullOrEmpty(value))
			{
				array = context.Request.Form.GetValues(keyName);
				if (array == null)
				{
					array = context.Request.QueryString.GetValues(keyName);
				}
			}
			return array;
		}

		// Token: 0x060000D7 RID: 215 RVA: 0x0000DF88 File Offset: 0x0000C188
		public void Dispose()
		{
			this.Dispose(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x060000D8 RID: 216 RVA: 0x0000DF97 File Offset: 0x0000C197
		protected virtual void Dispose(bool disposing)
		{
			if (this.disposed)
			{
				return;
			}
			this.disposed = true;
		}

		// Token: 0x04000071 RID: 113
		private bool disposed;

		// Token: 0x04000072 RID: 114
		private char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x04000073 RID: 115
		private bool _b_IsDebug;

		// Token: 0x04000074 RID: 116
		private string _str_DebugFilePath = string.Empty;

		// Token: 0x04000075 RID: 117
		private string _str_licenseKeyPath = string.Empty;

		// Token: 0x04000076 RID: 118
		private string _str_tempPath = string.Empty;

		// Token: 0x04000077 RID: 119
		private string _str_virtualPath = string.Empty;

		// Token: 0x04000078 RID: 120
		private string _str_physicalPath = string.Empty;

		// Token: 0x04000079 RID: 121
		private string _str_ConfigPhysicalPath = string.Empty;

		// Token: 0x0400007A RID: 122
		private string _str_fileBlackList = string.Empty;

		// Token: 0x0400007B RID: 123
		private string _str_fileWhiteList = string.Empty;

		// Token: 0x0400007C RID: 124
		private string _str_Credentials_Method = "impersonate";

		// Token: 0x0400007D RID: 125
		private string _str_Credentials_UserID = string.Empty;

		// Token: 0x0400007E RID: 126
		private string _str_Credentials_UserPassword = string.Empty;

		// Token: 0x0400007F RID: 127
		private string _str_Credentials_Domain = string.Empty;

		// Token: 0x04000080 RID: 128
		private Dext5CustomError _CustomError;
	}
}

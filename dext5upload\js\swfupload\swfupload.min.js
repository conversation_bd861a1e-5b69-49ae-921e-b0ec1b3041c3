var SWFUpload,swfobject;void 0==SWFUpload&&(SWFUpload=function(a,b,c,g){this.initSWFUpload(a,b,c,g)});
SWFUpload.prototype.initSWFUpload=function(a,b,c,g){try{this.customSettings={},this.settings={},this.eventQueue=[],this.movieName="SWFUpload_"+b,this.movieIndex=SWFUpload.movieCount++,this.movieElement=null,SWFUpload.instances[this.movieName]=this,this.initSettings(a),this.loadSupport(),this.swfuploadPreload()&&this.loadFlash(c,g),this.displayDebugInfo()}catch(e){throw delete SWFUpload.instances[this.movieName],e;}};SWFUpload.instances={};SWFUpload.movieCount=0;SWFUpload.version="2.5.0 2010-01-15 Beta 2";
SWFUpload.QUEUE_ERROR={QUEUE_LIMIT_EXCEEDED:-100,FILE_EXCEEDS_SIZE_LIMIT:-110,ZERO_BYTE_FILE:-120,INVALID_FILETYPE:-130};SWFUpload.UPLOAD_ERROR={HTTP_ERROR:-200,MISSING_UPLOAD_URL:-210,IO_ERROR:-220,SECURITY_ERROR:-230,UPLOAD_LIMIT_EXCEEDED:-240,UPLOAD_FAILED:-250,SPECIFIED_FILE_ID_NOT_FOUND:-260,FILE_VALIDATION_FAILED:-270,FILE_CANCELLED:-280,UPLOAD_STOPPED:-290,RESIZE:-300};SWFUpload.FILE_STATUS={QUEUED:-1,IN_PROGRESS:-2,ERROR:-3,COMPLETE:-4,CANCELLED:-5};SWFUpload.UPLOAD_TYPE={NORMAL:-1,RESIZED:-2};
SWFUpload.BUTTON_ACTION={SELECT_FILE:-100,SELECT_FILES:-110,START_UPLOAD:-120,JAVASCRIPT:-130,NONE:-130};SWFUpload.CURSOR={ARROW:-2,HAND:-2};SWFUpload.WINDOW_MODE={WINDOW:"window",TRANSPARENT:"transparent",OPAQUE:"opaque"};SWFUpload.RESIZE_ENCODING={JPEG:-1,PNG:-2};
SWFUpload.completeURL=function(a){try{var b="",c=-1;if("string"!==typeof a||a.match(/^https?:\/\//i)||a.match(/^\//)||""===a)return a;c=window.location.pathname.lastIndexOf("/");b=0>=c?"/":window.location.pathname.substr(0,c)+"/";return b+a}catch(g){return a}};SWFUpload.onload=function(){};
SWFUpload.prototype.initSettings=function(a){this.ensureDefault=function(b,c){var g=a[b];this.settings[b]=void 0!=g?g:c};this.ensureDefault("upload_url","");this.ensureDefault("preserve_relative_urls",!1);this.ensureDefault("file_post_name","Filedata");this.ensureDefault("post_params",{});this.ensureDefault("use_query_string",!1);this.ensureDefault("requeue_on_error",!1);this.ensureDefault("http_success",[]);this.ensureDefault("assume_success_timeout",0);this.ensureDefault("file_types","*.*");this.ensureDefault("file_types_description",
"All Files");this.ensureDefault("file_size_limit",0);this.ensureDefault("file_upload_limit",0);this.ensureDefault("file_queue_limit",0);this.ensureDefault("flash_url","swfupload.swf");this.ensureDefault("flash9_url","swfupload_fp9.swf");this.ensureDefault("prevent_swf_caching",!0);this.ensureDefault("button_image_url","");this.ensureDefault("button_width",1);this.ensureDefault("button_height",1);this.ensureDefault("button_text","");this.ensureDefault("button_text_style","");this.ensureDefault("button_text_top_padding",
0);this.ensureDefault("button_text_left_padding",0);this.ensureDefault("button_action",SWFUpload.BUTTON_ACTION.SELECT_FILES);this.ensureDefault("button_disabled",!1);this.ensureDefault("button_placeholder_id","");this.ensureDefault("button_placeholder",null);this.ensureDefault("button_cursor",SWFUpload.CURSOR.ARROW);this.ensureDefault("button_window_mode",SWFUpload.WINDOW_MODE.TRANSPARENT);this.ensureDefault("debug",!1);this.settings.debug_enabled=this.settings.debug;this.settings.return_upload_start_handler=
this.returnUploadStart;this.ensureDefault("swfupload_preload_handler",null);this.ensureDefault("swfupload_load_failed_handler",null);this.ensureDefault("swfupload_loaded_handler",null);this.ensureDefault("file_dialog_start_handler",null);this.ensureDefault("file_queued_handler",null);this.ensureDefault("file_queue_error_handler",null);this.ensureDefault("file_dialog_complete_handler",null);this.ensureDefault("upload_resize_start_handler",null);this.ensureDefault("upload_start_handler",null);this.ensureDefault("upload_progress_handler",
null);this.ensureDefault("upload_error_handler",null);this.ensureDefault("upload_success_handler",null);this.ensureDefault("upload_complete_handler",null);this.ensureDefault("mouse_click_handler",null);this.ensureDefault("mouse_out_handler",null);this.ensureDefault("mouse_over_handler",null);this.ensureDefault("debug_handler",this.debugMessage);this.ensureDefault("custom_settings",{});this.customSettings=this.settings.custom_settings;this.settings.prevent_swf_caching&&(this.settings.flash_url=this.settings.flash_url+
(0>this.settings.flash_url.indexOf("?")?"?":"&")+"preventswfcaching="+(new Date).getTime(),this.settings.flash9_url=this.settings.flash9_url+(0>this.settings.flash9_url.indexOf("?")?"?":"&")+"preventswfcaching="+(new Date).getTime());this.settings.preserve_relative_urls||(this.settings.upload_url=SWFUpload.completeURL(this.settings.upload_url),this.settings.button_image_url=SWFUpload.completeURL(this.settings.button_image_url));delete this.ensureDefault};
SWFUpload.prototype.loadSupport=function(){this.support={loading:swfobject.hasFlashPlayerVersion("9.0.28"),imageResize:swfobject.hasFlashPlayerVersion("10.0.0")}};
SWFUpload.prototype.loadFlash=function(a,b){var c,g,e;if(this.support.loading)if(null!==document.getElementById(this.movieName))this.support.loading=!1,this.queueEvent("swfupload_load_failed_handler",["Element ID already in use"]);else if(c=document.getElementById(this.settings.button_placeholder_id)||this.settings.button_placeholder,void 0==c)this.support.loading=!1,this.queueEvent("swfupload_load_failed_handler",["button place holder not found"]);else{g="block"!==(c.currentStyle&&c.currentStyle.display||
window.getComputedStyle&&document.defaultView.getComputedStyle(c,null).getPropertyValue("display"))?"span":"div";g=document.createElement(g);e=this.getFlashHTML(a,b);try{g.innerHTML=e}catch(p){this.support.loading=!1;this.queueEvent("swfupload_load_failed_handler",["Exception loading Flash HTML into placeholder"]);return}e=g.getElementsByTagName("object");!e||1<e.length||0===e.length?(this.support.loading=!1,this.queueEvent("swfupload_load_failed_handler",["Unable to find movie after adding to DOM"])):
(1===e.length&&(this.movieElement=e[0]),c.parentNode.replaceChild(g.firstChild,c),void 0==window[this.movieName]&&(window[this.movieName]=this.getMovieElement()))}else this.queueEvent("swfupload_load_failed_handler",["Flash Player doesn't support SWFUpload"])};
SWFUpload.prototype.getFlashHTML=function(a,b,c){return['<object style="'+(""!=a||""!=b?a+";"+b:"")+'" id="',this.movieName,'" type="application/x-shockwave-flash" data="',this.support.imageResize?this.settings.flash_url:this.settings.flash9_url,'" width="',this.settings.button_width,'" height="',this.settings.button_height,'" class="swfupload"><param name="wmode" value="',this.settings.button_window_mode,'" /><param name="movie" value="',this.support.imageResize?this.settings.flash_url:this.settings.flash9_url,
'" /><param name="quality" value="high" /><param name="allowScriptAccess" value="always" />','<param name="flashvars" value="'+this.getFlashVars()+'" />',"</object>"].join("")};
SWFUpload.prototype.getFlashVars=function(){var a,b;b=this.buildParamString();a=this.settings.http_success.join(",");return["movieName=",encodeURIComponent(this.movieName),"&amp;uploadURL=",encodeURIComponent(this.settings.upload_url),"&amp;useQueryString=",encodeURIComponent(this.settings.use_query_string),"&amp;requeueOnError=",encodeURIComponent(this.settings.requeue_on_error),"&amp;httpSuccess=",encodeURIComponent(a),"&amp;assumeSuccessTimeout=",encodeURIComponent(this.settings.assume_success_timeout),
"&amp;params=",encodeURIComponent(b),"&amp;filePostName=",encodeURIComponent(this.settings.file_post_name),"&amp;fileTypes=",encodeURIComponent(this.settings.file_types),"&amp;fileTypesDescription=",encodeURIComponent(this.settings.file_types_description),"&amp;fileSizeLimit=",encodeURIComponent(this.settings.file_size_limit),"&amp;fileUploadLimit=",encodeURIComponent(this.settings.file_upload_limit),"&amp;fileQueueLimit=",encodeURIComponent(this.settings.file_queue_limit),"&amp;debugEnabled=",encodeURIComponent(this.settings.debug_enabled),
"&amp;buttonImageURL=",encodeURIComponent(this.settings.button_image_url),"&amp;buttonWidth=",encodeURIComponent(this.settings.button_width),"&amp;buttonHeight=",encodeURIComponent(this.settings.button_height),"&amp;buttonText=",encodeURIComponent(this.settings.button_text),"&amp;buttonTextTopPadding=",encodeURIComponent(this.settings.button_text_top_padding),"&amp;buttonTextLeftPadding=",encodeURIComponent(this.settings.button_text_left_padding),"&amp;buttonTextStyle=",encodeURIComponent(this.settings.button_text_style),
"&amp;buttonAction=",encodeURIComponent(this.settings.button_action),"&amp;buttonDisabled=",encodeURIComponent(this.settings.button_disabled),"&amp;buttonCursor=",encodeURIComponent(this.settings.button_cursor)].join("")};SWFUpload.prototype.getMovieElement=function(){void 0==this.movieElement&&(this.movieElement=document.getElementById(this.movieName));if(null===this.movieElement)throw"Could not find Flash element";return this.movieElement};
SWFUpload.prototype.buildParamString=function(){var a,b,c=[];b=this.settings.post_params;if("object"===typeof b)for(a in b)b.hasOwnProperty(a)&&c.push(encodeURIComponent(a.toString())+"="+encodeURIComponent(b[a].toString()));return c.join("&amp;")};
SWFUpload.prototype.destroy=function(){var a;try{this.cancelUpload(null,!1);if(a=this.cleanUp())try{a.parentNode.removeChild(a)}catch(b){}window[this.movieName]=null;SWFUpload.instances[this.movieName]=null;delete SWFUpload.instances[this.movieName];this.movieIndex=this.movieName=this.eventQueue=this.customSettings=this.settings=this.movieElement=null;return!0}catch(c){return!1}};
SWFUpload.prototype.displayDebugInfo=function(){this.debug(["---SWFUpload Instance Info---\nVersion: ",SWFUpload.version,"\nMovie Name: ",this.movieName,"\nSettings:\n\tupload_url:               ",this.settings.upload_url,"\n\tflash_url:                ",this.settings.flash_url,"\n\tflash9_url:                ",this.settings.flash9_url,"\n\tuse_query_string:         ",this.settings.use_query_string.toString(),"\n\trequeue_on_error:         ",this.settings.requeue_on_error.toString(),"\n\thttp_success:             ",
this.settings.http_success.join(", "),"\n\tassume_success_timeout:   ",this.settings.assume_success_timeout,"\n\tfile_post_name:           ",this.settings.file_post_name,"\n\tpost_params:              ",this.settings.post_params.toString(),"\n\tfile_types:               ",this.settings.file_types,"\n\tfile_types_description:   ",this.settings.file_types_description,"\n\tfile_size_limit:          ",this.settings.file_size_limit,"\n\tfile_upload_limit:        ",this.settings.file_upload_limit,"\n\tfile_queue_limit:         ",
this.settings.file_queue_limit,"\n\tdebug:                    ",this.settings.debug.toString(),"\n\tprevent_swf_caching:      ",this.settings.prevent_swf_caching.toString(),"\n\tbutton_placeholder_id:    ",this.settings.button_placeholder_id.toString(),"\n\tbutton_placeholder:       ",this.settings.button_placeholder?"Set":"Not Set","\n\tbutton_image_url:         ",this.settings.button_image_url.toString(),"\n\tbutton_width:             ",this.settings.button_width.toString(),"\n\tbutton_height:            ",
this.settings.button_height.toString(),"\n\tbutton_text:              ",this.settings.button_text.toString(),"\n\tbutton_text_style:        ",this.settings.button_text_style.toString(),"\n\tbutton_text_top_padding:  ",this.settings.button_text_top_padding.toString(),"\n\tbutton_text_left_padding: ",this.settings.button_text_left_padding.toString(),"\n\tbutton_action:            ",this.settings.button_action.toString(),"\n\tbutton_cursor:            ",this.settings.button_cursor.toString(),"\n\tbutton_disabled:          ",
this.settings.button_disabled.toString(),"\n\tcustom_settings:          ",this.settings.custom_settings.toString(),"\nEvent Handlers:\n\tswfupload_preload_handler assigned:  ",("function"===typeof this.settings.swfupload_preload_handler).toString(),"\n\tswfupload_load_failed_handler assigned:  ",("function"===typeof this.settings.swfupload_load_failed_handler).toString(),"\n\tswfupload_loaded_handler assigned:  ",("function"===typeof this.settings.swfupload_loaded_handler).toString(),"\n\tmouse_click_handler assigned:       ",
("function"===typeof this.settings.mouse_click_handler).toString(),"\n\tmouse_over_handler assigned:        ",("function"===typeof this.settings.mouse_over_handler).toString(),"\n\tmouse_out_handler assigned:         ",("function"===typeof this.settings.mouse_out_handler).toString(),"\n\tfile_dialog_start_handler assigned: ",("function"===typeof this.settings.file_dialog_start_handler).toString(),"\n\tfile_queued_handler assigned:       ",("function"===typeof this.settings.file_queued_handler).toString(),
"\n\tfile_queue_error_handler assigned:  ",("function"===typeof this.settings.file_queue_error_handler).toString(),"\n\tupload_resize_start_handler assigned:      ",("function"===typeof this.settings.upload_resize_start_handler).toString(),"\n\tupload_start_handler assigned:      ",("function"===typeof this.settings.upload_start_handler).toString(),"\n\tupload_progress_handler assigned:   ",("function"===typeof this.settings.upload_progress_handler).toString(),"\n\tupload_error_handler assigned:      ",
("function"===typeof this.settings.upload_error_handler).toString(),"\n\tupload_success_handler assigned:    ",("function"===typeof this.settings.upload_success_handler).toString(),"\n\tupload_complete_handler assigned:   ",("function"===typeof this.settings.upload_complete_handler).toString(),"\n\tdebug_handler assigned:             ",("function"===typeof this.settings.debug_handler).toString(),"\nSupport:\n\tLoad:                     ",this.support.loading?"Yes":"No","\n\tImage Resize:             ",
this.support.imageResize?"Yes":"No","\n"].join(""))};SWFUpload.prototype.addSetting=function(a,b,c){return void 0==b?this.settings[a]=c:this.settings[a]=b};SWFUpload.prototype.getSetting=function(a){return void 0!=this.settings[a]?this.settings[a]:""};
SWFUpload.prototype.callFlash=function(a,b){var c,g,e;b=b||[];c=this.getMovieElement();try{void 0!=c?(e=c.CallFunction('<invoke name="'+a+'" returntype="javascript">'+__flash__argumentsToXML(b,0)+"</invoke>"),g=eval(e)):this.debug("Can't call flash because the movie wasn't found.")}catch(p){this.debug("Exception calling flash function '"+a+"': "+p.message)}void 0!=g&&"object"===typeof g.post&&(g=this.unescapeFilePostParams(g));return g};SWFUpload.prototype.selectFile=function(){this.callFlash("SelectFile")};
SWFUpload.prototype.selectFiles=function(){this.callFlash("SelectFiles")};SWFUpload.prototype.startUpload=function(a){this.callFlash("StartUpload",[a])};SWFUpload.prototype.startResizedUpload=function(a,b,c,g,e,p){this.callFlash("StartUpload",[a,{width:b,height:c,encoding:g,quality:e,allowEnlarging:p}])};SWFUpload.prototype.cancelUpload=function(a,b){!1!==b&&(b=!0);this.callFlash("CancelUpload",[a,b])};SWFUpload.prototype.stopUpload=function(){this.callFlash("StopUpload")};
SWFUpload.prototype.requeueUpload=function(a){return this.callFlash("RequeueUpload",[a])};SWFUpload.prototype.getStats=function(){return this.callFlash("GetStats")};SWFUpload.prototype.setStats=function(a){this.callFlash("SetStats",[a])};SWFUpload.prototype.getFile=function(a){return"number"===typeof a?this.callFlash("GetFileByIndex",[a]):this.callFlash("GetFile",[a])};
SWFUpload.prototype.getQueueFile=function(a){return"number"===typeof a?this.callFlash("GetFileByQueueIndex",[a]):this.callFlash("GetFile",[a])};SWFUpload.prototype.addFileParam=function(a,b,c){return this.callFlash("AddFileParam",[a,b,c])};SWFUpload.prototype.removeFileParam=function(a,b){this.callFlash("RemoveFileParam",[a,b])};SWFUpload.prototype.setUploadURL=function(a){this.settings.upload_url=a.toString();this.callFlash("SetUploadURL",[a])};
SWFUpload.prototype.setPostParams=function(a){this.settings.post_params=a;this.callFlash("SetPostParams",[a])};SWFUpload.prototype.addPostParam=function(a,b){this.settings.post_params[a]=b;this.callFlash("SetPostParams",[this.settings.post_params])};SWFUpload.prototype.removePostParam=function(a){delete this.settings.post_params[a];this.callFlash("SetPostParams",[this.settings.post_params])};
SWFUpload.prototype.setFileTypes=function(a,b){this.settings.file_types=a;this.settings.file_types_description=b;this.callFlash("SetFileTypes",[a,b])};SWFUpload.prototype.setFileSizeLimit=function(a){this.settings.file_size_limit=a;this.callFlash("SetFileSizeLimit",[a])};SWFUpload.prototype.setFileUploadLimit=function(a){this.settings.file_upload_limit=a;this.callFlash("SetFileUploadLimit",[a])};
SWFUpload.prototype.setFileQueueLimit=function(a){this.settings.file_queue_limit=a;this.callFlash("SetFileQueueLimit",[a])};SWFUpload.prototype.setFilePostName=function(a){this.settings.file_post_name=a;this.callFlash("SetFilePostName",[a])};SWFUpload.prototype.setUseQueryString=function(a){this.settings.use_query_string=a;this.callFlash("SetUseQueryString",[a])};SWFUpload.prototype.setRequeueOnError=function(a){this.settings.requeue_on_error=a;this.callFlash("SetRequeueOnError",[a])};
SWFUpload.prototype.setHTTPSuccess=function(a){"string"===typeof a&&(a=a.replace(" ","").split(","));this.settings.http_success=a;this.callFlash("SetHTTPSuccess",[a])};SWFUpload.prototype.setAssumeSuccessTimeout=function(a){this.settings.assume_success_timeout=a;this.callFlash("SetAssumeSuccessTimeout",[a])};SWFUpload.prototype.setDebugEnabled=function(a){this.settings.debug_enabled=a;this.callFlash("SetDebugEnabled",[a])};
SWFUpload.prototype.setButtonImageURL=function(a){void 0==a&&(a="");this.settings.button_image_url=a;this.callFlash("SetButtonImageURL",[a])};SWFUpload.prototype.setButtonDimensions=function(a,b){this.settings.button_width=a;this.settings.button_height=b;var c=this.getMovieElement();void 0!=c&&(c.style.width=a+"px",c.style.height=b+"px");this.callFlash("SetButtonDimensions",[a,b])};SWFUpload.prototype.setButtonText=function(a){this.settings.button_text=a;this.callFlash("SetButtonText",[a])};
SWFUpload.prototype.setButtonTextPadding=function(a,b){this.settings.button_text_top_padding=b;this.settings.button_text_left_padding=a;this.callFlash("SetButtonTextPadding",[a,b])};SWFUpload.prototype.setButtonTextStyle=function(a){this.settings.button_text_style=a;this.callFlash("SetButtonTextStyle",[a])};SWFUpload.prototype.setButtonDisabled=function(a){this.settings.button_disabled=a;this.callFlash("SetButtonDisabled",[a])};
SWFUpload.prototype.setButtonAction=function(a){this.settings.button_action=a;this.callFlash("SetButtonAction",[a])};SWFUpload.prototype.setButtonCursor=function(a){this.settings.button_cursor=a;this.callFlash("SetButtonCursor",[a])};
SWFUpload.prototype.queueEvent=function(a,b){var c=this;void 0==b?b=[]:b instanceof Array||(b=[b]);if("function"===typeof this.settings[a])this.eventQueue.push(function(){this.settings[a].apply(this,b)}),setTimeout(function(){c.executeNextEvent()},0);else if(null!==this.settings[a])throw"Event handler "+a+" is unknown or is not a function";};SWFUpload.prototype.executeNextEvent=function(){var a=this.eventQueue?this.eventQueue.shift():null;"function"===typeof a&&a.apply(this)};
SWFUpload.prototype.unescapeFilePostParams=function(a){var b=/[$]([0-9a-f]{4})/i,c={},g,e,p;if(void 0!=a){for(e in a.post)if(a.post.hasOwnProperty(e)){for(g=e;null!==(p=b.exec(g));)g=g.replace(p[0],String.fromCharCode(parseInt("0x"+p[1],16)));c[g]=a.post[e]}a.post=c}return a};
SWFUpload.prototype.swfuploadPreload=function(){var a;if("function"===typeof this.settings.swfupload_preload_handler)a=this.settings.swfupload_preload_handler.call(this);else if(void 0!=this.settings.swfupload_preload_handler)throw"upload_start_handler must be a function";void 0===a&&(a=!0);return!!a};SWFUpload.prototype.flashReady=function(){this.cleanUp()?this.queueEvent("swfupload_loaded_handler"):this.debug("Flash called back ready but the flash movie can't be found.")};
SWFUpload.prototype.cleanUp=function(){var a,b=this.getMovieElement();try{if(b&&"unknown"===typeof b.CallFunction)for(a in this.debug("Removing Flash functions hooks (this should only run in IE and should prevent memory leaks)"),b)try{"function"===typeof b[a]&&(b[a]=null)}catch(c){}}catch(g){}window.__flash__removeCallback=function(a,b){try{a&&(a[b]=null)}catch(c){}};return b};SWFUpload.prototype.mouseClick=function(){this.queueEvent("mouse_click_handler")};SWFUpload.prototype.mouseOver=function(){this.queueEvent("mouse_over_handler")};
SWFUpload.prototype.mouseOut=function(){this.queueEvent("mouse_out_handler")};SWFUpload.prototype.fileDialogStart=function(){this.queueEvent("file_dialog_start_handler")};SWFUpload.prototype.fileQueued=function(a){a=this.unescapeFilePostParams(a);this.queueEvent("file_queued_handler",a)};SWFUpload.prototype.fileQueueError=function(a,b,c){a=this.unescapeFilePostParams(a);this.queueEvent("file_queue_error_handler",[a,b,c])};
SWFUpload.prototype.fileDialogComplete=function(a,b,c){this.queueEvent("file_dialog_complete_handler",[a,b,c])};SWFUpload.prototype.uploadResizeStart=function(a,b){a=this.unescapeFilePostParams(a);this.queueEvent("upload_resize_start_handler",[a,b.width,b.height,b.encoding,b.quality])};SWFUpload.prototype.uploadStart=function(a){a=this.unescapeFilePostParams(a);this.queueEvent("return_upload_start_handler",a)};
SWFUpload.prototype.returnUploadStart=function(a){var b;if("function"===typeof this.settings.upload_start_handler)a=this.unescapeFilePostParams(a),b=this.settings.upload_start_handler.call(this,a);else if(void 0!=this.settings.upload_start_handler)throw"upload_start_handler must be a function";void 0===b&&(b=!0);this.callFlash("ReturnUploadStart",[!!b])};SWFUpload.prototype.uploadProgress=function(a,b,c){a=this.unescapeFilePostParams(a);this.queueEvent("upload_progress_handler",[a,b,c])};
SWFUpload.prototype.uploadError=function(a,b,c){a=this.unescapeFilePostParams(a);this.queueEvent("upload_error_handler",[a,b,c])};SWFUpload.prototype.uploadSuccess=function(a,b,c){a=this.unescapeFilePostParams(a);this.queueEvent("upload_success_handler",[a,b,c])};SWFUpload.prototype.uploadComplete=function(a){a=this.unescapeFilePostParams(a);this.queueEvent("upload_complete_handler",a)};SWFUpload.prototype.debug=function(a){this.queueEvent("debug_handler",a)};
SWFUpload.prototype.debugMessage=function(a){var b,c;if(this.settings.debug){b=[];if("object"===typeof a&&"string"===typeof a.name&&"string"===typeof a.message){for(c in a)a.hasOwnProperty(c)&&b.push(c+": "+a[c]);a=b.join("\n")||"";b=a.split("\n");a="EXCEPTION: "+b.join("\nEXCEPTION: ")}SWFUpload.Console.writeLine(a)}};SWFUpload.Console={};
SWFUpload.Console.writeLine=function(a){var b,c;try{b=document.getElementById("SWFUpload_Console"),b||(c=document.createElement("form"),document.getElementsByTagName("body")[0].appendChild(c),b=document.createElement("textarea"),b.id="SWFUpload_Console",b.style.fontFamily="monospace",b.setAttribute("wrap","off"),b.wrap="off",b.style.overflow="auto",b.style.width="700px",b.style.height="350px",b.style.margin="5px",c.appendChild(b)),b.value+=a+"\n",b.scrollTop=b.scrollHeight-b.clientHeight}catch(g){alert("Exception: "+
g.name+" Message: "+g.message)}};
swfobject=function(){function a(){if(!w){try{var a=d.getElementsByTagName("body")[0].appendChild(d.createElement("span"));a.parentNode.removeChild(a)}catch(b){return}w=!0;for(var a=A.length,k=0;k<a;k++)A[k]()}}function b(a){w?a():A[A.length]=a}function c(a){if("undefined"!=typeof n.addEventListener)n.addEventListener("load",a,!1);else if("undefined"!=typeof d.addEventListener)d.addEventListener("load",a,!1);else if("undefined"!=typeof n.attachEvent)R(n,"onload",a);else if("function"==typeof n.onload){var b=
n.onload;n.onload=function(){b();a()}}else n.onload=a}function g(){var a=d.getElementsByTagName("body")[0],b=d.createElement("object");b.setAttribute("type","application/x-shockwave-flash");var k=a.appendChild(b);if(k){var c=0;(function(){if("undefined"!=typeof k.GetVariable){var h=k.GetVariable("$version");h&&(h=h.split(" ")[1].split(","),f.pv=[parseInt(h[0],10),parseInt(h[1],10),parseInt(h[2],10)])}else if(10>c){c++;setTimeout(arguments.callee,10);return}a.removeChild(b);k=null;e()})()}else e()}
function e(){var a=u.length;if(0<a)for(var b=0;b<a;b++){var k=u[b].id,c=u[b].callbackFn,h={success:!1,id:k};if(0<f.pv[0]){var d=q(k);if(d)if(!B(u[b].swfVersion)||f.wk&&312>f.wk)if(u[b].expressInstall&&F()){h={};h.data=u[b].expressInstall;h.width=d.getAttribute("width")||"0";h.height=d.getAttribute("height")||"0";d.getAttribute("class")&&(h.styleclass=d.getAttribute("class"));d.getAttribute("align")&&(h.align=d.getAttribute("align"));for(var l={},d=d.getElementsByTagName("param"),g=d.length,e=0;e<
g;e++)"movie"!=d[e].getAttribute("name").toLowerCase()&&(l[d[e].getAttribute("name")]=d[e].getAttribute("value"));G(h,l,k,c)}else S(d),c&&c(h);else x(k,!0),c&&(h.success=!0,h.ref=p(k),c(h))}else x(k,!0),c&&((k=p(k))&&"undefined"!=typeof k.SetVariable&&(h.success=!0,h.ref=k),c(h))}}function p(a){var b=null;(a=q(a))&&"OBJECT"==a.nodeName&&("undefined"!=typeof a.SetVariable?b=a:(a=a.getElementsByTagName("object")[0])&&(b=a));return b}function F(){return!C&&B("6.0.65")&&(f.win||f.mac)&&!(f.wk&&312>f.wk)}
function G(a,b,k,c){C=!0;H=c||null;L={success:!1,id:k};var h=q(k);if(h){"OBJECT"==h.nodeName?(z=I(h),D=null):(z=h,D=k);a.id="SWFObjectExprInst";if("undefined"==typeof a.width||!/%$/.test(a.width)&&310>parseInt(a.width,10))a.width="310";if("undefined"==typeof a.height||!/%$/.test(a.height)&&137>parseInt(a.height,10))a.height="137";d.title=d.title.slice(0,47)+" - Flash Player Installation";c=f.ie&&f.win?"ActiveX":"PlugIn";c="MMredirectURL="+n.location.toString().replace(/&/g,"%26")+"&MMplayerType="+
c+"&MMdoctitle="+d.title;b.flashvars="undefined"!=typeof b.flashvars?b.flashvars+("&"+c):c;f.ie&&f.win&&4!=h.readyState&&(c=d.createElement("div"),k+="SWFObjectNew",c.setAttribute("id",k),h.parentNode.insertBefore(c,h),h.style.display="none",function(){4==h.readyState?h.parentNode.removeChild(h):setTimeout(arguments.callee,10)}());J(a,b,k)}}function S(a){if(f.ie&&f.win&&4!=a.readyState){var b=d.createElement("div");a.parentNode.insertBefore(b,a);b.parentNode.replaceChild(I(a),b);a.style.display="none";
(function(){4==a.readyState?a.parentNode.removeChild(a):setTimeout(arguments.callee,10)})()}else a.parentNode.replaceChild(I(a),a)}function I(a){var b=d.createElement("div");if(f.win&&f.ie)b.innerHTML=a.innerHTML;else if(a=a.getElementsByTagName("object")[0])if(a=a.childNodes)for(var k=a.length,c=0;c<k;c++)1==a[c].nodeType&&"PARAM"==a[c].nodeName||8==a[c].nodeType||b.appendChild(a[c].cloneNode(!0));return b}function J(a,b,c){var g,h=q(c);if(f.wk&&312>f.wk)return g;if(h)if("undefined"==typeof a.id&&
(a.id=c),f.ie&&f.win){var e="",l;for(l in a)a[l]!=Object.prototype[l]&&("data"==l.toLowerCase()?b.movie=a[l]:"styleclass"==l.toLowerCase()?e+=' class="'+a[l]+'"':"classid"!=l.toLowerCase()&&(e+=" "+l+'="'+a[l]+'"'));l="";for(var m in b)b[m]!=Object.prototype[m]&&(l+='<param name="'+m+'" value="'+b[m]+'" />');h.outerHTML='<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"'+e+">"+l+"</object>";E[E.length]=a.id;g=q(a.id)}else{m=d.createElement("object");m.setAttribute("type","application/x-shockwave-flash");
for(var r in a)a[r]!=Object.prototype[r]&&("styleclass"==r.toLowerCase()?m.setAttribute("class",a[r]):"classid"!=r.toLowerCase()&&m.setAttribute(r,a[r]));for(e in b)b[e]!=Object.prototype[e]&&"movie"!=e.toLowerCase()&&(a=m,l=e,r=b[e],c=d.createElement("param"),c.setAttribute("name",l),c.setAttribute("value",r),a.appendChild(c));h.parentNode.replaceChild(m,h);g=m}return g}function M(a){var b=q(a);b&&"OBJECT"==b.nodeName&&(f.ie&&f.win?(b.style.display="none",function(){if(4==b.readyState){var c=q(a);
if(c){for(var d in c)"function"==typeof c[d]&&(c[d]=null);c.parentNode.removeChild(c)}}else setTimeout(arguments.callee,10)}()):b.parentNode.removeChild(b))}function q(a){var b=null;try{b=d.getElementById(a)}catch(c){}return b}function R(a,b,c){a.attachEvent(b,c);y[y.length]=[a,b,c]}function B(a){var b=f.pv;a=a.split(".");a[0]=parseInt(a[0],10);a[1]=parseInt(a[1],10)||0;a[2]=parseInt(a[2],10)||0;return b[0]>a[0]||b[0]==a[0]&&b[1]>a[1]||b[0]==a[0]&&b[1]==a[1]&&b[2]>=a[2]?!0:!1}function N(a,b,c,g){if(!f.ie||
!f.mac){var h=d.getElementsByTagName("head")[0];h&&(c=c&&"string"==typeof c?c:"screen",g&&(K=t=null),t&&K==c||(g=d.createElement("style"),g.setAttribute("type","text/css"),g.setAttribute("media",c),t=h.appendChild(g),f.ie&&f.win&&"undefined"!=typeof d.styleSheets&&0<d.styleSheets.length&&(t=d.styleSheets[d.styleSheets.length-1]),K=c),f.ie&&f.win?t&&"object"==typeof t.addRule&&t.addRule(a,b):t&&"undefined"!=typeof d.createTextNode&&t.appendChild(d.createTextNode(a+" {"+b+"}")))}}function x(a,b){if(O){var c=
b?"visible":"hidden";w&&q(a)?q(a).style.visibility=c:N("#"+a,"visibility:"+c)}}function P(a){return null!=/[\\\"<>\.;]/.exec(a)&&"undefined"!=typeof encodeURIComponent?encodeURIComponent(a):a}var n=window,d=document,v=navigator,Q=!1,A=[function(){Q?g():e()}],u=[],E=[],y=[],z,D,H,L,w=!1,C=!1,t,K,O=!0,f=function(){var a="undefined"!=typeof d.getElementById&&"undefined"!=typeof d.getElementsByTagName&&"undefined"!=typeof d.createElement,b=v.userAgent.toLowerCase(),c=v.platform.toLowerCase(),f=c?/win/.test(c):
/win/.test(b),c=c?/mac/.test(c):/mac/.test(b),b=/webkit/.test(b)?parseFloat(b.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")):!1,h=!+"\v1",g=[0,0,0],e=null;if("undefined"!=typeof v.plugins&&"object"==typeof v.plugins["Shockwave Flash"])!(e=v.plugins["Shockwave Flash"].description)||"undefined"!=typeof v.mimeTypes&&v.mimeTypes["application/x-shockwave-flash"]&&!v.mimeTypes["application/x-shockwave-flash"].enabledPlugin||(Q=!0,h=!1,e=e.replace(/^.*\s+(\S+\s+\S+$)/,"$1"),g[0]=parseInt(e.replace(/^(.*)\..*$/,
"$1"),10),g[1]=parseInt(e.replace(/^.*\.(.*)\s.*$/,"$1"),10),g[2]=/[a-zA-Z]/.test(e)?parseInt(e.replace(/^.*[a-zA-Z]+(.*)$/,"$1"),10):0);else if("undefined"!=typeof n.ActiveXObject)try{var m=new ActiveXObject("ShockwaveFlash.ShockwaveFlash");m&&(e=m.GetVariable("$version"))&&(h=!0,e=e.split(" ")[1].split(","),g=[parseInt(e[0],10),parseInt(e[1],10),parseInt(e[2],10)])}catch(r){}return{w3:a,pv:g,wk:b,ie:h,win:f,mac:c}}();(function(){f.w3&&(("undefined"!=typeof d.readyState&&"complete"==d.readyState||
"undefined"==typeof d.readyState&&(d.getElementsByTagName("body")[0]||d.body))&&a(),w||("undefined"!=typeof d.addEventListener&&d.addEventListener("DOMContentLoaded",a,!1),f.ie&&f.win&&(d.attachEvent("onreadystatechange",function(){"complete"==d.readyState&&(d.detachEvent("onreadystatechange",arguments.callee),a())}),n==top&&function(){if(!w){try{d.documentElement.doScroll("left")}catch(b){setTimeout(arguments.callee,0);return}a()}}()),f.wk&&function(){w||(/loaded|complete/.test(d.readyState)?a():
setTimeout(arguments.callee,0))}(),c(a)))})();(function(){f.ie&&f.win&&window.attachEvent("onunload",function(){for(var a=y.length,b=0;b<a;b++)y[b][0].detachEvent(y[b][1],y[b][2]);a=E.length;for(b=0;b<a;b++)M(E[b]);for(var c in f)f[c]=null;f=null;for(var d in swfobject)swfobject[d]=null;swfobject=null})})();return{registerObject:function(a,b,c,d){if(f.w3&&a&&b){var e={};e.id=a;e.swfVersion=b;e.expressInstall=c;e.callbackFn=d;u[u.length]=e;x(a,!1)}else d&&d({success:!1,id:a})},getObjectById:function(a){if(f.w3)return p(a)},
embedSWF:function(a,c,d,e,g,p,l,m,r,n){var q={success:!1,id:c};f.w3&&!(f.wk&&312>f.wk)&&a&&c&&d&&e&&g?(x(c,!1),b(function(){d+="";e+="";var b={};if(r&&"object"===typeof r)for(var f in r)b[f]=r[f];b.data=a;b.width=d;b.height=e;f={};if(m&&"object"===typeof m)for(var t in m)f[t]=m[t];if(l&&"object"===typeof l)for(var u in l)f.flashvars="undefined"!=typeof f.flashvars?f.flashvars+("&"+u+"="+l[u]):u+"="+l[u];if(B(g))t=J(b,f,c),b.id==c&&x(c,!0),q.success=!0,q.ref=t;else{if(p&&F()){b.data=p;G(b,f,c,n);return}x(c,
!0)}n&&n(q)})):n&&n(q)},switchOffAutoHideShow:function(){O=!1},ua:f,getFlashPlayerVersion:function(){return{major:f.pv[0],minor:f.pv[1],release:f.pv[2]}},hasFlashPlayerVersion:B,createSWF:function(a,b,c){if(f.w3)return J(a,b,c)},showExpressInstall:function(a,b,c,d){f.w3&&F()&&G(a,b,c,d)},removeSWF:function(a){f.w3&&M(a)},createCSS:function(a,b,c,d){f.w3&&N(a,b,c,d)},addDomLoadEvent:b,addLoadEvent:c,getQueryParamValue:function(a){var b=d.location.search||d.location.hash;if(b){/\?/.test(b)&&(b=b.split("?")[1]);
if(null==a)return P(b);for(var b=b.split("&"),c=0;c<b.length;c++)if(b[c].substring(0,b[c].indexOf("="))==a)return P(b[c].substring(b[c].indexOf("=")+1))}return""},expressInstallCallback:function(){if(C){var a=q("SWFObjectExprInst");a&&z&&(a.parentNode.replaceChild(z,a),D&&(x(D,!0),f.ie&&f.win&&(z.style.display="block")),H&&H(L));C=!1}}}}();swfobject.addDomLoadEvent(function(){"function"===typeof SWFUpload.onload&&SWFUpload.onload.call(window)});

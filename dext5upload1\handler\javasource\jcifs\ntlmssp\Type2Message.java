package jcifs.ntlmssp;

import java.io.IOException;
import java.net.UnknownHostException;
import jcifs.Config;
import jcifs.netbios.NbtAddress;
import jcifs.smb.SmbConstants;
import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/ntlmssp/Type2Message.class */
public class Type2Message extends NtlmMessage {
    private static final int DEFAULT_FLAGS;
    private static final String DEFAULT_DOMAIN;
    private static final byte[] DEFAULT_TARGET_INFORMATION;
    private byte[] challenge;
    private String target;
    private byte[] context;
    private byte[] targetInformation;

    static {
        DEFAULT_FLAGS = 512 | (Config.getBoolean("jcifs.smb.client.useUnicode", true) ? 1 : 2);
        DEFAULT_DOMAIN = Config.getProperty("jcifs.smb.client.domain", null);
        byte[] domain = new byte[0];
        if (DEFAULT_DOMAIN != null) {
            try {
                domain = DEFAULT_DOMAIN.getBytes(SmbConstants.UNI_ENCODING);
            } catch (IOException e) {
            }
        }
        int domainLength = domain.length;
        byte[] server = new byte[0];
        try {
            String host = NbtAddress.getLocalHost().getHostName();
            if (host != null) {
                try {
                    server = host.getBytes(SmbConstants.UNI_ENCODING);
                } catch (IOException e2) {
                }
            }
        } catch (UnknownHostException e3) {
        }
        int serverLength = server.length;
        byte[] targetInfo = new byte[(domainLength > 0 ? domainLength + 4 : 0) + (serverLength > 0 ? serverLength + 4 : 0) + 4];
        int offset = 0;
        if (domainLength > 0) {
            writeUShort(targetInfo, 0, 2);
            int offset2 = 0 + 2;
            writeUShort(targetInfo, offset2, domainLength);
            int offset3 = offset2 + 2;
            System.arraycopy(domain, 0, targetInfo, offset3, domainLength);
            offset = offset3 + domainLength;
        }
        if (serverLength > 0) {
            writeUShort(targetInfo, offset, 1);
            int offset4 = offset + 2;
            writeUShort(targetInfo, offset4, serverLength);
            System.arraycopy(server, 0, targetInfo, offset4 + 2, serverLength);
        }
        DEFAULT_TARGET_INFORMATION = targetInfo;
    }

    public Type2Message() {
        this(getDefaultFlags(), (byte[]) null, (String) null);
    }

    public Type2Message(Type1Message type1) {
        this(type1, (byte[]) null, (String) null);
    }

    public Type2Message(Type1Message type1, byte[] challenge, String target) {
        this(getDefaultFlags(type1), challenge, (type1 != null && target == null && type1.getFlag(4)) ? getDefaultDomain() : target);
    }

    public Type2Message(int flags, byte[] challenge, String target) {
        setFlags(flags);
        setChallenge(challenge);
        setTarget(target);
        if (target != null) {
            setTargetInformation(getDefaultTargetInformation());
        }
    }

    public Type2Message(byte[] material) throws IOException {
        parse(material);
    }

    public byte[] getChallenge() {
        return this.challenge;
    }

    public void setChallenge(byte[] challenge) {
        this.challenge = challenge;
    }

    public String getTarget() {
        return this.target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public byte[] getTargetInformation() {
        return this.targetInformation;
    }

    public void setTargetInformation(byte[] targetInformation) {
        this.targetInformation = targetInformation;
    }

    public byte[] getContext() {
        return this.context;
    }

    public void setContext(byte[] context) {
        this.context = context;
    }

    @Override // jcifs.ntlmssp.NtlmMessage
    public byte[] toByteArray() {
        try {
            String targetName = getTarget();
            byte[] challenge = getChallenge();
            byte[] context = getContext();
            byte[] targetInformation = getTargetInformation();
            int flags = getFlags();
            byte[] target = new byte[0];
            if ((flags & 4) != 0) {
                if (targetName != null && targetName.length() != 0) {
                    target = (flags & 1) != 0 ? targetName.getBytes(SmbConstants.UNI_ENCODING) : targetName.toUpperCase().getBytes(getOEMEncoding());
                } else {
                    flags &= -5;
                }
            }
            if (targetInformation != null) {
                flags |= NtlmFlags.NTLMSSP_NEGOTIATE_TARGET_INFO;
                if (context == null) {
                    context = new byte[8];
                }
            }
            int data = 32;
            if (context != null) {
                data = 32 + 8;
            }
            if (targetInformation != null) {
                data += 8;
            }
            byte[] type2 = new byte[data + target.length + (targetInformation != null ? targetInformation.length : 0)];
            System.arraycopy(NTLMSSP_SIGNATURE, 0, type2, 0, 8);
            writeULong(type2, 8, 2);
            writeSecurityBuffer(type2, 12, data, target);
            writeULong(type2, 20, flags);
            System.arraycopy(challenge != null ? challenge : new byte[8], 0, type2, 24, 8);
            if (context != null) {
                System.arraycopy(context, 0, type2, 32, 8);
            }
            if (targetInformation != null) {
                writeSecurityBuffer(type2, 40, data + target.length, targetInformation);
            }
            return type2;
        } catch (IOException ex) {
            throw new IllegalStateException(ex.getMessage());
        }
    }

    public String toString() {
        String target = getTarget();
        byte[] challenge = getChallenge();
        byte[] context = getContext();
        byte[] targetInformation = getTargetInformation();
        return "Type2Message[target=" + target + ",challenge=" + (challenge == null ? "null" : "<" + challenge.length + " bytes>") + ",context=" + (context == null ? "null" : "<" + context.length + " bytes>") + ",targetInformation=" + (targetInformation == null ? "null" : "<" + targetInformation.length + " bytes>") + ",flags=0x" + Hexdump.toHexString(getFlags(), 8) + "]";
    }

    public static int getDefaultFlags() {
        return DEFAULT_FLAGS;
    }

    public static int getDefaultFlags(Type1Message type1) {
        if (type1 == null) {
            return DEFAULT_FLAGS;
        }
        int type1Flags = type1.getFlags();
        int flags = 512 | ((type1Flags & 1) != 0 ? 1 : 2);
        if ((type1Flags & 4) != 0) {
            String domain = getDefaultDomain();
            if (domain != null) {
                flags |= 65540;
            }
        }
        return flags;
    }

    public static String getDefaultDomain() {
        return DEFAULT_DOMAIN;
    }

    public static byte[] getDefaultTargetInformation() {
        return DEFAULT_TARGET_INFORMATION;
    }

    private void parse(byte[] material) throws IOException {
        for (int i = 0; i < 8; i++) {
            if (material[i] != NTLMSSP_SIGNATURE[i]) {
                throw new IOException("Not an NTLMSSP message.");
            }
        }
        if (readULong(material, 8) != 2) {
            throw new IOException("Not a Type 2 message.");
        }
        int flags = readULong(material, 20);
        setFlags(flags);
        String target = null;
        byte[] bytes = readSecurityBuffer(material, 12);
        if (bytes.length != 0) {
            target = new String(bytes, (flags & 1) != 0 ? SmbConstants.UNI_ENCODING : getOEMEncoding());
        }
        setTarget(target);
        int i2 = 24;
        while (true) {
            if (i2 >= 32) {
                break;
            }
            if (material[i2] == 0) {
                i2++;
            } else {
                byte[] challenge = new byte[8];
                System.arraycopy(material, 24, challenge, 0, 8);
                setChallenge(challenge);
                break;
            }
        }
        int offset = readULong(material, 16);
        if (offset == 32 || material.length == 32) {
            return;
        }
        int i3 = 32;
        while (true) {
            if (i3 >= 40) {
                break;
            }
            if (material[i3] == 0) {
                i3++;
            } else {
                byte[] context = new byte[8];
                System.arraycopy(material, 32, context, 0, 8);
                setContext(context);
                break;
            }
        }
        if (offset == 40 || material.length == 40) {
            return;
        }
        byte[] bytes2 = readSecurityBuffer(material, 40);
        if (bytes2.length != 0) {
            setTargetInformation(bytes2);
        }
    }
}

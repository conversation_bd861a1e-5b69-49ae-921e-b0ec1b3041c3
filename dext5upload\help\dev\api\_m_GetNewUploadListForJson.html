﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: GetNewUploadListForJson</h3>
    <p class="ttl">object GetNewUploadListForJson(uploadID)</p>
    <p class="txt">
        전송완료 후 새롭게 업로드 된 파일 리스트를 json 형태로 리턴합니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
       <span class="firebrick">object</span>&nbsp;&nbsp;전송완료 후 새롭게 업로드 된 파일 리스트를 의미합니다.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;파일 리스트를 가져올 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function DEXT5UPLOAD_OnTransfer_Complete(uploadID) {
            var file_list = DEXT5UPLOAD.GetNewUploadListForJson("upload1");
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


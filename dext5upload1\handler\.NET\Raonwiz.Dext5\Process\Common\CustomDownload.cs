﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x0200001D RID: 29
	public class CustomDownload : Download
	{
		// Token: 0x06000228 RID: 552 RVA: 0x00016820 File Offset: 0x00014A20
		public CustomDownload(HttpContext context, string pTempPath, string pZipFileName, string pWorkType, List<string> pDownloadList) : base(context, pTempPath, pZipFileName)
		{
			this.workType = pWorkType;
			this.strDownloadList = pDownloadList;
		}

		// Token: 0x06000229 RID: 553 RVA: 0x00016846 File Offset: 0x00014A46
		public CustomDownload(HttpContext context, string pTempPath, string pZipFileName, string pWorkType, List<Stream> pDownloadList) : base(context, pTempPath, pZipFileName)
		{
			this.workType = pWorkType;
			this.sDownloadList = pDownloadList;
		}

		// Token: 0x0600022A RID: 554 RVA: 0x0001686C File Offset: 0x00014A6C
		public CustomDownload(HttpContext context, string pTempPath, string pZipFileName, string pWorkType, List<string> pDownloadList, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, "", pAllowExtensionSpecialSymbol)
		{
			this.workType = pWorkType;
			this.strDownloadList = pDownloadList;
		}

		// Token: 0x0600022B RID: 555 RVA: 0x000168AC File Offset: 0x00014AAC
		public CustomDownload(HttpContext context, string pTempPath, string pZipFileName, string pWorkType, List<Stream> pDownloadList, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, "", pAllowExtensionSpecialSymbol)
		{
			this.workType = pWorkType;
			this.sDownloadList = pDownloadList;
		}

		// Token: 0x0600022C RID: 556 RVA: 0x000168EC File Offset: 0x00014AEC
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			if (this.workType == "ProcessCustomDownload")
			{
				return this.ProcessCustomDownload(pOpenDownloadBeforeInitializeEventEx, ref pCustomError);
			}
			if (this.workType == "ProcessCustomDownloadPlugin")
			{
				return this.ProcessCustomDownloadPlugin(pOpenDownloadBeforeInitializeEventEx, ref pCustomError);
			}
			return null;
		}

		// Token: 0x0600022D RID: 557 RVA: 0x0001694C File Offset: 0x00014B4C
		public object ProcessCustomDownload(OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			if (!string.IsNullOrEmpty(this._entity_dextParam.zipFileName))
			{
				base.ZipFileDownload();
				return null;
			}
			string fileOrgName = this._entity_dextParam.fileOrgName;
			string empty = string.Empty;
			if (this.sDownloadList == null && (this.strDownloadList == null || this.strDownloadList.Count <= 0))
			{
				string text = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text += "<html><head>";
					text += "<script type=\"text/javascript\">";
					text += "if (window.postMessage) {";
					text += "if (window.addEventListener) {";
					text += "window.addEventListener('message', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "}, false);";
					text += "}";
					text += "else if (window.attachEvent) {";
					text += "window.attachEvent('onmessage', function (e) {";
					text += "var sendUrl = e.origin;";
					text += "var data = document.body.innerHTML;";
					text += "e.source.postMessage(data, sendUrl);";
					text += "});";
					text += "}";
					text += "}";
					text += "</script>";
					text += "</head>";
					text += "<body>";
					text += "{0}";
					text += "</body>";
					text += "</html>";
				}
				else
				{
					text = "{0}";
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text);
				return "error|009|Invalid parameter on server";
			}
			string[] array = this._entity_dextParam.fileOrgNameAry;
			if (!string.IsNullOrEmpty(this._entity_dextParam.dext5CMD) || array == null)
			{
				string dext5CMD = this._entity_dextParam.dext5CMD;
				if (array == null)
				{
					array = new string[]
					{
						this._entity_dextParam.fileOrgName
					};
				}
			}
			List<string> list = new List<string>();
			List<Stream> list2 = new List<Stream>();
			List<string> list3 = new List<string>();
			List<string> list4 = new List<string>();
			if (this._entity_dextParam.mode == "normal")
			{
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = HttpUtility.UrlDecode(array[i], Encoding.UTF8);
				}
			}
			string empty2 = string.Empty;
			if (this.sDownloadList != null)
			{
				for (int j = 0; j < this.sDownloadList.Count; j++)
				{
					list2.Add(this.sDownloadList[j]);
					list3.Add(array[j]);
					list4.Add("");
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list3[j]))
					{
						string text2 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text2 += "<html><head>";
							text2 += "<script type=\"text/javascript\">";
							text2 += "if (window.postMessage) {";
							text2 += "if (window.addEventListener) {";
							text2 += "window.addEventListener('message', function (e) {";
							text2 += "var sendUrl = e.origin;";
							text2 += "var data = document.body.innerHTML;";
							text2 += "e.source.postMessage(data, sendUrl);";
							text2 += "}, false);";
							text2 += "}";
							text2 += "else if (window.attachEvent) {";
							text2 += "window.attachEvent('onmessage', function (e) {";
							text2 += "var sendUrl = e.origin;";
							text2 += "var data = document.body.innerHTML;";
							text2 += "e.source.postMessage(data, sendUrl);";
							text2 += "});";
							text2 += "}";
							text2 += "}";
							text2 += "</script>";
							text2 += "</head>";
							text2 += "<body>";
							text2 += "{0}";
							text2 += "</body>";
							text2 += "</html>";
						}
						else
						{
							text2 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text2);
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list3[j]))
					{
						string text3 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text3 += "<html><head>";
							text3 += "<script type=\"text/javascript\">";
							text3 += "if (window.postMessage) {";
							text3 += "if (window.addEventListener) {";
							text3 += "window.addEventListener('message', function (e) {";
							text3 += "var sendUrl = e.origin;";
							text3 += "var data = document.body.innerHTML;";
							text3 += "e.source.postMessage(data, sendUrl);";
							text3 += "}, false);";
							text3 += "}";
							text3 += "else if (window.attachEvent) {";
							text3 += "window.attachEvent('onmessage', function (e) {";
							text3 += "var sendUrl = e.origin;";
							text3 += "var data = document.body.innerHTML;";
							text3 += "e.source.postMessage(data, sendUrl);";
							text3 += "});";
							text3 += "}";
							text3 += "}";
							text3 += "</script>";
							text3 += "</head>";
							text3 += "<body>";
							text3 += "{0}";
							text3 += "</body>";
							text3 += "</html>";
						}
						else
						{
							text3 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						text3 = text3.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text3);
						return "error|018|There does not allow the string included in file name";
					}
				}
			}
			else if (this.strDownloadList != null)
			{
				for (int k = 0; k < this.strDownloadList.Count; k++)
				{
					if (!File.Exists(this.strDownloadList[k]))
					{
						string text4 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text4 += "<html><head>";
							text4 += "<script type=\"text/javascript\">";
							text4 += "if (window.postMessage) {";
							text4 += "if (window.addEventListener) {";
							text4 += "window.addEventListener('message', function (e) {";
							text4 += "var sendUrl = e.origin;";
							text4 += "var data = document.body.innerHTML;";
							text4 += "e.source.postMessage(data, sendUrl);";
							text4 += "}, false);";
							text4 += "}";
							text4 += "else if (window.attachEvent) {";
							text4 += "window.attachEvent('onmessage', function (e) {";
							text4 += "var sendUrl = e.origin;";
							text4 += "var data = document.body.innerHTML;";
							text4 += "e.source.postMessage(data, sendUrl);";
							text4 += "});";
							text4 += "}";
							text4 += "}";
							text4 += "</script>";
							text4 += "</head>";
							text4 += "<body>";
							text4 += "{0}";
							text4 += "</body>";
							text4 += "</html>";
						}
						else
						{
							text4 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
						}
						text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text4);
						return "error|010|Not found file on server";
					}
					list.Add(this.strDownloadList[k]);
					list3.Add(array[k]);
					list4.Add("");
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list3[k]))
					{
						string text5 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text5 += "<html><head>";
							text5 += "<script type=\"text/javascript\">";
							text5 += "if (window.postMessage) {";
							text5 += "if (window.addEventListener) {";
							text5 += "window.addEventListener('message', function (e) {";
							text5 += "var sendUrl = e.origin;";
							text5 += "var data = document.body.innerHTML;";
							text5 += "e.source.postMessage(data, sendUrl);";
							text5 += "}, false);";
							text5 += "}";
							text5 += "else if (window.attachEvent) {";
							text5 += "window.attachEvent('onmessage', function (e) {";
							text5 += "var sendUrl = e.origin;";
							text5 += "var data = document.body.innerHTML;";
							text5 += "e.source.postMessage(data, sendUrl);";
							text5 += "});";
							text5 += "}";
							text5 += "}";
							text5 += "</script>";
							text5 += "</head>";
							text5 += "<body>";
							text5 += "{0}";
							text5 += "</body>";
							text5 += "</html>";
						}
						else
						{
							text5 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text5);
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list3[k]))
					{
						string text6 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text6 += "<html><head>";
							text6 += "<script type=\"text/javascript\">";
							text6 += "if (window.postMessage) {";
							text6 += "if (window.addEventListener) {";
							text6 += "window.addEventListener('message', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "}, false);";
							text6 += "}";
							text6 += "else if (window.attachEvent) {";
							text6 += "window.attachEvent('onmessage', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "});";
							text6 += "}";
							text6 += "}";
							text6 += "</script>";
							text6 += "</head>";
							text6 += "<body>";
							text6 += "{0}";
							text6 += "</body>";
							text6 += "</html>";
						}
						else
						{
							text6 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text6);
						return "error|018|There does not allow the string included in file name";
					}
				}
			}
			if (!string.IsNullOrEmpty(empty))
			{
				return null;
			}
			if ((list2 == null || list2.Count <= 0) && (list == null || list.Count <= 0))
			{
				return null;
			}
			bool resumeMode = false;
			if (!string.IsNullOrEmpty(this._entity_dextParam.resumeMode) && this._entity_dextParam.resumeMode == "1")
			{
				resumeMode = true;
			}
			string dext5CMD2 = this._entity_dextParam.dext5CMD;
			string contentDispositionType = "";
			if (dext5CMD2 == "openRequest")
			{
				contentDispositionType = "inline";
			}
			else
			{
				contentDispositionType = "attachment";
			}
			string[] array2 = new string[list.Count];
			string[] array3 = new string[list3.Count];
			for (int l = 0; l < list4.Count; l++)
			{
				if (list2 != null && list2.Count > 0)
				{
					array2[l] = "";
				}
				else if (list != null && list.Count > 0)
				{
					array2[l] = list[l];
				}
			}
			for (int m = 0; m < list3.Count; m++)
			{
				array3[m] = list3[m];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			bool bUseDownloadServerFileName = false;
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array2;
				uploadEventEntity.DownloadFileName = array3;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				if (list != null && list.Count > 0)
				{
					array2 = uploadEventEntity.DownloadFilePath;
				}
				array3 = uploadEventEntity.DownloadFileName;
				if (list != null && list.Count > 0)
				{
					for (int n = 0; n < array2.Length; n++)
					{
						list[n] = array2[n];
					}
				}
				for (int num = 0; num < array3.Length; num++)
				{
					list3[num] = array3[num];
				}
				bUseDownloadServerFileName = uploadEventEntity.UseDownloadServerFileName;
			}
			catch
			{
			}
			if (pCustomError == null)
			{
				if (list2 != null && list2.Count > 1)
				{
					string text7 = base.CompressionFiles(list2, list3);
					if (string.IsNullOrEmpty(this.zipFileName))
					{
						this.zipFileName = Path.GetFileName(text7);
					}
					base.SendFileToClient(contentDispositionType, text7, this.zipFileName, this.zipFileName, resumeMode, false);
				}
				else if (list != null && list.Count > 1)
				{
					string text8 = base.CompressionFiles(list, list3);
					if (string.IsNullOrEmpty(this.zipFileName))
					{
						this.zipFileName = Path.GetFileName(text8);
					}
					base.SendFileToClient(contentDispositionType, text8, this.zipFileName, this.zipFileName, resumeMode, false);
				}
				else
				{
					string headerFileName = base.GetHeaderFileName(list3[0]);
					if (list2 != null && list2.Count > 0)
					{
						base.SendFileToClient(contentDispositionType, list2[0], list3[0], headerFileName, resumeMode, bUseDownloadServerFileName);
					}
					else if (list != null && list.Count > 0)
					{
						base.SendFileToClient(contentDispositionType, list[0], list3[0], headerFileName, resumeMode, bUseDownloadServerFileName);
					}
				}
				string viewerGUID = this._entity_dextParam.viewerGUID;
				if (!string.IsNullOrEmpty(viewerGUID))
				{
					UploadStatus uploadStatus = new UploadStatus();
					uploadStatus.Message = "ok";
					this.hContext.Application.Add(viewerGUID, uploadStatus);
				}
				if (list2 != null && list2.Count > 0)
				{
					for (int num2 = 0; num2 < list2.Count; num2++)
					{
						list4[num2] = "\f" + list3[num2];
					}
				}
				else if (list != null && list.Count > 0)
				{
					for (int num3 = 0; num3 < list.Count; num3++)
					{
						list4[num3] = list[num3] + "\f" + list3[num3];
					}
				}
				return list4;
			}
			if (dext5CMD2 == "openRequest")
			{
				string text9 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug(text9, this._str_DebugFilePath);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text9);
				return text9;
			}
			string text10 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text10 += "<html><head>";
				text10 += "<script type=\"text/javascript\">";
				text10 += "if (window.postMessage) {";
				text10 += "if (window.addEventListener) {";
				text10 += "window.addEventListener('message', function (e) {";
				text10 += "var sendUrl = e.origin;";
				text10 += "var data = document.body.innerHTML;";
				text10 += "e.source.postMessage(data, sendUrl);";
				text10 += "}, false);";
				text10 += "}";
				text10 += "else if (window.attachEvent) {";
				text10 += "window.attachEvent('onmessage', function (e) {";
				text10 += "var sendUrl = e.origin;";
				text10 += "var data = document.body.innerHTML;";
				text10 += "e.source.postMessage(data, sendUrl);";
				text10 += "});";
				text10 += "}";
				text10 += "}";
				text10 += "</script>";
				text10 += "</head>";
				text10 += "<body>";
				text10 += "{0}";
				text10 += "</body>";
				text10 += "</html>";
			}
			else
			{
				text10 = "{0}";
			}
			string text11 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("[FAIL]" + text11, this._str_DebugFilePath);
			}
			text10 = text10.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text11));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text10);
			return text11;
		}

		// Token: 0x0600022E RID: 558 RVA: 0x00017CB4 File Offset: 0x00015EB4
		public object ProcessCustomDownloadPlugin(OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			if (!string.IsNullOrEmpty(this._entity_dextParam.zipFileName))
			{
				base.ZipFileDownload();
				return null;
			}
			string empty = string.Empty;
			if (this.sDownloadList == null && this.strDownloadList == null)
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				return "error|009|Invalid parameter on server";
			}
			string[] array = this._entity_dextParam.fileOrgNameAry;
			if (!string.IsNullOrEmpty(this._entity_dextParam.dext5CMD) || array == null)
			{
				string dext5CMD = this._entity_dextParam.dext5CMD;
				if (dext5CMD == "openRequest")
				{
					array = new string[]
					{
						this._entity_dextParam.fileOrgName
					};
				}
			}
			List<string> list = new List<string>();
			List<Stream> list2 = new List<Stream>();
			List<string> list3 = new List<string>();
			List<string> list4 = new List<string>();
			if (this._entity_dextParam.mode == "normal")
			{
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = HttpUtility.UrlDecode(array[i], Encoding.UTF8);
				}
			}
			string empty2 = string.Empty;
			if (this.sDownloadList != null)
			{
				for (int j = 0; j < this.sDownloadList.Count; j++)
				{
					list2.Add(this.sDownloadList[j]);
					list3.Add(array[j]);
					list4.Add("");
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list3[j]))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						this.hContext.Response.Clear();
						this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list3[j]))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						this.hContext.Response.Clear();
						this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						return "error|018|There does not allow the string included in file name";
					}
				}
			}
			else if (this.strDownloadList != null)
			{
				for (int k = 0; k < this.strDownloadList.Count; k++)
				{
					if (!File.Exists(this.strDownloadList[k]))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
						}
						this.hContext.Response.Clear();
						this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
						return "error|010|Not found file on server";
					}
					list.Add(this.strDownloadList[k]);
					list3.Add(array[k]);
					list4.Add("");
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list3[k]))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						this.hContext.Response.Clear();
						this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list3[k]))
					{
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						this.hContext.Response.Clear();
						this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						return "error|018|There does not allow the string included in file name";
					}
				}
			}
			if (!string.IsNullOrEmpty(empty))
			{
				return null;
			}
			if ((list2 == null || list2.Count <= 0) && (list == null || list.Count <= 0))
			{
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
				return "error|010|Not found file on server";
			}
			bool resumeMode = false;
			if (!string.IsNullOrEmpty(this._entity_dextParam.resumeMode) && this._entity_dextParam.resumeMode == "1")
			{
				resumeMode = true;
			}
			string[] array2 = new string[list.Count];
			string[] array3 = new string[list3.Count];
			for (int l = 0; l < list4.Count; l++)
			{
				if (list2 != null && list2.Count > 0)
				{
					array2[l] = "";
				}
				else if (list != null && list.Count > 0)
				{
					array2[l] = list[l];
				}
			}
			for (int m = 0; m < list3.Count; m++)
			{
				array3[m] = list3[m];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			bool bUseDownloadServerFileName = false;
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array2;
				uploadEventEntity.DownloadFileName = array3;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				if (list != null && list.Count > 0)
				{
					array2 = uploadEventEntity.DownloadFilePath;
				}
				array3 = uploadEventEntity.DownloadFileName;
				if (list != null && list.Count > 0)
				{
					for (int n = 0; n < array2.Length; n++)
					{
						list[n] = array2[n];
					}
				}
				for (int num = 0; num < array3.Length; num++)
				{
					list3[num] = array3[num];
				}
				bUseDownloadServerFileName = uploadEventEntity.UseDownloadServerFileName;
			}
			catch
			{
			}
			if (pCustomError == null)
			{
				if (list2 != null && list2.Count > 1)
				{
					string text = base.CompressionFiles(list2, list3);
					if (string.IsNullOrEmpty(this.zipFileName))
					{
						this.zipFileName = Path.GetFileName(text);
					}
					base.SendFileToClient("attachment", text, this.zipFileName, this.zipFileName, resumeMode, false);
				}
				else if (list != null && list.Count > 1)
				{
					string text2 = base.CompressionFiles(list, list3);
					if (string.IsNullOrEmpty(this.zipFileName))
					{
						this.zipFileName = Path.GetFileName(text2);
					}
					base.SendFileToClient("attachment", text2, this.zipFileName, this.zipFileName, resumeMode, false);
				}
				else
				{
					string headerFileName = base.GetHeaderFileName(list3[0]);
					if (list2 != null && list2.Count > 0)
					{
						base.SendFileToClient("attachment", list2[0], list3[0], headerFileName, resumeMode, bUseDownloadServerFileName);
					}
					else if (list != null && list.Count > 0)
					{
						base.SendFileToClient("attachment", list[0], list3[0], headerFileName, resumeMode, bUseDownloadServerFileName);
					}
				}
				if (list2 != null && list2.Count > 0)
				{
					for (int num2 = 0; num2 < list2.Count; num2++)
					{
						list4[num2] = "\f" + list3[num2];
					}
				}
				else if (list != null && list.Count > 0)
				{
					for (int num3 = 0; num3 < list.Count; num3++)
					{
						list4[num3] = list[num3] + "\f" + list3[num3];
					}
				}
				return list4;
			}
			string text3 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("[FAIL]" + text3, this._str_DebugFilePath);
			}
			this.hContext.Response.Clear();
			this.hContext.Response.Write("[FAIL]" + Dext5Parameter.MakeParameter(text3));
			return text3;
		}

		// Token: 0x0400012C RID: 300
		private string workType = string.Empty;

		// Token: 0x0400012D RID: 301
		private List<string> strDownloadList;

		// Token: 0x0400012E RID: 302
		private List<Stream> sDownloadList;
	}
}

﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x02000009 RID: 9
	public class LogUtil
	{
		// Token: 0x060000B7 RID: 183 RVA: 0x0000D841 File Offset: 0x0000BA41
		public static void DextDebug(string logMessage, string logPath)
		{
			if (!string.IsNullOrEmpty(logPath))
			{
				LogUtil.WriteLogFile(logPath, logMessage);
			}
		}

		// Token: 0x060000B8 RID: 184 RVA: 0x0000D854 File Offset: 0x0000BA54
		private static void WriteDebug(string message)
		{
			string arg = DateTime.Now.ToString("HH:mm:ss");
			string empty = string.Empty;
			if (string.IsNullOrEmpty(message))
			{
				return;
			}
			string.Format("[DEXT5 Upload {0}] {1}", arg, message);
		}

		// Token: 0x060000B9 RID: 185 RVA: 0x0000D890 File Offset: 0x0000BA90
		private static void WriteLogFile(string logPath, string message)
		{
			DateTime now = DateTime.Now;
			string arg = now.ToString("yyyy-MM-dd");
			string path = string.Format("{0}.log", arg);
			string text = now.ToString("HH:mm:ss");
			text = ", " + text;
			try
			{
				if (!Directory.Exists(logPath))
				{
					Directory.CreateDirectory(logPath);
				}
				string path2 = Path.Combine(logPath, path);
				StreamWriter streamWriter = new StreamWriter(path2, true, Encoding.UTF8);
				StackTrace stackTrace = new StackTrace(true);
				StackFrame[] frames = stackTrace.GetFrames();
				string arg2 = "";
				if (frames.Length > 3)
				{
					MethodBase method = stackTrace.GetFrame(2).GetMethod();
					int fileLineNumber = stackTrace.GetFrame(2).GetFileLineNumber();
					Type reflectedType = method.ReflectedType;
					string @namespace = reflectedType.Namespace;
					arg2 = string.Concat(new string[]
					{
						", ",
						@namespace,
						".",
						reflectedType.Name,
						".",
						method.Name,
						" ",
						fileLineNumber.ToString(),
						"L"
					});
				}
				string value = string.Empty;
				if (string.IsNullOrEmpty(message))
				{
					value = "[DEXT5 Upload] --------------------------";
				}
				else
				{
					value = string.Format("[DEXT5 Upload{0}{1}] {2}", text, arg2, message);
				}
				streamWriter.WriteLine(value);
				streamWriter.Close();
				streamWriter.Dispose();
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x04000069 RID: 105
		private static char m_PathChar = Path.DirectorySeparatorChar;
	}
}

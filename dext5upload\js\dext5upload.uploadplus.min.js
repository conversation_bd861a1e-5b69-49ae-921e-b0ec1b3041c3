importScripts("dext5upload.base64.js");importScripts("dext5upload.xhr.js");importScripts("json2.min.js");var numberOfServerUploadedChunks=0,G_currSocket=null;
function processFile(c,d,e,g,k,h,l,m,f,n,p){var q=e.size;g={currentNumber:1,currentSize:0,numberOfChunks:Math.ceil(q/k),size:e.size,fileBlob:e,currentJobNumber:0};var a=0,b=0,a=f.split("|");f=a[1];b=a[2];g.currentJobNumber=f;a=(parseInt(f,10)-1)*n;b=f==b?q:a+n;e=e.name;null==G_currSocket?(G_currSocket=new socketTransfer(e,g,k,c,m,h.wsHandlerUrl,d,h,l,a,b,p),G_currSocket.start()):(G_currSocket.setUploadInfo(e,g,k,c,m,h.wsHandlerUrl,d,h,l,a,b,p),G_currSocket.initializeUpload())}
function socketTransfer(c,d,e,g,k,h,l,m,f,n,p,q){this.file=d.fileBlob;this.fileName=c;this.fileExt=getExtension(c);this.guid=l;this.blockSize=e;this.chunkInfo=d;this.reader=this.socket=null;this.curIndex=0;this.lastBlock=!1;this.type="binary";this.idx=g;this.beforeEventValue=k;this.wsHandlerUrl=h;this._config=m;this.formDataEx=f;this.startSize=n;this.endSize=p;this.workerIdx=q;this.isZeroFile=0==this.file.size?"1":"0";var a=this;this.setUploadInfo=function(b,c,d,e,f,g,h,k,l,m,n,p){a.file=c.fileBlob;
a.fileName=b;a.fileExt=getExtension(b);a.guid=h;a.blockSize=d;a.chunkInfo=c;a.idx=e;a.beforeEventValue=f;a.wsHandlerUrl=g;a._config=k;a.formDataEx=l;a.startSize=m;a.endSize=n;a.workerIdx=p;a.isZeroFile=0==a.file.size?"1":"0"};this.start=function(){a.socket=a.createSocket(this.wsHandlerUrl);"binary"==a.type&&(a.socket.binaryType="blob");a.socket.onopen=function(b){a.onOpen(b)};a.socket.onmessage=function(b){a.onMessage(b)};a.socket.onerror=function(b){a.onError(b)};a.socket.onclose=function(b){a.onClose(b)}};
this.createSocket=function(a){try{return new WebSocket(a)}catch(c){return new MozWebSocket(a)}};this.onOpen=function(b){a.initializeUpload()};this.initializeUpload=function(){var b=a.beforeEventValue.tempPath+"|"+a.beforeEventValue.blackList+"|"+a.beforeEventValue.blackWordList+"|"+a.beforeEventValue.whiteList+"|",b=b+(a.beforeEventValue.newFileLocation+"|"+a.beforeEventValue.responseFileServerPath+"|"+a.beforeEventValue.responseFileName);""!=a.beforeEventValue.responseCustomValue&&(b+="|"+a.beforeEventValue.responseCustomValue);
a.socket.send("DEXTINIT "+JSON.encode({filename:a.file.name,fileext:a.fileExt,size:a.file.size,type:"binary",guid:a.guid,beforeEventInfo:b,parameters:[],currentJobNumber:a.chunkInfo.currentJobNumber,isZeroFile:a.isZeroFile}))};this.onMessage=function(b){response=JSON.decode(b.data);if(200!=response.code)return b={message:response.message,code:response.code},a.onError(b),!1;if("AUTH"==response.type)a.initializeUpload();else if("DEXTINIT"==response.type)a.readSlice(a.startSize,a.blockSize);else if("DEXTINIT_0"==
response.type)self.postMessage({type:"progress",id:a.idx,uploadedSize:0,uploadedChunks:a.chunkInfo.currentNumber,chunkInfo:a.chunkInfo,blockSize:a.blockSize}),self.postMessage({type:"complete",id:a.idx,beforeEventValue:a.beforeEventValue,guid:a.guid,fileExt:a.fileExt,fileName:a.fileName,workerIdx:a.workerIdx});else if("DATA"==response.type)b=a.lastBlock,self.postMessage({type:"progress",id:a.idx,uploadedSize:response.bytesRead,uploadedChunks:a.chunkInfo.currentNumber,chunkInfo:a.chunkInfo,blockSize:a.blockSize}),
a.chunkInfo.currentNumber++,b?self.postMessage({type:"complete",id:a.idx,beforeEventValue:a.beforeEventValue,guid:a.guid,fileExt:a.fileExt,fileName:a.fileName,workerIdx:a.workerIdx}):a.readSlice(a.curIndex+a.blockSize,a.blockSize);else return b={message:""!=response.message?response.message:"socket error",code:response.code?response.code:0},a.onError(b),!1};this.readSlice=function(b,c){a.curIndex=b;var d=Math.min(b+c-1,a.endSize-1);a.lastBlock=d==a.endSize-1;d=a.file["mozSlice"in a.file?"mozSlice":
"webkitSlice"in a.file?"webkitSlice":"slice"](b,b+(d-b+1));if("binary"==a.type)return a.sendSlice(d),!0;a.reader=new FileReader;a.reader.onabort=function(){};a.reader.onerror=function(a){};a.reader.onloadend=function(b){a.sendB64Slice(b.target.result)};a.reader.readAsBinaryString(d)};this.onError=function(b){try{a.socket.close()}catch(c){}self.postMessage({type:"error",code:"200013",message:"socket connect error",id:a.idx})};this.onClose=function(b){try{a.socket.close()}catch(c){}};this.sendSlice=
function(b){a.socket.send(b)}}function socketClose(c){if(G_currSocket)try{G_currSocket.onClose()}catch(d){}self.postMessage({type:"closeComplete",workerIdx:c})}function getExtension(c){c=c.split(".");var d="";1<c.length&&(d=c[c.length-1]);return d.toLowerCase()}
self.onmessage=function(c){c=c.data;switch(c.type){case "start":BYTES_PER_CHUNK=c.bytesperchunk;processFile(c.id,c.guid,c.file,c.asyncstate,c.bytesperchunk,c.configValue,c.formdataex,c.beforeEventValue,c.currWorkInfo,c.socketWorkerJobSize,c.workerIdx);break;case "close":socketClose(c.workerIdx)}};

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: Config :: UseAfterAddEndTimeEvent</h3>
    <p class="ttl">config.UseAfterAddEndTimeEvent</p>
    <p class="txt">
        업로드 리스트 컨트롤에서 모든 파일추가 완료 후 이벤트 함수 사용을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "1" 사용이고, "0"으로 설정시 이벤트 미사용합니다.<br />
        "1"으로 설정 후 파일 추가 완료 시 DEXT5UPLOAD_AfterAddItemEndTime(uploadID) 함수가 호출 됩니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;script type="text/javascript"&#62;
    
&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 모든 파일 추가 완료 후 이벤트 함수 미사용으로 설정합니다.
        DEXT5UPLOAD.config.UseAfterAddEndTimeEvent = '0';
    
        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


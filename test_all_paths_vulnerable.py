#!/usr/bin/env python3
"""
测试脚本：验证 is_all_paths_vulnerable 方法是否正常工作
"""

import sys
import os

# 添加当前目录到路径，以便导入 husky_scanner
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from husky_scanner import <PERSON><PERSON>Scanner

def test_is_all_paths_vulnerable():
    """测试 is_all_paths_vulnerable 方法"""
    
    # 创建扫描器实例
    scanner = HuskyScanner()
    
    print("测试 is_all_paths_vulnerable 方法")
    print("=" * 50)
    
    # 获取漏洞路径总数
    total_paths = len(scanner.vulnerability_paths)
    print(f"总漏洞路径数量: {total_paths}")
    
    # 测试用例1: 空列表
    test_paths_1 = []
    result_1 = scanner.is_all_paths_vulnerable(test_paths_1)
    print(f"\n测试1 - 空列表: {result_1} (期望: False)")
    
    # 测试用例2: 少量路径（正常情况）
    test_paths_2 = ["http://example.com/path1", "http://example.com/path2"]
    result_2 = scanner.is_all_paths_vulnerable(test_paths_2)
    print(f"测试2 - 少量路径 ({len(test_paths_2)}个): {result_2} (期望: False)")
    
    # 测试用例3: 所有路径都存在（异常情况）
    test_paths_3 = [f"http://example.com/path{i}" for i in range(total_paths)]
    result_3 = scanner.is_all_paths_vulnerable(test_paths_3)
    print(f"测试3 - 所有路径都存在 ({len(test_paths_3)}个): {result_3} (期望: True)")
    
    # 测试用例4: 超过80%阈值（异常情况）
    threshold_count = int(total_paths * 0.8) + 1
    test_paths_4 = [f"http://example.com/path{i}" for i in range(threshold_count)]
    result_4 = scanner.is_all_paths_vulnerable(test_paths_4)
    print(f"测试4 - 超过80%阈值 ({len(test_paths_4)}个): {result_4} (期望: True)")
    
    # 测试用例5: 刚好在阈值以下（正常情况）
    below_threshold_count = int(total_paths * 0.7)
    test_paths_5 = [f"http://example.com/path{i}" for i in range(below_threshold_count)]
    result_5 = scanner.is_all_paths_vulnerable(test_paths_5)
    print(f"测试5 - 低于阈值 ({len(test_paths_5)}个): {result_5} (期望: False)")
    
    print("\n" + "=" * 50)
    print("测试完成!")
    
    # 验证结果
    expected_results = [False, False, True, True, False]
    actual_results = [result_1, result_2, result_3, result_4, result_5]
    
    if expected_results == actual_results:
        print("✅ 所有测试通过!")
        return True
    else:
        print("❌ 测试失败!")
        print(f"期望结果: {expected_results}")
        print(f"实际结果: {actual_results}")
        return False

if __name__ == "__main__":
    test_is_all_paths_vulnerable()

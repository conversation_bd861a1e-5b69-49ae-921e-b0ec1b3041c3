﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: SetSelectItem</h3>
    <p class="ttl">void SetSelectItem(itemIndex, appendMode, uploadID)</p>
    <p class="txt">
        업로드 영역 외부에서 파일을 선택합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">itemIndex</span>&nbsp;&nbsp;-1 : appendMode가 0이면 전체 해제, appendMode가 1이면 전체 선택<br/>
        <span style="padding-left:63px">-1이 아닌 0이상의 숫자 : DEXT5 Upload 영역에 있는 파일의 0부터 시작되는 순서 값</span><br/>
        <span class="firebrick">appendMode</span>&nbsp;&nbsp;0 : ItemIndex가 -1면 전체 해제, ItemIndex가 -1 이외의 숫자이면 전체 해제 후 ItemIndex 파일 선택<br/>
        <span style="padding-left:77px">1 : ItemIndex가 -1면 전체 선택, ItemIndex가 -1 이외의 숫자이면 기존 체크상태 유지 하면서 ItemIndex 파일 선택</span><br/>
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;설정하는 업로드의 id값을 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function setSelectItem() {
            DEXT5UPLOAD.SetSelectItem('-1', '0', 'upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


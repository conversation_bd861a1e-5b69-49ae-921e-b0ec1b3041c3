package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComReadAndXResponse.class */
class SmbComReadAndXResponse extends AndXServerMessageBlock {
    byte[] b;
    int off;
    int dataCompactionMode;
    int dataLength;
    int dataOffset;

    SmbComReadAndXResponse() {
    }

    SmbComReadAndXResponse(byte[] b, int off) {
        this.b = b;
        this.off = off;
    }

    void setParam(byte[] b, int off) {
        this.b = b;
        this.off = off;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        int bufferIndex2 = bufferIndex + 2;
        this.dataCompactionMode = readInt2(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 4;
        this.dataLength = readInt2(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 2;
        this.dataOffset = readInt2(buffer, bufferIndex4);
        return (bufferIndex4 + 12) - bufferIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComReadAndXResponse[" + super.toString() + ",dataCompactionMode=" + this.dataCompactionMode + ",dataLength=" + this.dataLength + ",dataOffset=" + this.dataOffset + "]");
    }
}

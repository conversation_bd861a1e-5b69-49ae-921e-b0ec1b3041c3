目标: 52.78.221.179:80
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://f-static.medijob.cc/js/plugin/smarteditor2/
发现的资源: https://f-static.medijob.cc/js/plugin/smarteditor2/js/HuskyEZCreator.js
发现的漏洞:
  - https://f-static.medijob.cc/js/plugin/smarteditor2/sample/photo_uploader/file_uploader.php (upload, high)

--------------------------------------------------

目标: 52.78.221.179:80
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://f-static.medijob.cc/js/plugin/smarteditor2/
发现的资源: https://f-static.medijob.cc/js/plugin/smarteditor2/js/HuskyEZCreator.js
发现的漏洞:
  - https://f-static.medijob.cc/js/plugin/smarteditor2/sample/photo_uploader/file_uploader.php (upload, high)

--------------------------------------------------

目标: 110.10.130.45:80
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: http://110.10.130.45:80
发现的漏洞:
  - http://110.10.130.45:80/sample/photo_uploader/file_uploader.jsp (upload, high)
  - http://110.10.130.45:80/sample/photo_uploader/file_uploader_html5.jsp (upload, high)
  - http://110.10.130.45:80/photo_uploader/popup/file_uploader.jsp (upload, high)

--------------------------------------------------

目标: 110.10.130.45:80
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: http://110.10.130.45:80
发现的漏洞:
  - http://110.10.130.45:80/core/connector/java/connector.java (rce, critical)
  - http://110.10.130.45:80/plugins/fileeditor/plugin.js (rce, high)

--------------------------------------------------

目标: anapro.com:80
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: http://anapro.com:80
发现的漏洞:
  - http://anapro.com:80/dext5upload/handler/dext5handler.jsp (upload, critical)
  - http://anapro.com:80/dext5upload/handler/dext5handler.ashx (upload, critical)

--------------------------------------------------

目标: mydatasafe.kr:443
编辑器类型: dext5upload
检测方法: directory_fuzzing
基础路径: https://mydatasafe.kr:443
发现的漏洞:
  - https://mydatasafe.kr:443/dext5upload/handler/dext5handler.jsp (upload, critical)
  - https://mydatasafe.kr:443/dext5upload/handler/dext5handler.ashx (upload, critical)

--------------------------------------------------

目标: 211.188.61.42:3000
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: http://211.188.61.42:3000/admin/
发现的漏洞:
  - http://211.188.61.42:3000/admin/imageUpload/delete.jsp (delete, high)

--------------------------------------------------

目标: 218.237.119.247:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://218.237.119.247:443/editor/
发现的漏洞:
  - https://218.237.119.247:443/editor/sample/photo_uploader/photo_uploader.html (info, medium)

--------------------------------------------------

目标: 218.237.119.247:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://218.237.119.247:443/editor/
发现的漏洞:
  - https://218.237.119.247:443/editor/imageUpload/delete.jsp (delete, high)
  - https://218.237.119.247:443/editor/imageUpload/config.jsp (info, medium)

--------------------------------------------------

目标: 218.237.119.247:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://218.237.119.247:443/editor/
发现的漏洞:
  - https://218.237.119.247:443/editor/ckfinder.html (info, low)

--------------------------------------------------

目标: www.dsliquid.com:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com:443
发现的漏洞:
  - https://www.dsliquid.com:443/sample/photo_uploader/file_uploader.jsp (upload, high)
  - https://www.dsliquid.com:443/sample/photo_uploader/file_uploader_html5.jsp (upload, high)
  - https://www.dsliquid.com:443/photo_uploader/popup/file_uploader.jsp (upload, high)

--------------------------------------------------

目标: www.dsliquid.com:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://www.dsliquid.com:443
发现的漏洞:
  - https://www.dsliquid.com:443/core/connector/java/connector.java (rce, critical)
  - https://www.dsliquid.com:443/plugins/fileeditor/plugin.js (rce, high)

--------------------------------------------------

目标: 52.78.221.179:443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://f-static.medijob.cc/js/plugin/smarteditor2/
发现的资源: https://f-static.medijob.cc/js/plugin/smarteditor2/js/HuskyEZCreator.js
发现的漏洞:
  - https://f-static.medijob.cc/js/plugin/smarteditor2/sample/photo_uploader/file_uploader.php (upload, high)

--------------------------------------------------

目标: 52.78.221.179:443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://f-static.medijob.cc/js/plugin/smarteditor2/
发现的资源: https://f-static.medijob.cc/js/plugin/smarteditor2/js/HuskyEZCreator.js
发现的漏洞:
  - https://f-static.medijob.cc/js/plugin/smarteditor2/sample/photo_uploader/file_uploader.php (upload, high)

--------------------------------------------------

目标: mosim.seesaw.land:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://mosim.seesaw.land:443/scripts/
发现的漏洞:
  - https://mosim.seesaw.land:443/scripts/sample/photo_uploader/photo_uploader.html (info, medium)

--------------------------------------------------

目标: mosim.seesaw.land:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://mosim.seesaw.land:443
发现的漏洞:
  - https://mosim.seesaw.land:443/imageUpload/delete.jsp (delete, high)

--------------------------------------------------

目标: mosim.seesaw.land:443
编辑器类型: ckfinder
检测方法: directory_fuzzing
基础路径: https://mosim.seesaw.land:443/scripts/
发现的漏洞:
  - https://mosim.seesaw.land:443/scripts/ckfinder.html (info, low)

--------------------------------------------------

目标: **************:443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://**************/wm_engine_SW/_engine/smartEditor/
发现的资源: https://**************/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的漏洞:
  - https://**************/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload, high)
  - https://**************/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload, high)
  - https://**************/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload, high)

--------------------------------------------------

目标: **************:443
编辑器类型: smarteditor
检测方法: homepage_analysis
基础路径: https://**************/wm_engine_SW/_engine/smartEditor/
发现的资源: https://**************/wm_engine_SW/_engine/smartEditor/js/HuskyEZCreator.js
发现的漏洞:
  - https://**************/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader.jsp (upload, high)
  - https://**************/wm_engine_SW/_engine/smartEditor/sample/photo_uploader/file_uploader_html5.jsp (upload, high)
  - https://**************/wm_engine_SW/_engine/smartEditor/photo_uploader/popup/file_uploader.jsp (upload, high)

--------------------------------------------------

目标: **************:443
编辑器类型: smarteditor
检测方法: directory_fuzzing
基础路径: https://**************:443
发现的漏洞:
  - https://**************:443/sample/photo_uploader/file_uploader.jsp (upload, high)
  - https://**************:443/sample/photo_uploader/file_uploader_html5.jsp (upload, high)
  - https://**************:443/photo_uploader/popup/file_uploader.jsp (upload, high)

--------------------------------------------------

目标: 211.239.154.20:8111
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: http://211.239.154.20:8111
发现的漏洞:
  - http://211.239.154.20:8111/imageUpload/delete.jsp (delete, high)

--------------------------------------------------

目标: 49.247.40.204:443
编辑器类型: cheditor
检测方法: directory_fuzzing
基础路径: https://49.247.40.204:443/admin/
发现的漏洞:
  - https://49.247.40.204:443/admin/imageUpload/delete.jsp (delete, high)

--------------------------------------------------


﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: Config :: FileNameRule</h3>
    <p class="ttl">config.FileNameRule</p>
    <p class="txt">
        서버에서 저장할 파일의 이름을 지정하는 규칙입니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "REALFILENAME" 입니다.<br/>
        값 지정은 "GUID" 또는 "REALFILENAME" 을 입력합니다.<br />
        "REALFILENAME" 의 경우 파일의 실제 파일명으로 설정됩니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 파일의 이름을 서버에 실제 파일명으로 저장합니다.
        DEXT5UPLOAD.config.FileNameRule = 'REALFILENAME';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


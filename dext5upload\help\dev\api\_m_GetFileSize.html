﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con ">           
    <h3 class="title">DEXT5 Upload :: GetFileSize</h3>
    <p class="ttl">void GetFileSize(urlAddress, callBackFunc)</p>
    <p class="txt">
        파일 사이즈를 요청합니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음.
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">urlAddress</span>&nbsp;&nbsp;파일의 저장위치(webPath)를 의미합니다.(http:// 부터의 전체경로를 넣으셔야 합니다.)<br />
        <span class="firebrick">callBackFunc</span>&nbsp;&nbsp;파일사이즈를 return할 CallBack function을 의미합니다.<br />       
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
       없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;
        function getFileSize() {
            DEXT5UPLOAD.GetFileSize('http://location.host/Test/test.html', callBackFunc);
        }

        // GetFileSize에서 설정한 function으로 파일사이즈를 넘겨줍니다.
        function callBackFunc(fileSize) {
            
        }
            

&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px;"  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;       
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


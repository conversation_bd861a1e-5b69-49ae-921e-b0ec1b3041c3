﻿using System;
using System.Runtime.InteropServices;

namespace Raonwiz.Dext5.Common
{
	// Token: 0x02000011 RID: 17
	public class Dext5UNCAccessWithCredentials : IDisposable
	{
		// Token: 0x06000105 RID: 261
		[DllImport("NetApi32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
		internal static extern uint NetUseAdd(string UncServerName, uint Level, ref Dext5UNCAccessWithCredentials.USE_INFO_2 Buf, out uint ParmError);

		// Token: 0x06000106 RID: 262
		[DllImport("NetApi32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
		internal static extern uint NetUseDel(string UncServerName, string UseName, uint ForceCond);

		// Token: 0x17000044 RID: 68
		// (get) Token: 0x06000108 RID: 264 RVA: 0x0001217D File Offset: 0x0001037D
		public int LastError
		{
			get
			{
				return this.iLastError;
			}
		}

		// Token: 0x06000109 RID: 265 RVA: 0x00012185 File Offset: 0x00010385
		public void Dispose()
		{
			if (!this.disposed)
			{
				this.NetUseDelete();
			}
			this.disposed = true;
			GC.SuppressFinalize(this);
		}

		// Token: 0x0600010A RID: 266 RVA: 0x000121A3 File Offset: 0x000103A3
		public bool NetUseWithCredentials(string UNCPath, string User, string Domain, string Password)
		{
			this.sUNCPath = UNCPath;
			this.sUser = User;
			this.sPassword = Password;
			this.sDomain = Domain;
			return this.NetUseWithCredentials();
		}

		// Token: 0x0600010B RID: 267 RVA: 0x000121C8 File Offset: 0x000103C8
		private bool NetUseWithCredentials()
		{
			bool result;
			try
			{
				Dext5UNCAccessWithCredentials.USE_INFO_2 use_INFO_ = default(Dext5UNCAccessWithCredentials.USE_INFO_2);
				use_INFO_.ui2_remote = this.sUNCPath;
				use_INFO_.ui2_username = this.sUser;
				use_INFO_.ui2_domainname = this.sDomain;
				use_INFO_.ui2_password = this.sPassword;
				use_INFO_.ui2_asg_type = 0U;
				use_INFO_.ui2_usecount = 1U;
				uint num2;
				uint num = Dext5UNCAccessWithCredentials.NetUseAdd(null, 2U, ref use_INFO_, out num2);
				this.iLastError = (int)num;
				result = (num == 0U);
			}
			catch
			{
				this.iLastError = Marshal.GetLastWin32Error();
				result = false;
			}
			return result;
		}

		// Token: 0x0600010C RID: 268 RVA: 0x0001225C File Offset: 0x0001045C
		public bool NetUseDelete()
		{
			bool result;
			try
			{
				uint num = Dext5UNCAccessWithCredentials.NetUseDel(null, this.sUNCPath, 2U);
				this.iLastError = (int)num;
				result = (num == 0U);
			}
			catch
			{
				this.iLastError = Marshal.GetLastWin32Error();
				result = false;
			}
			return result;
		}

		// Token: 0x0600010D RID: 269 RVA: 0x000122A8 File Offset: 0x000104A8
		~Dext5UNCAccessWithCredentials()
		{
			this.Dispose();
		}

		// Token: 0x04000092 RID: 146
		private bool disposed;

		// Token: 0x04000093 RID: 147
		private string sUNCPath;

		// Token: 0x04000094 RID: 148
		private string sUser;

		// Token: 0x04000095 RID: 149
		private string sPassword;

		// Token: 0x04000096 RID: 150
		private string sDomain;

		// Token: 0x04000097 RID: 151
		private int iLastError;

		// Token: 0x02000012 RID: 18
		[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
		internal struct USE_INFO_2
		{
			// Token: 0x04000098 RID: 152
			internal string ui2_local;

			// Token: 0x04000099 RID: 153
			internal string ui2_remote;

			// Token: 0x0400009A RID: 154
			internal string ui2_password;

			// Token: 0x0400009B RID: 155
			internal uint ui2_status;

			// Token: 0x0400009C RID: 156
			internal uint ui2_asg_type;

			// Token: 0x0400009D RID: 157
			internal uint ui2_refcount;

			// Token: 0x0400009E RID: 158
			internal uint ui2_usecount;

			// Token: 0x0400009F RID: 159
			internal string ui2_username;

			// Token: 0x040000A0 RID: 160
			internal string ui2_domainname;
		}
	}
}

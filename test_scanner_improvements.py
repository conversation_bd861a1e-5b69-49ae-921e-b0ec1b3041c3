#!/usr/bin/env python3
"""
测试扫描器改进功能
包括蜜罐检测、URL拼接修复、内容验证等
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from husky_scanner import HuskyScanner
    from dext5_scanner import DEXT5Scanner
except ImportError:
    # 如果在editor目录下
    sys.path.insert(0, r'e:\docs\untitled\editor')
    from husky_scanner import HuskyScanner
    from dext5_scanner import DEXT5Scanner

def test_husky_improvements():
    """测试Husky扫描器改进"""
    print("测试Husky扫描器改进")
    print("=" * 50)
    
    scanner = HuskyScanner()
    
    # 测试404特征检测
    print("\n[*] 测试404特征检测:")
    signatures = scanner.get_directory_404_signature('https://httpbin.org', '/')
    for ext, signature in signatures.items():
        if signature:
            print(f"  {ext.upper()}: 状态码={signature['status_code']}, 长度={signature['content_length']}")
        else:
            print(f"  {ext.upper()}: 获取失败")
    
    # 测试响应验证
    print("\n[*] 测试响应验证:")
    
    # 模拟响应对象
    class MockResponse:
        def __init__(self, status_code, text, headers=None):
            self.status_code = status_code
            self.text = text
            self.headers = headers or {}
    
    test_cases = [
        {
            'name': '有效的JSP上传页面',
            'response': MockResponse(200, '<form enctype="multipart/form-data"><input type="file"></form>'),
            'expected_ext': 'jsp',
            'expected': True
        },
        {
            'name': 'JSP页面包含PHP代码',
            'response': MockResponse(200, '<?php echo "hello"; ?>'),
            'expected_ext': 'jsp',
            'expected': False
        },
        {
            'name': '404错误页面',
            'response': MockResponse(200, 'Error 404: File not found'),
            'expected_ext': 'jsp',
            'expected': False
        },
        {
            'name': 'JSP页面包含.php报错',
            'response': MockResponse(200, 'Fatal error in /path/to/file.php on line 123'),
            'expected_ext': 'jsp',
            'expected': False
        }
    ]
    
    for case in test_cases:
        print(f"\n  测试: {case['name']}")
        
        is_valid = scanner.is_valid_vulnerability_response(
            case['response'], 
            case['expected_ext'], 
            None,  # directory_404_signatures
            None   # not_found_signature
        )
        
        print(f"    结果: {'有效' if is_valid else '无效'}")
        print(f"    期望: {'有效' if case['expected'] else '无效'}")
        
        if is_valid == case['expected']:
            print(f"    ✅ 测试通过")
        else:
            print(f"    ❌ 测试失败")

def test_dext5_improvements():
    """测试DEXT5扫描器改进"""
    print("\n\n测试DEXT5扫描器改进")
    print("=" * 50)
    
    scanner = DEXT5Scanner()
    
    # 测试蜜罐检测
    print("\n[*] 测试蜜罐检测:")
    
    # 正常情况
    normal_result = {
        'target': 'normal-site.com',
        'valid_handlers': [
            {'handler_url': 'https://normal-site.com/dext5upload/handler/dext5handler.jsp', 'content_length': 150}
        ],
        'dext5_resources': [
            {'url': 'https://normal-site.com/dext5upload/dext5upload.js', 'type': 'js'}
        ]
    }
    
    is_suspicious, reason = scanner.detect_dext5_honeypot(normal_result)
    print(f"  正常情况: {'可疑' if is_suspicious else '正常'} - {reason}")
    
    # 可疑情况 - 过多handler
    suspicious_result = {
        'target': 'suspicious-site.com',
        'valid_handlers': [
            {'handler_url': 'https://suspicious-site.com/handler/dext5handler.jsp', 'content_length': 150},
            {'handler_url': 'https://suspicious-site.com/handler/dext5handler.ashx', 'content_length': 150},
            {'handler_url': 'https://suspicious-site.com/dext5upload/handler/dext5handler.jsp', 'content_length': 150},
            {'handler_url': 'https://suspicious-site.com/dext5upload/handler/dext5handler.ashx', 'content_length': 150}
        ],
        'dext5_resources': [
            {'url': 'https://suspicious-site.com/dext5upload.js', 'type': 'js'},
            {'url': 'https://suspicious-site.com/dext5upload.css', 'type': 'css'}
        ]
    }
    
    is_suspicious, reason = scanner.detect_dext5_honeypot(suspicious_result)
    print(f"  可疑情况: {'可疑' if is_suspicious else '正常'} - {reason}")

def test_husky_honeypot_detection():
    """测试Husky蜜罐检测"""
    print("\n\n测试Husky蜜罐检测")
    print("=" * 50)
    
    scanner = HuskyScanner()
    
    # 正常情况
    normal_paths = [
        'https://normal-site.com/sample/photo_uploader/file_uploader.jsp',
        'https://normal-site.com/sample/photo_uploader/file_uploader.php'
    ]
    
    is_suspicious, reason = scanner.detect_honeypot_or_suspicious(normal_paths, 'https://normal-site.com/')
    print(f"  正常情况: {'可疑' if is_suspicious else '正常'} - {reason}")
    
    # 可疑情况 - 过多路径
    suspicious_paths = [
        'https://suspicious-site.com/sample/photo_uploader/file_uploader_html5.php',
        'https://suspicious-site.com/sample/photo_uploader/file_uploader_html5.jsp',
        'https://suspicious-site.com/sample/photo_uploader/file_uploader.jsp',
        'https://suspicious-site.com/sample/photo_uploader/file_uploader.php',
        'https://suspicious-site.com/sample/photo_uploader/photo_uploader.html',
        'https://suspicious-site.com/photo_uploader/popup/photo_uploader.html',
        'https://suspicious-site.com/photo_uploader/popup/file_uploader.php',
        'https://suspicious-site.com/photo_uploader/popup/file_uploader.jsp'
    ]
    
    is_suspicious, reason = scanner.detect_honeypot_or_suspicious(suspicious_paths, 'https://suspicious-site.com/')
    print(f"  可疑情况: {'可疑' if is_suspicious else '正常'} - {reason}")
    
    # 可疑情况 - JSP和PHP都存在且数量多
    mixed_paths = [
        'https://mixed-site.com/sample/photo_uploader/file_uploader.jsp',
        'https://mixed-site.com/sample/photo_uploader/file_uploader.php',
        'https://mixed-site.com/sample/photo_uploader/file_uploader_html5.jsp',
        'https://mixed-site.com/sample/photo_uploader/file_uploader_html5.php'
    ]
    
    is_suspicious, reason = scanner.detect_honeypot_or_suspicious(mixed_paths, 'https://mixed-site.com/')
    print(f"  混合情况: {'可疑' if is_suspicious else '正常'} - {reason}")

def test_url_joining_fixes():
    """测试URL拼接修复"""
    print("\n\n测试URL拼接修复")
    print("=" * 50)
    
    from urllib.parse import urljoin
    
    # 测试用例
    test_cases = [
        {
            'base_url': 'https://example.com',
            'path': '/smarteditor/',
            'file': 'js/HuskyEZCreator.js',
            'expected': 'https://example.com/smarteditor/js/HuskyEZCreator.js'
        },
        {
            'base_url': 'http://49.247.174.232',
            'path': '/dext5upload/',
            'file': 'handler/dext5handler.jsp',
            'expected': 'http://49.247.174.232/dext5upload/handler/dext5handler.jsp'
        },
        {
            'base_url': 'https://test.com:8080',
            'path': '/',
            'file': 'sample/photo_uploader/file_uploader.jsp',
            'expected': 'https://test.com:8080/sample/photo_uploader/file_uploader.jsp'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n[*] 测试用例 {i}:")
        print(f"  基础URL: {case['base_url']}")
        print(f"  路径: {case['path']}")
        print(f"  文件: {case['file']}")
        
        # 第一步：拼接基础路径
        base_path = urljoin(case['base_url'], case['path'])
        print(f"  拼接基础路径: {base_path}")
        
        # 第二步：拼接文件
        final_url = urljoin(base_path, case['file'])
        print(f"  最终URL: {final_url}")
        print(f"  期望URL: {case['expected']}")
        
        if final_url == case['expected']:
            print(f"  ✅ 测试通过")
        else:
            print(f"  ❌ 测试失败")

def main():
    print("扫描器改进功能测试")
    print("=" * 60)
    
    try:
        # 测试Husky扫描器改进
        test_husky_improvements()
        
        # 测试DEXT5扫描器改进
        test_dext5_improvements()
        
        # 测试Husky蜜罐检测
        test_husky_honeypot_detection()
        
        # 测试URL拼接修复
        test_url_joining_fixes()
        
        print("\n" + "=" * 60)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

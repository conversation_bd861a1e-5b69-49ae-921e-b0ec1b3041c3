﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">          
    <h3 class="title">DEXT5 Upload :: SetSize</h3>
    <p class="ttl">void SetSize(uploadID)</p>
    <p class="txt">
        업로드 생성 후 업로드의 크기를 변경할 수 있습니다.
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음. 
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">width </span>&nbsp;&nbsp;업로드의 가로 값을 의미 합니다. (% 또는 px 모두 가능)<br/>
        <span class="firebrick">height</span>&nbsp;&nbsp;업로드의 세로 값을 의미 합니다. (px만 가능)<br />
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;실행할 업로드의 id를 의미합니다. (단일 업로드일 경우 생략 가능)
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        업로드를 1개만 생성했을 경우 uploadID를 입력하지 않아도 업로드 크기를 설정할 수 있습니다.<br />
        &nbsp;&nbsp;&nbsp;&nbsp;예) id가 upload1인 업로드를 생성했을 경우<br />
        &nbsp;&nbsp;&nbsp;&nbsp; DEXT5UPLOAD.SetSize("700", "500", "upload1"), DEXT5UPLOAD.SetSize("700", "500") <br />
        &nbsp;&nbsp;&nbsp;&nbsp;모두 upload1업로드의 크기를 설정합니다.<br /><br />

        업로드를 2개 이상 생성했을 경우 uploadID를 반드시 입력해야 합니다.<br />
        &nbsp;&nbsp;&nbsp;&nbsp;예) id가 upload1, upload2인 업로드를 생성했을 경우<br />
        &nbsp;&nbsp;&nbsp;&nbsp;    DEXT5UPLOAD.SetSize("700", "500", "upload1") upload1 업로드의 크기를 설정합니다.<br />
        &nbsp;&nbsp;&nbsp;&nbsp;    DEXT5UPLOAD.SetSize("700", "500", "upload2") upload2 업로드의 크기를 설정합니다.<br />
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;          
        function setSize() {
            DEXT5UPLOAD.SetSize('700','500','upload1');
        }
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;

&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿using System;

namespace Raonwiz.Dext5.Process.Html4.Entity
{
	// Token: 0x02000029 RID: 41
	[Serializable]
	public class UploadStatus
	{
		// Token: 0x0600027E RID: 638 RVA: 0x0001E5F8 File Offset: 0x0001C7F8
		public UploadStatus()
		{
		}

		// Token: 0x0600027F RID: 639 RVA: 0x0001E67C File Offset: 0x0001C87C
		public UploadStatus(string currentStatus)
		{
			this._currentStatusID = currentStatus;
		}

		// Token: 0x170000DA RID: 218
		// (get) Token: 0x06000280 RID: 640 RVA: 0x0001E707 File Offset: 0x0001C907
		// (set) Token: 0x06000281 RID: 641 RVA: 0x0001E70F File Offset: 0x0001C90F
		public long CurrentBytesTransfered
		{
			get
			{
				return this._currentBytesTransfered;
			}
			set
			{
				this._currentBytesTransfered = value;
			}
		}

		// Token: 0x170000DB RID: 219
		// (get) Token: 0x06000282 RID: 642 RVA: 0x0001E718 File Offset: 0x0001C918
		// (set) Token: 0x06000283 RID: 643 RVA: 0x0001E720 File Offset: 0x0001C920
		public long TotalBytes
		{
			get
			{
				return this._totalBytes;
			}
			set
			{
				this._totalBytes = value;
			}
		}

		// Token: 0x170000DC RID: 220
		// (get) Token: 0x06000284 RID: 644 RVA: 0x0001E729 File Offset: 0x0001C929
		// (set) Token: 0x06000285 RID: 645 RVA: 0x0001E731 File Offset: 0x0001C931
		public long FileSize
		{
			get
			{
				return this._fileSize;
			}
			set
			{
				this._fileSize = value;
			}
		}

		// Token: 0x170000DD RID: 221
		// (get) Token: 0x06000286 RID: 646 RVA: 0x0001E73A File Offset: 0x0001C93A
		// (set) Token: 0x06000287 RID: 647 RVA: 0x0001E742 File Offset: 0x0001C942
		public string TempFileName
		{
			get
			{
				return this._tempFileName;
			}
			set
			{
				this._tempFileName = value;
			}
		}

		// Token: 0x170000DE RID: 222
		// (get) Token: 0x06000288 RID: 648 RVA: 0x0001E74B File Offset: 0x0001C94B
		// (set) Token: 0x06000289 RID: 649 RVA: 0x0001E753 File Offset: 0x0001C953
		public string FileName
		{
			get
			{
				return this._fileName;
			}
			set
			{
				this._fileName = value;
			}
		}

		// Token: 0x170000DF RID: 223
		// (get) Token: 0x0600028A RID: 650 RVA: 0x0001E75C File Offset: 0x0001C95C
		// (set) Token: 0x0600028B RID: 651 RVA: 0x0001E764 File Offset: 0x0001C964
		public string FolderNameRule
		{
			get
			{
				return this._folderNameRule;
			}
			set
			{
				this._folderNameRule = value;
			}
		}

		// Token: 0x170000E0 RID: 224
		// (get) Token: 0x0600028C RID: 652 RVA: 0x0001E76D File Offset: 0x0001C96D
		// (set) Token: 0x0600028D RID: 653 RVA: 0x0001E775 File Offset: 0x0001C975
		public string FileNameRule
		{
			get
			{
				return this._fileNameRule;
			}
			set
			{
				this._fileNameRule = value;
			}
		}

		// Token: 0x170000E1 RID: 225
		// (get) Token: 0x0600028E RID: 654 RVA: 0x0001E77E File Offset: 0x0001C97E
		// (set) Token: 0x0600028F RID: 655 RVA: 0x0001E786 File Offset: 0x0001C986
		public string FileNameRuleEx
		{
			get
			{
				return this._fileNameRuleEx;
			}
			set
			{
				this._fileNameRuleEx = value;
			}
		}

		// Token: 0x170000E2 RID: 226
		// (get) Token: 0x06000290 RID: 656 RVA: 0x0001E78F File Offset: 0x0001C98F
		// (set) Token: 0x06000291 RID: 657 RVA: 0x0001E797 File Offset: 0x0001C997
		public string D5_prefix
		{
			get
			{
				return this._d5_prefix;
			}
			set
			{
				this._d5_prefix = value;
			}
		}

		// Token: 0x170000E3 RID: 227
		// (get) Token: 0x06000292 RID: 658 RVA: 0x0001E7A0 File Offset: 0x0001C9A0
		// (set) Token: 0x06000293 RID: 659 RVA: 0x0001E7A8 File Offset: 0x0001C9A8
		public string D5_subfix
		{
			get
			{
				return this._d5_subfix;
			}
			set
			{
				this._d5_subfix = value;
			}
		}

		// Token: 0x170000E4 RID: 228
		// (get) Token: 0x06000294 RID: 660 RVA: 0x0001E7B1 File Offset: 0x0001C9B1
		// (set) Token: 0x06000295 RID: 661 RVA: 0x0001E7B9 File Offset: 0x0001C9B9
		public bool CloseConnection
		{
			get
			{
				return this._closeConnection;
			}
			set
			{
				this._closeConnection = value;
			}
		}

		// Token: 0x170000E5 RID: 229
		// (get) Token: 0x06000296 RID: 662 RVA: 0x0001E7C2 File Offset: 0x0001C9C2
		// (set) Token: 0x06000297 RID: 663 RVA: 0x0001E7CA File Offset: 0x0001C9CA
		public string Message
		{
			get
			{
				return this._message;
			}
			set
			{
				this._message = value;
			}
		}

		// Token: 0x0400015A RID: 346
		private string _currentStatusID = Guid.NewGuid().ToString();

		// Token: 0x0400015B RID: 347
		private long _currentBytesTransfered;

		// Token: 0x0400015C RID: 348
		private long _totalBytes;

		// Token: 0x0400015D RID: 349
		private long _fileSize;

		// Token: 0x0400015E RID: 350
		private string _tempFileName = string.Empty;

		// Token: 0x0400015F RID: 351
		private string _fileName = string.Empty;

		// Token: 0x04000160 RID: 352
		private string _folderNameRule = string.Empty;

		// Token: 0x04000161 RID: 353
		private string _fileNameRule = string.Empty;

		// Token: 0x04000162 RID: 354
		private string _fileNameRuleEx = string.Empty;

		// Token: 0x04000163 RID: 355
		private string _d5_prefix = string.Empty;

		// Token: 0x04000164 RID: 356
		private string _d5_subfix = string.Empty;

		// Token: 0x04000165 RID: 357
		private string _message = string.Empty;

		// Token: 0x04000166 RID: 358
		private bool _closeConnection;
	}
}

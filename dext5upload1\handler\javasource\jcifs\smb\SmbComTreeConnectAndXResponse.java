package jcifs.smb;

import java.io.UnsupportedEncodingException;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComTreeConnectAndXResponse.class */
class SmbComTreeConnectAndXResponse extends AndXServerMessageBlock {
    private static final int SMB_SUPPORT_SEARCH_BITS = 1;
    private static final int SMB_SHARE_IS_IN_DFS = 2;
    boolean supportSearchBits;
    boolean shareIsInDfs;
    String service;
    String nativeFileSystem;

    SmbComTreeConnectAndXResponse(ServerMessageBlock andx) {
        super(andx);
        this.nativeFileSystem = "";
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        this.supportSearchBits = (buffer[bufferIndex] & 1) == 1;
        this.shareIsInDfs = (buffer[bufferIndex] & 2) == 2;
        return 2;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        int len = readStringLength(buffer, bufferIndex, 32);
        try {
            this.service = new String(buffer, bufferIndex, len, "ASCII");
            return (bufferIndex + (len + 1)) - bufferIndex;
        } catch (UnsupportedEncodingException e) {
            return 0;
        }
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        String result = new String("SmbComTreeConnectAndXResponse[" + super.toString() + ",supportSearchBits=" + this.supportSearchBits + ",shareIsInDfs=" + this.shareIsInDfs + ",service=" + this.service + ",nativeFileSystem=" + this.nativeFileSystem + "]");
        return result;
    }
}

﻿using System;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000015 RID: 21
	public sealed class ExifTag
	{
		// Token: 0x170000BE RID: 190
		// (get) Token: 0x06000202 RID: 514 RVA: 0x0001304A File Offset: 0x0001124A
		public int Id
		{
			get
			{
				return this._id;
			}
		}

		// Token: 0x170000BF RID: 191
		// (get) Token: 0x06000203 RID: 515 RVA: 0x00013052 File Offset: 0x00011252
		public string Description
		{
			get
			{
				return this._description;
			}
		}

		// Token: 0x170000C0 RID: 192
		// (get) Token: 0x06000204 RID: 516 RVA: 0x0001305A File Offset: 0x0001125A
		public string FieldName
		{
			get
			{
				return this._fieldName;
			}
		}

		// Token: 0x170000C1 RID: 193
		// (get) Token: 0x06000205 RID: 517 RVA: 0x00013062 File Offset: 0x00011262
		// (set) Token: 0x06000206 RID: 518 RVA: 0x0001306A File Offset: 0x0001126A
		public string Value
		{
			get
			{
				return this._value;
			}
			set
			{
				this._value = value;
			}
		}

		// Token: 0x06000207 RID: 519 RVA: 0x00013073 File Offset: 0x00011273
		public ExifTag(int id, string fieldName, string description)
		{
			this._id = id;
			this._description = description;
			this._fieldName = fieldName;
		}

		// Token: 0x06000208 RID: 520 RVA: 0x00013090 File Offset: 0x00011290
		public override string ToString()
		{
			return string.Format("{0} ({1}) = {2}", this.Description, this.FieldName, this.Value);
		}

		// Token: 0x0400011A RID: 282
		private int _id;

		// Token: 0x0400011B RID: 283
		private string _description;

		// Token: 0x0400011C RID: 284
		private string _fieldName;

		// Token: 0x0400011D RID: 285
		private string _value;
	}
}

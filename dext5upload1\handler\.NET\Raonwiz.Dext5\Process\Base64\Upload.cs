﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Base64
{
	// Token: 0x0200001B RID: 27
	public class Upload : Base
	{
		// Token: 0x06000223 RID: 547 RVA: 0x0001590C File Offset: 0x00013B0C
		public Upload(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
		}

		// Token: 0x06000224 RID: 548 RVA: 0x00015974 File Offset: 0x00013B74
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string path = string.Empty;
			string text = string.Empty;
			string empty = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string text4 = string.Empty;
			string text5 = string.Empty;
			string text6 = string.Empty;
			string text7 = string.Empty;
			string str = string.Empty;
			char directorySeparatorChar = Path.DirectorySeparatorChar;
			string text8 = this.hContext.Request["serverdomain"];
			string text9 = this.hContext.Request["cd"];
			string text10 = this.hContext.Request["fileName"];
			string text11 = this.hContext.Request["GUID"];
			string text12 = this.hContext.Request["folderNameRule"];
			string text13 = this.hContext.Request["fileNameRule"];
			string text14 = this.hContext.Request["fileNameRuleEx"];
			string text15 = this.hContext.Request["imagedata"];
			string text16 = this.hContext.Request["uo"];
			string text17 = this.hContext.Request["oUrl"];
			if (text10 != null)
			{
				text10 = HttpUtility.UrlDecode(text10, Encoding.UTF8);
			}
			if (text17 != null)
			{
				text17 = HttpUtility.UrlDecode(text17, Encoding.UTF8);
			}
			try
			{
				if (text16 != null && text16.Equals("1"))
				{
					int num = text17.LastIndexOf('/');
					string text18 = text17.Substring(0, num);
					string text19 = text17.Substring(num + 1);
					string text20 = "";
					if (text19.IndexOf('?') > -1)
					{
						text20 = text19.Substring(text19.IndexOf('?'));
						text19 = text19.Substring(0, text19.IndexOf('?'));
					}
					text4 = this.hContext.Request.MapPath(text18) + directorySeparatorChar + text19;
					text8 = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Host;
					if (this.hContext.Request.Url.Port != 80)
					{
						text8 = text8 + ":" + this.hContext.Request.Url.Port;
					}
					text7 = string.Concat(new string[]
					{
						text8,
						text18,
						"/",
						text19,
						text20
					});
					text = text19;
					text6 = Path.GetExtension(text19);
				}
				else
				{
					string text21 = this.hContext.Request.ApplicationPath;
					text21 = text21.TrimEnd(new char[]
					{
						'/'
					});
					string text22 = string.Empty;
					if (!string.IsNullOrEmpty(this.virtualPath))
					{
						text2 = this.virtualPath;
						if (text2.Equals("/"))
						{
							text2 = "";
						}
						if (!text2.StartsWith("/") || text2.Equals(""))
						{
							text2 = text21 + "/" + text2;
						}
						if (!text2.EndsWith("/"))
						{
							text2 += "/";
						}
						text3 = this.hContext.Request.MapPath(text2);
					}
					if (!string.IsNullOrEmpty(this.physicalPath))
					{
						text3 = this.physicalPath;
					}
					if (!string.IsNullOrEmpty(text12) && text22.IndexOf(directorySeparatorChar) < 0 && text12.IndexOf(':') < 0 && text12.IndexOf('*') < 0 && text12.IndexOf('?') < 0 && text12.IndexOf('"') < 0 && text12.IndexOf('<') < 0 && text12.IndexOf('>') < 0 && text12.IndexOf('|') < 0)
					{
						if (text12.IndexOf("/") > -1)
						{
							string text23 = string.Empty;
							foreach (string text24 in text12.Split(new char[]
							{
								'/'
							}))
							{
								if (!string.IsNullOrEmpty(text24))
								{
									string text25 = text24.ToLower();
									if (text25.Equals("yyyy") || text25.Equals("mm") || text25.Equals("dd") || text25.Equals("yyyymm") || text25.Equals("yyyymmdd"))
									{
										string a;
										if ((a = text25) != null)
										{
											if (!(a == "yyyy"))
											{
												if (!(a == "mm"))
												{
													if (!(a == "dd"))
													{
														if (!(a == "yyyymm"))
														{
															if (a == "yyyymmdd")
															{
																text23 = text23 + DateTime.Now.ToString("yyyyMMdd") + directorySeparatorChar;
															}
														}
														else
														{
															text23 = text23 + DateTime.Now.ToString("yyyyMM") + directorySeparatorChar;
														}
													}
													else
													{
														text23 = text23 + DateTime.Now.ToString("dd") + directorySeparatorChar;
													}
												}
												else
												{
													text23 = text23 + DateTime.Now.ToString("MM") + directorySeparatorChar;
												}
											}
											else
											{
												text23 = text23 + DateTime.Now.ToString("yyyy") + directorySeparatorChar;
											}
										}
									}
									else
									{
										text23 = text23 + text24 + directorySeparatorChar;
									}
								}
							}
							text22 = text23;
						}
						else
						{
							text22 = text12 + directorySeparatorChar;
						}
					}
					if (!string.IsNullOrEmpty(this.virtualPath))
					{
						text2 += text22.Replace(directorySeparatorChar, '/');
					}
					text3 += text22;
					if (text8.Equals(""))
					{
						text8 = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Host;
						if (this.hContext.Request.Url.Port != 80)
						{
							text8 = text8 + ":" + this.hContext.Request.Url.Port;
						}
					}
					else if (text8.Equals("/"))
					{
						text8 = "";
					}
					if (!string.IsNullOrEmpty(text10))
					{
						text10 = text10.Normalize(NormalizationForm.FormC);
					}
					string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(text10);
					text5 = fileNameWithoutExtension;
					if (text13.Equals("GUID"))
					{
						text5 = text11;
					}
					text6 = Path.GetExtension(text10);
					if (!Directory.Exists(text3))
					{
						base.CreateFolder(text3);
					}
					text4 = text3 + text5 + text6;
					if (File.Exists(text4))
					{
						if (!string.IsNullOrEmpty(text14))
						{
							string text26 = text14;
							if (text26.Equals("i"))
							{
								path = text3 + text5 + text6;
								int num2 = 0;
								while (File.Exists(path))
								{
									num2++;
									text26 = " (" + num2 + ")";
									path = text3 + text5 + text26 + text6;
								}
							}
							else
							{
								path = text3 + text5 + text26 + text6;
								while (File.Exists(path))
								{
									text26 += text14;
									path = text3 + text5 + text26 + text6;
								}
							}
							text = text5 + text26 + text6;
							text4 = text3 + text;
							text5 += text26;
						}
						else
						{
							File.Delete(text4);
						}
					}
					if (text2.Equals(""))
					{
						text2 = "/";
					}
					text7 = text8 + text2 + text5 + text6;
					text = text5 + text6;
				}
				byte[] array2 = null;
				try
				{
					if (text15 != null && text15.Length > 0)
					{
						array2 = Convert.FromBase64String(text15);
					}
					else
					{
						Stream inputStream = this.hContext.Request.Files["blobdata"].InputStream;
						array2 = new byte[inputStream.Length];
						inputStream.Read(array2, 0, Convert.ToInt32(inputStream.Length));
					}
					if (array2.Length > 0)
					{
						MemoryStream memoryStream = new MemoryStream(array2);
						MemoryStream memoryStream2 = new MemoryStream();
						try
						{
							Image image = Image.FromStream(memoryStream);
							string key;
							if ((key = text6.ToLower()) != null)
							{
								if (<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000221-1 == null)
								{
									<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000221-1 = new Dictionary<string, int>(7)
									{
										{
											".bmp",
											0
										},
										{
											".rle",
											1
										},
										{
											".jpeg",
											2
										},
										{
											".jpg",
											3
										},
										{
											".gif",
											4
										},
										{
											".ttif",
											5
										},
										{
											".tif",
											6
										}
									};
								}
								int num3;
								if (<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000221-1.TryGetValue(key, out num3))
								{
									switch (num3)
									{
									case 0:
									case 1:
										image.Save(memoryStream2, ImageFormat.Bmp);
										goto IL_9D8;
									case 2:
									case 3:
										try
										{
											int width = image.Width;
											int height = image.Height;
											Bitmap bitmap = new Bitmap(width, height);
											Graphics graphics = Graphics.FromImage(bitmap);
											graphics.CompositingQuality = CompositingQuality.HighQuality;
											graphics.SmoothingMode = SmoothingMode.HighQuality;
											graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
											Rectangle rect = new Rectangle(0, 0, width, height);
											graphics.DrawImage(image, rect);
											bitmap.Save(memoryStream2, ImageFormat.Jpeg);
											goto IL_9D8;
										}
										catch
										{
											image.Save(memoryStream2, ImageFormat.Jpeg);
											goto IL_9D8;
										}
										break;
									case 4:
										break;
									case 5:
									case 6:
										image.Save(memoryStream2, ImageFormat.Tiff);
										goto IL_9D8;
									default:
										goto IL_9CA;
									}
									image.Save(memoryStream2, ImageFormat.Gif);
									goto IL_9D8;
								}
							}
							IL_9CA:
							image.Save(memoryStream2, ImageFormat.Png);
							IL_9D8:
							array2 = memoryStream2.ToArray();
						}
						catch
						{
						}
						finally
						{
							memoryStream.Close();
							memoryStream2.Close();
						}
						File.WriteAllBytes(text4, array2);
					}
					string text27 = text7;
					try
					{
						pCompleteBeforeEvent(this.hContext, ref text4, ref text27, ref text, ref this._str_ResponseCustomValue);
					}
					catch
					{
					}
					string text28 = string.Empty;
					if (text9 != null && text9.Equals("1"))
					{
						text28 += "<html><head>";
						text28 += "<script type=\"text/javascript\">";
						text28 += "if (window.postMessage) {";
						text28 += "if (window.addEventListener) {";
						text28 += "window.addEventListener('message', function (e) {";
						text28 += "var sendUrl = e.origin;";
						text28 += "var data = document.body.innerHTML;";
						text28 += "e.source.postMessage(data, sendUrl);";
						text28 += "}, false);";
						text28 += "}";
						text28 += "else if (window.attachEvent) {";
						text28 += "window.attachEvent('onmessage', function (e) {";
						text28 += "var sendUrl = e.origin;";
						text28 += "var data = document.body.innerHTML;";
						text28 += "e.source.postMessage(data, sendUrl);";
						text28 += "});";
						text28 += "}";
						text28 += "}";
						text28 += "</script>";
						text28 += "</head>";
						text28 += "<body>";
						text28 += "{0}";
						text28 += "</body>";
						text28 += "</html>";
					}
					else
					{
						text28 = "{0}";
					}
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text28.Replace("{0}", text27));
					try
					{
						pCompleteEvent(this.hContext, text4, text27, text);
					}
					catch
					{
					}
				}
				catch (Exception ex)
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|002|Error occured on the server side during save image"));
					str = ex.Message;
				}
			}
			catch (Exception ex2)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|001|Complete error occured on the server side"));
				str = ex2.Message;
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("DEXT5 Photo Image Upload, " + str, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x06000225 RID: 549 RVA: 0x000166A0 File Offset: 0x000148A0
		private ImageCodecInfo GetEncoderInfo(string mimeType)
		{
			ImageCodecInfo[] imageEncoders = ImageCodecInfo.GetImageEncoders();
			for (int i = 0; i < imageEncoders.Length; i++)
			{
				if (imageEncoders[i].MimeType == mimeType)
				{
					return imageEncoders[i];
				}
			}
			return null;
		}

		// Token: 0x04000126 RID: 294
		private string physicalPath = string.Empty;

		// Token: 0x04000127 RID: 295
		private string virtualPath = string.Empty;

		// Token: 0x04000128 RID: 296
		private string fileWhiteList = string.Empty;

		// Token: 0x04000129 RID: 297
		private string fileBlackList = string.Empty;
	}
}

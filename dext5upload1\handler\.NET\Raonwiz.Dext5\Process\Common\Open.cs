﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000022 RID: 34
	public class Open : Download
	{
		// Token: 0x06000244 RID: 580 RVA: 0x0001C658 File Offset: 0x0001A858
		public Open(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pDownloadRootPath, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, pDownloadRootPath, pAllowExtensionSpecialSymbol)
		{
		}

		// Token: 0x06000245 RID: 581 RVA: 0x0001C678 File Offset: 0x0001A878
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			string fileVirtualPath = this._entity_dextParam.fileVirtualPath;
			string empty = string.Empty;
			string empty2 = string.Empty;
			if (!string.IsNullOrEmpty(fileVirtualPath))
			{
				string[] fileVirtualPathAry = this._entity_dextParam.fileVirtualPathAry;
				string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
				if (this._entity_dextParam.mode == "normal")
				{
					for (int i = 0; i < fileVirtualPathAry.Length; i++)
					{
						fileVirtualPathAry[i] = HttpUtility.UrlDecode(fileVirtualPathAry[i], Encoding.UTF8);
						fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
					}
				}
				List<string> list = new List<string>();
				List<string> list2 = new List<string>();
				string text = string.Empty;
				string empty3 = string.Empty;
				string str = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Authority;
				for (int j = 0; j < fileVirtualPathAry.Length; j++)
				{
					text = fileVirtualPathAry[j];
					if (!string.IsNullOrEmpty(this.downloadRootPath) && File.Exists(this.downloadRootPath + text))
					{
						list.Add(this.downloadRootPath + text);
						list2.Add(fileOrgNameAry[j]);
					}
					else if (File.Exists(text))
					{
						list.Add(text);
						list2.Add(fileOrgNameAry[j]);
					}
					else
					{
						string text2 = text.Replace("http://", "");
						text2 = text2.Replace("https://", "");
						text2 = text2.Substring(text2.IndexOf("/"));
						if (!string.IsNullOrEmpty(this.downloadRootPath))
						{
							text2 = this.hContext.Request.MapPath(this.downloadRootPath + text2);
						}
						else
						{
							text2 = this.hContext.Request.MapPath(text2);
						}
						if (File.Exists(text2))
						{
							list.Add(text2);
							list2.Add(fileOrgNameAry[j]);
						}
						else
						{
							if (this._b_useExternalDownload)
							{
								if (text.IndexOf("http://") == -1 && text.IndexOf("https://") == -1)
								{
									text = str + text.Substring(text.IndexOf("/"));
								}
								text = base.CheckExternalWebFile(text);
							}
							else
							{
								text = "";
							}
							list.Add(text);
							list2.Add(fileOrgNameAry[j]);
						}
					}
				}
				if (string.IsNullOrEmpty(empty2) && list.Count > 0)
				{
					string[] array = new string[list.Count];
					string[] array2 = new string[list2.Count];
					for (int k = 0; k < list.Count; k++)
					{
						array[k] = list[k];
					}
					for (int l = 0; l < list2.Count; l++)
					{
						array2[l] = list2[l];
					}
					string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
					try
					{
						UploadEventEntity uploadEventEntity = new UploadEventEntity();
						uploadEventEntity.Context = this.hContext;
						uploadEventEntity.DownloadFilePath = array;
						uploadEventEntity.DownloadFileName = array2;
						uploadEventEntity.DownloadCustomValue = requestValue;
						pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
						array = uploadEventEntity.DownloadFilePath;
						array2 = uploadEventEntity.DownloadFileName;
						for (int m = 0; m < array.Length; m++)
						{
							list[m] = array[m];
						}
						for (int n = 0; n < array2.Length; n++)
						{
							list2[n] = array2[n];
						}
					}
					catch
					{
					}
					if (pCustomError == null)
					{
						if (list.Count > 1)
						{
							string text3 = base.CompressionFiles(list, list2);
							if (string.IsNullOrEmpty(this.zipFileName))
							{
								this.zipFileName = Path.GetFileName(text3);
							}
							base.SendFileToClient("inline", text3, this.zipFileName, this.zipFileName, false, false);
						}
						else
						{
							string text4 = list[0].ToLower();
							string text5 = string.Empty;
							if (text4.StartsWith("http"))
							{
								text5 = base.DownloadOutsideUrlFile(list[0], list2[0], "inline", false);
							}
							else
							{
								text5 = list[0];
							}
							if (!string.IsNullOrEmpty(text5))
							{
								string headerFileName = base.GetHeaderFileName(list2[0]);
								base.SendFileToClient("inline", text5, list2[0], headerFileName, false, false);
							}
						}
						string viewerGUID = this._entity_dextParam.viewerGUID;
						if (!string.IsNullOrEmpty(viewerGUID))
						{
							UploadStatus uploadStatus = new UploadStatus();
							uploadStatus.Message = "ok";
							this.hContext.Application.Add(viewerGUID, uploadStatus);
						}
						for (int num = 0; num < list.Count; num++)
						{
							list[num] = list[num] + "\f" + list2[num];
						}
						return list;
					}
					string text6 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text6);
					return text6;
				}
			}
			return null;
		}
	}
}

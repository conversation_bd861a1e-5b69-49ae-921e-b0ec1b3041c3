package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/TransPeekNamedPipeResponse.class */
class TransPeekNamedPipeResponse extends SmbComTransactionResponse {
    private SmbNamedPipe pipe;
    private int head;
    static final int STATUS_DISCONNECTED = 1;
    static final int STATUS_LISTENING = 2;
    static final int STATUS_CONNECTION_OK = 3;
    static final int STATUS_SERVER_END_CLOSED = 4;
    int status;
    int available;

    TransPeekNamedPipeResponse(SmbNamedPipe pipe) {
        this.pipe = pipe;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        this.available = readInt2(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 2;
        this.head = readInt2(buffer, bufferIndex2);
        this.status = readInt2(buffer, bufferIndex2 + 2);
        return 6;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("TransPeekNamedPipeResponse[" + super.toString() + "]");
    }
}

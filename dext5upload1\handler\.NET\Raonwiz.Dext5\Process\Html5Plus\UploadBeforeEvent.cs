﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5Plus
{
	// Token: 0x02000033 RID: 51
	public class UploadBeforeEvent : Base
	{
		// Token: 0x0600030A RID: 778 RVA: 0x00021E54 File Offset: 0x00020054
		public UploadBeforeEvent(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pWhiteList, string pBlackList, string[] pBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.whiteList = pWhiteList;
			this.blackList = pBlackList;
			this.blackWordList = pBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x0600030B RID: 779 RVA: 0x00021ED8 File Offset: 0x000200D8
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			try
			{
				if (!base.CheckCaller("html5"))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|013|Bad Request Type"));
					return null;
				}
				if (!base.CheckFileExtension(this.whiteList, this.blackList, this.allowExtensionSpecialSymbol, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					return null;
				}
				if (!base.CheckBlackWord(this.blackWordList, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					return null;
				}
				this.tempPath = base.GetTempPath(this.tempPath);
				string text = this._entity_dextParam.fileName;
				text = text.Normalize(NormalizationForm.FormC);
				string folderNameRule = this._entity_dextParam.folderNameRule;
				string guid = this._entity_dextParam.GUID;
				string fileNameRule = this._entity_dextParam.fileNameRule;
				string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
				string filePrefix = this._entity_dextParam.filePrefix;
				string fileSubfix = this._entity_dextParam.fileSubfix;
				string text2 = base.GetTempFileFolder(this.tempPath, guid) + this.m_PathChar;
				new DirectoryInfo(text2);
				string str = guid;
				string extension = Path.GetExtension(text);
				text2 + str + extension;
				string fileLocationInfo = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
				string text3 = string.Empty;
				string text4 = string.Empty;
				if (string.IsNullOrEmpty(fileLocationInfo))
				{
					throw new Exception("Error occured on the server side");
				}
				if (fileLocationInfo.IndexOf("error") == 0)
				{
					string[] array = fileLocationInfo.Split(new char[]
					{
						'|'
					});
					throw new Exception(array[2]);
				}
				string[] array2 = fileLocationInfo.Split(new char[]
				{
					'|'
				});
				text3 = array2[0];
				text4 = array2[1];
				bool flag = false;
				string text5 = text3;
				try
				{
					pBeforeInitializeEvent(this.hContext, ref text3, ref text4);
					if (!text5.Equals(text3))
					{
						flag = true;
					}
				}
				catch
				{
				}
				try
				{
					UploadEventEntity uploadEventEntity = new UploadEventEntity();
					uploadEventEntity.Context = this.hContext;
					uploadEventEntity.NewFileLocation = text3;
					uploadEventEntity.ResponseFileName = text4;
					pBeforeInitializeEventEx(uploadEventEntity);
					text3 = uploadEventEntity.NewFileLocation;
					text4 = uploadEventEntity.ResponseFileName;
					if (!text5.Equals(text3))
					{
						flag = true;
					}
				}
				catch
				{
				}
				if (flag)
				{
					string[] array3 = base.InitializeEventFileExec(text3, text4);
					text3 = array3[0];
					text4 = array3[1];
				}
				string text6 = string.Empty;
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text6 = text3;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					string oldValue = HostingEnvironment.MapPath("~/");
					text6 = "/" + text3.Replace(oldValue, "");
					text6 = text6.Replace(this.m_PathChar, '/');
				}
				string text7 = "";
				if (this.blackWordList != null)
				{
					foreach (string text8 in this.blackWordList)
					{
						text7 += "\f";
					}
				}
				string text9 = string.Concat(new string[]
				{
					this.tempPath,
					"|",
					this.whiteList,
					"|",
					this.blackList,
					"|",
					text7,
					"|",
					text3,
					"|",
					text6,
					"|",
					text4
				});
				string text10 = text9;
				text9 = string.Concat(new string[]
				{
					text10,
					"|",
					this._entity_dextParam.fileGroupID,
					"|",
					this._entity_dextParam.fileIndex
				});
				text9 = Dext5Parameter.MakeParameter(text9);
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text9);
				if (pCustomError != null)
				{
					this.hContext.Response.Clear();
					text9 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
					text9 = Dext5Parameter.MakeParameter(text9);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text9);
				}
			}
			catch (Exception)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|022|Error occured on before upload"));
			}
			return null;
		}

		// Token: 0x04000193 RID: 403
		private string physicalPath = string.Empty;

		// Token: 0x04000194 RID: 404
		private string virtualPath = string.Empty;

		// Token: 0x04000195 RID: 405
		private string whiteList = string.Empty;

		// Token: 0x04000196 RID: 406
		private string blackList = string.Empty;

		// Token: 0x04000197 RID: 407
		private string[] blackWordList;

		// Token: 0x04000198 RID: 408
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

/*
Copyright (C) NAVER corp.  

This library is free software; you can redistribute it and/or  
modify it under the terms of the GNU Lesser General Public  
License as published by the Free Software Foundation; either  
version 2.1 of the License, or (at your option) any later version.  

This library is distributed in the hope that it will be useful,  
but WITHOUT ANY WARRANTY; without even the implied warranty of  
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU  
Lesser General Public License for more details.  

You should have received a copy of the GNU Lesser General Public  
License along with this library; if not, write to the Free Software  
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA  
*/
if(typeof window.nhn=='undefined'){window.nhn = {};}
/**
 * @fileOverview This file contains a message mapping(Korean), which is used to map the message code to the actual message
 * @name husky_SE2B_Lang_ja_JP.js
 * @ unescape
 */
var oMessageMap_ja_JP = {
	'SE_EditingAreaManager.onExit' : '内容が変更されました。',
	'SE_Color.invalidColorCode' : 'カラーコードを正しく入力してください。 \n\n 例) #000000, #FF0000, #FFFFFF, #ffffff, ffffff',
	'SE_Hyperlink.invalidURL' : '入力したURLが正しくありません。',
	'SE_FindReplace.keywordMissing' : 'お探しの単語を入力してください。',
	'SE_FindReplace.keywordNotFound' : 'お探しの単語がありません。',
	'SE_FindReplace.replaceAllResultP1' : '一致する内容が計',
	'SE_FindReplace.replaceAllResultP2' : '件変わりました。',
	'SE_FindReplace.notSupportedBrowser' : '現在ご使用中のブラウザーではご利用いただけない機能です。\n\nご不便をおかけしまして申し訳ございません。',
	'SE_FindReplace.replaceKeywordNotFound' : '変更される単語がありません。',
	'SE_LineHeight.invalidLineHeight' : '誤った値です。',
	'SE_Footnote.defaultText' : '脚注内容を入力してください。',
	'SE.failedToLoadFlash' : 'フラッシュが遮断されているため、この機能は使用できません。',
	'SE2M_EditingModeChanger.confirmTextMode' : 'テキストモードに切り換えると、作成された内容は維持されますが、\n\nフォント等の編集効果と画像等の添付内容が消えることになります。\n\n切り換えますか？',
	'SE2M_FontNameWithLayerUI.sSampleText' : 'あいうえお'
};
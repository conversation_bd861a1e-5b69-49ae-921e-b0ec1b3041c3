﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using Ionic.Zip;
using Ionic.Zlib;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000021 RID: 33
	public class MakeZip : Base
	{
		// Token: 0x0600023E RID: 574 RVA: 0x0001955C File Offset: 0x0001775C
		public MakeZip(HttpContext context, string pTempPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pDownloadRootPath, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.downloadRootPath = pDownloadRootPath;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x0600023F RID: 575 RVA: 0x000195D8 File Offset: 0x000177D8
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			string text = this._entity_dextParam.zipFileName;
			string fileVirtualPath = this._entity_dextParam.fileVirtualPath;
			string empty = string.Empty;
			string empty2 = string.Empty;
			if (string.IsNullOrEmpty(fileVirtualPath) && string.IsNullOrEmpty(text))
			{
				string text2 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text2 += "<html><head>";
					text2 += "<script type=\"text/javascript\">";
					text2 += "if (window.postMessage) {";
					text2 += "if (window.addEventListener) {";
					text2 += "window.addEventListener('message', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "}, false);";
					text2 += "}";
					text2 += "else if (window.attachEvent) {";
					text2 += "window.attachEvent('onmessage', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "});";
					text2 += "}";
					text2 += "}";
					text2 += "</script>";
					text2 += "</head>";
					text2 += "<body>";
					text2 += "{0}";
					text2 += "</body>";
					text2 += "</html>";
				}
				else
				{
					text2 = "{0}";
				}
				text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text2);
				return "error|009|Invalid parameter on server";
			}
			string[] fileVirtualPathAry = this._entity_dextParam.fileVirtualPathAry;
			string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
			if (this._entity_dextParam.mode == "normal")
			{
				for (int i = 0; i < fileVirtualPathAry.Length; i++)
				{
					fileVirtualPathAry[i] = HttpUtility.UrlDecode(fileVirtualPathAry[i], Encoding.UTF8);
					fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
				}
			}
			List<string> list = new List<string>();
			List<string> list2 = new List<string>();
			string text3 = string.Empty;
			string empty3 = string.Empty;
			string str = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Authority;
			for (int j = 0; j < fileVirtualPathAry.Length; j++)
			{
				text3 = fileVirtualPathAry[j];
				if (!string.IsNullOrEmpty(this.downloadRootPath) && File.Exists(this.downloadRootPath + text3))
				{
					list.Add(this.downloadRootPath + text3);
					list2.Add(fileOrgNameAry[j]);
				}
				else if (File.Exists(text3))
				{
					list.Add(text3);
					list2.Add(fileOrgNameAry[j]);
				}
				else
				{
					string text4 = text3.Replace("http://", "");
					text4 = text4.Replace("https://", "");
					text4 = text4.Substring(text4.IndexOf("/"));
					if (!string.IsNullOrEmpty(this.downloadRootPath))
					{
						text4 = this.hContext.Request.MapPath(this.downloadRootPath + text4);
					}
					else
					{
						text4 = this.hContext.Request.MapPath(text4);
					}
					if (File.Exists(text4))
					{
						list.Add(text4);
						list2.Add(fileOrgNameAry[j]);
					}
					else
					{
						if (this._b_useExternalDownload)
						{
							if (text3.IndexOf("http://") == -1 && text3.IndexOf("https://") == -1)
							{
								text3 = str + text3.Substring(text3.IndexOf("/"));
							}
							text3 = base.CheckExternalWebFile(text3);
						}
						else
						{
							text3 = "";
						}
						if (string.IsNullOrEmpty(text3))
						{
							string text5 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text5 += "<html><head>";
								text5 += "<script type=\"text/javascript\">";
								text5 += "if (window.postMessage) {";
								text5 += "if (window.addEventListener) {";
								text5 += "window.addEventListener('message', function (e) {";
								text5 += "var sendUrl = e.origin;";
								text5 += "var data = document.body.innerHTML;";
								text5 += "e.source.postMessage(data, sendUrl);";
								text5 += "}, false);";
								text5 += "}";
								text5 += "else if (window.attachEvent) {";
								text5 += "window.attachEvent('onmessage', function (e) {";
								text5 += "var sendUrl = e.origin;";
								text5 += "var data = document.body.innerHTML;";
								text5 += "e.source.postMessage(data, sendUrl);";
								text5 += "});";
								text5 += "}";
								text5 += "}";
								text5 += "</script>";
								text5 += "</head>";
								text5 += "<body>";
								text5 += "{0}";
								text5 += "</body>";
								text5 += "</html>";
							}
							else
							{
								text5 = "{0}";
							}
							text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text5);
							return "error|010|Not found file on server";
						}
						list.Add(text3);
						list2.Add(fileOrgNameAry[j]);
					}
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list2[j]))
				{
					string text6 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text6 += "<html><head>";
						text6 += "<script type=\"text/javascript\">";
						text6 += "if (window.postMessage) {";
						text6 += "if (window.addEventListener) {";
						text6 += "window.addEventListener('message', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "}, false);";
						text6 += "}";
						text6 += "else if (window.attachEvent) {";
						text6 += "window.attachEvent('onmessage', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "});";
						text6 += "}";
						text6 += "}";
						text6 += "</script>";
						text6 += "</head>";
						text6 += "<body>";
						text6 += "{0}";
						text6 += "</body>";
						text6 += "</html>";
					}
					else
					{
						text6 = "{0}";
					}
					text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text6);
					return "error|012|Not allowed file extension";
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, list2[j]))
				{
					string text7 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text7 += "<html><head>";
						text7 += "<script type=\"text/javascript\">";
						text7 += "if (window.postMessage) {";
						text7 += "if (window.addEventListener) {";
						text7 += "window.addEventListener('message', function (e) {";
						text7 += "var sendUrl = e.origin;";
						text7 += "var data = document.body.innerHTML;";
						text7 += "e.source.postMessage(data, sendUrl);";
						text7 += "}, false);";
						text7 += "}";
						text7 += "else if (window.attachEvent) {";
						text7 += "window.attachEvent('onmessage', function (e) {";
						text7 += "var sendUrl = e.origin;";
						text7 += "var data = document.body.innerHTML;";
						text7 += "e.source.postMessage(data, sendUrl);";
						text7 += "});";
						text7 += "}";
						text7 += "}";
						text7 += "</script>";
						text7 += "</head>";
						text7 += "<body>";
						text7 += "{0}";
						text7 += "</body>";
						text7 += "</html>";
					}
					else
					{
						text7 = "{0}";
					}
					text7 = text7.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text7);
					return "error|018|There does not allow the string included in file name";
				}
			}
			if (!string.IsNullOrEmpty(empty2))
			{
				return null;
			}
			string[] array = new string[list.Count];
			string[] array2 = new string[list2.Count];
			for (int k = 0; k < list.Count; k++)
			{
				array[k] = list[k];
			}
			for (int l = 0; l < list2.Count; l++)
			{
				array2[l] = list2[l];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array;
				uploadEventEntity.DownloadFileName = array2;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				array = uploadEventEntity.DownloadFilePath;
				array2 = uploadEventEntity.DownloadFileName;
				for (int m = 0; m < array.Length; m++)
				{
					list[m] = array[m];
				}
				for (int n = 0; n < array2.Length; n++)
				{
					list2[n] = array2[n];
				}
			}
			catch
			{
			}
			if (pCustomError != null)
			{
				string text8 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text8 += "<html><head>";
					text8 += "<script type=\"text/javascript\">";
					text8 += "if (window.postMessage) {";
					text8 += "if (window.addEventListener) {";
					text8 += "window.addEventListener('message', function (e) {";
					text8 += "var sendUrl = e.origin;";
					text8 += "var data = document.body.innerHTML;";
					text8 += "e.source.postMessage(data, sendUrl);";
					text8 += "}, false);";
					text8 += "}";
					text8 += "else if (window.attachEvent) {";
					text8 += "window.attachEvent('onmessage', function (e) {";
					text8 += "var sendUrl = e.origin;";
					text8 += "var data = document.body.innerHTML;";
					text8 += "e.source.postMessage(data, sendUrl);";
					text8 += "});";
					text8 += "}";
					text8 += "}";
					text8 += "</script>";
					text8 += "</head>";
					text8 += "<body>";
					text8 += "{0}";
					text8 += "</body>";
					text8 += "</html>";
				}
				else
				{
					text8 = "{0}";
				}
				string text9 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
				text8 = text8.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text9));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text8);
				return text9;
			}
			if (list.Count > 1)
			{
				this.CompressionFiles(text, list, list2);
				for (int num = 0; num < list.Count; num++)
				{
					list[num] = list[num] + "\f" + list2[num];
				}
				return list;
			}
			string text10 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text10 += "<html><head>";
				text10 += "<script type=\"text/javascript\">";
				text10 += "if (window.postMessage) {";
				text10 += "if (window.addEventListener) {";
				text10 += "window.addEventListener('message', function (e) {";
				text10 += "var sendUrl = e.origin;";
				text10 += "var data = document.body.innerHTML;";
				text10 += "e.source.postMessage(data, sendUrl);";
				text10 += "}, false);";
				text10 += "}";
				text10 += "else if (window.attachEvent) {";
				text10 += "window.attachEvent('onmessage', function (e) {";
				text10 += "var sendUrl = e.origin;";
				text10 += "var data = document.body.innerHTML;";
				text10 += "e.source.postMessage(data, sendUrl);";
				text10 += "});";
				text10 += "}";
				text10 += "}";
				text10 += "</script>";
				text10 += "</head>";
				text10 += "<body>";
				text10 += "{0}";
				text10 += "</body>";
				text10 += "</html>";
			}
			else
			{
				text10 = "{0}";
			}
			text10 = text10.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text10);
			return "error|010|Not found file on server";
		}

		// Token: 0x06000240 RID: 576 RVA: 0x0001A4DC File Offset: 0x000186DC
		public object Run2(UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerDelegate pCompleteEvent, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError, List<string> downloadList)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			string text = this._entity_dextParam.zipFileName;
			string empty = string.Empty;
			string empty2 = string.Empty;
			if (string.IsNullOrEmpty(text))
			{
				string text2 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text2 += "<html><head>";
					text2 += "<script type=\"text/javascript\">";
					text2 += "if (window.postMessage) {";
					text2 += "if (window.addEventListener) {";
					text2 += "window.addEventListener('message', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "}, false);";
					text2 += "}";
					text2 += "else if (window.attachEvent) {";
					text2 += "window.attachEvent('onmessage', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "});";
					text2 += "}";
					text2 += "}";
					text2 += "</script>";
					text2 += "</head>";
					text2 += "<body>";
					text2 += "{0}";
					text2 += "</body>";
					text2 += "</html>";
				}
				else
				{
					text2 = "{0}";
				}
				text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text2);
				return "error|009|Invalid parameter on server";
			}
			string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
			if (!base.IsMobile())
			{
				for (int i = 0; i < fileOrgNameAry.Length; i++)
				{
					fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
				}
			}
			if (fileOrgNameAry.Length != downloadList.Count)
			{
				string text3 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text3 += "<html><head>";
					text3 += "<script type=\"text/javascript\">";
					text3 += "if (window.postMessage) {";
					text3 += "if (window.addEventListener) {";
					text3 += "window.addEventListener('message', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "}, false);";
					text3 += "}";
					text3 += "else if (window.attachEvent) {";
					text3 += "window.attachEvent('onmessage', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "});";
					text3 += "}";
					text3 += "}";
					text3 += "</script>";
					text3 += "</head>";
					text3 += "<body>";
					text3 += "{0}";
					text3 += "</body>";
					text3 += "</html>";
				}
				else
				{
					text3 = "{0}";
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				text3 = text3.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text3);
				return "error|009|Invalid parameter on server";
			}
			List<string> list = new List<string>();
			string empty3 = string.Empty;
			string empty4 = string.Empty;
			for (int j = 0; j < downloadList.Count; j++)
			{
				if (!File.Exists(downloadList[j]))
				{
					string text4 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text4 += "<html><head>";
						text4 += "<script type=\"text/javascript\">";
						text4 += "if (window.postMessage) {";
						text4 += "if (window.addEventListener) {";
						text4 += "window.addEventListener('message', function (e) {";
						text4 += "var sendUrl = e.origin;";
						text4 += "var data = document.body.innerHTML;";
						text4 += "e.source.postMessage(data, sendUrl);";
						text4 += "}, false);";
						text4 += "}";
						text4 += "else if (window.attachEvent) {";
						text4 += "window.attachEvent('onmessage', function (e) {";
						text4 += "var sendUrl = e.origin;";
						text4 += "var data = document.body.innerHTML;";
						text4 += "e.source.postMessage(data, sendUrl);";
						text4 += "});";
						text4 += "}";
						text4 += "}";
						text4 += "</script>";
						text4 += "</head>";
						text4 += "<body>";
						text4 += "{0}";
						text4 += "</body>";
						text4 += "</html>";
					}
					else
					{
						text4 = "{0}";
					}
					text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text4);
					return "error|010|Not found file on server";
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, downloadList[j]))
				{
					string text5 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text5 += "<html><head>";
						text5 += "<script type=\"text/javascript\">";
						text5 += "if (window.postMessage) {";
						text5 += "if (window.addEventListener) {";
						text5 += "window.addEventListener('message', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "}, false);";
						text5 += "}";
						text5 += "else if (window.attachEvent) {";
						text5 += "window.attachEvent('onmessage', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "});";
						text5 += "}";
						text5 += "}";
						text5 += "</script>";
						text5 += "</head>";
						text5 += "<body>";
						text5 += "{0}";
						text5 += "</body>";
						text5 += "</html>";
					}
					else
					{
						text5 = "{0}";
					}
					text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text5);
					return "error|012|Not allowed file extension";
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, downloadList[j]))
				{
					string text6 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text6 += "<html><head>";
						text6 += "<script type=\"text/javascript\">";
						text6 += "if (window.postMessage) {";
						text6 += "if (window.addEventListener) {";
						text6 += "window.addEventListener('message', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "}, false);";
						text6 += "}";
						text6 += "else if (window.attachEvent) {";
						text6 += "window.attachEvent('onmessage', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "});";
						text6 += "}";
						text6 += "}";
						text6 += "</script>";
						text6 += "</head>";
						text6 += "<body>";
						text6 += "{0}";
						text6 += "</body>";
						text6 += "</html>";
					}
					else
					{
						text6 = "{0}";
					}
					text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text6);
					return "error|018|There does not allow the string included in file name";
				}
				list.Add(fileOrgNameAry[j]);
			}
			if (!string.IsNullOrEmpty(empty2))
			{
				return null;
			}
			if (downloadList.Count <= 1)
			{
				string text7 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text7 += "<html><head>";
					text7 += "<script type=\"text/javascript\">";
					text7 += "if (window.postMessage) {";
					text7 += "if (window.addEventListener) {";
					text7 += "window.addEventListener('message', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "}, false);";
					text7 += "}";
					text7 += "else if (window.attachEvent) {";
					text7 += "window.attachEvent('onmessage', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "});";
					text7 += "}";
					text7 += "}";
					text7 += "</script>";
					text7 += "</head>";
					text7 += "<body>";
					text7 += "{0}";
					text7 += "</body>";
					text7 += "</html>";
				}
				else
				{
					text7 = "{0}";
				}
				text7 = text7.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text7);
				return "error|010|Not found file on server";
			}
			string[] array = new string[downloadList.Count];
			string[] array2 = new string[list.Count];
			for (int k = 0; k < downloadList.Count; k++)
			{
				array[k] = downloadList[k];
			}
			for (int l = 0; l < list.Count; l++)
			{
				array2[l] = list[l];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array;
				uploadEventEntity.DownloadFileName = array2;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				array = uploadEventEntity.DownloadFilePath;
				array2 = uploadEventEntity.DownloadFileName;
				for (int m = 0; m < array.Length; m++)
				{
					downloadList[m] = array[m];
				}
				for (int n = 0; n < array2.Length; n++)
				{
					list[n] = array2[n];
				}
			}
			catch
			{
			}
			if (pCustomError == null)
			{
				this.CompressionFiles(text, downloadList, list);
				for (int num = 0; num < downloadList.Count; num++)
				{
					downloadList[num] = downloadList[num] + "\f" + list[num];
				}
				return downloadList;
			}
			string text8 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text8 += "<html><head>";
				text8 += "<script type=\"text/javascript\">";
				text8 += "if (window.postMessage) {";
				text8 += "if (window.addEventListener) {";
				text8 += "window.addEventListener('message', function (e) {";
				text8 += "var sendUrl = e.origin;";
				text8 += "var data = document.body.innerHTML;";
				text8 += "e.source.postMessage(data, sendUrl);";
				text8 += "}, false);";
				text8 += "}";
				text8 += "else if (window.attachEvent) {";
				text8 += "window.attachEvent('onmessage', function (e) {";
				text8 += "var sendUrl = e.origin;";
				text8 += "var data = document.body.innerHTML;";
				text8 += "e.source.postMessage(data, sendUrl);";
				text8 += "});";
				text8 += "}";
				text8 += "}";
				text8 += "</script>";
				text8 += "</head>";
				text8 += "<body>";
				text8 += "{0}";
				text8 += "</body>";
				text8 += "</html>";
			}
			else
			{
				text8 = "{0}";
			}
			string text9 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			text8 = text8.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text9));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text8);
			return text9;
		}

		// Token: 0x06000241 RID: 577 RVA: 0x0001B3E0 File Offset: 0x000195E0
		public object Run2(UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerDelegate pCompleteEvent, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError, List<Stream> downloadList)
		{
			this.tempPath = base.GetTempPath(this.tempPath);
			string text = this._entity_dextParam.zipFileName;
			string empty = string.Empty;
			string empty2 = string.Empty;
			if (string.IsNullOrEmpty(text))
			{
				string text2 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text2 += "<html><head>";
					text2 += "<script type=\"text/javascript\">";
					text2 += "if (window.postMessage) {";
					text2 += "if (window.addEventListener) {";
					text2 += "window.addEventListener('message', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "}, false);";
					text2 += "}";
					text2 += "else if (window.attachEvent) {";
					text2 += "window.attachEvent('onmessage', function (e) {";
					text2 += "var sendUrl = e.origin;";
					text2 += "var data = document.body.innerHTML;";
					text2 += "e.source.postMessage(data, sendUrl);";
					text2 += "});";
					text2 += "}";
					text2 += "}";
					text2 += "</script>";
					text2 += "</head>";
					text2 += "<body>";
					text2 += "{0}";
					text2 += "</body>";
					text2 += "</html>";
				}
				else
				{
					text2 = "{0}";
				}
				text2 = text2.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text2);
				return "error|009|Invalid parameter on server";
			}
			string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
			if (!base.IsMobile())
			{
				for (int i = 0; i < fileOrgNameAry.Length; i++)
				{
					fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
				}
			}
			if (fileOrgNameAry.Length != downloadList.Count)
			{
				string text3 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text3 += "<html><head>";
					text3 += "<script type=\"text/javascript\">";
					text3 += "if (window.postMessage) {";
					text3 += "if (window.addEventListener) {";
					text3 += "window.addEventListener('message', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "}, false);";
					text3 += "}";
					text3 += "else if (window.attachEvent) {";
					text3 += "window.attachEvent('onmessage', function (e) {";
					text3 += "var sendUrl = e.origin;";
					text3 += "var data = document.body.innerHTML;";
					text3 += "e.source.postMessage(data, sendUrl);";
					text3 += "});";
					text3 += "}";
					text3 += "}";
					text3 += "</script>";
					text3 += "</head>";
					text3 += "<body>";
					text3 += "{0}";
					text3 += "</body>";
					text3 += "</html>";
				}
				else
				{
					text3 = "{0}";
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
				}
				text3 = text3.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text3);
				return "error|009|Invalid parameter on server";
			}
			List<string> list = new List<string>();
			string empty3 = string.Empty;
			string empty4 = string.Empty;
			for (int j = 0; j < downloadList.Count; j++)
			{
				if (downloadList == null || downloadList.Count <= 0)
				{
					string text4 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text4 += "<html><head>";
						text4 += "<script type=\"text/javascript\">";
						text4 += "if (window.postMessage) {";
						text4 += "if (window.addEventListener) {";
						text4 += "window.addEventListener('message', function (e) {";
						text4 += "var sendUrl = e.origin;";
						text4 += "var data = document.body.innerHTML;";
						text4 += "e.source.postMessage(data, sendUrl);";
						text4 += "}, false);";
						text4 += "}";
						text4 += "else if (window.attachEvent) {";
						text4 += "window.attachEvent('onmessage', function (e) {";
						text4 += "var sendUrl = e.origin;";
						text4 += "var data = document.body.innerHTML;";
						text4 += "e.source.postMessage(data, sendUrl);";
						text4 += "});";
						text4 += "}";
						text4 += "}";
						text4 += "</script>";
						text4 += "</head>";
						text4 += "<body>";
						text4 += "{0}";
						text4 += "</body>";
						text4 += "</html>";
					}
					else
					{
						text4 = "{0}";
					}
					text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text4);
					return "error|010|Not found file on server";
				}
				list.Add(fileOrgNameAry[j]);
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list[j]))
				{
					string text5 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text5 += "<html><head>";
						text5 += "<script type=\"text/javascript\">";
						text5 += "if (window.postMessage) {";
						text5 += "if (window.addEventListener) {";
						text5 += "window.addEventListener('message', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "}, false);";
						text5 += "}";
						text5 += "else if (window.attachEvent) {";
						text5 += "window.attachEvent('onmessage', function (e) {";
						text5 += "var sendUrl = e.origin;";
						text5 += "var data = document.body.innerHTML;";
						text5 += "e.source.postMessage(data, sendUrl);";
						text5 += "});";
						text5 += "}";
						text5 += "}";
						text5 += "</script>";
						text5 += "</head>";
						text5 += "<body>";
						text5 += "{0}";
						text5 += "</body>";
						text5 += "</html>";
					}
					else
					{
						text5 = "{0}";
					}
					text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text5);
					return "error|012|Not allowed file extension";
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, list[j]))
				{
					string text6 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text6 += "<html><head>";
						text6 += "<script type=\"text/javascript\">";
						text6 += "if (window.postMessage) {";
						text6 += "if (window.addEventListener) {";
						text6 += "window.addEventListener('message', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "}, false);";
						text6 += "}";
						text6 += "else if (window.attachEvent) {";
						text6 += "window.attachEvent('onmessage', function (e) {";
						text6 += "var sendUrl = e.origin;";
						text6 += "var data = document.body.innerHTML;";
						text6 += "e.source.postMessage(data, sendUrl);";
						text6 += "});";
						text6 += "}";
						text6 += "}";
						text6 += "</script>";
						text6 += "</head>";
						text6 += "<body>";
						text6 += "{0}";
						text6 += "</body>";
						text6 += "</html>";
					}
					else
					{
						text6 = "{0}";
					}
					text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text6);
					return "error|018|There does not allow the string included in file name";
				}
			}
			if (!string.IsNullOrEmpty(empty2))
			{
				return null;
			}
			if (downloadList == null || downloadList.Count <= 1)
			{
				string text7 = "";
				if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
				{
					text7 += "<html><head>";
					text7 += "<script type=\"text/javascript\">";
					text7 += "if (window.postMessage) {";
					text7 += "if (window.addEventListener) {";
					text7 += "window.addEventListener('message', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "}, false);";
					text7 += "}";
					text7 += "else if (window.attachEvent) {";
					text7 += "window.attachEvent('onmessage', function (e) {";
					text7 += "var sendUrl = e.origin;";
					text7 += "var data = document.body.innerHTML;";
					text7 += "e.source.postMessage(data, sendUrl);";
					text7 += "});";
					text7 += "}";
					text7 += "}";
					text7 += "</script>";
					text7 += "</head>";
					text7 += "<body>";
					text7 += "{0}";
					text7 += "</body>";
					text7 += "</html>";
				}
				else
				{
					text7 = "{0}";
				}
				text7 = text7.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text7);
				return "error|010|Not found file on server";
			}
			string[] array = new string[downloadList.Count];
			string[] array2 = new string[list.Count];
			for (int k = 0; k < downloadList.Count; k++)
			{
				array[k] = "";
			}
			for (int l = 0; l < list.Count; l++)
			{
				array2[l] = list[l];
			}
			string[] requestValue = base.GetRequestValue(this.hContext, "customValue");
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.DownloadFilePath = array;
				uploadEventEntity.DownloadFileName = array2;
				uploadEventEntity.DownloadCustomValue = requestValue;
				pOpenDownloadBeforeInitializeEventEx(uploadEventEntity);
				array2 = uploadEventEntity.DownloadFileName;
				for (int m = 0; m < array2.Length; m++)
				{
					list[m] = array2[m];
				}
			}
			catch
			{
			}
			if (pCustomError == null)
			{
				this.CompressionFiles(text, downloadList, list);
				for (int n = 0; n < downloadList.Count; n++)
				{
					list[n] = "\f" + list[n];
				}
				return list;
			}
			string text8 = "";
			if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
			{
				text8 += "<html><head>";
				text8 += "<script type=\"text/javascript\">";
				text8 += "if (window.postMessage) {";
				text8 += "if (window.addEventListener) {";
				text8 += "window.addEventListener('message', function (e) {";
				text8 += "var sendUrl = e.origin;";
				text8 += "var data = document.body.innerHTML;";
				text8 += "e.source.postMessage(data, sendUrl);";
				text8 += "}, false);";
				text8 += "}";
				text8 += "else if (window.attachEvent) {";
				text8 += "window.attachEvent('onmessage', function (e) {";
				text8 += "var sendUrl = e.origin;";
				text8 += "var data = document.body.innerHTML;";
				text8 += "e.source.postMessage(data, sendUrl);";
				text8 += "});";
				text8 += "}";
				text8 += "}";
				text8 += "</script>";
				text8 += "</head>";
				text8 += "<body>";
				text8 += "{0}";
				text8 += "</body>";
				text8 += "</html>";
			}
			else
			{
				text8 = "{0}";
			}
			string text9 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			text8 = text8.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter(text9));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text8);
			return text9;
		}

		// Token: 0x06000242 RID: 578 RVA: 0x0001C2B0 File Offset: 0x0001A4B0
		protected string CompressionFiles(string zipFileUniqueName, List<string> lPhysicalPath, List<string> lFileOrgName)
		{
			string text = zipFileUniqueName;
			string empty = string.Empty;
			string text2 = string.Empty;
			for (;;)
			{
				text2 = string.Concat(new object[]
				{
					this.tempPath,
					this.m_PathChar,
					text,
					".zip"
				});
				if (!File.Exists(text2))
				{
					break;
				}
				text = base.GenerateUniqueKey();
			}
			int count = lFileOrgName.Count;
			string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
			for (int i = count - 1; i >= 0; i--)
			{
				for (int j = i - 1; j >= 0; j--)
				{
					if (lFileOrgName[i].ToLower() == lFileOrgName[j].ToLower())
					{
						lFileOrgName[i] = base.GenerateUniqueKey() + "_" + lFileOrgName[i];
					}
				}
			}
			using (ZipFile zipFile = new ZipFile())
			{
				zipFile.AlternateEncodingUsage = ZipOption.AsNecessary;
				zipFile.AlternateEncoding = Encoding.GetEncoding(949);
				zipFile.CompressionLevel = CompressionLevel.None;
				zipFile.UseZip64WhenSaving = this._enableZip64;
				for (int k = 0; k < lPhysicalPath.Count; k++)
				{
					if (lPhysicalPath[k].ToLower().StartsWith("http"))
					{
						if (this._b_externalWebFileDirectDownload)
						{
							this._b_externalWebFileDirectDownload = false;
							Stream outsideUrlFileStream = base.GetOutsideUrlFileStream(lPhysicalPath[k]);
							zipFile.AddEntry(lFileOrgName[k], outsideUrlFileStream).FileName = lFileOrgName[k];
							this._b_externalWebFileDirectDownload = true;
						}
						else
						{
							lPhysicalPath[k] = base.DownloadOutsideUrlFile(lPhysicalPath[k], lFileOrgName[k], "attachment", false);
							zipFile.AddFile(lPhysicalPath[k], "").FileName = lFileOrgName[k];
						}
					}
					else
					{
						zipFile.AddFile(lPhysicalPath[k], "").FileName = lFileOrgName[k];
					}
				}
				zipFile.Comment = "This zip was created from DEXT5 Upload.";
				zipFile.Save(text2);
			}
			return text2;
		}

		// Token: 0x06000243 RID: 579 RVA: 0x0001C4E0 File Offset: 0x0001A6E0
		protected string CompressionFiles(string zipFileUniqueName, List<Stream> lStream, List<string> lFileOrgName)
		{
			string text = zipFileUniqueName;
			string empty = string.Empty;
			string text2 = string.Empty;
			for (;;)
			{
				text2 = string.Concat(new object[]
				{
					this.tempPath,
					this.m_PathChar,
					text,
					".zip"
				});
				if (!File.Exists(text2))
				{
					break;
				}
				text = base.GenerateUniqueKey();
			}
			int count = lFileOrgName.Count;
			string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
			for (int i = count - 1; i >= 0; i--)
			{
				for (int j = i - 1; j >= 0; j--)
				{
					if (lFileOrgName[i].ToLower() == lFileOrgName[j].ToLower())
					{
						lFileOrgName[i] = base.GenerateUniqueKey() + "_" + lFileOrgName[i];
					}
				}
			}
			using (ZipFile zipFile = new ZipFile())
			{
				zipFile.AlternateEncodingUsage = ZipOption.AsNecessary;
				zipFile.AlternateEncoding = Encoding.GetEncoding(949);
				zipFile.CompressionLevel = CompressionLevel.None;
				zipFile.UseZip64WhenSaving = this._enableZip64;
				for (int k = 0; k < lStream.Count; k++)
				{
					zipFile.AddEntry(lFileOrgName[k], lStream[k]).FileName = lFileOrgName[k];
				}
				zipFile.Comment = "This zip was created from DEXT5 Upload.";
				zipFile.Save(text2);
			}
			return text2;
		}

		// Token: 0x04000133 RID: 307
		protected const string RexRangeValuePattern = "^bytes=(d+)-$";

		// Token: 0x04000134 RID: 308
		protected string zipFileName = string.Empty;

		// Token: 0x04000135 RID: 309
		protected string fileWhiteList = string.Empty;

		// Token: 0x04000136 RID: 310
		protected string fileBlackList = string.Empty;

		// Token: 0x04000137 RID: 311
		protected string[] fileBlackWordList;

		// Token: 0x04000138 RID: 312
		protected string downloadRootPath = string.Empty;

		// Token: 0x04000139 RID: 313
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000025 RID: 37
	public class UploadZeroFile : Base
	{
		// Token: 0x06000260 RID: 608 RVA: 0x0001D6D4 File Offset: 0x0001B8D4
		public UploadZeroFile(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000261 RID: 609 RVA: 0x0001D758 File Offset: 0x0001B958
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string text = string.Empty;
			bool flag = false;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string text4 = string.Empty;
			if (!base.CheckCaller("ieplugin") && !base.CheckCaller("html5") && !base.CheckCaller("html4"))
			{
				text = "error|013|Bad Request Type";
			}
			else if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
			{
				text = "error|012|Not allowed file extension";
			}
			else if (!base.CheckBlackWord(this.fileBlackWordList, null))
			{
				text = "error|018|There does not allow the string included in file name";
			}
			else
			{
				flag = base.CheckCaller("ieplugin");
				this.tempPath = base.GetTempPath(this.tempPath);
				string text5 = this._entity_dextParam.fileName;
				text5 = text5.Normalize(NormalizationForm.FormC);
				string guid = this._entity_dextParam.GUID;
				string folderNameRule = this._entity_dextParam.folderNameRule;
				string fileNameRule = this._entity_dextParam.fileNameRule;
				string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
				string filePrefix = this._entity_dextParam.filePrefix;
				string fileSubfix = this._entity_dextParam.fileSubfix;
				string text6 = base.GetTempFileFolder(this.tempPath, guid) + this.m_PathChar;
				new DirectoryInfo(text6);
				string str = guid;
				string extension = Path.GetExtension(text5);
				FileStream fileStream = new FileStream(text6 + str + extension, FileMode.OpenOrCreate, FileAccess.Write);
				try
				{
					byte[] buffer = new byte[0];
					fileStream.Write(buffer, 0, 0);
					fileStream.Flush();
					fileStream.Close();
					string sourceFileName = text6 + str + extension;
					string fileLocationInfo = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, true, null, null, null, null, null, null, null);
					text2 = string.Empty;
					text3 = string.Empty;
					if (string.IsNullOrEmpty(fileLocationInfo))
					{
						text = "error|008|Error occured on the server side";
					}
					else if (fileLocationInfo.IndexOf("error") == 0)
					{
						text = fileLocationInfo;
					}
					else
					{
						string[] array = fileLocationInfo.Split(new char[]
						{
							'|'
						});
						text2 = array[0];
						text3 = array[1];
						bool flag2 = false;
						string text7 = text2;
						try
						{
							pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
							if (!text7.Equals(text2))
							{
								flag2 = true;
							}
						}
						catch
						{
						}
						try
						{
							UploadEventEntity uploadEventEntity = new UploadEventEntity();
							uploadEventEntity.Context = this.hContext;
							uploadEventEntity.NewFileLocation = text2;
							uploadEventEntity.ResponseFileName = text3;
							pBeforeInitializeEventEx(uploadEventEntity);
							text2 = uploadEventEntity.NewFileLocation;
							text3 = uploadEventEntity.ResponseFileName;
							if (!text7.Equals(text2))
							{
								flag2 = true;
							}
						}
						catch
						{
						}
						if (flag2)
						{
							string[] array2 = base.InitializeEventFileExec(text2, text3);
							text2 = array2[0];
							text3 = array2[1];
						}
						if (pCustomError == null)
						{
							File.Move(sourceFileName, text2);
							Directory.Delete(text6);
							if (!string.IsNullOrEmpty(this.physicalPath))
							{
								text4 = text2;
							}
							else if (!string.IsNullOrEmpty(this.virtualPath))
							{
								string oldValue = HostingEnvironment.MapPath("~/");
								text4 = "/" + text2.Replace(oldValue, "");
								text4 = text4.Replace(this.m_PathChar, '/');
							}
							try
							{
								pCompleteBeforeEvent(this.hContext, ref text2, ref text4, ref text3, ref this._str_ResponseCustomValue);
							}
							catch
							{
							}
							try
							{
								UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
								uploadEventEntity2.Context = this.hContext;
								uploadEventEntity2.NewFileLocation = text2;
								uploadEventEntity2.ResponseFileServerPath = text4;
								uploadEventEntity2.ResponseFileName = text3;
								uploadEventEntity2.ResponseGroupId = this._entity_dextParam.fileGroupID;
								uploadEventEntity2.FileIndex = this._entity_dextParam.fileIndex;
								pCompleteBeforeEventEx(uploadEventEntity2);
								text2 = uploadEventEntity2.NewFileLocation;
								text4 = uploadEventEntity2.ResponseFileServerPath;
								text3 = uploadEventEntity2.ResponseFileName;
								this._str_ResponseCustomValue = uploadEventEntity2.ResponseCustomValue;
								this._str_ResponseGroupId = uploadEventEntity2.ResponseGroupId;
							}
							catch
							{
							}
							string text8 = "";
							if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
							{
								text8 = text8 + "|" + this._str_ResponseCustomValue;
							}
							if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
							{
								text8 = text8 + "\b" + this._str_ResponseGroupId;
							}
							string text9;
							if (flag)
							{
								text9 = text4 + "*" + text3 + text8;
							}
							else
							{
								text9 = string.Concat(new string[]
								{
									text5,
									"::",
									text4,
									"|",
									text3,
									text8
								});
							}
							text = text9;
						}
						if (pCustomError != null)
						{
							text = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
						}
					}
				}
				catch (Exception)
				{
					text = "error|008|Complete error occured on the server side";
				}
			}
			this.hContext.Response.Clear();
			string text10 = "";
			if (flag)
			{
				if (text.Substring(0, 5) == "error")
				{
					text10 = "[FAIL]";
				}
				else
				{
					text10 = "[OK]";
				}
			}
			text10 += Dext5Parameter.MakeParameter(text);
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text10);
			if (pCustomError == null && text.Substring(0, 5) != "error")
			{
				try
				{
					pCompleteEvent(this.hContext, text2, text4, text3);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text2,
						ResponseFileServerPath = text4,
						ResponseFileName = text3
					});
				}
				catch
				{
				}
			}
			return null;
		}

		// Token: 0x04000146 RID: 326
		private string physicalPath = string.Empty;

		// Token: 0x04000147 RID: 327
		private string virtualPath = string.Empty;

		// Token: 0x04000148 RID: 328
		private string fileWhiteList = string.Empty;

		// Token: 0x04000149 RID: 329
		private string fileBlackList = string.Empty;

		// Token: 0x0400014A RID: 330
		private string[] fileBlackWordList;

		// Token: 0x0400014B RID: 331
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

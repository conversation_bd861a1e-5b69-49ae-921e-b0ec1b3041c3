﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5
{
	// Token: 0x02000034 RID: 52
	public class Merge : Base
	{
		// Token: 0x0600030C RID: 780 RVA: 0x00022400 File Offset: 0x00020600
		public Merge(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
		}

		// Token: 0x0600030D RID: 781 RVA: 0x00022448 File Offset: 0x00020648
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string str = string.Empty;
			string text = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string text4 = string.Empty;
			string path = string.Empty;
			DirectoryInfo directoryInfo = null;
			if (this._b_html5SliceAppend)
			{
				text = this._entity_dextParam.GUID;
				text2 = base.FileLocationInfoReadWrite("R", text, "");
				text3 = string.Empty;
				if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
					return null;
				}
				text3 = Path.GetFileName(text2);
				text4 = this._entity_dextParam.fileName;
				text4 = text4.Normalize(NormalizationForm.FormC);
				path = base.GetTempFileFolder(this.tempPath, text) + this.m_PathChar;
				directoryInfo = new DirectoryInfo(path);
			}
			else
			{
				this.tempPath = base.GetTempPath(this.tempPath);
				text4 = this._entity_dextParam.fileName;
				text4 = text4.Normalize(NormalizationForm.FormC);
				string folderNameRule = this._entity_dextParam.folderNameRule;
				text = this._entity_dextParam.GUID;
				int num = Convert.ToInt32(this._entity_dextParam.numberOfChunks);
				string fileNameRule = this._entity_dextParam.fileNameRule;
				string fileNameRuleEx = this._entity_dextParam.fileNameRuleEx;
				string filePrefix = this._entity_dextParam.filePrefix;
				string fileSubfix = this._entity_dextParam.fileSubfix;
				path = base.GetTempFileFolder(this.tempPath, text) + this.m_PathChar;
				directoryInfo = new DirectoryInfo(path);
				Path.GetExtension(text4);
				int num2 = directoryInfo.GetFiles("*.tmp").Length;
				if (!this._b_html5SliceAppend && num2 != num)
				{
					Directory.Delete(path, true);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|005|Number of file chunks less than total count"));
					return null;
				}
			}
			try
			{
				if (this._b_html5SliceAppend)
				{
					FileInfo[] files = directoryInfo.GetFiles("*.tmp");
					if (files.Length > 1)
					{
						string fullName = files[files.Length - 1].FullName;
						for (int i = 0; i < files.Length - 1; i++)
						{
							base.MergeChunkFile(files[i].FullName, text2);
						}
					}
					string tempFileFolder = base.GetTempFileFolder(this.tempPath, text);
					string.Concat(new object[]
					{
						tempFileFolder,
						this.m_PathChar,
						text,
						this.m_strHSTempSuffix
					});
					Directory.Delete(tempFileFolder, true);
				}
				else
				{
					string text5 = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
					if (string.IsNullOrEmpty(text5))
					{
						Directory.Delete(path, true);
						throw new Exception("Error occured on the server side");
					}
					if (text5.IndexOf("error") == 0)
					{
						Directory.Delete(path, true);
						text5 = Dext5Parameter.MakeParameter(text5);
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text5);
						return null;
					}
					string[] array = text5.Split(new char[]
					{
						'|'
					});
					text2 = array[0];
					text3 = array[1];
					bool flag = false;
					string text6 = text2;
					try
					{
						pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
						if (!text6.Equals(text2))
						{
							flag = true;
						}
					}
					catch
					{
					}
					try
					{
						UploadEventEntity uploadEventEntity = new UploadEventEntity();
						uploadEventEntity.Context = this.hContext;
						uploadEventEntity.NewFileLocation = text2;
						uploadEventEntity.ResponseFileName = text3;
						pBeforeInitializeEventEx(uploadEventEntity);
						text2 = uploadEventEntity.NewFileLocation;
						text3 = uploadEventEntity.ResponseFileName;
						if (!text6.Equals(text2))
						{
							flag = true;
						}
					}
					catch
					{
					}
					if (flag)
					{
						string[] array2 = base.InitializeEventFileExec(text2, text3);
						text2 = array2[0];
						text3 = array2[1];
					}
					if (pCustomError != null)
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage));
						return null;
					}
					FileInfo[] files2 = directoryInfo.GetFiles("*.tmp");
					Array.Sort<FileInfo>(files2, (FileInfo f1, FileInfo f2) => f1.Name.CompareTo(f2.Name));
					using (FileStream fileStream = new FileStream(text2, FileMode.OpenOrCreate, FileAccess.Write))
					{
						foreach (FileInfo fileInfo in files2)
						{
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("CMD - Html5 Merge, " + fileInfo.FullName, this._str_DebugFilePath);
							}
							using (FileStream fileStream2 = File.OpenRead(fileInfo.FullName))
							{
								base.CopyStreamToStream(fileStream2, fileStream);
							}
						}
						fileStream.Flush();
						fileStream.Close();
					}
					Directory.Delete(path, true);
				}
				string text7 = string.Empty;
				string text8 = string.Empty;
				bool flag2 = true;
				string fileDataIntegrity = this._entity_dextParam.fileDataIntegrity;
				if (!string.IsNullOrEmpty(fileDataIntegrity))
				{
					string integrityHashValue = base.getIntegrityHashValue(text2);
					if (integrityHashValue != fileDataIntegrity)
					{
						flag2 = false;
					}
				}
				if (!flag2)
				{
					File.Delete(text2);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|024|After the completion of file data transfer and file data before transmitting do not match"));
					return null;
				}
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text7 = text2;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					string oldValue = HostingEnvironment.MapPath("~/");
					text7 = "/" + text2.Replace(oldValue, "");
					text7 = text7.Replace(this.m_PathChar, '/');
				}
				try
				{
					pCompleteBeforeEvent(this.hContext, ref text2, ref text7, ref text3, ref this._str_ResponseCustomValue);
				}
				catch
				{
				}
				try
				{
					UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
					uploadEventEntity2.Context = this.hContext;
					uploadEventEntity2.NewFileLocation = text2;
					uploadEventEntity2.ResponseFileServerPath = text7;
					uploadEventEntity2.ResponseFileName = text3;
					uploadEventEntity2.ResponseGroupId = this._entity_dextParam.fileGroupID;
					uploadEventEntity2.FileIndex = this._entity_dextParam.fileIndex;
					pCompleteBeforeEventEx(uploadEventEntity2);
					text2 = uploadEventEntity2.NewFileLocation;
					text7 = uploadEventEntity2.ResponseFileServerPath;
					text3 = uploadEventEntity2.ResponseFileName;
					this._str_ResponseCustomValue = uploadEventEntity2.ResponseCustomValue;
					this._str_ResponseGroupId = uploadEventEntity2.ResponseGroupId;
				}
				catch
				{
				}
				string text9 = "";
				if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
				{
					text9 = text9 + "|" + this._str_ResponseCustomValue;
				}
				if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
				{
					text9 = text9 + "\b" + this._str_ResponseGroupId;
				}
				text8 = string.Concat(new string[]
				{
					text4,
					"::",
					text7,
					"|",
					text3,
					text9
				});
				str = text8;
				text8 = Dext5Parameter.MakeParameter(text8);
				this.hContext.Response.Clear();
				this.hContext.Response.Write(text8);
				if (pCustomError != null)
				{
					File.Delete(text2);
					this.hContext.Response.Clear();
					text8 = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
					str = text8;
					text8 = Dext5Parameter.MakeParameter(text8);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text8);
				}
				if (pCustomError == null)
				{
					try
					{
						pCompleteEvent(this.hContext, text2, text7, text3);
					}
					catch
					{
					}
					try
					{
						pCompleteEventEx(new UploadEventEntity
						{
							Context = this.hContext,
							NewFileLocation = text2,
							ResponseFileServerPath = text7,
							ResponseFileName = text3
						});
					}
					catch
					{
					}
				}
			}
			catch (Exception ex)
			{
				Directory.Delete(path, true);
				if (!string.IsNullOrEmpty(text2) && File.Exists(text2))
				{
					File.Delete(text2);
				}
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|008|Complete error occured on the server side"));
				str = ex.Message;
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html5 Merge, " + str, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x04000199 RID: 409
		private string physicalPath = string.Empty;

		// Token: 0x0400019A RID: 410
		private string virtualPath = string.Empty;
	}
}

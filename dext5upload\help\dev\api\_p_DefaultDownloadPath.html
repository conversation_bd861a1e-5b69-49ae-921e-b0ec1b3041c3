﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">              
    <span class="pl_type">[플러그인 전용]</span>   
    <h3 class="title">DEXT5 Upload :: Config :: DefaultDownloadPath</h3>
    <p class="ttl">config.DefaultDownloadPath</p>
    <p class="txt">
        다운로드 시 저장경로를 설정합니다. 
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        1. 경로 앞에 “*”를 붙이면 사용자가 설정한 경로를 무시하고 해당경로로만 저장됩니다.<br/>
        예) “*D:\Temp”<br /><br />

        2. “*”가 없이 경로를 입력하면 사용자가 기본저장 폴더를 설정하지 않은 경우에 적용 됩니다.<br />
        예) “D:\Temp”<br /><br />
        3. “-“ 값 입력 시 설정한 defaultDownloadPath가 삭제 됩니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 다운로드 시 저장경로를 설정합니다. 
        DEXT5UPLOAD.config.DefaultDownloadPath  = 'D:\\temp';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


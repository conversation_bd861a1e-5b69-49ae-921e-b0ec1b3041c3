package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2SetFileInformation.class */
class Trans2SetFileInformation extends SmbComTransaction {
    static final int SMB_FILE_BASIC_INFO = 257;
    private int fid;
    private int attributes;
    private long createTime;
    private long lastWriteTime;

    Trans2SetFileInformation(int fid, int attributes, long createTime, long lastWriteTime) {
        this.fid = fid;
        this.attributes = attributes;
        this.createTime = createTime;
        this.lastWriteTime = lastWriteTime;
        this.command = (byte) 50;
        this.subCommand = (byte) 8;
        this.maxParameterCount = 6;
        this.maxDataCount = 0;
        this.maxSetupCount = (byte) 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        int i = dstIndex2 + 1;
        dst[dstIndex2] = 0;
        return 2;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.fid, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        writeInt2(257L, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 2;
        writeInt2(0L, dst, dstIndex3);
        return (dstIndex3 + 2) - dstIndex;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        writeTime(this.createTime, dst, dstIndex);
        int dstIndex2 = dstIndex + 8;
        writeInt8(0L, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 8;
        writeTime(this.lastWriteTime, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 8;
        writeInt8(0L, dst, dstIndex4);
        int dstIndex5 = dstIndex4 + 8;
        writeInt2(128 | this.attributes, dst, dstIndex5);
        int dstIndex6 = dstIndex5 + 2;
        writeInt8(0L, dst, dstIndex6);
        return (dstIndex6 + 6) - dstIndex;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2SetFileInformation[" + super.toString() + ",fid=" + this.fid + "]");
    }
}

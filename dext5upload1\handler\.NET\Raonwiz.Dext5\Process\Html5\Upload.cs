﻿using System;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5
{
	// Token: 0x02000036 RID: 54
	public class Upload : Base
	{
		// Token: 0x06000315 RID: 789 RVA: 0x000241C0 File Offset: 0x000223C0
		public Upload(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x06000316 RID: 790 RVA: 0x00024244 File Offset: 0x00022444
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string str = string.Empty;
			try
			{
				if (!base.CheckCaller("html5"))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|013|Bad Request Type"));
					return null;
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
					return null;
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, null))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
					return null;
				}
				string text = string.Empty;
				string text2 = string.Empty;
				string text3 = string.Empty;
				string guid = this._entity_dextParam.GUID;
				if (this._b_html5SliceAppend)
				{
					string tempFileFolder = base.GetTempFileFolder(this.tempPath, guid);
					string path = string.Concat(new object[]
					{
						tempFileFolder,
						this.m_PathChar,
						guid,
						this.m_strHSTempSuffix
					});
					if (File.Exists(path))
					{
						text2 = base.FileLocationInfoReadWrite("R", guid, "");
						if (string.IsNullOrEmpty(text2) || !File.Exists(text2))
						{
							this.hContext.Response.Clear();
							this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|006|Not found directory on server"));
							return null;
						}
						text3 = Path.GetFileName(text2);
					}
					else
					{
						text = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
						if (string.IsNullOrEmpty(text))
						{
							throw new Exception("Error occured on the server side");
						}
						if (text.IndexOf("error") == 0)
						{
							this.hContext.Response.Clear();
							this.hContext.Response.Write(Dext5Parameter.MakeParameter(text));
							return null;
						}
						string[] array = text.Split(new char[]
						{
							'|'
						});
						text2 = array[0];
						text3 = array[1];
						bool flag = false;
						string text4 = text2;
						try
						{
							pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
							if (!text4.Equals(text2))
							{
								flag = true;
							}
						}
						catch
						{
						}
						try
						{
							UploadEventEntity uploadEventEntity = new UploadEventEntity();
							uploadEventEntity.Context = this.hContext;
							uploadEventEntity.NewFileLocation = text2;
							uploadEventEntity.ResponseFileName = text3;
							pBeforeInitializeEventEx(uploadEventEntity);
							text2 = uploadEventEntity.NewFileLocation;
							text3 = uploadEventEntity.ResponseFileName;
							if (!text4.Equals(text2))
							{
								flag = true;
							}
						}
						catch
						{
						}
						if (flag)
						{
							string[] array2 = base.InitializeEventFileExec(text2, text3);
							text2 = array2[0];
							text3 = array2[1];
						}
						base.FileLocationInfoReadWrite("W", guid, text2);
					}
				}
				this.tempPath = base.GetTempPath(this.tempPath);
				byte[] array3 = null;
				if (this.hContext.Request.Headers["Dext5-Encoded"] != null)
				{
					using (Stream inputStream = this.hContext.Request.Files["Slice"].InputStream)
					{
						byte[] array4 = new byte[inputStream.Length];
						inputStream.Read(array4, 0, Convert.ToInt32(inputStream.Length));
						string @string = Encoding.UTF8.GetString(array4);
						array3 = Convert.FromBase64String(@string);
						inputStream.Close();
						goto IL_3DB;
					}
				}
				using (Stream inputStream2 = this.hContext.Request.Files["Slice"].InputStream)
				{
					array3 = new byte[inputStream2.Length];
					inputStream2.Read(array3, 0, Convert.ToInt32(inputStream2.Length));
					inputStream2.Close();
				}
				IL_3DB:
				if (array3 == null)
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|003|No file chunk uploaded"));
					return null;
				}
				int num = Convert.ToInt32(this._entity_dextParam.chunkNumber);
				if (this._b_html5SliceAppend)
				{
					if (!File.Exists(text2))
					{
						try
						{
							using (FileStream fileStream = new FileStream(text2, FileMode.Create, FileAccess.Write, FileShare.None))
							{
								fileStream.SetLength(Math.Max(Convert.ToInt64(this._entity_dextParam.fileSize), 0L));
								fileStream.Close();
							}
						}
						catch
						{
						}
					}
					try
					{
						using (FileStream fileStream2 = new FileStream(text2, FileMode.OpenOrCreate, FileAccess.Write, FileShare.ReadWrite))
						{
							long offset = Convert.ToInt64(this._entity_dextParam.chunkSize) * (Convert.ToInt64(this._entity_dextParam.chunkNumber) - 1L);
							fileStream2.Seek(offset, SeekOrigin.Begin);
							fileStream2.Write(array3, 0, array3.Length);
							fileStream2.Close();
						}
					}
					catch
					{
						string path2 = string.Concat(new object[]
						{
							base.GetTempFileFolder(this.tempPath, guid),
							this.m_PathChar,
							guid,
							".",
							num.ToString().PadLeft(16, Convert.ToChar("0")),
							".",
							this._entity_dextParam.chunkSize,
							".tmp"
						});
						using (FileStream fileStream3 = new FileStream(path2, FileMode.Create))
						{
							using (BinaryWriter binaryWriter = new BinaryWriter(fileStream3))
							{
								binaryWriter.Write(array3);
								binaryWriter.Close();
								fileStream3.Close();
							}
						}
					}
					str = text2 + "(" + this._entity_dextParam.chunkNumber + ")";
				}
				else
				{
					string text5 = string.Concat(new object[]
					{
						base.GetTempFileFolder(this.tempPath, guid),
						this.m_PathChar,
						guid,
						".",
						num.ToString().PadLeft(16, Convert.ToChar("0")),
						".tmp"
					});
					using (FileStream fileStream4 = new FileStream(text5, FileMode.Create))
					{
						using (BinaryWriter binaryWriter2 = new BinaryWriter(fileStream4))
						{
							binaryWriter2.Write(array3);
							binaryWriter2.Close();
							fileStream4.Close();
						}
					}
					str = text5;
				}
			}
			catch (Exception ex)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|004|ProcessUpload error occured on the server side"));
				str = ex.Message;
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html5 Upload, " + str, this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x040001A2 RID: 418
		private string physicalPath = string.Empty;

		// Token: 0x040001A3 RID: 419
		private string virtualPath = string.Empty;

		// Token: 0x040001A4 RID: 420
		private string fileWhiteList = string.Empty;

		// Token: 0x040001A5 RID: 421
		private string fileBlackList = string.Empty;

		// Token: 0x040001A6 RID: 422
		private string[] fileBlackWordList;

		// Token: 0x040001A7 RID: 423
		private string allowExtensionSpecialSymbol = string.Empty;

		// Token: 0x040001A8 RID: 424
		private static object lockObject = new object();
	}
}

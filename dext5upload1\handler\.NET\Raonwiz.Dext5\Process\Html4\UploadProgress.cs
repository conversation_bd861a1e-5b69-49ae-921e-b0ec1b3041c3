﻿using System;
using System.IO;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Html4.Entity;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x0200002E RID: 46
	public class UploadProgress : Base
	{
		// Token: 0x060002BA RID: 698 RVA: 0x00020014 File Offset: 0x0001E214
		public UploadProgress(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x060002BB RID: 699 RVA: 0x00020098 File Offset: 0x0001E298
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string empty = string.Empty;
			string text = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			string text4 = string.Empty;
			string text5 = string.Empty;
			string text6 = this.hContext.Request.QueryString["g"];
			UploadStatus uploadStatus = (UploadStatus)this.hContext.Application[text6];
			if (uploadStatus == null)
			{
				return null;
			}
			if (!(uploadStatus.Message == "cancel") && uploadStatus.Message.IndexOf("error|") <= -1)
			{
				if (!base.CheckCaller("html4"))
				{
					text = "error|013|Bad Request Type";
					uploadStatus.Message = text;
					return null;
				}
				if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, uploadStatus.FileName))
				{
					text = "error|012|Not allowed file extension";
					uploadStatus.Message = text;
					return null;
				}
				if (!base.CheckBlackWord(this.fileBlackWordList, null))
				{
					text = "error|018|There does not allow the string included in file name";
					uploadStatus.Message = text;
					return null;
				}
				this.tempPath = base.GetTempPath(this.tempPath);
				text = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, uploadStatus.FileName, text6, uploadStatus.FolderNameRule, uploadStatus.FileNameRule, uploadStatus.FileNameRuleEx, uploadStatus.D5_prefix, uploadStatus.D5_subfix);
				if (string.IsNullOrEmpty(text) || text.IndexOf("error") == 0)
				{
					uploadStatus.Message = text;
					return null;
				}
				string[] array = text.Split(new char[]
				{
					'|'
				});
				text2 = array[0];
				text3 = array[1];
				string text7 = this.hContext.Request.QueryString["m"];
				if (!string.IsNullOrEmpty(text7) && text7 != "0")
				{
					long num = Convert.ToInt64(text7);
					string fileName = Path.Combine(this.tempPath, uploadStatus.TempFileName);
					FileInfo fileInfo = new FileInfo(fileName);
					if (num < fileInfo.Length)
					{
						uploadStatus.Message = "error|014|File is exceeded the max one file size";
						return null;
					}
				}
				File.Move(Path.Combine(this.tempPath, uploadStatus.TempFileName), text2);
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text4 = text2;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					string oldValue = HostingEnvironment.MapPath("~/");
					text4 = "/" + text2.Replace(oldValue, "");
					text4 = text4.Replace(this.m_PathChar, '/');
				}
				FileInfo fileInfo2 = new FileInfo(text2);
				text5 = fileInfo2.Length.ToString();
				try
				{
					pCompleteBeforeEvent(this.hContext, ref text2, ref text4, ref text3, ref this._str_ResponseCustomValue);
				}
				catch
				{
				}
				if (pCustomError != null)
				{
					uploadStatus.Message = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
				}
				else
				{
					try
					{
						pCompleteEvent(this.hContext, text2, text4, text3);
					}
					catch
					{
					}
					uploadStatus.Message = string.Concat(new string[]
					{
						"success|",
						text4,
						"|",
						text3,
						"|",
						text5
					});
				}
			}
			return null;
		}

		// Token: 0x04000189 RID: 393
		private string physicalPath = string.Empty;

		// Token: 0x0400018A RID: 394
		private string virtualPath = string.Empty;

		// Token: 0x0400018B RID: 395
		private string fileWhiteList = string.Empty;

		// Token: 0x0400018C RID: 396
		private string fileBlackList = string.Empty;

		// Token: 0x0400018D RID: 397
		private string[] fileBlackWordList;

		// Token: 0x0400018E RID: 398
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/TransPeekNamedPipe.class */
class TransPeekNamedPipe extends SmbComTransaction {
    private int fid;

    TransPeekNamedPipe(String pipeName, int fid) {
        this.name = pipeName;
        this.fid = fid;
        this.command = (byte) 37;
        this.subCommand = (byte) 35;
        this.timeout = -1;
        this.maxParameterCount = 6;
        this.maxDataCount = 1;
        this.maxSetupCount = (byte) 0;
        this.setupCount = 2;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.subCommand;
        dst[dstIndex2] = 0;
        writeInt2(this.fid, dst, dstIndex2 + 1);
        return 4;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransaction, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("TransPeekNamedPipe[" + super.toString() + ",pipeName=" + this.name + "]");
    }
}

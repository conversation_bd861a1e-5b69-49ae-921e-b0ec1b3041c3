package jcifs.dcerpc;

import javax.servlet.http.HttpServletResponse;
import jcifs.dcerpc.rpc;
import jcifs.http.Handler;
import jcifs.smb.WinError;
import net.lingala.zip4j.util.InternalZipConstants;
import org.apache.commons.io.IOUtils;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/UUID.class */
public class UUID extends rpc.uuid_t {
    static final char[] HEXCHARS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    public static int hex_to_bin(char[] arr, int offset, int length) {
        int i;
        int i2;
        int value = 0;
        int count = 0;
        for (int ai = offset; ai < arr.length && count < length; ai++) {
            int value2 = value << 4;
            switch (arr[ai]) {
                case InternalZipConstants.FOLDER_MODE_ARCHIVE /* 48 */:
                case '1':
                case InternalZipConstants.FOLDER_MODE_HIDDEN_ARCHIVE /* 50 */:
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                    i = value2;
                    i2 = arr[ai] - '0';
                    break;
                case ':':
                case ';':
                case '<':
                case '=':
                case '>':
                case '?':
                case '@':
                case WinError.ERROR_REQ_NOT_ACCEP /* 71 */:
                case 'H':
                case 'I':
                case 'J':
                case 'K':
                case 'L':
                case 'M':
                case 'N':
                case 'O':
                case Handler.DEFAULT_HTTP_PORT /* 80 */:
                case 'Q':
                case 'R':
                case 'S':
                case 'T':
                case 'U':
                case 'V':
                case 'W':
                case 'X':
                case 'Y':
                case 'Z':
                case '[':
                case IOUtils.DIR_SEPARATOR_WINDOWS /* 92 */:
                case ']':
                case '^':
                case '_':
                case '`':
                default:
                    throw new IllegalArgumentException(new String(arr, offset, length));
                case 'A':
                case 'B':
                case 'C':
                case 'D':
                case 'E':
                case 'F':
                    i = value2;
                    i2 = 10 + (arr[ai] - 'A');
                    break;
                case 'a':
                case 'b':
                case 'c':
                case HttpServletResponse.SC_CONTINUE /* 100 */:
                case HttpServletResponse.SC_SWITCHING_PROTOCOLS /* 101 */:
                case 'f':
                    i = value2;
                    i2 = 10 + (arr[ai] - 'a');
                    break;
            }
            value = i + i2;
            count++;
        }
        return value;
    }

    public static String bin_to_hex(int value, int length) {
        char[] arr = new char[length];
        int ai = arr.length;
        while (true) {
            int i = ai;
            ai = i - 1;
            if (i > 0) {
                arr[ai] = HEXCHARS[value & 15];
                value >>>= 4;
            } else {
                return new String(arr);
            }
        }
    }

    private static byte B(int i) {
        return (byte) (i & 255);
    }

    private static short S(int i) {
        return (short) (i & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH);
    }

    public UUID(rpc.uuid_t uuid) {
        this.time_low = uuid.time_low;
        this.time_mid = uuid.time_mid;
        this.time_hi_and_version = uuid.time_hi_and_version;
        this.clock_seq_hi_and_reserved = uuid.clock_seq_hi_and_reserved;
        this.clock_seq_low = uuid.clock_seq_low;
        this.node = new byte[6];
        this.node[0] = uuid.node[0];
        this.node[1] = uuid.node[1];
        this.node[2] = uuid.node[2];
        this.node[3] = uuid.node[3];
        this.node[4] = uuid.node[4];
        this.node[5] = uuid.node[5];
    }

    public UUID(String str) {
        char[] arr = str.toCharArray();
        this.time_low = hex_to_bin(arr, 0, 8);
        this.time_mid = S(hex_to_bin(arr, 9, 4));
        this.time_hi_and_version = S(hex_to_bin(arr, 14, 4));
        this.clock_seq_hi_and_reserved = B(hex_to_bin(arr, 19, 2));
        this.clock_seq_low = B(hex_to_bin(arr, 21, 2));
        this.node = new byte[6];
        this.node[0] = B(hex_to_bin(arr, 24, 2));
        this.node[1] = B(hex_to_bin(arr, 26, 2));
        this.node[2] = B(hex_to_bin(arr, 28, 2));
        this.node[3] = B(hex_to_bin(arr, 30, 2));
        this.node[4] = B(hex_to_bin(arr, 32, 2));
        this.node[5] = B(hex_to_bin(arr, 34, 2));
    }

    public String toString() {
        return bin_to_hex(this.time_low, 8) + '-' + bin_to_hex(this.time_mid, 4) + '-' + bin_to_hex(this.time_hi_and_version, 4) + '-' + bin_to_hex(this.clock_seq_hi_and_reserved, 2) + bin_to_hex(this.clock_seq_low, 2) + '-' + bin_to_hex(this.node[0], 2) + bin_to_hex(this.node[1], 2) + bin_to_hex(this.node[2], 2) + bin_to_hex(this.node[3], 2) + bin_to_hex(this.node[4], 2) + bin_to_hex(this.node[5], 2);
    }
}

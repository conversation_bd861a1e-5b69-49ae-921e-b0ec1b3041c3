﻿using System;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000027 RID: 39
	public class GetFileSize : Base
	{
		// Token: 0x06000265 RID: 613 RVA: 0x0001E0E4 File Offset: 0x0001C2E4
		public GetFileSize(HttpContext context) : base(context)
		{
		}

		// Token: 0x06000266 RID: 614 RVA: 0x0001E0F8 File Offset: 0x0001C2F8
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string text = string.Empty;
			string[] urlAddressAry = this._entity_dextParam.urlAddressAry;
			if (this._entity_dextParam.mode == "normal")
			{
				for (int i = 0; i < urlAddressAry.Length; i++)
				{
					if (this._entity_dextParam.crossDomain == "1")
					{
						urlAddressAry[i] = HttpUtility.UrlDecode(HttpUtility.UrlDecode(urlAddressAry[i], Encoding.UTF8), Encoding.UTF8);
					}
					else
					{
						urlAddressAry[i] = HttpUtility.UrlDecode(urlAddressAry[i], Encoding.UTF8);
					}
				}
			}
			string text2 = string.Empty;
			if (urlAddressAry != null)
			{
				int num = urlAddressAry.Length;
				for (int j = 0; j < num; j++)
				{
					if (!string.IsNullOrEmpty(urlAddressAry[j]))
					{
						if (File.Exists(urlAddressAry[j]))
						{
							FileInfo fileInfo = new FileInfo(urlAddressAry[j]);
							text2 += fileInfo.Length.ToString();
							if (j != num - 1)
							{
								text2 += "\f";
							}
						}
						else
						{
							string text3 = urlAddressAry[j].Replace("http://", "");
							text3 = text3.Replace("https://", "");
							text3 = text3.Substring(text3.IndexOf("/"));
							text3 = this.hContext.Request.MapPath(text3);
							if (File.Exists(text3))
							{
								FileInfo fileInfo2 = new FileInfo(text3);
								text2 += fileInfo2.Length.ToString();
								if (j != num - 1)
								{
									text2 += "\f";
								}
							}
							else
							{
								try
								{
									if (this._b_IsDebug)
									{
										LogUtil.DextDebug("File Web URL => " + urlAddressAry[j], this._str_DebugFilePath);
									}
									HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(urlAddressAry[j]);
									httpWebRequest.UserAgent = "DEXT5" + Guid.NewGuid();
									httpWebRequest.Timeout = 60000;
									HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
									text2 += httpWebResponse.ContentLength.ToString();
									if (j != num - 1)
									{
										text2 += "\f";
									}
									httpWebResponse.Close();
								}
								catch
								{
									if (j != num - 1)
									{
										text2 += "\f";
									}
								}
							}
						}
					}
				}
			}
			bool flag = false;
			if (this._entity_dextParam.crossDomain != null && this._entity_dextParam.crossDomain.Equals("1"))
			{
				flag = true;
			}
			if (flag)
			{
				text += "<html><head>";
				text += "<script type=\"text/javascript\">";
				text += "if (window.postMessage) {";
				text += "if (window.addEventListener) {";
				text += "window.addEventListener('message', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "}, false);";
				text += "}";
				text += "else if (window.attachEvent) {";
				text += "window.attachEvent('onmessage', function (e) {";
				text += "var sendUrl = e.origin;";
				text += "var data = document.body.innerHTML;";
				text += "e.source.postMessage(data, sendUrl);";
				text += "});";
				text += "}";
				text += "}";
				text += "</script>";
				text += "</head>";
				text += "<body>";
				text += "{0}";
				text += "</body>";
				text += "</html>";
			}
			else
			{
				text = "{0}";
			}
			text = text.Replace("{0}", Dext5Parameter.MakeParameter(text2));
			this.hContext.Response.Clear();
			this.hContext.Response.Write(text);
			return null;
		}

		// Token: 0x0400014E RID: 334
		private string _urlAddress = string.Empty;
	}
}

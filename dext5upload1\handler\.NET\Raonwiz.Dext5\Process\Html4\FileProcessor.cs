﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Raonwiz.Dext5.Process.Html4
{
	// Token: 0x0200002B RID: 43
	public class FileProcessor : IDisposable
	{
		// Token: 0x0600029A RID: 666 RVA: 0x0001EA84 File Offset: 0x0001CC84
		public FileProcessor(string uploadLocation, string uploadFileName)
		{
			this._currentFilePath = uploadLocation;
			this._currentFileName = uploadFileName;
		}

		// Token: 0x170000E6 RID: 230
		// (get) Token: 0x0600029B RID: 667 RVA: 0x0001EB52 File Offset: 0x0001CD52
		public string FieldSeperator
		{
			get
			{
				return this._fieldSeperator;
			}
		}

		// Token: 0x170000E7 RID: 231
		// (get) Token: 0x0600029C RID: 668 RVA: 0x0001EB5A File Offset: 0x0001CD5A
		public List<string> FinishedFiles
		{
			get
			{
				return this._finishedFiles;
			}
		}

		// Token: 0x170000E8 RID: 232
		// (get) Token: 0x0600029D RID: 669 RVA: 0x0001EB62 File Offset: 0x0001CD62
		public bool FileEndRead
		{
			get
			{
				return this._fileEndRead;
			}
		}

		// Token: 0x170000E9 RID: 233
		// (get) Token: 0x0600029E RID: 670 RVA: 0x0001EB6A File Offset: 0x0001CD6A
		// (set) Token: 0x0600029F RID: 671 RVA: 0x0001EB72 File Offset: 0x0001CD72
		public string FileName
		{
			get
			{
				return this._fileName;
			}
			set
			{
				this._fileName = value;
			}
		}

		// Token: 0x170000EA RID: 234
		// (get) Token: 0x060002A0 RID: 672 RVA: 0x0001EB7B File Offset: 0x0001CD7B
		// (set) Token: 0x060002A1 RID: 673 RVA: 0x0001EB83 File Offset: 0x0001CD83
		public string FolderNameRule
		{
			get
			{
				return this._folderNameRule;
			}
			set
			{
				this._folderNameRule = value;
			}
		}

		// Token: 0x170000EB RID: 235
		// (get) Token: 0x060002A2 RID: 674 RVA: 0x0001EB8C File Offset: 0x0001CD8C
		// (set) Token: 0x060002A3 RID: 675 RVA: 0x0001EB94 File Offset: 0x0001CD94
		public string FileNameRule
		{
			get
			{
				return this._fileNameRule;
			}
			set
			{
				this._fileNameRule = value;
			}
		}

		// Token: 0x170000EC RID: 236
		// (get) Token: 0x060002A4 RID: 676 RVA: 0x0001EB9D File Offset: 0x0001CD9D
		// (set) Token: 0x060002A5 RID: 677 RVA: 0x0001EBA5 File Offset: 0x0001CDA5
		public string FileNameRuleEx
		{
			get
			{
				return this._fileNameRuleEx;
			}
			set
			{
				this._fileNameRuleEx = value;
			}
		}

		// Token: 0x170000ED RID: 237
		// (get) Token: 0x060002A6 RID: 678 RVA: 0x0001EBAE File Offset: 0x0001CDAE
		// (set) Token: 0x060002A7 RID: 679 RVA: 0x0001EBB6 File Offset: 0x0001CDB6
		public string D5_prefix
		{
			get
			{
				return this._d5_prefix;
			}
			set
			{
				this._d5_prefix = value;
			}
		}

		// Token: 0x170000EE RID: 238
		// (get) Token: 0x060002A8 RID: 680 RVA: 0x0001EBBF File Offset: 0x0001CDBF
		// (set) Token: 0x060002A9 RID: 681 RVA: 0x0001EBC7 File Offset: 0x0001CDC7
		public string D5_subfix
		{
			get
			{
				return this._d5_subfix;
			}
			set
			{
				this._d5_subfix = value;
			}
		}

		// Token: 0x060002AA RID: 682 RVA: 0x0001EBD0 File Offset: 0x0001CDD0
		public string ProcessBufferForFileInfo(ref byte[] bufferData, string findString)
		{
			string result = string.Empty;
			int num = -1;
			if (!this._startFound)
			{
				num = this.GetStartBytePosition(ref bufferData, findString);
				if (num != -1)
				{
					this._startIndexBufferID = this._currentBufferIndex + 1L;
					this._startLocationInBufferID = num;
					this._startFound = true;
				}
			}
			if (this._startFound)
			{
				int num2 = 0;
				if (num != -1)
				{
					num2 = num;
				}
				int num3 = bufferData.Length - num2;
				int endBytePosition = this.GetEndBytePosition(ref bufferData);
				if (endBytePosition != -1)
				{
					num3 = endBytePosition - num2;
					this._endFound = true;
					this._endIndexBufferID = this._currentBufferIndex + 1L;
					this._endLocationInBufferID = endBytePosition;
				}
				if (num2 == 0 && endBytePosition == -1)
				{
					result = "";
				}
				else if (num3 > 0)
				{
					byte[] array = new byte[num3];
					for (int i = 0; i < num3; i++)
					{
						array[i] = bufferData[num2 + i];
					}
					result = Encoding.UTF8.GetString(array);
				}
			}
			if (this._endFound)
			{
				this._startFound = false;
				this._endFound = false;
			}
			return result;
		}

		// Token: 0x060002AB RID: 683 RVA: 0x0001ECC0 File Offset: 0x0001CEC0
		public void ProcessBuffer(ref byte[] bufferData, bool addToBufferHistory, string findString, ref MemoryStream memoryStream)
		{
			int num = -1;
			if (!this._startFound)
			{
				num = this.GetStartBytePosition(ref bufferData, findString);
				if (num != -1)
				{
					this._startIndexBufferID = this._currentBufferIndex + 1L;
					this._startLocationInBufferID = num;
					this._startFound = true;
					memoryStream.Write(bufferData, 0, bufferData.Length - num);
				}
			}
			if (this._startFound)
			{
				int num2 = 0;
				if (num != -1)
				{
					num2 = num;
				}
				int num3 = bufferData.Length - num2;
				int endBytePosition = this.GetEndBytePosition(ref bufferData);
				if (endBytePosition != -1)
				{
					num3 = endBytePosition - num2;
					this._endFound = true;
					this._endIndexBufferID = this._currentBufferIndex + 1L;
					this._endLocationInBufferID = endBytePosition;
					memoryStream.Write(bufferData, endBytePosition + 1, bufferData.Length - endBytePosition - 1);
				}
				if (num3 > 0)
				{
					if (this._fileStream == null)
					{
						this._fileStream = new FileStream(this._currentFilePath + this.m_PathChar + this._currentFileName, FileMode.OpenOrCreate);
					}
					this._fileStream.Write(bufferData, num2, num3);
					this._fileStream.Flush();
				}
			}
			if (this._endFound)
			{
				this.CloseStreams();
				this._startFound = false;
				this._endFound = false;
				this._fileEndRead = true;
				this._fileName = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"fileName\"");
				this._folderNameRule = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"folderNameRule\"");
				this._fileNameRule = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"fileNameRule\"");
				this._fileNameRuleEx = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"fileNameRuleEx\"");
				this._d5_prefix = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"d5_prefix\"");
				this._d5_subfix = this.ProcessBufferForFileInfo(ref bufferData, "Content-Disposition: form-data; name=\"d5_subfix\"");
			}
			if (addToBufferHistory)
			{
				this._bufferHistory.Add(this._currentBufferIndex, bufferData);
				this._currentBufferIndex += 1L;
				this.RemoveOldBufferData();
			}
		}

		// Token: 0x060002AC RID: 684 RVA: 0x0001EE7C File Offset: 0x0001D07C
		public int GetProcessBufferStartLocation(ref byte[] bufferData, bool addToBufferHistory, string findString)
		{
			int result = 0;
			int num = -1;
			if (!this._startFound)
			{
				num = this.GetStartBytePosition(ref bufferData, findString);
				if (num != -1)
				{
					this._startIndexBufferID = this._currentBufferIndex + 1L;
					this._startLocationInBufferID = num;
					this._startFound = true;
				}
			}
			if (this._startFound && num != -1)
			{
				result = num;
			}
			return result;
		}

		// Token: 0x060002AD RID: 685 RVA: 0x0001EED0 File Offset: 0x0001D0D0
		private void RemoveOldBufferData()
		{
			for (long num = this._currentBufferIndex; num >= 0L; num -= 1L)
			{
				if (num <= this._currentBufferIndex - 3L)
				{
					if (this._bufferHistory.ContainsKey(num))
					{
						this._bufferHistory.Remove(num);
					}
					else
					{
						num = 0L;
					}
				}
			}
			GC.Collect();
		}

		// Token: 0x060002AE RID: 686 RVA: 0x0001EF21 File Offset: 0x0001D121
		public void CloseStreams()
		{
			if (this._fileStream != null)
			{
				this._fileStream.Dispose();
				this._fileStream.Close();
				this._fileStream = null;
				this._finishedFiles.Add(this._currentFileName);
			}
		}

		// Token: 0x060002AF RID: 687 RVA: 0x0001EF5C File Offset: 0x0001D15C
		public void GetFieldSeperators(ref byte[] bufferData)
		{
			try
			{
				string @string = Encoding.UTF8.GetString(bufferData);
				int length = @string.IndexOf("\r\n") - 29;
				this._formPostID = @string.Substring(29, length);
				this._fieldSeperator = "-----------------------------" + this._formPostID;
			}
			catch (Exception)
			{
			}
		}

		// Token: 0x060002B0 RID: 688 RVA: 0x0001EFC0 File Offset: 0x0001D1C0
		private int GetStartBytePosition(ref byte[] bufferData, string findString)
		{
			int num = 0;
			if (this._startIndexBufferID == this._currentBufferIndex + 1L)
			{
				num = this._startLocationInBufferID;
			}
			if (this._endIndexBufferID == this._currentBufferIndex + 1L)
			{
				num = this._endLocationInBufferID;
			}
			byte[] bytes = Encoding.UTF8.GetBytes(findString);
			int num2 = FileProcessor.FindBytePattern(ref bufferData, ref bytes, num);
			if (num2 != -1)
			{
				bytes = Encoding.UTF8.GetBytes("\r\n\r\n");
				int num3 = FileProcessor.FindBytePattern(ref bufferData, ref bytes, num2);
				if (num3 != -1)
				{
					return num3 + 4;
				}
			}
			else
			{
				if (num - bytes.Length > 0)
				{
					return -1;
				}
				if (this._currentBufferIndex > 0L)
				{
					byte[] array = this._bufferHistory[this._currentBufferIndex - 1L];
					byte[] array2 = FileProcessor.MergeArrays(ref array, ref bufferData);
					byte[] bytes2 = Encoding.UTF8.GetBytes("Content-Type: ");
					num2 = FileProcessor.FindBytePattern(ref array2, ref bytes2, array.Length - bytes2.Length);
					if (num2 != -1)
					{
						bytes2 = Encoding.UTF8.GetBytes("Content-Type: ");
						int num4 = FileProcessor.FindBytePattern(ref array2, ref bytes2, array.Length - bytes2.Length);
						if (num4 != -1)
						{
							if (num4 > array.Length)
							{
								return num4 - array.Length;
							}
							return 0;
						}
					}
				}
			}
			return -1;
		}

		// Token: 0x060002B1 RID: 689 RVA: 0x0001F0E4 File Offset: 0x0001D2E4
		private int GetEndBytePosition(ref byte[] bufferData)
		{
			int num = 0;
			if (this._startIndexBufferID == this._currentBufferIndex + 1L)
			{
				num = this._startLocationInBufferID;
			}
			byte[] bytes = Encoding.UTF8.GetBytes(this._fieldSeperator);
			int num2 = FileProcessor.FindBytePattern(ref bufferData, ref bytes, num);
			if (num2 != -1)
			{
				if (num2 - 2 >= 0)
				{
					return num2 - 2;
				}
			}
			else
			{
				if (num - bytes.Length > 0)
				{
					return -1;
				}
				if (this._currentBufferIndex > 0L)
				{
					byte[] array = this._bufferHistory[this._currentBufferIndex - 1L];
					byte[] array2 = FileProcessor.MergeArrays(ref array, ref bufferData);
					byte[] bytes2 = Encoding.UTF8.GetBytes(this._fieldSeperator);
					num2 = FileProcessor.FindBytePattern(ref array2, ref bytes2, array.Length - bytes2.Length + num);
					if (num2 != -1)
					{
						bytes2 = Encoding.UTF8.GetBytes("\r\n\r\n");
						int num3 = FileProcessor.FindBytePattern(ref array2, ref bytes2, num2);
						if (num3 != -1)
						{
							if (num3 > array.Length)
							{
								return num3 - array.Length;
							}
							return -1;
						}
					}
				}
			}
			return -1;
		}

		// Token: 0x060002B2 RID: 690 RVA: 0x0001F1D0 File Offset: 0x0001D3D0
		private static int FindBytePattern(ref byte[] containerBytes, ref byte[] searchBytes, int startAtIndex)
		{
			int result = -1;
			for (int i = startAtIndex; i < containerBytes.Length; i++)
			{
				if (i + searchBytes.Length > containerBytes.Length)
				{
					return -1;
				}
				if (containerBytes[i] == searchBytes[0])
				{
					bool flag = true;
					int num = i;
					for (int j = 1; j < searchBytes.Length; j++)
					{
						num++;
						if (searchBytes[j] != containerBytes[num])
						{
							flag = false;
							break;
						}
					}
					if (flag)
					{
						return i;
					}
				}
			}
			return result;
		}

		// Token: 0x060002B3 RID: 691 RVA: 0x0001F238 File Offset: 0x0001D438
		private static byte[] MergeArrays(ref byte[] arrayOne, ref byte[] arrayTwo)
		{
			arrayOne.GetType().GetElementType();
			byte[] array = new byte[arrayOne.Length + arrayTwo.Length];
			arrayOne.CopyTo(array, 0);
			arrayTwo.CopyTo(array, arrayOne.Length);
			return array;
		}

		// Token: 0x060002B4 RID: 692 RVA: 0x0001F278 File Offset: 0x0001D478
		private static void DeleteFile(object filePath)
		{
			try
			{
				if (File.Exists((string)filePath))
				{
					File.Delete((string)filePath);
				}
			}
			catch
			{
			}
		}

		// Token: 0x060002B5 RID: 693 RVA: 0x0001F2B4 File Offset: 0x0001D4B4
		public void Dispose()
		{
			this._bufferHistory.Clear();
			GC.Collect();
		}

		// Token: 0x04000167 RID: 359
		private char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x04000168 RID: 360
		private string _formPostID = "";

		// Token: 0x04000169 RID: 361
		private string _fieldSeperator = "";

		// Token: 0x0400016A RID: 362
		private long _currentBufferIndex;

		// Token: 0x0400016B RID: 363
		private bool _startFound;

		// Token: 0x0400016C RID: 364
		private bool _endFound;

		// Token: 0x0400016D RID: 365
		private string _currentFilePath = string.Empty;

		// Token: 0x0400016E RID: 366
		private string _currentFileName = string.Empty;

		// Token: 0x0400016F RID: 367
		private FileStream _fileStream;

		// Token: 0x04000170 RID: 368
		private long _startIndexBufferID = -1L;

		// Token: 0x04000171 RID: 369
		private int _startLocationInBufferID = -1;

		// Token: 0x04000172 RID: 370
		private long _endIndexBufferID = -1L;

		// Token: 0x04000173 RID: 371
		private int _endLocationInBufferID = -1;

		// Token: 0x04000174 RID: 372
		private Dictionary<long, byte[]> _bufferHistory = new Dictionary<long, byte[]>();

		// Token: 0x04000175 RID: 373
		private List<string> _finishedFiles = new List<string>();

		// Token: 0x04000176 RID: 374
		private bool _fileEndRead;

		// Token: 0x04000177 RID: 375
		private string _fileName = string.Empty;

		// Token: 0x04000178 RID: 376
		private string _folderNameRule = string.Empty;

		// Token: 0x04000179 RID: 377
		private string _fileNameRule = string.Empty;

		// Token: 0x0400017A RID: 378
		private string _fileNameRuleEx = string.Empty;

		// Token: 0x0400017B RID: 379
		private string _d5_prefix = string.Empty;

		// Token: 0x0400017C RID: 380
		private string _d5_subfix = string.Empty;
	}
}

package jcifs.netbios;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.StringTokenizer;
import jcifs.Config;
import jcifs.util.Hexdump;
import jcifs.util.LogStream;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NameServiceClient.class */
class NameServiceClient implements Runnable {
    static final int DEFAULT_RCV_BUF_SIZE = 576;
    static final int DEFAULT_SND_BUF_SIZE = 576;
    static final int NAME_SERVICE_UDP_PORT = 137;
    static final int DEFAULT_RETRY_COUNT = 2;
    static final int RESOLVER_LMHOSTS = 1;
    static final int RESOLVER_BCAST = 2;
    static final int RESOLVER_WINS = 3;
    private final Object LOCK;
    private int lport;
    private int closeTimeout;
    private byte[] snd_buf;
    private byte[] rcv_buf;
    private DatagramSocket socket;
    private DatagramPacket in;
    private DatagramPacket out;
    private HashMap responseTable;
    private Thread thread;
    private int nextNameTrnId;
    private int[] resolveOrder;
    InetAddress laddr;
    InetAddress baddr;
    private static final int SND_BUF_SIZE = Config.getInt("jcifs.netbios.snd_buf_size", 576);
    private static final int RCV_BUF_SIZE = Config.getInt("jcifs.netbios.rcv_buf_size", 576);
    static final int DEFAULT_SO_TIMEOUT = 5000;
    private static final int SO_TIMEOUT = Config.getInt("jcifs.netbios.soTimeout", DEFAULT_SO_TIMEOUT);
    private static final int RETRY_COUNT = Config.getInt("jcifs.netbios.retryCount", 2);
    static final int DEFAULT_RETRY_TIMEOUT = 3000;
    private static final int RETRY_TIMEOUT = Config.getInt("jcifs.netbios.retryTimeout", DEFAULT_RETRY_TIMEOUT);
    private static final int LPORT = Config.getInt("jcifs.netbios.lport", 0);
    private static final InetAddress LADDR = Config.getInetAddress("jcifs.netbios.laddr", null);
    private static final String RO = Config.getProperty("jcifs.resolveOrder");
    private static LogStream log = LogStream.getInstance();

    NameServiceClient() {
        this(LPORT, LADDR);
    }

    NameServiceClient(int lport, InetAddress laddr) {
        this.LOCK = new Object();
        this.responseTable = new HashMap();
        this.nextNameTrnId = 0;
        this.lport = lport;
        this.laddr = laddr;
        try {
            this.baddr = Config.getInetAddress("jcifs.netbios.baddr", InetAddress.getByName("***************"));
        } catch (UnknownHostException e) {
        }
        this.snd_buf = new byte[SND_BUF_SIZE];
        this.rcv_buf = new byte[RCV_BUF_SIZE];
        this.out = new DatagramPacket(this.snd_buf, SND_BUF_SIZE, this.baddr, NAME_SERVICE_UDP_PORT);
        this.in = new DatagramPacket(this.rcv_buf, RCV_BUF_SIZE);
        if (RO == null || RO.length() == 0) {
            if (NbtAddress.getWINSAddress() == null) {
                this.resolveOrder = new int[2];
                this.resolveOrder[0] = 1;
                this.resolveOrder[1] = 2;
                return;
            } else {
                this.resolveOrder = new int[3];
                this.resolveOrder[0] = 1;
                this.resolveOrder[1] = 3;
                this.resolveOrder[2] = 2;
                return;
            }
        }
        int[] tmp = new int[3];
        StringTokenizer st = new StringTokenizer(RO, ",");
        int i = 0;
        while (st.hasMoreTokens()) {
            String s = st.nextToken().trim();
            if (s.equalsIgnoreCase("LMHOSTS")) {
                int i2 = i;
                i++;
                tmp[i2] = 1;
            } else if (s.equalsIgnoreCase("WINS")) {
                if (NbtAddress.getWINSAddress() == null) {
                    LogStream logStream = log;
                    if (LogStream.level > 1) {
                        log.println("NetBIOS resolveOrder specifies WINS however the jcifs.netbios.wins property has not been set");
                    }
                } else {
                    int i3 = i;
                    i++;
                    tmp[i3] = 3;
                }
            } else if (s.equalsIgnoreCase("BCAST")) {
                int i4 = i;
                i++;
                tmp[i4] = 2;
            } else if (!s.equalsIgnoreCase("DNS")) {
                LogStream logStream2 = log;
                if (LogStream.level > 1) {
                    log.println("unknown resolver method: " + s);
                }
            }
        }
        this.resolveOrder = new int[i];
        System.arraycopy(tmp, 0, this.resolveOrder, 0, i);
    }

    int getNextNameTrnId() {
        int i = this.nextNameTrnId + 1;
        this.nextNameTrnId = i;
        if ((i & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH) == 0) {
            this.nextNameTrnId = 1;
        }
        return this.nextNameTrnId;
    }

    void ensureOpen(int timeout) throws IOException {
        this.closeTimeout = 0;
        if (SO_TIMEOUT != 0) {
            this.closeTimeout = Math.max(SO_TIMEOUT, timeout);
        }
        if (this.socket == null) {
            this.socket = new DatagramSocket(this.lport, this.laddr);
            this.thread = new Thread(this, "JCIFS-NameServiceClient");
            this.thread.setDaemon(true);
            this.thread.start();
        }
    }

    void tryClose() {
        synchronized (this.LOCK) {
            if (this.socket != null) {
                this.socket.close();
                this.socket = null;
            }
            this.thread = null;
            this.responseTable.clear();
        }
    }

    @Override // java.lang.Runnable
    public void run() {
        while (this.thread == Thread.currentThread()) {
            try {
                try {
                    this.in.setLength(RCV_BUF_SIZE);
                    this.socket.setSoTimeout(this.closeTimeout);
                    this.socket.receive(this.in);
                    LogStream logStream = log;
                    if (LogStream.level > 3) {
                        log.println("NetBIOS: new data read from socket");
                    }
                    int nameTrnId = NameServicePacket.readNameTrnId(this.rcv_buf, 0);
                    NameServicePacket response = (NameServicePacket) this.responseTable.get(new Integer(nameTrnId));
                    if (response != null && !response.received) {
                        synchronized (response) {
                            response.readWireFormat(this.rcv_buf, 0);
                            response.received = true;
                            LogStream logStream2 = log;
                            if (LogStream.level > 3) {
                                log.println(response);
                                Hexdump.hexdump(log, this.rcv_buf, 0, this.in.getLength());
                            }
                            response.notify();
                        }
                    }
                } catch (SocketTimeoutException e) {
                    tryClose();
                    return;
                } catch (Exception ex) {
                    LogStream logStream3 = log;
                    if (LogStream.level > 2) {
                        ex.printStackTrace(log);
                    }
                    tryClose();
                    return;
                }
            } catch (Throwable th) {
                tryClose();
                throw th;
            }
        }
        tryClose();
    }

    void send(NameServicePacket request, NameServicePacket response, int timeout) throws IOException {
        Integer nid = null;
        int max = NbtAddress.NBNS.length;
        if (max == 0) {
            max = 1;
        }
        synchronized (response) {
            while (true) {
                int i = max;
                max = i - 1;
                if (i <= 0) {
                    break;
                }
                try {
                    try {
                        synchronized (this.LOCK) {
                            request.nameTrnId = getNextNameTrnId();
                            nid = new Integer(request.nameTrnId);
                            this.out.setAddress(request.addr);
                            this.out.setLength(request.writeWireFormat(this.snd_buf, 0));
                            response.received = false;
                            this.responseTable.put(nid, response);
                            ensureOpen(timeout + 1000);
                            this.socket.send(this.out);
                            LogStream logStream = log;
                            if (LogStream.level > 3) {
                                log.println(request);
                                Hexdump.hexdump(log, this.snd_buf, 0, this.out.getLength());
                            }
                        }
                        long start = System.currentTimeMillis();
                        while (timeout > 0) {
                            response.wait(timeout);
                            if (!response.received || request.questionType != response.recordType) {
                                response.received = false;
                                timeout = (int) (timeout - (System.currentTimeMillis() - start));
                            } else {
                                this.responseTable.remove(nid);
                                return;
                            }
                        }
                        this.responseTable.remove(nid);
                        synchronized (this.LOCK) {
                            if (!NbtAddress.isWINS(request.addr)) {
                                break;
                            }
                            if (request.addr == NbtAddress.getWINSAddress()) {
                                NbtAddress.switchWINS();
                            }
                            request.addr = NbtAddress.getWINSAddress();
                        }
                        break;
                    } catch (Throwable th) {
                        this.responseTable.remove(nid);
                        throw th;
                    }
                } catch (InterruptedException ie) {
                    throw new IOException(ie.getMessage());
                }
            }
        }
    }

    NbtAddress[] getAllByName(Name name, InetAddress addr) throws UnknownHostException {
        int n;
        NameQueryRequest request = new NameQueryRequest(name);
        NameQueryResponse response = new NameQueryResponse();
        request.addr = addr != null ? addr : NbtAddress.getWINSAddress();
        request.isBroadcast = request.addr == null;
        if (request.isBroadcast) {
            request.addr = this.baddr;
            n = RETRY_COUNT;
        } else {
            request.isBroadcast = false;
            n = 1;
        }
        do {
            try {
                send(request, response, RETRY_TIMEOUT);
                if (response.received && response.resultCode == 0) {
                    return response.addrEntry;
                }
                n--;
                if (n <= 0) {
                    break;
                }
            } catch (IOException ioe) {
                LogStream logStream = log;
                if (LogStream.level > 1) {
                    ioe.printStackTrace(log);
                }
                throw new UnknownHostException(name.name);
            }
        } while (request.isBroadcast);
        throw new UnknownHostException(name.name);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    NbtAddress getByName(Name name, InetAddress addr) throws UnknownHostException {
        NameQueryRequest request = new NameQueryRequest(name);
        NameQueryResponse response = new NameQueryResponse();
        if (addr != null) {
            request.addr = addr;
            request.isBroadcast = addr.getAddress()[3] == -1;
            int n = RETRY_COUNT;
            do {
                try {
                    send(request, response, RETRY_TIMEOUT);
                    if (response.received && response.resultCode == 0) {
                        int last = response.addrEntry.length - 1;
                        response.addrEntry[last].hostName.srcHashCode = addr.hashCode();
                        return response.addrEntry[last];
                    }
                    n--;
                    if (n <= 0) {
                        break;
                    }
                } catch (IOException ioe) {
                    LogStream logStream = log;
                    if (LogStream.level > 1) {
                        ioe.printStackTrace(log);
                    }
                    throw new UnknownHostException(name.name);
                }
            } while (request.isBroadcast);
            throw new UnknownHostException(name.name);
        }
        for (int i = 0; i < this.resolveOrder.length; i++) {
            switch (this.resolveOrder[i]) {
                case 1:
                    NbtAddress ans = Lmhosts.getByName(name);
                    if (ans != null) {
                        ans.hostName.srcHashCode = 0;
                        return ans;
                    }
                case 2:
                case 3:
                    if (this.resolveOrder[i] == 3 && name.name != NbtAddress.MASTER_BROWSER_NAME && name.hexCode != 29) {
                        request.addr = NbtAddress.getWINSAddress();
                        request.isBroadcast = false;
                    } else {
                        request.addr = this.baddr;
                        request.isBroadcast = true;
                    }
                    int n2 = RETRY_COUNT;
                    do {
                        int i2 = n2;
                        n2 = i2 - 1;
                        if (i2 > 0) {
                            try {
                                send(request, response, RETRY_TIMEOUT);
                                if (response.received && response.resultCode == 0) {
                                    response.addrEntry[0].hostName.srcHashCode = request.addr.hashCode();
                                    return response.addrEntry[0];
                                }
                            } catch (IOException ioe2) {
                                LogStream logStream2 = log;
                                if (LogStream.level > 1) {
                                    ioe2.printStackTrace(log);
                                }
                                throw new UnknownHostException(name.name);
                            }
                        }
                    } while (this.resolveOrder[i] != 3);
                    break;
                default:
            }
        }
        throw new UnknownHostException(name.name);
    }

    NbtAddress[] getNodeStatus(NbtAddress addr) throws UnknownHostException {
        NodeStatusResponse response = new NodeStatusResponse(addr);
        NodeStatusRequest request = new NodeStatusRequest(new Name("*������������������������������", 0, null));
        request.addr = addr.getInetAddress();
        int n = RETRY_COUNT;
        while (true) {
            int i = n;
            n = i - 1;
            if (i > 0) {
                try {
                    send(request, response, RETRY_TIMEOUT);
                    if (response.received && response.resultCode == 0) {
                        int srcHashCode = request.addr.hashCode();
                        for (int i2 = 0; i2 < response.addressArray.length; i2++) {
                            response.addressArray[i2].hostName.srcHashCode = srcHashCode;
                        }
                        return response.addressArray;
                    }
                } catch (IOException ioe) {
                    LogStream logStream = log;
                    if (LogStream.level > 1) {
                        ioe.printStackTrace(log);
                    }
                    throw new UnknownHostException(addr.toString());
                }
            } else {
                throw new UnknownHostException(addr.hostName.name);
            }
        }
    }
}

package javax.servlet.http;

import java.util.Hashtable;
import java.util.ResourceBundle;
import java.util.StringTokenizer;

/* loaded from: servlet-api.jar:javax/servlet/http/HttpUtils.class */
public class HttpUtils {
    private static final String LSTRING_FILE = "javax.servlet.http.LocalStrings";
    private static ResourceBundle lStrings = ResourceBundle.getBundle(LSTRING_FILE);

    public static Hashtable parseQueryString(String s) {
        String[] valArray;
        if (s == null) {
            throw new IllegalArgumentException();
        }
        Hashtable ht = new Hashtable();
        StringBuffer sb = new StringBuffer();
        StringTokenizer st = new StringTokenizer(s, "&");
        while (st.hasMoreTokens()) {
            String pair = st.nextToken();
            int pos = pair.indexOf(61);
            if (pos == -1) {
                throw new IllegalArgumentException();
            }
            String key = parseName(pair.substring(0, pos), sb);
            String val = parseName(pair.substring(pos + 1, pair.length()), sb);
            if (ht.containsKey(key)) {
                String[] oldVals = (String[]) ht.get(key);
                valArray = new String[oldVals.length + 1];
                for (int i = 0; i < oldVals.length; i++) {
                    valArray[i] = oldVals[i];
                }
                valArray[oldVals.length] = val;
            } else {
                valArray = new String[]{val};
            }
            ht.put(key, valArray);
        }
        return ht;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x006f, code lost:
    
        r10 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x007b, code lost:
    
        throw new java.lang.IllegalArgumentException(r10.getMessage());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.util.Hashtable parsePostData(int r7, javax.servlet.ServletInputStream r8) {
        /*
            r0 = r7
            if (r0 > 0) goto Lc
            java.util.Hashtable r0 = new java.util.Hashtable
            r1 = r0
            r1.<init>()
            return r0
        Lc:
            r0 = r8
            if (r0 != 0) goto L18
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r1 = r0
            r1.<init>()
            throw r0
        L18:
            r0 = r7
            byte[] r0 = new byte[r0]
            r9 = r0
            r0 = 0
            r10 = r0
        L1e:
            r0 = r8
            r1 = r9
            r2 = r10
            r3 = r7
            r4 = r10
            int r3 = r3 - r4
            int r0 = r0.read(r1, r2, r3)     // Catch: java.io.IOException -> L50
            r11 = r0
            r0 = r11
            if (r0 > 0) goto L42
            java.util.ResourceBundle r0 = javax.servlet.http.HttpUtils.lStrings     // Catch: java.io.IOException -> L50
            java.lang.String r1 = "err.io.short_read"
            java.lang.String r0 = r0.getString(r1)     // Catch: java.io.IOException -> L50
            r12 = r0
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException     // Catch: java.io.IOException -> L50
            r1 = r0
            r2 = r12
            r1.<init>(r2)     // Catch: java.io.IOException -> L50
            throw r0     // Catch: java.io.IOException -> L50
        L42:
            r0 = r10
            r1 = r11
            int r0 = r0 + r1
            r10 = r0
            r0 = r7
            r1 = r10
            int r0 = r0 - r1
            if (r0 > 0) goto L1e
            goto L5d
        L50:
            r10 = move-exception
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r1 = r0
            r2 = r10
            java.lang.String r2 = r2.getMessage()
            r1.<init>(r2)
            throw r0
        L5d:
            java.lang.String r0 = new java.lang.String     // Catch: java.io.UnsupportedEncodingException -> L6f
            r1 = r0
            r2 = r9
            r3 = 0
            r4 = r7
            java.lang.String r5 = "8859_1"
            r1.<init>(r2, r3, r4, r5)     // Catch: java.io.UnsupportedEncodingException -> L6f
            r10 = r0
            r0 = r10
            java.util.Hashtable r0 = parseQueryString(r0)     // Catch: java.io.UnsupportedEncodingException -> L6f
            return r0
        L6f:
            r10 = move-exception
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r1 = r0
            r2 = r10
            java.lang.String r2 = r2.getMessage()
            r1.<init>(r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: javax.servlet.http.HttpUtils.parsePostData(int, javax.servlet.ServletInputStream):java.util.Hashtable");
    }

    private static String parseName(String s, StringBuffer sb) {
        sb.setLength(0);
        int i = 0;
        while (i < s.length()) {
            char c = s.charAt(i);
            switch (c) {
                case '%':
                    try {
                        sb.append((char) Integer.parseInt(s.substring(i + 1, i + 3), 16));
                        i += 2;
                        break;
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException();
                    } catch (StringIndexOutOfBoundsException e2) {
                        String rest = s.substring(i);
                        sb.append(rest);
                        if (rest.length() != 2) {
                            break;
                        } else {
                            i++;
                            break;
                        }
                    }
                case '+':
                    sb.append(' ');
                    break;
                default:
                    sb.append(c);
                    break;
            }
            i++;
        }
        return sb.toString();
    }

    public static StringBuffer getRequestURL(HttpServletRequest req) {
        StringBuffer url = new StringBuffer();
        String scheme = req.getScheme();
        int port = req.getServerPort();
        String urlPath = req.getRequestURI();
        url.append(scheme);
        url.append("://");
        url.append(req.getServerName());
        if ((scheme.equals("http") && port != 80) || (scheme.equals("https") && port != 443)) {
            url.append(':');
            url.append(req.getServerPort());
        }
        url.append(urlPath);
        return url;
    }
}

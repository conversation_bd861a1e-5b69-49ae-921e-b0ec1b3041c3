/*
 Copyright (c) 2014, Raonwiz Technology Inc. All rights reserved.
*/
window.DEXT5UPLOAD||(window.DEXT5UPLOAD=function(){var b=window.DEXT5UPLOAD_ROOTPATH||"";if(!b)for(var c=document.getElementsByTagName("script"),a=c.length,d=null,e=0;e<a;e++)if(d=c[e],d=d.src.match(/(^|.*[\\\/])dext5upload.js/i)){b=d[1];break}-1==b.indexOf(":/")&&(b=0===b.indexOf("/")?location.href.match(/^.*?:\/\/[^\/]*/)[0]+b:location.href.match(/^[^\?]*\/(?:)/)[0]+b);b=b.substring(0,b.length-1);b=b.substring(0,b.lastIndexOf("/"))+"/";if(!b)throw"DEXT5 Upload installation path could not be automatically detected.";
return{isRelease:!0,isPopUpCssLoaded:!1,version:"DEXT5 Upload",ReleaseVer:"2.7.1127028.1826.01",ServerReleaseVer:"",cabVersion:"5,0,0,47",rootPath:b,DEXTMULTIPLE:{},DEXTHOLDER:{},DEXTMULTIPLEID:[],CUploadID:""}}(),DEXT5UPLOAD.brower||(DEXT5UPLOAD.browser=function(){var b=navigator.userAgent.toLowerCase(),c=window.opera,c={ie:-1<b.search("trident")||-1<b.search("msie")||-1<b.search("edge/12")||-1<b.search("edge/13")?!0:!1,edge:-1<b.search("edge/12")||-1<b.search("edge/13")?!0:!1,opera:!!c&&c.version,
webkit:-1<b.indexOf(" applewebkit/"),mac:-1<b.indexOf("macintosh"),quirks:"BackCompat"==document.compatMode,mobile:-1<b.indexOf("mobile"),iOS:/(ipad|iphone|ipod)/.test(b),isCustomDomain:function(a){if(!this.ie)return!1;var b=a.domain;a=DEXT5UPLOAD.util.getDocWindow(a).location.hostname;return b!=a&&b!="["+a+"]"},isHttps:"https:"==location.protocol,HTML5Supported:!0,HTML5PlusSupported:!1,ajaxOnProgressSupported:!1};c.gecko="Gecko"==navigator.product&&!c.webkit&&!c.opera;c.ie&&(c.gecko=!1);c.webkit&&
(-1<b.indexOf("chrome")?(c.chrome=!0,-1<b.indexOf("opr")&&(c.opera=!0,c.chrome=!1)):c.safari=!0);var a;c.ieVersion=0;c.ie&&(c.quirks||!document.documentMode?-1<b.indexOf("msie")?a=parseFloat(b.match(/msie (\d+)/)[1]):-1<b.indexOf("trident")?a=parseFloat(b.match(/rv:([\d\.]+)/)[1]):-1<b.indexOf("edge/12")||-1<b.indexOf("edge/13")?(a=12,c.chrome=!1):a=7:a=document.documentMode,c.ieVersion=a,c.ie12=12==a,c.ie11=11==a,c.ie10=10==a,c.ie9=9==a,c.ie8=8==a,c.ie7=7==a,c.ie6=7>a||c.quirks);c.gecko&&(a=b.match(/rv:([\d\.]+)/))&&
(a=a[1].split("."),a=1E4*a[0]+100*(a[1]||0)+1*(a[2]||0));c.webkit&&(a=parseFloat(b.match(/ applewebkit\/(\d+)/)[1]));c.HTML5Supported="File"in window&&"FileReader"in window&&"Blob"in window;c.HTML5PlusSupported="File"in window&&"FileReader"in window&&"Blob"in window&&("WebSocket"in window||"MozWebSocket"in window);c.WorkerSupported="Worker"in window;c.imageProcessWorkerSupported="Worker"in window;b=null;try{b=new XMLHttpRequest,c.ajaxOnProgressSupported=!!(b&&"upload"in b&&"onprogress"in b.upload)}catch(d){c.ajaxOnProgressSupported=
!1}b=null;return c}()),DEXT5UPLOAD.UserAgent||(DEXT5UPLOAD.UserAgent=function(){var b=window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"",c={extend:function(a,b){for(var c in b)-1!=="browser cpu device engine os".indexOf(c)&&0===b[c].length%2&&(a[c]=b[c].concat(a[c]));return a},has:function(a,b){return"string"===typeof a?-1!==b.toLowerCase().indexOf(a.toLowerCase()):!1},lowerize:function(a){return a.toLowerCase()},major:function(a){return"string"===typeof a?a.split(".")[0]:
void 0}},a=function(){for(var a,c=0,e,d,f,k,x,q,v=arguments;c<v.length&&!x;){var l=v[c],h=v[c+1];if("undefined"===typeof a)for(f in a={},h)k=h[f],"object"===typeof k?a[k[0]]=void 0:a[k]=void 0;for(e=d=0;e<l.length&&!x;)if(x=l[e++].exec(b))for(f=0;f<h.length;f++)q=x[++d],k=h[f],"object"===typeof k&&0<k.length?2==k.length?a[k[0]]="function"==typeof k[1]?k[1].call(this,q):k[1]:3==k.length?a[k[0]]="function"!==typeof k[1]||k[1].exec&&k[1].test?q?q.replace(k[1],k[2]):void 0:q?k[1].call(this,q,k[2]):void 0:
4==k.length&&(a[k[0]]=q?k[3].call(this,q.replace(k[1],k[2])):void 0):a[k]=q?q:void 0;c+=2}return a},d=function(a,b){for(var e in b)if("object"===typeof b[e]&&0<b[e].length)for(var d=0;d<b[e].length;d++){if(c.has(b[e][d],a))return"?"===e?void 0:e}else if(c.has(b[e],a))return"?"===e?void 0:e;return a},e={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2E3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},f=[[/\((ipad|playbook);[\w\s\);-]+(rim|apple)/i],
["model","vendor",["type","tablet"]],[/applecoremedia\/[\w\.]+ \((ipad)/],["model",["vendor","Apple"],["type","tablet"]],[/(apple\s{0,1}tv)/i],[["model","Apple TV"],["vendor","Apple"]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i],["vendor","model",["type","tablet"]],[/(kf[A-z]+)\sbuild\/[\w\.]+.*silk\//i],["model",["vendor","Amazon"],["type","tablet"]],[/(sd|kf)[0349hijorstuw]+\sbuild\/[\w\.]+.*silk\//i],[["model",
d,{"Fire Phone":["SD","KF"]}],["vendor","Amazon"],["type","mobile"]],[/\((ip[honed|\s\w*]+);.+(apple)/i],["model","vendor",["type","mobile"]],[/\((ip[honed|\s\w*]+);/i],["model",["vendor","Apple"],["type","mobile"]],[/(blackberry)[\s-]?(\w+)/i,/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|huawei|meizu|motorola|polytron)[\s_-]?([\w-]+)*/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i],["vendor","model",["type","mobile"]],[/\(bb10;\s(\w+)/i],["model",["vendor","BlackBerry"],["type","mobile"]],[/android.+(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus 7)/i],
["model",["vendor","Asus"],["type","tablet"]],[/(sony)\s(tablet\s[ps])\sbuild\//i,/(sony)?(?:sgp.+)\sbuild\//i],[["vendor","Sony"],["model","Xperia Tablet"],["type","tablet"]],[/(?:sony)?(?:(?:(?:c|d)\d{4})|(?:so[-l].+))\sbuild\//i],[["vendor","Sony"],["model","Xperia Phone"],["type","mobile"]],[/\s(ouya)\s/i,/(nintendo)\s([wids3u]+)/i],["vendor","model",["type","console"]],[/android.+;\s(shield)\sbuild/i],["model",["vendor","Nvidia"],["type","console"]],[/(playstation\s[3portablevi]+)/i],["model",
["vendor","Sony"],["type","console"]],[/(sprint\s(\w+))/i],[["vendor",d,{HTC:"APA",Sprint:"Sprint"}],["model",d,{"Evo Shift 4G":"7373KT"}],["type","mobile"]],[/(lenovo)\s?(S(?:5000|6000)+(?:[-][\w+]))/i],["vendor","model",["type","tablet"]],[/(htc)[;_\s-]+([\w\s]+(?=\))|\w+)*/i,/(zte)-(\w+)*/i,/(alcatel|geeksphone|huawei|lenovo|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]+)*/i],["vendor",["model",/_/g," "],["type","mobile"]],[/(nexus\s9)/i],["model",["vendor","HTC"],["type","tablet"]],[/[\s\(;](xbox(?:\sone)?)[\s\);]/i],
["model",["vendor","Microsoft"],["type","console"]],[/(kin\.[onetw]{3})/i],[["model",/\./g," "],["vendor","Microsoft"],["type","mobile"]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?(:?\s4g)?)[\w\s]+build\//i,/mot[\s-]?(\w+)*/i,/(XT\d{3,4}) build\//i],["model",["vendor","Motorola"],["type","mobile"]],[/android.+\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],["model",["vendor","Motorola"],["type","tablet"]],[/android.+((sch-i[89]0\d|shw-m380s|gt-p\d{4}|gt-n8000|sgh-t8[56]9|nexus 10))/i,/((SM-T\w+))/i],
[["vendor","Samsung"],"model",["type","tablet"]],[/((s[cgp]h-\w+|gt-\w+|galaxy\snexus|sm-n900))/i,/(sam[sung]*)[\s-]*(\w+-?[\w-]*)*/i,/sec-((sgh\w+))/i],[["vendor","Samsung"],"model",["type","mobile"]],[/(samsung);smarttv/i],["vendor","model",["type","smarttv"]],[/\(dtv[\);].+(aquos)/i],["model",["vendor","Sharp"],["type","smarttv"]],[/sie-(\w+)*/i],["model",["vendor","Siemens"],["type","mobile"]],[/(maemo|nokia).*(n900|lumia\s\d+)/i,/(nokia)[\s_-]?([\w-]+)*/i],[["vendor","Nokia"],"model",["type",
"mobile"]],[/android\s3\.[\s\w;-]{10}(a\d{3})/i],["model",["vendor","Acer"],["type","tablet"]],[/android\s3\.[\s\w;-]{10}(lg?)-([06cv9]{3,4})/i],[["vendor","LG"],"model",["type","tablet"]],[/(lg) netcast\.tv/i],["vendor","model",["type","smarttv"]],[/(nexus\s[45])/i,/lg[e;\s\/-]+(\w+)*/i],["model",["vendor","LG"],["type","mobile"]],[/android.+(ideatab[a-z0-9\-\s]+)/i],["model",["vendor","Lenovo"],["type","tablet"]],[/linux;.+((jolla));/i],["vendor","model",["type","mobile"]],[/((pebble))app\/[\d\.]+\s/i],
["vendor","model",["type","wearable"]],[/android.+;\s(glass)\s\d/i],["model",["vendor","Google"],["type","wearable"]],[/android.+(\w+)\s+build\/hm\1/i,/android.+(hm[\s\-_]*note?[\s_]*(?:\d\w)?)\s+build/i,/android.+(mi[\s\-_]*(?:one|one[\s_]plus)?[\s_]*(?:\d\w)?)\s+build/i],[["model",/_/g," "],["vendor","Xiaomi"],["type","mobile"]],[/(mobile|tablet);.+rv\:.+gecko\//i],[["type",c.lowerize],"vendor","model"]],e=[[/microsoft\s(windows)\s(vista|xp)/i],["name","version"],[/(windows)\snt\s6\.2;\s(arm)/i,
/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],["name",["version",d,e]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[["name","Windows"],["version",d,e]],[/\((bb)(10);/i],[["name","BlackBerry"],"version"],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],["name","version"],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[["name","Symbian"],"version"],
[/\((series40);/i],["name"],[/mozilla.+\(mobile;.+gecko.+firefox/i],[["name","Firefox OS"],"version"],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],["name","version"],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[["name","Chromium OS"],"version"],[/(sunos)\s?([\w\.]+\d)*/i],
[["name","Solaris"],"version"],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],["name","version"],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[["name","iOS"],["version",/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[["name","Mac OS"],["version",/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],["name","version"]],
d=a.apply(this,[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],["name","version"],[/\s(opr)\/([\w\.]+)/i],[["name","Opera"],"version"],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],
["name","version"],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[["name","IE"],"version"],[/(edge)\/((\d+)?[\w\.]+)/i],["name","version"],[/(yabrowser)\/([\w\.]+)/i],[["name","Yandex"],"version"],[/(comodo_dragon)\/([\w\.]+)/i],[["name",/_/g," "],"version"],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],["name","version"],[/(dolfin)\/([\w\.]+)/i],[["name","Dolphin"],"version"],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[["name","Chrome"],
"version"],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],["version",["name","MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],["version",["name","Android Browser"]],[/FBAV\/([\w\.]+);/i],["version",["name","Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],["version",["name","Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],["version","name"],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],["name",["version",d,{"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412",
"2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],["name","version"],[/(navigator|netscape)\/([\w\.-]+)/i],[["name","Netscape"],"version"],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,
/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],["name","version"]]);d.major=c.major(d.version);e=a.apply(this,e);if(a=a.apply(this,f))void 0==a.model&&(a.model=""),void 0==a.type&&(a.type=""),void 0==a.vendor&&(a.vendor="");return{browser:d,os:e,device:a}}()),DEXT5UPLOAD.ajax||(DEXT5UPLOAD.ajax=function(){var b=function(){try{return new XMLHttpRequest}catch(a){}try{return new ActiveXObject("Msxml2.XHLHTTP")}catch(b){}try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(c){}return null},
c=function(){},a=function(a){return 4==a.readyState&&(200<=a.status&&300>a.status||304==a.status||0===a.status||1223==a.status)},d=function(b){var e=null;a(b)&&(e=b.responseText);b&&b.onreadystatechange&&(b.onreadystatechange=c);return e},e=function(b){var e=null;a(b)&&(e=b.responseXML,e||(e=b.responseText));b&&b.onreadystatechange&&(b.onreadystatechange=c);return e},f=function(a,c,e){var d=!!c,f=b();if(!f)return null;f.open("GET",a,d);d&&(f.onreadystatechange=function(){4==f.readyState&&c(e(f))});
try{f.send(null)}catch(g){return null}if(!d){var q=setTimeout(function(){try{f.abort()}catch(a){}clearTimeout(q)},5E3);"undefined"==typeof DEXT5UPLOAD.DEXTMULTIPLETIMEOUT&&(DEXT5UPLOAD.DEXTMULTIPLETIMEOUT=[]);DEXT5UPLOAD.DEXTMULTIPLETIMEOUT.push(q)}return d?"":e(f)},g=function(a,c,e,d){var f=!!e,g=b();if(!g)return null;g.open("POST",a,f);g.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");f&&(g.onreadystatechange=function(){4==g.readyState&&e(d(g,c))});try{g.send(c)}catch(q){return null}if(!f){var v=
setTimeout(function(){try{g.abort()}catch(a){}clearTimeout(v)},5E3);"undefined"==typeof DEXT5UPLOAD.DEXTMULTIPLETIMEOUT&&(DEXT5UPLOAD.DEXTMULTIPLETIMEOUT=[]);DEXT5UPLOAD.DEXTMULTIPLETIMEOUT.push(v)}return f?"":d(g)};return{load:function(a,b){return f(a,b,d)},loadXml:function(a,b){return f(a,b,e)},postData:function(a,b,c){return g(a,b,c,d)},postFileData:function(a,c){var e;a:if(e=b()){try{e.open("POST",a,!1);var d="--------------------"+(new Date).getTime();e.setRequestHeader("Content-Type","multipart/form-data; boundary="+
d);for(var d="--"+d,f="",g=c.split("&"),q=g.length,v=null,l="",h="",r=0;r<q;r++)v=g[r].split("="),"imagedata"==v[0]?l=v[1]:(f+=d+"\r\n",f+='Content-Disposition: form-data; name="'+v[0]+'"\r\n\r\n',f+=v[1]+"\r\n");for(var g=null,q="",w=window.atob(l),y=w.length,g=new Uint8Array(new ArrayBuffer(y)),r=0;r<y;r++)g[r]=w.charCodeAt(r),q+=String.fromCharCode(g[r]);String.fromCharCode.apply([],new Uint8Array(g));new Uint8Array(g);h=q;f+=d+"\r\n";f+='Content-Disposition: form-data; name="Filedata"; filename="'+
(new Date).getTime()+'"\r\n';f+="Content-Type: image/png\r\n";f+="\r\n";f+=h+"\r\n";e.send(f+(d+"--\r\n"))}catch(A){e=null;break a}e=e.responseText}else e=null;return e},createXMLHttpRequest:function(){return b()}}}()),DEXT5UPLOAD.util||(DEXT5UPLOAD.util={G_IMG_LIST:{},trim:function(b){return b.trim?b.trim():b.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")},getDefaultIframeSrc:function(){var b="",b="document.open();"+(DEXT5UPLOAD.browser.isCustomDomain(document)?'document.domain="'+document.domain+'";':"")+
" document.close();";return b=DEXT5UPLOAD.browser.ie?"javascript:void(function(){"+encodeURIComponent(b)+"}())":""},makeIframe:function(){var b;try{b=document.createElement('<iframe frameborder="0" >')}catch(c){b=document.createElement("iframe")}b.style.margin="0px";b.style.padding="0px";b.frameBorder=0;b.style.overflow="auto";b.style.overflowX="hidden";b.style.backgroundColor="#ffffff";b.title="DEXT5Upload";b.src=this.getDefaultIframeSrc();return b},addEvent:function(b,c,a,d){b.addEventListener?
b.addEventListener(c,a,d):b.attachEvent&&b.attachEvent("on"+c,a);try{""!=DEXT5UPLOAD.CUploadID?(DEXT5UPLOAD.DEXTMULTIPLEEVENT={},DEXT5UPLOAD.DEXTMULTIPLEEVENT[DEXT5UPLOAD.CUploadID]=[],DEXT5UPLOAD.DEXTMULTIPLEEVENT[DEXT5UPLOAD.CUploadID].push({element:b,event:c,func:a}),DEXT5UPLOAD.CUploadID=""):UPLOADTOP.G_CURRUPLOADER&&DEXT5UPLOAD.DEXTMULTIPLEEVENT[UPLOADTOP.G_CURRUPLOADER.ID]&&DEXT5UPLOAD.DEXTMULTIPLEEVENT[UPLOADTOP.G_CURRUPLOADER.ID].push({element:b,event:c,func:a})}catch(e){}},removeEvent:function(b,
c,a,d){b.removeEventListener?b.removeEventListener(c,a,d):b.detachEvent&&b.detachEvent("on"+c,a)},stopEvent:function(b){"bubbles"in b?b.bubbles&&b.stopPropagation():b.cancelBubble=!0},cancelEvent:function(b){b.preventDefault?b.preventDefault():b.returnValue=!1},ajax:{xml_http_request:function(){var b;window.XMLHttpRequest?b=new XMLHttpRequest:window.ActiveXObject&&(b=new ActiveXObject("Microsoft.XMLHTTP"));return b}},url:{full_url:function(b){var c="";if(0==b.indexOf("http://")||0==b.indexOf("https://"))c=
b;else if(0==b.indexOf("/"))c=location.protocol+"//"+location.host+b;else var c=location.href,a=c.lastIndexOf("/"),c=c.substring(0,a+1),c=c+b;return c}},xml:{count:function(b,c){return b?b.getElementsByTagName(c).length:0},getNode:function(b,c){return null==b||void 0==b?null:this.getNodeIdx(b,c,0)},getNodeIdx:function(b,c,a){return b.getElementsByTagName(c)[a]},getNodeValue:function(b,c){return null==b||void 0==b?"":this.getNodeValueIdx(b,c,0)},getNodeValueIdx:function(b,c,a){b=this.getNodeIdx(b,
c,a);return this.getValue(b)},getValue:function(b){var c="",a="";if(void 0!=b)try{0<b.childNodes.length&&(a=c=b.firstChild.nodeValue);try{("product_key"==b.nodeName||"font"==b.nodeName||"encoding"==b.nodeName||"doctype"==b.nodeName)&&2<=b.childNodes.length&&(c=b.textContent?b.textContent:a)}catch(d){c=a}}catch(e){c="parsing error"}return c}},removeEvents:function(b,c,a){b=c.split(",");c=a.length;for(var d=0,e=0;e<c;e++)for(var d=b.length,f=0;f<d;f++)a[e].removeAttribute(b[f])},removeElementWithChildNodes:function(b){try{if(null!=
b){for(;b.hasChildNodes();)b.removeChild(b.firstChild),b.firstChild=null;b.parentNode.removeChild(b)}}catch(c){}},bytesToSize:function(b){b=parseInt(b,10);var c="0 byte";isNaN(b)&&(b="",c="N/A");c={size:0,unit:"byte",toString:c};if(0==b)return c;var a=parseInt(Math.floor(Math.log(b)/Math.log(1024)));c.size=Math.round(b/Math.pow(1024,a)*100,2)/100;c.unit=["bytes","KB","MB","GB","TB"][a];c.toString=c.size+" "+c.unit;return c},stringToXML:function(b){var c;try{window.ActiveXObject?(c=new ActiveXObject("Microsoft.XMLDOM"),
c.async="false",c.loadXML(b)):c=(new DOMParser).parseFromString(b,"text/xml")}catch(a){c=null}return c},getUnitSize:function(b){var c=1;switch(b.toLowerCase()){case "kb":c*=1024;break;case "mb":c*=1048576;break;case "gb":c*=1073741824}return c},getUnit:function(b){b=b.toLowerCase();var c="";-1<b.indexOf("mb")?c=b.substring(b.indexOf("mb")):-1<b.indexOf("gb")?c=b.substring(b.indexOf("gb")):-1<b.indexOf("kb")?c=b.substring(b.indexOf("kb")):-1<b.indexOf("b")&&(c=b.substring(b.indexOf("b")));return c},
getDate:function(){var b=0,c=new Date,b=""+c.getFullYear(),a=""+(c.getMonth()+1);1==a.length&&(a="0"+a);c=""+c.getDate();1==c.length&&(c="0"+c);return b=parseInt(b+a+c,10)},getMimeFilter:function(b){var c="",c=b.split(",").join(",.");""!=c&&(c="."+c);return c},getMimeFilterEx:function(b){var c="",c=b.split(",").join(";*.");""!=c&&(c="*."+c);return c},getExtStringFromExtEx:function(b){b=b.toLowerCase().split("|");for(var c=b.length,a="",d=1;d<c;d+=2){var e=b[d].replace(/\*./gi,""),e=e.replace(/\*/gi,
""),e=e.replace(/;$/gi,""),e=e.replace(/;/gi,",");""!=a&&(a+=",");a+=e}return a},parseIntOr0:function(b){b=parseInt(b,10);return isNaN(b)?0:b},makeGuidTagName:function(b){var c=0,a=(new Date).getTime().toString(32),d;for(d=0;5>d;d++)a+=Math.floor(65535*Math.random()).toString(32);return(b||"o_")+a+(c++).toString(32)},makeGuid:function(b){var c=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},c=(c()+c()+"-"+c()+"-"+c()+"-"+c()+"-"+c()+c()+c()).toUpperCase();void 0!=b&&(c=b+"-"+
c);return c},getUserLang:function(b){b=b.toLowerCase();var c="ko-kr";-1<b.indexOf("ko")?c="ko-kr":-1<b.indexOf("en")?c="en-us":-1<b.indexOf("ja")?c="ja-jp":-1<b.indexOf("zh-cn")?c="zh-cn":-1<b.indexOf("zh-tw")&&(c="zh-tw");return c},getDocWindow:function(b){return b.parentWindow||b.defaultView},getClientRect:function(b){var c={left:0,top:0,right:0,bottom:0};try{c=b.getBoundingClientRect()}catch(a){}return c},getParentbyTagName:function(b,c){for(var a=b;null!=a&&(!a.tagName||a.tagName.toLowerCase()!=
c);)a=a.parentNode;return a},getElementsByClass:function(b,c,a){var d=[];null==c&&(c=document);null==a&&(a="*");c=c.getElementsByTagName(a);a=c.length;b=new RegExp("(^|\\s)"+b+"(\\s|$)");for(j=i=0;i<a;i++)b.test(c[i].className)&&(d[j]=c[i],j++);return d},postFormData:function(b,c,a,d){void 0==d&&(d=[]);var e=b.createElement("form");e.method="post";e.action=c;e.target=a;c=d.length;for(a=0;a<c;a++){var f=b.createElement("input");f.type="hidden";f.name=d[a][0];f.value=d[a][1];e.appendChild(f)}b.body.appendChild(e);
e.submit();b.body.removeChild(e)},base64_decode:function(b){var c="",a,d,e,f,g,p=0;for(b=b.replace(/[^A-Za-z0-9\+\/\=]/g,"");p<b.length;)a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(p++)),d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(p++)),f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(p++)),g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(b.charAt(p++)),
a=a<<2|d>>4,d=(d&15)<<4|f>>2,e=(f&3)<<6|g,c+=String.fromCharCode(a),64!=f&&(c+=String.fromCharCode(d)),64!=g&&(c+=String.fromCharCode(e));return c=DEXT5UPLOAD.util.utf8_decode(c)},base64_encode:function(b){var c="",a,d,e,f,g,p,u=0;for(b=DEXT5UPLOAD.util.utf8_encode(b);u<b.length;)a=b.charCodeAt(u++),d=b.charCodeAt(u++),e=b.charCodeAt(u++),f=a>>2,a=(a&3)<<4|d>>4,g=(d&15)<<2|e>>6,p=e&63,isNaN(d)?g=p=64:isNaN(e)&&(p=64),c=c+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(f)+
"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(a)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(g)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(p);return c},utf8_encode:function(b){b=b.replace(/\r\n/g,"\n");for(var c="",a=0;a<b.length;a++){var d=b.charCodeAt(a);128>d?c+=String.fromCharCode(d):(127<d&&2048>d?c+=String.fromCharCode(d>>6|192):(c+=String.fromCharCode(d>>12|224),c+=String.fromCharCode(d>>6&63|128)),
c+=String.fromCharCode(d&63|128))}return c},utf8_decode:function(b){for(var c="",a=0,d=c1=c2=0;a<b.length;)d=b.charCodeAt(a),128>d?(c+=String.fromCharCode(d),a++):191<d&&224>d?(c2=b.charCodeAt(a+1),c+=String.fromCharCode((d&31)<<6|c2&63),a+=2):(c2=b.charCodeAt(a+1),c3=b.charCodeAt(a+2),c+=String.fromCharCode((d&15)<<12|(c2&63)<<6|c3&63),a+=3);return c},dataURItoBlob:function(b){var c=atob(b.split(",")[1]);b=b.split(",")[0].split(":")[1].split(";")[0];for(var a=new ArrayBuffer(c.length),d=new Uint8Array(a),
e=0;e<c.length;e++)d[e]=c.charCodeAt(e);c=new DataView(a);return new Blob([c.buffer],{type:b})},makeDecryptReponseMessage:function(b){b=DEXT5UPLOAD.util.base64_decode(b);b=b.substring(1);return b=DEXT5UPLOAD.util.base64_decode(b)},set_handlerUrl:function(b){var c="",c=0==b.length?DEXT5UPLOAD.rootPath:"/"==b.substring(0,1)?location.protocol+"//"+location.host:4<b.length&&"http"==b.substring(0,4).toLowerCase()?"":DEXT5UPLOAD.rootPath;return c+b},buildFormData:function(b,c,a,d){c=""+("--"+b+'\r\nContent-Disposition: form-data; name="'+
c+'"')+"\r\n";c+="\r\n";c+=a;c+="\r\n";d&&(c+="--"+b+"--");return c},hashTable:function(b){this.length=0;this.items={};for(var c in b)b.hasOwnProperty(c)&&(this.items[c]=b[c],this.length++);this.setItem=function(a,b){var c=void 0;this.hasItem(a)?c=this.items[a]:this.length++;this.items[a]=b;return c};this.getItem=function(a){return this.hasItem(a)?this.items[a]:void 0};this.hasItem=function(a){return this.items.hasOwnProperty(a)};this.removeItem=function(a){if(this.hasItem(a))return previous=this.items[a],
this.length--,delete this.items[a],previous};this.keys=function(){var a=[],b;for(b in this.items)this.hasItem(b)&&a.push(b);return a};this.values=function(){var a=[],b;for(b in this.items)this.hasItem(b)&&a.push(this.items[b]);return a};this.each=function(a){for(var b in this.items)this.hasItem(b)&&a(b,this.items[b])};this.clear=function(){this.items={};this.length=0}},setSecuritySetting:function(b,c,a){if(1==a)"NONE"==c.developLang&&(c.security.encryptParam="0");else{a=c.userRunTimeMode;if(DEXT5UPLOAD.config.Security.EncryptParam&&
""!=DEXT5UPLOAD.config.Security.EncryptParam)c.security.encryptParam="1"==DEXT5UPLOAD.config.Security.EncryptParam?"1":"0";else{var d=DEXT5UPLOAD.util.xml.getNodeValue(b,"encrypt_param");c.security.encryptParam="1"==d?"1":"0"}DEXT5UPLOAD.config.Security.FileExtensionDetector&&""!=DEXT5UPLOAD.config.Security.FileExtensionDetector?c.security.fileExtensionDetector="1"==DEXT5UPLOAD.config.Security.FileExtensionDetector?"1":"0":(d=DEXT5UPLOAD.util.xml.getNodeValue(b,"file_extension_detector"),c.security.fileExtensionDetector=
"1"==d?"1":"0");if(-1<a.indexOf("html5")||"ieplugin"==a)DEXT5UPLOAD.config.Security.FileIntegrity&&""!=DEXT5UPLOAD.config.Security.FileIntegrity?c.security.fileIntegrity="1"==DEXT5UPLOAD.config.Security.FileIntegrity?"1":"0":(d=DEXT5UPLOAD.util.xml.getNodeValue(b,"file_integrity"),c.security.fileIntegrity="1"==d?"1":"0"),"ieplugin"==a?DEXT5UPLOAD.config.Security.FileEncrypt&&""!=DEXT5UPLOAD.config.Security.FileEncrypt?c.security.fileEncrypt="1"==DEXT5UPLOAD.config.Security.FileEncrypt?"1":"0":(b=
DEXT5UPLOAD.util.xml.getNodeValue(b,"file_encrypt"),c.security.fileEncrypt="1"==b?"1":"0"):c.security.fileEncrypt="0"}},makeEncryptParam:function(b){b=DEXT5UPLOAD.util.base64_encode(b);b="R"+b;b=DEXT5UPLOAD.util.base64_encode(b);return b=b.replace(/[+]/g,"%2B")},getIframeDocument:function(b){return"undefined"!=typeof b.contentDocument?b.contentDocument:"undefined"!=typeof b.contentWindow?b.contentWindow.document:null},isExistUploaderName:function(b){if(void 0==b||""==b)return 1;var c=DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+
b];return void 0==c||null==c?0:""!=DEXT5UPLOAD.config.UploadHolder&&DEXT5UPLOAD.DEXTHOLDER[b]==DEXT5UPLOAD.config.UploadHolder?3:2},getNewNextUploaderName:function(b){var c="",c=b.split("_"),a=c.length;1<a&&(b=b.replace("_"+c[a-1],""));c=b+DEXT5UPLOAD.util.getNextUploaderNumber();0<DEXT5UPLOAD.util.isExistUploaderName(c)&&(c=DEXT5UPLOAD.util.getNewNextUploaderName(c));return c},getNextUploaderNumber:function(){var b=0;return function(){return"_"+ ++b}},createEvent:function(b,c,a){var d=document.createElement("script");
d.setAttribute("for",b);d.event=c;d.appendChild(document.createTextNode(a));document.body.appendChild(d)},initLicenseCheck:function(b,c){if(b&&("hi, dext5 upload !!!"==b.toLowerCase()||-1<b.toLowerCase().indexOf("hi, dext5 upload !!!"))){if(-1<b.indexOf("-")){DEXT5UPLOAD.ServerReleaseVer=b.split("-")[1];b.split("-");var a=b.split("-")[3];0==a.length||-1<a.toLowerCase().indexOf("m")||"1"!=c.hybridDownload||(c.hybridDownload="0");-1==a.toLowerCase().indexOf("h")&&(c.highSpeed="0")}try{if(""==c.licenseKeyEx&&
window.localStorage){var d=DEXT5UPLOAD.util.getDate();window.localStorage.Dext5Upload_gabageCall?d>window.localStorage.Dext5Upload_gabageCall&&(window.localStorage.Dext5Upload_gabageCall=d,DEXT5UPLOAD.util.gcRequest_postMessage(c)):(window.localStorage.Dext5Upload_gabageCall=d,DEXT5UPLOAD.util.gcRequest_postMessage(c))}else DEXT5UPLOAD.util.gcRequest_postMessage(c)}catch(e){}}else b&&0<b.length?-1<b.indexOf(c.unitDelimiter)?(a=b.split(c.unitDelimiter),DEXT5UPLOAD.ServerReleaseVer=a[1],alert(a[0])):
alert(b):(a="",d=0<DEXT5UPLOAD.config.InitXml.length?DEXT5UPLOAD.config.InitXml:"dext5upload.config.xml","ko-kr"==c.lang?(a="Uploader\uc758 \uc124\uc815\uac12\uc774 \uc62c\ubc14\ub974\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. \uc544\ub798 URL \uc811\uadfc\uc774 \uc720\ud6a8\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.\n\n"+(c.handlerUrl+"\n\n"),a+=d+" \ud30c\uc77c\uc758 uploader_setting \uc139\uc158\uc758 <develop_langage>\uc640 <handler_url> \uc124\uc815\uac12\uc744 \ud655\uc778\ud558\uc138\uc694."):(a="Uploader's setting is not correct. Access the following URL is not valid.\n\n"+
(c.handlerUrl+"\n\n"),a+='Please check the settings, <handler_url> and <develop_langage> in "uploader_setting" section in the "'+d+'."'),c.handlerUrlCheck=!1,alert(a))},createUploaderIframe:function(b,c,a,d,e,f){var g=document.createElement("iframe");c.appendChild(g);g.id=d;g.title=e;""==g.title&&(g.title="DEXT5Upload "+f);g.frameBorder=0;g.style.width="100%";g.style.height=DEXT5UPLOAD.browser.quirks?b.height:"100%";g.style.borderWidth="0px";c=DEXT5UPLOAD.util.getDefaultIframeSrc();g.src=c;DEXT5UPLOAD.util.addEvent(g,
"load",function(){try{g.contentWindow.dext_frame_loaded_event(f,b,a);var c=UPLOADTOP.G_CURRUPLOADER.frameWin.getDialogDocument(),e=c.getElementsByTagName("head")[0],d=e.getElementsByTagName("link"),t=d.length,k="dext5upload.popup.min.css";DEXT5UPLOAD.isRelease||(k="dext5upload.popup.css");for(var x=!1,q=0;q<t;q++)-1<d[q].href.indexOf(k)&&(x=!0);if(!x){var v=c.createElement("link");v.type="text/css";v.rel="stylesheet";var l=DEXT5UPLOAD.ReleaseVer;"1"==UPLOADTOP.G_CURRUPLOADER._config.cacheProtectMode&&
(l=(new Date).getTime());v.href=DEXT5UPLOAD.isRelease?b.webPath.css+"dext5upload.popup.min.css?ver="+l:b.webPath.cssDev+"dext5upload.popup.css?ver="+l;e.appendChild(v)}a=b=null}catch(h){null!=DEXT5UPLOAD&&void 0!=DEXT5UPLOAD&&null!=g&&void 0!=g&&null!=g.contentWindow&&void 0!=g.contentWindow||alert("Error occurred load Frame")}},!1);c=DEXT5UPLOAD.util.getIframeDocument(g);d='<!DOCTYPE html><html lang="ko">';d+="<head>";d+="<title>Dext5 Upload</title>";e=DEXT5UPLOAD.ReleaseVer;"1"==b.cacheProtectMode&&
(e=(new Date).getTime());if(DEXT5UPLOAD.isRelease){d+='<link rel="stylesheet" type="text/css" href="'+b.webPath.css+"dext5upload.min.css?ver="+e+'" />';d+='<link rel="stylesheet" type="text/css" href="'+b.webPath.css+"dext5upload.context.min.css?ver="+e+'" />';DEXT5UPLOAD.browser.isCustomDomain(document)&&(d+='<script type="text/javascript"> document.domain = "'+document.domain+'"; \x3c/script>');d+='<script type="text/javascript" src="'+b.webPath.js+"dext5upload.xhr.js?ver="+e+'">\x3c/script>';1>
b.imageQuality.quality&&0==DEXT5UPLOAD.browser.imageProcessWorkerSupported&&(d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.image.exif.js?ver="+e+'">\x3c/script>');d+='<script type="text/javascript" src="'+b.webPath.js+"dext5upload.core.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.js+"json2.min.js?ver="+e+'">\x3c/script>';if("1"==b.security.fileIntegrity||"1"==b.security.fileEncrypt)d+='<script type="text/javascript" src="'+b.webPath.js+"dext5upload.fdi.aes.js?ver="+
e+'">\x3c/script>';"1"==b.security.fileIntegrity&&(d+='<script type="text/javascript" src="'+b.webPath.js+"hmac-sha256.js?ver="+e+'">\x3c/script>');"1"==b.security.fileEncrypt&&(d+='<script type="text/javascript" src="'+b.webPath.js+"aes.js?ver="+e+'">\x3c/script>');d+='<script type="text/javascript" src="'+b.webPath.lang+b.lang+".js?ver="+e+'">\x3c/script>';"html4"==b.userRunTimeMode&&"0"==b.uploadMethodHtml4&&(d+='<script type="text/javascript" src="'+b.webPath.js+"swfupload/swfupload.min.js?ver="+
e+'">\x3c/script>',d+='<script type="text/javascript" src="'+b.webPath.js+"dext5upload.swf.handlers.min.js?ver="+e+'">\x3c/script>')}else{d+='<link rel="stylesheet" type="text/css" href="'+b.webPath.cssDev+"dext5upload.css?ver="+e+'" />';d+='<link rel="stylesheet" type="text/css" href="'+b.webPath.cssDev+"dext5upload.context.css?ver="+e+'" />';DEXT5UPLOAD.browser.isCustomDomain(document)&&(d+='<script type="text/javascript"> document.domain = "'+document.domain+'"; \x3c/script>');d+='<script type="text/javascript" src="'+
b.webPath.jsDev+"dext5upload.xhr.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.src.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.api.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.control.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.base64.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+
b.webPath.jsDev+"dext5pl.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.context.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"json2.min.js?ver="+e+'">\x3c/script>';d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.detector.js?ver="+e+'">\x3c/script>';1>b.imageQuality.quality&&0==DEXT5UPLOAD.browser.imageProcessWorkerSupported&&(d+='<script type="text/javascript" src="'+b.webPath.jsDev+
"dext5upload.image.exif.js?ver="+e+'">\x3c/script>');if("1"==b.security.fileIntegrity||"1"==b.security.fileEncrypt)d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.fdi.aes.js?ver="+e+'">\x3c/script>';"1"==b.security.fileIntegrity&&(d+='<script type="text/javascript" src="'+b.webPath.jsDev+"hmac-sha256.js?ver="+e+'">\x3c/script>');"1"==b.security.fileEncrypt&&(d+='<script type="text/javascript" src="'+b.webPath.jsDev+"aes.js?ver="+e+'">\x3c/script>');d+='<script type="text/javascript" src="'+
b.webPath.langDev+b.lang+".js?ver="+e+'">\x3c/script>';"html4"==b.userRunTimeMode&&"0"==b.uploadMethodHtml4&&(d+='<script type="text/javascript" src="'+b.webPath.jsDev+"swfupload/swfupload.js?ver="+e+'">\x3c/script>',d+='<script type="text/javascript" src="'+b.webPath.jsDev+"dext5upload.swf.handlers.js?ver="+e+'">\x3c/script>')}d+="</head>";d+='<body oncontextmenu="return false">';d+="</body>";d+="</html>";c.open("text/html","replace");c.write(d);c.close();return g},GetUserRunTimeUpload:function(b,
c){b=b.toLowerCase();var a="";"html5plus"==b?a=1==DEXT5UPLOAD.browser.HTML5PlusSupported&&"1"==c?"html5plus":1==DEXT5UPLOAD.browser.HTML5Supported?"html5":"html4":""==b||"html5"==b?a=1==DEXT5UPLOAD.browser.HTML5Supported?"html5":"html4":"html4"==b?a="html4":"ieplugin"==b?a=1==DEXT5UPLOAD.browser.ie&&12>DEXT5UPLOAD.browser.ieVersion?"ieplugin":1==DEXT5UPLOAD.browser.HTML5PlusSupported&&"1"==c?"html5plus":1==DEXT5UPLOAD.browser.HTML5Supported?"html5":"html4":-1<b.indexOf("ieplugin")&&8<b.length?(a=
DEXT5UPLOAD.util.parseIntOr0(b),a=1==DEXT5UPLOAD.browser.ie&&a>=DEXT5UPLOAD.browser.ieVersion?"ieplugin":1==DEXT5UPLOAD.browser.HTML5PlusSupported&&"1"==c?"html5plus":1==DEXT5UPLOAD.browser.HTML5Supported?"html5":"html4"):a=1==DEXT5UPLOAD.browser.HTML5PlusSupported&&"1"==c?"html5plus":1==DEXT5UPLOAD.browser.HTML5Supported?"html5":"html4";return a},gcRequest_postMessage:function(b){if("html4"==b.userRunTimeMode&&"1"==b.uploadMethodHtml4&&b.isCrossDomain){var c=document.createElement("div"),a=DEXT5UPLOAD.util.getDefaultIframeSrc();
c.innerHTML='<iframe name="initCheckframe" id="initCheckframe" style="display:none;" src="'+a+'"></iframe>';c.style.display="none";document.body.appendChild(c);DEXT5UPLOAD.util.postFormData(document,b.handlerUrl,"initCheckframe",[["dext5CMD","gcRequest"]]);document.body.removeChild(c)}else DEXT5UPLOAD.ajax.postData(b.handlerUrl,"dext5CMD=gcRequest",function(){})},_setDext5Uploader:function(b){b=DEXT5UPLOAD.util._getDext5Uploader(b);return void 0==b||null==b?!1:b},_getDext5Uploader:function(b){var c=
null,c=DEXT5UPLOAD.util._getUploaderByName(b);return void 0==c||null==c?(alert("Uploader's Name is not correct. Please check uploader's name."),null):c},_getUploaderByName:function(b){var c=null;return c=void 0==b||""==b?UPLOADTOP.G_CURRUPLOADER:DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+b]}}),DEXT5UPLOAD.Transfer=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.startUpload()},DEXT5UPLOAD.TransferEx=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b)){UPLOADTOP.G_CURRUPLOADER=
b;var c=UPLOADTOP.G_CURRUPLOADER.frameWin;c&&("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?setTimeout(function(){c.startUpload()},100):c.startUpload())}},DEXT5UPLOAD.AddUploadedFile=function(b,c,a,d,e,f){var g=DEXT5UPLOAD.util._setDext5Uploader(f);g&&(UPLOADTOP.G_CURRUPLOADER=g,(g=UPLOADTOP.G_CURRUPLOADER.frameWin)&&g._addUploadedFile(b,c,a,d,e,f))},DEXT5UPLOAD.AddUploadedFileAsArray=function(b,c,a,d,e,f){var g,p="",p="[object Array]"===Object.prototype.toString.call(f)?f[0]:f;if(g=
DEXT5UPLOAD.util._setDext5Uploader(p)){UPLOADTOP.G_CURRUPLOADER=g;var u=UPLOADTOP.G_CURRUPLOADER.frameWin;"ieplugin"!=g._config.userRunTimeMode&&g.frameWin.displayCommonReady(!0,g);setTimeout(function(){if("[object Array]"===Object.prototype.toString.call(b)){var f=b.length;if(!u||0==u.isExecuteApi())return;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&50<f&&u.Dext5PL.SetHugeInsertMode(f);for(var t=0;t<f;t++){var k="";void 0!=b&&null!=b&&void 0!=b[t]&&null!=b[t]&&(k=b[t]);var x="";
void 0!=c&&null!=c&&void 0!=c[t]&&null!=c[t]&&(x=c[t]);var q="";void 0!=a&&null!=a&&void 0!=a[t]&&null!=a[t]&&(q=a[t]);var v="";void 0!=d&&null!=d&&void 0!=d[t]&&null!=d[t]&&(v=d[t]);var l="";void 0!=e&&null!=e&&void 0!=e[t]&&null!=e[t]&&(l=e[t]);v=v.toString();v=v.replace(/,/g,"");if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){var h=q;"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload?(h=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&
(h=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl),q="&fileVirtualPath="+encodeURIComponent(q)+"&fileOrgName="+encodeURIComponent(x),l&&""!=l&&(q=q+"&customValue="+encodeURIComponent(l)),h=0>h.indexOf("?")?h+"?dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+q:h+"&dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+
q):"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(u.Dext5PL.nPe=0);try{v=isNaN(Number(v))||0>Number(v)||""==v?9E18:parseInt(d,10),u.Dext5PL.AddUploadedFile(k,x,h,v),"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(u.Dext5PL.nPe=1)}catch(r){}}else x=x.split("\\"),x=x[x.length-1],v=isNaN(Number(v))||0>Number(v)||""==v?"":parseInt(v,10),k={file:null,guid:"",fileIdx:k,originName:x,webPath:q,fileSize:v,uploadName:"",fileExt:"",
isDelete:"n",isWebFile:"y",customValue:l,localPath:"",mark:""},u.RESULTFILELIST.push(k),u.addFileList(k),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||u.sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc))}"ieplugin"!=
UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?(u.calcTotalSize(),u.displayTotalSizeAndNum(),u.setFileListBorder(),u.setListvalue(),u.setLargeFileAllList()):50<f&&u.Dext5PL.SetHugeInsertMode(0);u.setTabOrder();u.fileListSortIconReset()}else k="",void 0!=b&&null!=b&&(k=b),x="",void 0!=c&&null!=c&&(x=c),q="",void 0!=a&&null!=a&&(q=a),v="",void 0!=d&&null!=d&&(v=d),l="",void 0!=e&&null!=e&&(l=e),u._addUploadedFile(k,x,q,v,l,p);"ieplugin"!=g._config.userRunTimeMode&&g.frameWin.displayCommonReady(!1,
g)},1)}},DEXT5UPLOAD.AddUploadedFileWithGetFileSize=function(b,c,a,d,e,f){var g,p="",p="[object Array]"===Object.prototype.toString.call(f)?f[0]:f;if(g=DEXT5UPLOAD.util._setDext5Uploader(p)){UPLOADTOP.G_CURRUPLOADER=g;var u=UPLOADTOP.G_CURRUPLOADER.frameWin;u.displayCommonReady(!0,g);var n="",n=null==d||""==d||0==d.length?a:d,t="";d=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl;if(1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain){var k=u.document.createElement("div"),x=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();
k.innerHTML='<iframe name="sizeConfirmFrame" id="sizeConfirmFrame" style="display:none;" src="'+x+'"></iframe>';k.style.display="none";u.document.body.appendChild(k);var q=u.document.getElementById("sizeConfirmFrame");UPLOADTOP.DEXT5UPLOAD.util.addEvent(q,"load",function(){q.contentWindow.postMessage("check","*")},!0);if(u.postMessage){var v=function(d){u.document.body.removeChild(k);t=d.data;null!=t&&""!=t?(t=UPLOADTOP.DEXT5UPLOAD.util.trim(t),t=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=
UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?u.Dext5Base64.decode(t):u.Dext5Base64.makeDecryptReponseMessage(t)):t="";UPLOADTOP.DEXT5UPLOAD.util.removeEvent(u,"message",v);g&&g.frameWin.displayCommonReady(!1,g);if(u._addUploadedFile)if("[object Array]"===Object.prototype.toString.call(n)){d=n.length;for(var f=t.split(u.G_formfeed),l=0;l<d;l++){var h="";void 0!=b&&null!=b&&void 0!=b[l]&&null!=b[l]&&(h=b[l]);var q="";void 0!=c&&null!=c&&void 0!=c[l]&&null!=c[l]&&(q=c[l]);var r="";void 0!=a&&null!=a&&void 0!=
a[l]&&null!=a[l]&&(r=a[l]);var x="";void 0!=f&&null!=f&&void 0!=f[l]&&null!=f[l]&&(x=f[l]);var H="";void 0!=e&&null!=e&&void 0!=e[l]&&null!=e[l]&&(H=e[l]);u._addUploadedFile(h,q,r,x,H,p)}}else f=t,h="",void 0!=b&&null!=b&&(h=b),q="",void 0!=c&&null!=c&&(q=c),r="",void 0!=a&&null!=a&&(r=a),x="",void 0!=f&&null!=f&&(x=f),H="",void 0!=e&&null!=e&&(H=e),u._addUploadedFile(h,q,r,x,H,p)};UPLOADTOP.DEXT5UPLOAD.util.addEvent(u,"message",v)}var l;if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){l=
""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);l+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;var h="";if("[object Array]"===Object.prototype.toString.call(n))for(var x=n.length,r=0;r<x;r++)h+=n[r]+"|";else h+=n+"|";l+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+h+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;
l=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(l);l=[["d00",l]]}else if(l=[["dext5CMD","gfs"],["cd","1"]],"[object Array]"===Object.prototype.toString.call(n))for(x=n.length,r=0;r<x;r++)""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?l.push(["urlAddress",n[r]]):l.push(["urlAddress",encodeURI(encodeURIComponent(n[r]))]);else""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?l.push(["urlAddress",
n]):l.push(["urlAddress",encodeURI(encodeURIComponent(n))]);UPLOADTOP.DEXT5UPLOAD.util.postFormData(u.document,d,"download_frame",l)}else{l="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){l=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);h="";if("[object Array]"===Object.prototype.toString.call(n))for(x=n.length,r=0;r<x;r++)h+=n[r]+"|";else h+=n+"|";l+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
h+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;l=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(l);l="d00="+l}else if(l="dext5CMD=gfs","[object Array]"===Object.prototype.toString.call(n))for(x=n.length,r=0;r<x;r++)l=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?l+("&urlAddress="+n[r]):l+("&urlAddress="+encodeURI(encodeURIComponent(n[r])));else l=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?
l+("&urlAddress="+n):l+("&urlAddress="+encodeURI(encodeURIComponent(n)));UPLOADTOP.DEXT5UPLOAD.ajax.postData(d,l,function(d){null!=d&&""!=d?(d=UPLOADTOP.DEXT5UPLOAD.util.trim(d),d=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?u.Dext5Base64.decode(d):u.Dext5Base64.makeDecryptReponseMessage(d)):d="";g&&g.frameWin.displayCommonReady(!1,g);if(u._addUploadedFile)if("[object Array]"===Object.prototype.toString.call(n)){var l=n.length;d=d.split(u.G_formfeed);
for(var k=0;k<l;k++){var p="";void 0!=b&&null!=b&&void 0!=b[k]&&null!=b[k]&&(p=b[k]);var t="";void 0!=c&&null!=c&&void 0!=c[k]&&null!=c[k]&&(t=c[k]);var h="";void 0!=a&&null!=a&&void 0!=a[k]&&null!=a[k]&&(h=a[k]);var q="";void 0!=d&&null!=d&&void 0!=d[k]&&null!=d[k]&&(q=d[k]);var r="";void 0!=e&&null!=e&&void 0!=e[k]&&null!=e[k]&&(r=e[k]);var m="";void 0!=f&&null!=f&&(m=f);u._addUploadedFile(p,t,h,q,r,m)}}else p="",void 0!=b&&null!=b&&(p=b),t="",void 0!=c&&null!=c&&(t=c),h="",void 0!=a&&null!=a&&
(h=a),q="",void 0!=d&&null!=d&&(q=d),r="",void 0!=e&&null!=e&&(r=e),m="",void 0!=f&&null!=f&&(m=f),u._addUploadedFile(p,t,h,q,r,m)})}}},DEXT5UPLOAD.AddUploadedFileEx=function(b,c,a,d,e,f,g){var p=DEXT5UPLOAD.util._setDext5Uploader(g);p&&(UPLOADTOP.G_CURRUPLOADER=p,(p=UPLOADTOP.G_CURRUPLOADER.frameWin)&&p._addUploadedFileEx(b,c,a,d,e,f,g))},DEXT5UPLOAD.AddUploadedFileExAsArray=function(b,c,a,d,e,f,g){var p,u="",u="[object Array]"===Object.prototype.toString.call(g)?g[0]:g;if(p=DEXT5UPLOAD.util._setDext5Uploader(u)){UPLOADTOP.G_CURRUPLOADER=
p;var n=UPLOADTOP.G_CURRUPLOADER.frameWin;n&&("ieplugin"!=p._config.userRunTimeMode&&n.displayCommonReady(!0,p),setTimeout(function(){if("[object Array]"===Object.prototype.toString.call(b)){var g=b.length;if(0==n.isExecuteApi())return;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&50<g&&n.Dext5PL.SetHugeInsertMode(g);for(var k=0;k<g;k++){var x="";void 0!=b&&null!=b&&void 0!=b[k]&&null!=b[k]&&(x=b[k]);var q="";void 0!=c&&null!=c&&void 0!=c[k]&&null!=c[k]&&(q=c[k]);var v="";void 0!=
a&&null!=a&&void 0!=a[k]&&null!=a[k]&&(v=a[k]);var l="";void 0!=d&&null!=d&&void 0!=d[k]&&null!=d[k]&&(l=d[k]);var h="";void 0!=e&&null!=e&&void 0!=e[k]&&null!=e[k]&&(h=e[k]);var r="";void 0!=f&&null!=f&&void 0!=f[k]&&null!=f[k]&&(r=f[k]);var w=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length;if(""==r)for(var y=1;y<w;y++)r+="|";else{var A=r.split("|"),D=A.length;if(D<w)for(y=0;y<w-D;y++)r+="|";else if(D>w)for(r="",y=0;y<w;y++)0!=y&&(r+="|"),r+=A[y]}if(""!=UPLOADTOP.G_CURRUPLOADER._config.headerBarItem)for(w=
UPLOADTOP.G_CURRUPLOADER._config.headerBarItem.length,y=0;y<w;y++)n.document.getElementById("user_header_bar_"+y).getElementsByTagName("span")[0].style.display="none";l=l.toString();l=l.replace(/,/g,"");if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){w=v;"1"!=UPLOADTOP.G_CURRUPLOADER._config.directDownload?(w=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,""!=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl&&(w=UPLOADTOP.G_CURRUPLOADER._config.downloadHandlerUrl),v="&fileVirtualPath="+
encodeURIComponent(v)+"&fileOrgName="+encodeURIComponent(q),h&&""!=h&&(v=v+"&customValue="+encodeURIComponent(h)),w=0>w.indexOf("?")?w+"?dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+v:w+"&dext5CMD=downloadRequest&resumeMode="+UPLOADTOP.G_CURRUPLOADER._config.resumeDownload+"&fileNameRuleEx="+UPLOADTOP.G_CURRUPLOADER._config.fileNameRuleEx+v):"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&
(n.Dext5PL.nPe=0);try{l=isNaN(Number(l))||0>Number(l)||""==l?9E18:parseInt(l,10),n.Dext5PL.AddUploadedFileEx(x,q,w,l,r),"1"==UPLOADTOP.G_CURRUPLOADER._config.directDownload&&"1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam&&(n.Dext5PL.nPe=1)}catch(G){}}else q=q.split("\\"),q=q[q.length-1],l=isNaN(Number(l))||0>Number(l)||""==l?"":parseInt(l,10),w={file:null,guid:"",fileIdx:x,originName:q,webPath:v,fileSize:l,uploadName:"",fileExt:"",isDelete:"n",isWebFile:"y",customValue:h,localPath:"",
mark:"",headerEx:r},n.RESULTFILELIST.push(w),"form"==UPLOADTOP.G_CURRUPLOADER._config.subMode&&n.RESULTFILELISTCLON.push({file:null,guid:"",fileIdx:x,originName:q,webPath:v,fileSize:l,uploadName:"",fileExt:"",isDelete:"n",isWebFile:"y",customValue:h,localPath:"",mark:"",headerEx:r}),n.addFileList(w),"1"==UPLOADTOP.G_CURRUPLOADER._config.use_file_sort&&"1"==UPLOADTOP.G_CURRUPLOADER._config.auto_sort&&("html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&
"1"==UPLOADTOP.G_CURRUPLOADER._config.sort_field&&"0"==UPLOADTOP.G_CURRUPLOADER._config.displayFileSizeHtml4||n.sortTotalFileList(UPLOADTOP.G_CURRUPLOADER._config.sort_field,UPLOADTOP.G_CURRUPLOADER._config.sort_ascdesc))}"ieplugin"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?(n.calcTotalSize(),n.displayTotalSizeAndNum(),n.setFileListBorder(),n.setListvalue()):50<g&&n.Dext5PL.SetHugeInsertMode(0);n.setTabOrder();n.fileListSortIconReset()}else x="",void 0!=b&&null!=b&&(x=b),q="",void 0!=c&&null!=
c&&(q=c),v="",void 0!=a&&null!=a&&(v=a),l="",void 0!=d&&null!=d&&(l=d),h="",void 0!=e&&null!=e&&(h=e),r="",void 0!=f&&null!=f&&(r=f),n._addUploadedFileEx(x,q,v,l,h,r,u);"ieplugin"!=p._config.userRunTimeMode&&p.frameWin.displayCommonReady(!1,p)},1))}},DEXT5UPLOAD.AddUploadedFileExWithGetFileSize=function(b,c,a,d,e,f,g){var p,u="",u="[object Array]"===Object.prototype.toString.call(g)?g[0]:g;if(p=DEXT5UPLOAD.util._setDext5Uploader(u)){p.frameWin.displayCommonReady(!0,p);UPLOADTOP.G_CURRUPLOADER=p;var n=
UPLOADTOP.G_CURRUPLOADER.frameWin,t="",t=null==d||""==d||0==d.length?a:d,k="";d=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl;if(1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain){var x=n.document.createElement("div"),q=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();x.innerHTML='<iframe name="sizeConfirmFrame" id="sizeConfirmFrame" style="display:none;" src="'+q+'"></iframe>';x.style.display="none";n.document.body.appendChild(x);var v=n.document.getElementById("sizeConfirmFrame");UPLOADTOP.DEXT5UPLOAD.util.addEvent(v,
"load",function(){v.contentWindow.postMessage("check","*")},!0);if(n.postMessage){var l=function(d){n.document.body.removeChild(x);k=d.data;null!=k&&""!=k?(k=UPLOADTOP.DEXT5UPLOAD.util.trim(k),k=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n.Dext5Base64.decode(k):n.Dext5Base64.makeDecryptReponseMessage(k)):k="";UPLOADTOP.DEXT5UPLOAD.util.removeEvent(n,"message",l);p.frameWin.displayCommonReady(!1,p);if(n._addUploadedFile)if("[object Array]"===
Object.prototype.toString.call(t)){d=t.length;for(var g=k.split(n.G_formfeed),h=0;h<d;h++){var q="";void 0!=b&&null!=b&&void 0!=b[h]&&null!=b[h]&&(q=b[h]);var r="";void 0!=c&&null!=c&&void 0!=c[h]&&null!=c[h]&&(r=c[h]);var v="";void 0!=a&&null!=a&&void 0!=a[h]&&null!=a[h]&&(v=a[h]);var w="";void 0!=g&&null!=g&&void 0!=g[h]&&null!=g[h]&&(w=g[h]);var m="";void 0!=e&&null!=e&&void 0!=e[h]&&null!=e[h]&&(m=e[h]);var B="";void 0!=f&&null!=f&&void 0!=f[h]&&null!=f[h]&&(B=f[h]);n._addUploadedFileEx(q,r,v,
w,m,B,u)}}else g=k,q="",void 0!=b&&null!=b&&(q=b),r="",void 0!=c&&null!=c&&(r=c),v="",void 0!=a&&null!=a&&(v=a),w="",void 0!=g&&null!=g&&(w=g),m="",void 0!=e&&null!=e&&(m=e),B="",void 0!=f&&null!=f&&(B=f),n._addUploadedFileEx(q,r,v,w,m,B,u)};UPLOADTOP.DEXT5UPLOAD.util.addEvent(n,"message",l)}var h;if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){h=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);h+="d04"+
UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;var r="";if("[object Array]"===Object.prototype.toString.call(t))for(var q=t.length,w=0;w<q;w++)r+=t[w]+"|";else r+=t+"|";h+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+r+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;h=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(h);h=[["d00",h]]}else if(h=[["dext5CMD","gfs"],["cd","1"]],"[object Array]"===Object.prototype.toString.call(t))for(q=
t.length,w=0;w<q;w++)""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?h.push(["urlAddress",t[w]]):h.push(["urlAddress",encodeURI(encodeURIComponent(t[w]))]);else""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?h.push(["urlAddress",t]):h.push(["urlAddress",encodeURI(encodeURIComponent(t))]);UPLOADTOP.DEXT5UPLOAD.util.postFormData(n.document,d,"download_frame",h)}else{h="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){h=
""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);r="";if("[object Array]"===Object.prototype.toString.call(t))for(q=t.length,w=0;w<q;w++)r+=t[w]+"|";else r+=t+"|";h+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+r+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;h=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(h);h="d00="+h}else if(h="dext5CMD=gfs","[object Array]"===Object.prototype.toString.call(t))for(q=
t.length,w=0;w<q;w++)h=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?h+("&urlAddress="+t[w]):h+("&urlAddress="+encodeURI(encodeURIComponent(t[w])));else h=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?h+("&urlAddress="+t):h+("&urlAddress="+encodeURI(encodeURIComponent(t)));UPLOADTOP.DEXT5UPLOAD.ajax.postData(d,h,function(d){null!=d&&""!=d?(d=UPLOADTOP.DEXT5UPLOAD.util.trim(d),d=
""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n.Dext5Base64.decode(d):n.Dext5Base64.makeDecryptReponseMessage(d)):d="";p.frameWin.displayCommonReady(!1,p);if(n._addUploadedFile)if("[object Array]"===Object.prototype.toString.call(t)){var k=t.length;d=d.split(n.G_formfeed);for(var h=0;h<k;h++){var l="";void 0!=b&&null!=b&&void 0!=b[h]&&null!=b[h]&&(l=b[h]);var q="";void 0!=c&&null!=c&&void 0!=c[h]&&null!=c[h]&&(q=c[h]);var r="";void 0!=a&&
null!=a&&void 0!=a[h]&&null!=a[h]&&(r=a[h]);var u="";void 0!=d&&null!=d&&void 0!=d[h]&&null!=d[h]&&(u=d[h]);var m="";void 0!=e&&null!=e&&void 0!=e[h]&&null!=e[h]&&(m=e[h]);var v="";void 0!=f&&null!=f&&void 0!=f[h]&&null!=f[h]&&(v=f[h]);var x="";void 0!=g&&null!=g&&(x=g);n._addUploadedFileEx(l,q,r,u,m,v,x)}}else l="",void 0!=b&&null!=b&&(l=b),q="",void 0!=c&&null!=c&&(q=c),r="",void 0!=a&&null!=a&&(r=a),u="",void 0!=d&&null!=d&&(u=d),m="",void 0!=e&&null!=e&&(m=e),v="",void 0!=f&&null!=f&&(v=f),x=
"",void 0!=g&&null!=g&&(x=g),n._addUploadedFileEx(l,q,r,u,m,v,x)})}}},DEXT5UPLOAD.OpenFileDialog=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,UPLOADTOP.G_CURRUPLOADER.frameWin.selectFile()},DEXT5UPLOAD.DeleteAllFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.deleteAllFile(null,!0)},DEXT5UPLOAD.DeleteSelectedFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=
b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.deleteSelectedFile(null,!0)},DEXT5UPLOAD.OpenSelectedFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.openFile(null,!0)},DEXT5UPLOAD.Cancel=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.uploadCancel()},DEXT5UPLOAD.GetTotalFileCount=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=
b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&(c=b.getTotalFileCount());return c},DEXT5UPLOAD.GetTotalFileSize=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&(c=b.getTotalFileSize());return c},DEXT5UPLOAD.ResetUpload=function(b){var c=DEXT5UPLOAD.util._setDext5Uploader(b);if(c){UPLOADTOP.G_CURRUPLOADER=c;c=UPLOADTOP.G_CURRUPLOADER.frameWin;UPLOADTOP.G_CURRUPLOADER.reset(b);if(c&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{c.Dext5PL.ResetUpload()}catch(a){}DEXT5UPLOAD.ResetUploadCompleteState(UPLOADTOP.G_CURRUPLOADER.ID)}},
DEXT5UPLOAD.ResetUploadEx=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(b);if(a){c&&(UPLOADTOP.G_CURRUPLOADER=a);var d=a.frameWin;a.reset(b);if(d&&"ieplugin"==a._config.userRunTimeMode)try{d.Dext5PL.ResetUpload()}catch(e){}DEXT5UPLOAD.ResetUploadCompleteStateEx(a.ID,c)}},DEXT5UPLOAD.GetUploadCompleteState=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,c=UPLOADTOP.G_CURRUPLOADER.isUploadComplete;return c},DEXT5UPLOAD.ResetUploadCompleteState=function(b){if(b=
DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,UPLOADTOP.G_CURRUPLOADER.isUploadComplete=!1},DEXT5UPLOAD.ResetUploadCompleteStateEx=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(b);a&&(c&&(UPLOADTOP.G_CURRUPLOADER=a),UPLOADTOP.G_CURRUPLOADER.isUploadComplete=!1)},DEXT5UPLOAD.GetUserRuntimeMode=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,c=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode;return c},DEXT5UPLOAD.SetAllowOrLimitExtension=
function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,a=UPLOADTOP.G_CURRUPLOADER.frameWin)"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?(a.Dext5PL.nAllowOrLimit=b.toString(),-1<c.indexOf("|")?a.Dext5PL.strFileFilter=c:a.Dext5PL.strFileFilter=c.toLowerCase()):a.setAllowOrLimitExtension(b.toString(),c)},DEXT5UPLOAD.GetFileObjectList=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b)){var a=UPLOADTOP.G_CURRUPLOADER.frameWin;"ieplugin"==
b._config.userRunTimeMode?(UPLOADTOP.G_CURRUPLOADER=b,a&&(c=a.getFileObjectList())):alert(a.Dext5Upload_Lang.message_wrong_approach)}return c},DEXT5UPLOAD.SetFileObjectList=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a){var d=UPLOADTOP.G_CURRUPLOADER.frameWin;"ieplugin"==a._config.userRunTimeMode?(UPLOADTOP.G_CURRUPLOADER=a,d&&d.setFileObjectList(b)):alert(d.Dext5Upload_Lang.message_wrong_approach)}},DEXT5UPLOAD.CallExternalLibrary=DEXT5UPLOAD.callExternalLibrary=function(b,c,a){if(a=
DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==a._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin))return a.Dext5PL.DEXT5LoadLibrary(b,c)},DEXT5UPLOAD.SetMaxOneFileSize=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;if(d){b=b.toString();var e=DEXT5UPLOAD.util.getUnit(b),e=DEXT5UPLOAD.util.getUnitSize(e),e=parseInt(b,10)*e;a._config.maxOneFileSize=e;"ieplugin"==a._config.userRunTimeMode&&
(d.Dext5PL.nMaxOneFileSize=e)}}},DEXT5UPLOAD.SetMaxTotalFileSize=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;if(d){b=b.toString();var e=DEXT5UPLOAD.util.getUnit(b),e=DEXT5UPLOAD.util.getUnitSize(e),e=parseInt(b,10)*e;a._config.maxTotalFileSize=e;"ieplugin"==a._config.userRunTimeMode&&(d.Dext5PL.nMaxTotalSize=e);d.reLoadStatusBar(a._config)}}},DEXT5UPLOAD.SetMaxTotalFileCount=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);
if(a){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;d&&(a._config.maxTotalFileCount=parseInt(b,10),"ieplugin"==a._config.userRunTimeMode&&(d.Dext5PL.nMaxTotalCount=parseInt(b,10)),d.reLoadStatusBar(a._config))}},DEXT5UPLOAD.SetDefaultSavePath=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);a&&(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a.Dext5PL.strDefaultSavePath=b))},DEXT5UPLOAD.SetFolderTransfer=
function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);a&&(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a.Dext5PL.nUseFolderTransfer=parseInt(b,10),"2"==b?a.document.getElementById("button_add")&&(a.document.getElementById("button_add").style.display="none"):a.document.getElementById("button_add")&&(a.document.getElementById("button_add").style.display=""),"0"==b?a.document.getElementById("button_add_folder")&&
(a.document.getElementById("button_add_folder").style.display="none"):a.document.getElementById("button_add_folder")&&(a.document.getElementById("button_add_folder").style.display="")))},DEXT5UPLOAD.GetItemCount=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(c=b.G_FolderCount);return c},DEXT5UPLOAD.GetSelectedFileCount=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=
b,(b=b.frameWin)&&(c=b.getSelectedFileCount());return c},DEXT5UPLOAD.GetUploadNameSet=function(){return DEXT5UPLOAD.DEXTMULTIPLEID},DEXT5UPLOAD.AddDropZoneFile=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a)){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;d&&0!=d.isExecuteApi()&&"html4"!=a._config.userRunTimeMode&&(null!=b?0<b.length&&d.fileHandler(b,c):""!=c&&d.Dext5PL.AddLocalFileDirectly(c))}},DEXT5UPLOAD.SetLargeFile=function(b,c,a){if(void 0!=b&&null!=b&&(a=DEXT5UPLOAD.util._setDext5Uploader(a))){UPLOADTOP.G_CURRUPLOADER=
a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;if(d){b=DEXT5UPLOAD.util.parseIntOr0(b);if(void 0==c||null==c)c=!1;"ieplugin"==a._config.userRunTimeMode?d.Dext5PL.SetHugeMark(b,c):d.setLargeFile(b,c)}}},DEXT5UPLOAD.DoSaveFileEx=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)))return a.Dext5PL.SaveFileEx(b)},DEXT5UPLOAD.DoSaveAllFilesEx=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);
if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)))return a.Dext5PL.SaveAllFilesEx(b)},DEXT5UPLOAD.Destroy=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b)){UPLOADTOP.G_CURRUPLOADER=b;var c=UPLOADTOP.G_CURRUPLOADER.frameWin;try{if(DEXT5UPLOAD.DEXTMULTIPLEEVENT[b.ID]){uploadEventList=DEXT5UPLOAD.DEXTMULTIPLEEVENT[b.ID];for(var a=0,d=uploadEventList.length;a<d;a++)uploadEventList[a].element&&(DEXT5UPLOAD.util.removeEvent(uploadEventList[a].element,
uploadEventList[a].event,uploadEventList[a].func),delete uploadEventList[a].func,uploadEventList[a].func=null);delete uploadEventList;delete DEXT5UPLOAD.DEXTMULTIPLEEVENT[b.ID]}DEXT5UPLOAD.util.removeEvent(UPLOADTOP,"keydown",c._top_Keydown);DEXT5UPLOAD.util.removeEvent(UPLOADTOP,"click",c._top_filebtn)}catch(e){}c=c.getDialogDocument();try{DEXT5UPLOAD.util.removeElementWithChildNodes(c.getElementById("DEXT_fiVe_UP_ly_wrapper")),DEXT5UPLOAD.util.removeElementWithChildNodes(c.getElementById("DEXT_fiVe_UP_Popup_Mode")),
DEXT5UPLOAD.util.removeElementWithChildNodes(c.getElementById("dext5upload_context_menu_"+b.ID))}catch(f){}try{if(DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+b.ID]){for(var a=0,g=DEXT5UPLOAD.DEXTMULTIPLETIMEOUT,d=g.length;a<d;a++)g[a]&&(window.clearTimeout(g[a]),delete g[a]);delete DEXT5UPLOAD.DEXTMULTIPLETIMEOUT;b._config&&delete b._config;b._frame&&delete b._frame;if(b.frameWin._dext5_uploader)try{delete b.frameWin._dext5_uploader}catch(p){b.frameWin._dext5_uploader=null}b.frameWin&&delete b.frameWin;
var a=0,u;for(u in DEXT5UPLOAD.DEXTHOLDER)if(u==b.ID)break;else a++;DEXT5UPLOAD.DEXTMULTIPLEID.splice(a,1);DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+b.ID]&&delete DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+b.ID];"undefined"!=typeof DEXT5UPLOAD.DEXTHOLDER[b.ID]&&delete DEXT5UPLOAD.DEXTHOLDER[b.ID];if(DEXT5UPLOAD.DEXTMULTIPLEID&&0<DEXT5UPLOAD.DEXTMULTIPLEID.length)G_CURRUPLOADER=DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+DEXT5UPLOAD.DEXTMULTIPLEID[0]];else{G_CURRUPLOADER=null;try{delete DEXT5UPLOAD.DEXTMULTIPLEEVENT}catch(n){}}delete b}}catch(t){}}},
DEXT5UPLOAD.GetFileSize=function(b,c){var a="",d=UPLOADTOP.G_CURRUPLOADER._config.handlerUrl,e=UPLOADTOP.G_CURRUPLOADER.frameWin;if(1==UPLOADTOP.G_CURRUPLOADER._config.isCrossDomain){var f=e.document.createElement("div"),g=UPLOADTOP.DEXT5UPLOAD.util.getDefaultIframeSrc();f.innerHTML='<iframe title="sizeConfirmFrame" name="sizeConfirmFrame" id="sizeConfirmFrame" style="display:none;" src="'+g+'"></iframe>';f.style.display="none";e.document.body.appendChild(f);var p=e.document.getElementById("sizeConfirmFrame");
UPLOADTOP.DEXT5UPLOAD.util.addEvent(p,"load",function(){p.contentWindow.postMessage("check","*")},!0);if(e.postMessage){var u=function(b){e.document.body.removeChild(f);a=b.data;null!=a&&""!=a?(a=UPLOADTOP.DEXT5UPLOAD.util.trim(b.data),a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?e.Dext5Base64.decode(a):e.Dext5Base64.makeDecryptReponseMessage(a)):a="";UPLOADTOP.DEXT5UPLOAD.util.removeEvent(e,"message",u);c&&c(a)};UPLOADTOP.DEXT5UPLOAD.util.addEvent(e,
"message",u)}var n;if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){n=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);n+="d04"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"1"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;var t="";if("[object Array]"===Object.prototype.toString.call(b))for(var g=b.length,k=0;k<g;k++)t+=b[k]+"|";else t+=b+"|";n+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+
t+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(n);n=[["d00",n]]}else if(n=[["dext5CMD","gfs"],["cd","1"]],"[object Array]"===Object.prototype.toString.call(b))for(g=b.length,k=0;k<g;k++)""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n.push(["urlAddress",b[k]]):n.push(["urlAddress",encodeURI(encodeURIComponent(b[k]))]);else""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=
UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n.push(["urlAddress",b]):n.push(["urlAddress",encodeURI(encodeURIComponent(b))]);UPLOADTOP.DEXT5UPLOAD.util.postFormData(e.document,d,"download_frame",n)}else{n="";if("1"==UPLOADTOP.G_CURRUPLOADER._config.security.encryptParam){n=""+("d01"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+"gfs"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter);t="";if("[object Array]"===Object.prototype.toString.call(b))for(g=b.length,k=0;k<g;k++)t+=b[k]+"|";
else t+=b+"|";n+="d32"+UPLOADTOP.G_CURRUPLOADER._config.trans_unitAttributeDelimiter+t+UPLOADTOP.G_CURRUPLOADER._config.trans_unitDelimiter;n=UPLOADTOP.DEXT5UPLOAD.util.makeEncryptParam(n);n="d00="+n}else if(n="dext5CMD=gfs","[object Array]"===Object.prototype.toString.call(b))for(g=b.length,k=0;k<g;k++)n=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n+("&urlAddress="+b[k]):n+("&urlAddress="+encodeURI(encodeURIComponent(b[k])));else n=""==
UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1121526.1335.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?n+("&urlAddress="+b):n+("&urlAddress="+encodeURI(encodeURIComponent(b)));UPLOADTOP.DEXT5UPLOAD.ajax.postData(d,n,function(a){null!=a&&""!=a?(a=UPLOADTOP.DEXT5UPLOAD.util.trim(a),a=""==UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer||"2.7.1120889.1613.01">=UPLOADTOP.DEXT5UPLOAD.ServerReleaseVer?e.Dext5Base64.decode(a):e.Dext5Base64.makeDecryptReponseMessage(a)):a="";c&&c(a)})}return a},DEXT5UPLOAD.DownloadFile=
function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.downloadFile(null,!0)},DEXT5UPLOAD.DownloadAllFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.downloadAllFile(null,!0)},DEXT5UPLOAD.SetUploadMode=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=a.isExecuteApi())){if(""==
b||void 0==b||"edit"==b.toLowerCase())b="upload";"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&a.Dext5PL.SetAtModeChange("upload"==b?0:"view"==b?1:"open"==b?2:3);a.setUploadMode(b)}},DEXT5UPLOAD.Show=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b.uploadShow()},DEXT5UPLOAD.Hidden=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,UPLOADTOP.G_CURRUPLOADER.frameWin&&UPLOADTOP.G_CURRUPLOADER.frameWin.uploadHidden()},
DEXT5UPLOAD.SetSkinColor=function(b,c,a,d,e){if(e=DEXT5UPLOAD.util._setDext5Uploader(e))if(UPLOADTOP.G_CURRUPLOADER=e,e=UPLOADTOP.G_CURRUPLOADER.frameWin){G_StrCustomHeaderColor=" background:"+b;G_StrCustomHeaderColor2=" border-bottom-color:"+b;G_StrCustomFooterColor=" color:"+c;G_StrCustomProgressBarColor=" background:"+a;G_StrCustomTextColor=" color:"+d;UPLOADTOP.G_CURRUPLOADER._config.customHeaderColor=b;UPLOADTOP.G_CURRUPLOADER._config.customFooterColor=c;UPLOADTOP.G_CURRUPLOADER._config.customProgressBarColor=
a;UPLOADTOP.G_CURRUPLOADER._config.customTextColor=d;var f=e.document.getElementById("DEXT_fiVe_UP_content").getElementsByTagName("ul")[0];f.style.backgroundColor=b;f.style.color=c;e.document.getElementById("DEXT_fiVe_UP_uploadbox_btm").style.backgroundColor=b;if("upload"!=UPLOADTOP.G_CURRUPLOADER._config.mode&&"edit"!=UPLOADTOP.G_CURRUPLOADER._config.mode){var g=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView.length;if(0<g)for(f=0;f<g;f++)e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[f].split("|")[0])&&
(e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[f].split("|")[0]).style.backgroundColor=b,e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarView[f].split("|")[0]).style.color=c)}else if(g=UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit.length,0<g)for(f=0;f<g;f++)"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&"add"==UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[f]?
e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0]&&(e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundColor=b,e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.color=c):e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[f].split("|")[0])&&(e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[f].split("|")[0]).style.backgroundColor=
b,e.document.getElementById("button_"+UPLOADTOP.G_CURRUPLOADER._config.showButtonBarEdit[f].split("|")[0]).style.color=c);else"html4"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&"1"==UPLOADTOP.G_CURRUPLOADER._config.uploadMethodHtml4&&e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID)&&(e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.backgroundColor=b,e.document.getElementById("form_"+UPLOADTOP.G_CURRUPLOADER.TagID).childNodes[0].style.color=
c);"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?e.Dext5PL.SetSkinColor(b,c,a,d):(c=e.getDialogDocument(),null==c.getElementById("DEXT_fiVe_UP_ly_content")&&e.createTransferDiv(),e=c.getElementById("DEXT_fiVe_UP_ly_content"),f=e.getElementsByTagName("div")[1],e.getElementsByTagName("span")[7].style.borderBottomColor=b,e.style.borderColor=a,f.style.backgroundColor=a,f.style.color=d,c.getElementById("DEXT_fiVe_UP_total_upload_progress_bar").style.backgroundColor=a,c.getElementById("DEXT_fiVe_UP_upload_cancel").style.backgroundColor=
a,c.getElementById("DEXT_fiVe_UP_upload_cancel").style.color=d)}},DEXT5UPLOAD.MoveFirstFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=b.isExecuteApi()&&b.moveFirstFile()},DEXT5UPLOAD.MoveForwardFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=b.isExecuteApi()&&b.moveForwardFile()},DEXT5UPLOAD.MoveBackwardFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=
b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=b.isExecuteApi()&&b.moveBackwardFile()},DEXT5UPLOAD.MoveEndFile=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))UPLOADTOP.G_CURRUPLOADER=b,(b=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=b.isExecuteApi()&&b.moveEndFile()},DEXT5UPLOAD.SetFileInfo=function(b,c,a){var d=DEXT5UPLOAD.util._setDext5Uploader(a);d&&(UPLOADTOP.G_CURRUPLOADER=d,UPLOADTOP.G_CURRUPLOADER.frameWin&&(null!=b&&DEXT5UPLOAD.AddFormData("d5_prefix",b,a),null!=c&&DEXT5UPLOAD.AddFormData("d5_subfix",
c,a)))},DEXT5UPLOAD.AddFormData=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,a=UPLOADTOP.G_CURRUPLOADER.frameWin)if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{a.Dext5PL.AddUserFormData(b,c)}catch(d){}else if(b&&""!=b&&c&&""!=c){for(var e=a.G_FormData,f=e.length,g=!0,p=0;p<f;p++)if(e[p].form_name.toLowerCase()==b.toLowerCase()){e[p].form_value=c;g=!1;break}g&&a.G_FormData.push({form_name:b,form_value:c})}},DEXT5UPLOAD.AddLocalFileObject=
function(b,c,a){var d=DEXT5UPLOAD.util._setDext5Uploader(a);if(d&&(UPLOADTOP.G_CURRUPLOADER=d,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&0!=a.isExecuteApi()&&"object"==typeof b&&"file"==b.type))if(a.G_UseAddLocalFileObject="1","ieplugin"==d._config.userRunTimeMode){if(""!=b.value)try{a.Dext5PL.nNotAllowIfOpen="0",a.Dext5PL.AddLocalFileDirectly(b.value),a.fileHandler_plugin(a,b,b.value,c)}catch(e){}}else if("html5"==d._config.userRunTimeMode)0<b.files.length&&a.fileHandler(b.files,b.value,c);else if("html4"==
d._config.userRunTimeMode&&"1"==d._config.uploadMethodHtml4&&""!=b.value){d=b.id;a.G_TagID.push(UPLOADTOP.G_CURRUPLOADER.TagID);var f=[],g=[];f.push(UPLOADTOP.G_CURRUPLOADER.TagID);g.push(b);a.inputFileObjectChange(f,g);a.G_LocalFileObject.push(UPLOADTOP.document.getElementById(d));a.fileHandler_html4(b,b.value,c)}},DEXT5UPLOAD.SetSelectItem=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&!isNaN(Number(b))&&!isNaN(Number(c))&&
""!=b.toString()&&""!=c.toString())if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{a.Dext5PL.SetSelectItem(parseInt(b,10),parseInt(c,10))}catch(d){}else a.setSelectItem(parseInt(b,10),parseInt(c,10))},DEXT5UPLOAD.SetSelectItemEx=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,a=UPLOADTOP.G_CURRUPLOADER.frameWin)if(b=b.toString(),!isNaN(Number(c))&&""!=c.toString())if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{a.Dext5PL.SetSelectItemEx(b,
parseInt(c,10))}catch(d){}else a.setSelectItemEx(b,parseInt(c,10))},DEXT5UPLOAD.SetFileMark=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&!isNaN(Number(b))&&""!=b.toString())if("ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode)try{a.Dext5PL.SetExtraNameValue(parseInt(b,10),"mark",c)}catch(d){}else a.setFileMark(parseInt(b,10),c)},DEXT5UPLOAD.AddLocalFileDirectlyEX=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);
if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)))try{a.Dext5PL.nNotAllowIfOpen="0",a.Dext5PL.AddLocalFileDirectly(b)}catch(d){}},DEXT5UPLOAD.DoSaveAndOpenEx=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoSaveAndOpen()}catch(c){}},DEXT5UPLOAD.DoSaveAndFolderOpenEx=
function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoSaveAndFolderOpen()}catch(c){}},DEXT5UPLOAD.DoPrintFileEx=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoPrintFile()}catch(c){}},DEXT5UPLOAD.DoSelectFolder=
function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==b._config.userRunTimeMode&&"upload"==b._config.mode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoSelectFolder()}catch(c){}},DEXT5UPLOAD.DoSelectFolderOnly=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==b._config.userRunTimeMode&&"upload"==b._config.mode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoSelectFolderOnly()}catch(c){}},DEXT5UPLOAD.GetFolderPathName=
function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==b._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{c=b.Dext5PL.GetFolderPathName()}catch(a){}return c},DEXT5UPLOAD.GetItemInfoList=function(b,c,a){var d=null;if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==a._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin))try{d=a.Dext5PL.GetItemInfoList(b,c)}catch(e){}return d},DEXT5UPLOAD.MakeFolderDirectly=
function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==a._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)))try{a.Dext5PL.MakeFolderDirectly(b)}catch(d){}},DEXT5UPLOAD.MakeFolderMultiFolder=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==a._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin))try{a.Dext5PL.MakeFolderMultiFolder(b,c)}catch(d){}},DEXT5UPLOAD.DoOpenFileEx=function(b){if(b=
DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==b._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.DoOpenFile()}catch(c){}},DEXT5UPLOAD.AddHttpHeaderEx=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a)){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;if(d)if("ieplugin"==a._config.userRunTimeMode)try{d.Dext5PL.AddHttpHeader(b,c)}catch(e){}else"1"==a._config.hybridDownload&&(b={keyvalue:encodeURIComponent(b+"="+
c)},a.httpHeaderArr.push(b),a._config.hybridParam=d.setHybridParam(a._config.hybridParam,[["p46",a.httpHeaderArr]]))}},DEXT5UPLOAD.setSize=DEXT5UPLOAD.SetSize=function(b,c,a){""!=b&&""!=c&&(a=DEXT5UPLOAD.util._setDext5Uploader(a))&&(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&a.setUploadSize(b,c,!0))},DEXT5UPLOAD.SetForceRedrawWndEx=function(b){if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,"ieplugin"==b._config.userRunTimeMode&&(b=UPLOADTOP.G_CURRUPLOADER.frameWin))try{b.Dext5PL.SetForceRedrawWnd()}catch(c){}},
DEXT5UPLOAD.SetPopupMode=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"==a._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)))try{var d=a.Dext5PL.SetWindowPopupMode(b);if(1<=b){a.Dext5PL.style.width="0px";var e=a.document.getElementById("DEXT_fiVe_UP_PL_holder");if(e){var d=d.replace(/\\/g,"\\\\"),f=(new Date).getTime();e.style.backgroundImage="url('file:///"+d+"?t="+f+"')";e.style.backgroundColor="gray"}}else if(a.Dext5PL.style.width=
"100%",e=a.document.getElementById("DEXT_fiVe_UP_PL_holder"))e.style.removeAttribute("backgroundImage"),e.style.removeAttribute("backgroundColor")}catch(g){}},DEXT5UPLOAD.SetConfig=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a)){UPLOADTOP.G_CURRUPLOADER=a;var d=UPLOADTOP.G_CURRUPLOADER.frameWin;if(d)switch(b=b.substring(0,1).toLowerCase()+b.substring(1),b){case "handlerUrl":a._config[b]=UPLOADTOP.DEXT5UPLOAD.util.set_handlerUrl(c);"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&
(d.Dext5PL.sPostURL=a._config[b]);break;case "downloadHandlerUrl":a._config[b]=UPLOADTOP.DEXT5UPLOAD.util.set_handlerUrl(c);break;case "viewerHandlerUrl":a._config.viewerUrl=UPLOADTOP.DEXT5UPLOAD.util.set_handlerUrl(c);break;case "folderNameRule":a._config[b]=c;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(d.Dext5PL.strFolderNameRule=a._config[b]);break;case "fileNameRule":a._config[b]=c;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(d.Dext5PL.strFileNameRule=a._config[b]);
break;case "fileNameRuleEx":a._config[b]=c;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(d.Dext5PL.strFileNameRuleEx=a._config[b]);break;case "allowedZeroFileSize":a._config[b]=c;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(d.Dext5PL.nAllowedZeroFileSize=a._config[b]);break;case "silentUpload":a._config[b]=c;"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(d.Dext5PL.nSilentUpload=a._config[b]);break;case "uploadMethod":a._config[b]=c;"0"==a._config[b]?
(a._config[b]="0",UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported=!1):"html5"==a._config.userRunTimeMode&&(c=new XMLHttpRequest,UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported=!!(c&&"upload"in c&&"onprogress"in c.upload),a._config[b]=0==UPLOADTOP.DEXT5UPLOAD.browser.ajaxOnProgressSupported?"0":"1");break;case "wsWorkerCount":a._config.socketWorkerCount=c;break;case "wsWorkerJobSize":a._config.socketWorkerJobSize=c;break;case "wsMinSingleWorkerSize":a._config.minSingleSocketWorkerSize=
c;break;default:a._config[b]=c}}},DEXT5UPLOAD.GetNewUploadListForJson=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b)){UPLOADTOP.G_CURRUPLOADER=b;var a=UPLOADTOP.G_CURRUPLOADER.frameWin;if(a){a.setCurrFileStatus();var d=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;if(0<d){for(var e='{"originalName": [{0}], "uploadName": [{1}], "size": [{2}], "guid": [{3}], "uploadPath": [{4}], "status": [{5}], "logicalPath":[{6}], "extension":[{7}], "localPath":[{8}], "mark":[{9}], "responseCustomValue":[{10}]',
e=a.isSettingLargFile()?e+',"largeFiles":[{11}], "order":[{12}], "groupId":[{13}]':e+',"order":[{11}], "groupId":[{12}]',e=e+"}",f="",g=b="",p="",u="",n="",t="",k="",x="",q="",v="",l="",h="",r="",w=0;w<d;w++){0!=w&&(f+=",",b+=",",g+=",",p+=",",u+=",",n+=",",t+=",",k+=",",x+=",",q+=",",v+=",",a.isSettingLargFile()&&(l+=","),h+=",",r+=",");f+='"'+UPLOADTOP.G_CURRUPLOADER.newOriginalName[w]+'"';b+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadName[w]+'"';g+='"'+UPLOADTOP.G_CURRUPLOADER.newSize[w]+'"';p+='"'+
UPLOADTOP.G_CURRUPLOADER.newGuid[w]+'"';u+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadPath[w].replace(/\\/g,"\\\\")+'"';n+='"'+UPLOADTOP.G_CURRUPLOADER.newStatus[w]+'"';k+='"'+a.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[w])+'"';x+='"'+UPLOADTOP.G_CURRUPLOADER.newLocalPath[w].replace(/\\/g,"\\\\")+'"';q+='"'+UPLOADTOP.G_CURRUPLOADER.newMark[w]+'"';v+='"'+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[w]+'"';a.isSettingLargFile()&&(l+='"'+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[w]+'"');h+='"'+
UPLOADTOP.G_CURRUPLOADER.newOrder[w]+'"';if(""!=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[w])var y=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[w],y=y.replace(/\\/g,"\\\\"),t=t+('"'+y+'"');else t+='""';r+='"'+UPLOADTOP.G_CURRUPLOADER.newGroupId[w]+'"'}e=e.replace("{0}",f);e=e.replace("{1}",b);e=e.replace("{2}",g);e=e.replace("{3}",p);e=e.replace("{4}",u);e=e.replace("{5}",n);e=e.replace("{6}",t);e=e.replace("{7}",k);e=e.replace("{8}",x);e=e.replace("{9}",q);e=e.replace("{10}",v);a.isSettingLargFile()?(e=
e.replace("{11}",l),e=e.replace("{12}",h),e=e.replace("{13}",r)):(e=e.replace("{11}",h),e=e.replace("{12}",r));try{c=a.JSON.parse(e)}catch(A){c=null}}}}return c},DEXT5UPLOAD.GetSelectedNewUploadListForJson=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b)){UPLOADTOP.G_CURRUPLOADER=b;var a=UPLOADTOP.G_CURRUPLOADER.frameWin;if(a){a.setCurrFileStatus();var d=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;if(0<d){for(var e='{"originalName": [{0}], "uploadName": [{1}], "size": [{2}], "guid": [{3}], "uploadPath": [{4}], "status": [{5}], "logicalPath":[{6}], "extension":[{7}], "localPath":[{8}], "mark":[{9}], "responseCustomValue":[{10}]',
e=a.isSettingLargFile()?e+',"largeFiles":[{11}], "order":[{12}], "groupId":[{13}]':e+',"order":[{11}], "groupId":[{12}]',e=e+"}",f="",g=b="",p="",u="",n="",t="",k="",x="",q="",v="",l="",h="",r="",w=0,y=0;y<d;y++)if("0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[y]){0!=w&&(f+=",",b+=",",g+=",",p+=",",u+=",",n+=",",t+=",",k+=",",x+=",",q+=",",v+=",",a.isSettingLargFile()&&(l+=","),h+=",",r+=",");f+='"'+UPLOADTOP.G_CURRUPLOADER.newOriginalName[y]+'"';b+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadName[y]+'"';
g+='"'+UPLOADTOP.G_CURRUPLOADER.newSize[y]+'"';p+='"'+UPLOADTOP.G_CURRUPLOADER.newGuid[y]+'"';u+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadPath[y].replace(/\\/g,"\\\\")+'"';n+='"'+UPLOADTOP.G_CURRUPLOADER.newStatus[y]+'"';k+='"'+a.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[y])+'"';x+='"'+UPLOADTOP.G_CURRUPLOADER.newLocalPath[y].replace(/\\/g,"\\\\")+'"';q+='"'+UPLOADTOP.G_CURRUPLOADER.newMark[y]+'"';v+='"'+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[y]+'"';a.isSettingLargFile()&&(l+='"'+
UPLOADTOP.G_CURRUPLOADER.newLargeFiles[y]+'"');h+='"'+UPLOADTOP.G_CURRUPLOADER.newOrder[y]+'"';if(""!=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[y])var A=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[y],A=A.replace(/\\/g,"\\\\"),t=t+('"'+A+'"');else t+='""';r+='"'+UPLOADTOP.G_CURRUPLOADER.newGroupId[y]+'"';w++}e=e.replace("{0}",f);e=e.replace("{1}",b);e=e.replace("{2}",g);e=e.replace("{3}",p);e=e.replace("{4}",u);e=e.replace("{5}",n);e=e.replace("{6}",t);e=e.replace("{7}",k);e=e.replace("{8}",x);e=e.replace("{9}",
q);e=e.replace("{10}",v);a.isSettingLargFile()?(e=e.replace("{11}",l),e=e.replace("{12}",h),e=e.replace("{13}",r)):(e=e.replace("{11}",h),e=e.replace("{12}",r));try{c=a.JSON.parse(e)}catch(D){c=null}}}}return c},DEXT5UPLOAD.GetAllFileListForJson=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;
if(0<a||0<d){for(var e='{"newFile":{"originalName": [{0}], "uploadName": [{1}], "size": [{2}], "guid": [{3}], "uploadPath": [{4}], "status": [{5}], "logicalPath": [{6}], "extension": [{7}], "localPath": [{8}], "mark": [{9}], "responseCustomValue": [{10}]',e=b.isSettingLargFile()?e+', "largeFiles": [{11}], "order": [{12}], "groupId": [{13}]':e+', "order": [{11}], "groupId": [{12}]',e=e+'}, "webFile":{"uniqKey": [{14}], "originalName": [{15}], "size": [{16}], "customValue": [{17}]',e=b.isSettingLargFile()?
e+', "largeFiles": [{18}], "order": [{19}], "uploadPath": [{20}], "logicalPath": [{21}]':e+', "order": [{18}], "uploadPath": [{19}], "logicalPath": [{20}]',e=e+"}}",f="",g="",p="",u="",n="",t="",k="",x="",q="",v="",l="",h="",r="",w="",y="",A="",D="",G="",C="",I="",H="",m="",B=0;B<a;B++){0!=B&&(f+=",",g+=",",p+=",",u+=",",n+=",",t+=",",k+=",",I+=",",x+=",",q+=",",v+=",",b.isSettingLargFile()&&(l+=","),h+=",",r+=",");f+='"'+UPLOADTOP.G_CURRUPLOADER.newOriginalName[B]+'"';g+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadName[B]+
'"';p+='"'+UPLOADTOP.G_CURRUPLOADER.newSize[B]+'"';u+='"'+UPLOADTOP.G_CURRUPLOADER.newGuid[B]+'"';n+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadPath[B].replace(/\\/g,"\\\\")+'"';t+='"'+UPLOADTOP.G_CURRUPLOADER.newStatus[B]+'"';I+='"'+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[B])+'"';x+='"'+UPLOADTOP.G_CURRUPLOADER.newLocalPath[B].replace(/\\/g,"\\\\")+'"';q+='"'+UPLOADTOP.G_CURRUPLOADER.newMark[B]+'"';v+='"'+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[B]+'"';b.isSettingLargFile()&&(l+=
'"'+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[B]+'"');h+='"'+UPLOADTOP.G_CURRUPLOADER.newOrder[B]+'"';if(""!=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[B])var z=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[B],z=z.replace(/\\/g,"\\\\"),k=k+('"'+z+'"');else k+='""';r+='"'+UPLOADTOP.G_CURRUPLOADER.newGroupId[B]+'"'}for(B=0;B<d;B++)0!=B&&(w+=",",y+=",",A+=",",b.isSettingLargFile()&&(D+=","),G+=",",C+=",",H+=",",m+=","),w+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[B]+'"',y+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[B]+
'"',A+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelSize[B]+'"',b.isSettingLargFile()&&(D+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[B]+'"'),G+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[B]+'"',C+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[B].replace(/\\/g,"\\\\")+'"',H+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[B]+'"',""!=UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[B]?(z=UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[B],z=z.replace(/\\/g,"\\\\"),m+='"'+z+'"'):m+='""';e=e.replace("{0}",f);e=e.replace("{1}",
g);e=e.replace("{2}",p);e=e.replace("{3}",u);e=e.replace("{4}",n);e=e.replace("{5}",t);e=e.replace("{6}",k);e=e.replace("{7}",I);e=e.replace("{8}",x);e=e.replace("{9}",q);e=e.replace("{10}",v);b.isSettingLargFile()?(e=e.replace("{11}",l),e=e.replace("{12}",h),e=e.replace("{13}",r)):(e=e.replace("{11}",h),e=e.replace("{12}",r));e=e.replace("{14}",w);e=e.replace("{15}",y);e=e.replace("{16}",A);e=e.replace("{17}",H);b.isSettingLargFile()?(e=e.replace("{18}",D),e=e.replace("{19}",G),e=e.replace("{20}",
C),e=e.replace("{21}",m)):(e=e.replace("{18}",G),e=e.replace("{19}",C),e=e.replace("{20}",m));try{c=b.JSON.parse(e)}catch(M){c=null}}}return c},DEXT5UPLOAD.GetSelectedAllFileListForJson=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var e='{"newFile":{"originalName": [{0}], "uploadName": [{1}], "size": [{2}], "guid": [{3}], "uploadPath": [{4}], "status": [{5}], "logicalPath": [{6}], "extension": [{7}], "localPath": [{8}], "mark": [{9}], "responseCustomValue": [{10}]',
e=b.isSettingLargFile()?e+', "largeFiles": [{11}], "order": [{12}], "groupId": [{13}]':e+', "order": [{11}], "groupId": [{12}]',e=e+'}, "webFile":{"uniqKey": [{14}], "originalName": [{15}], "size": [{16}], "customValue": [{17}]',e=b.isSettingLargFile()?e+', "largeFiles": [{18}], "order": [{19}], "uploadPath": [{20}], "logicalPath": [{21}]':e+', "order": [{18}], "uploadPath": [{19}], "logicalPath": [{20}]',e=e+"}}",f="",g="",p="",u="",n="",t="",k="",x="",q="",v="",l="",h="",r="",w="",y="",A="",D="",
G="",C="",I="",H="",m="",B=0,z=0;z<a;z++)if("0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[z]){0!=B&&(f+=",",g+=",",p+=",",u+=",",n+=",",t+=",",k+=",",I+=",",x+=",",q+=",",v+=",",b.isSettingLargFile()&&(l+=","),h+=",",r+=",");f+='"'+UPLOADTOP.G_CURRUPLOADER.newOriginalName[z]+'"';g+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadName[z]+'"';p+='"'+UPLOADTOP.G_CURRUPLOADER.newSize[z]+'"';u+='"'+UPLOADTOP.G_CURRUPLOADER.newGuid[z]+'"';n+='"'+UPLOADTOP.G_CURRUPLOADER.newUploadPath[z].replace(/\\/g,"\\\\")+'"';t+=
'"'+UPLOADTOP.G_CURRUPLOADER.newStatus[z]+'"';I+='"'+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[z])+'"';x+='"'+UPLOADTOP.G_CURRUPLOADER.newLocalPath[z].replace(/\\/g,"\\\\")+'"';q+='"'+UPLOADTOP.G_CURRUPLOADER.newMark[z]+'"';v+='"'+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[z]+'"';b.isSettingLargFile()&&(l+='"'+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[z]+'"');h+='"'+UPLOADTOP.G_CURRUPLOADER.newOrder[z]+'"';if(""!=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[z])var M=UPLOADTOP.G_CURRUPLOADER.newLogicalPath[z],
M=M.replace(/\\/g,"\\\\"),k=k+('"'+M+'"');else k+='""';r+='"'+UPLOADTOP.G_CURRUPLOADER.newGroupId[z]+'"';B++}for(z=a=0;z<d;z++)"0"!=UPLOADTOP.G_CURRUPLOADER.nonDelSelectFlag[z]&&(0!=a&&(w+=",",y+=",",A+=",",b.isSettingLargFile()&&(D+=","),G+=",",C+=",",H+=",",m+=","),w+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[z]+'"',y+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[z]+'"',A+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelSize[z]+'"',b.isSettingLargFile()&&(D+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[z]+
'"'),G+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[z]+'"',C+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[z]+'"',H+='"'+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[z]+'"',""!=UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[z]?(M=UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[z],M=M.replace(/\\/g,"\\\\"),m+='"'+M+'"'):m+='""',a++);e=e.replace("{0}",f);e=e.replace("{1}",g);e=e.replace("{2}",p);e=e.replace("{3}",u);e=e.replace("{4}",n);e=e.replace("{5}",t);e=e.replace("{6}",k);e=e.replace("{7}",I);e=e.replace("{8}",
x);e=e.replace("{9}",q);e=e.replace("{10}",v);b.isSettingLargFile()?(e=e.replace("{11}",l),e=e.replace("{12}",h),e=e.replace("{13}",r)):(e=e.replace("{11}",h),e=e.replace("{12}",r));e=e.replace("{14}",w);e=e.replace("{15}",y);e=e.replace("{16}",A);e=e.replace("{17}",H);b.isSettingLargFile()?(e=e.replace("{18}",D),e=e.replace("{19}",G),e=e.replace("{20}",C),e=e.replace("{21}",m)):(e=e.replace("{18}",G),e=e.replace("{19}",C),e=e.replace("{20}",m));try{c=b.JSON.parse(e)}catch(qa){c=null}}}return c},
DEXT5UPLOAD.GetDeleteListForJson=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.delOriginalName.length;if(0<a){var d='{"uniqKey": [{0}], "originalName": [{1}], "size": [{2}]';b.isSettingLargFile()&&(d+=', "largeFiles": [{3}]');for(var d=d+"}",e="",f="",g="",p="",u=0;u<a;u++)0!=u&&(e+=",",f+=",",g+=",",b.isSettingLargFile()&&(p+=",")),e+='"'+UPLOADTOP.G_CURRUPLOADER.delUniqKey[u]+
'"',f+='"'+UPLOADTOP.G_CURRUPLOADER.delOriginalName[u]+'"',g+='"'+UPLOADTOP.G_CURRUPLOADER.delSize[u]+'"',b.isSettingLargFile()&&(p+='"'+UPLOADTOP.G_CURRUPLOADER.delLargeFiles[u]+'"');d=d.replace("{0}",e);d=d.replace("{1}",f);d=d.replace("{2}",g);b.isSettingLargFile()&&(d=d.replace("{3}",p));try{c=b.JSON.parse(d)}catch(n){c=null}}}return c},DEXT5UPLOAD.GetAllFileMergeListForJson=function(b){var c=null,a=DEXT5UPLOAD.util._setDext5Uploader(b);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,a=UPLOADTOP.G_CURRUPLOADER.frameWin)){a.setCurrFileStatus();
var d=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;b=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;var e=[];if(0<d||0<b){for(var f=0;f<d;f++){var g="",g=g+"0",g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+""),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newOriginalName[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newSize[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newStatus[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
a.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[f])),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[f]),g=g+(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[f]);a.isSettingLargFile()&&(g+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[f]);g+=
UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newOrder[f];g+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"";g+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[f];e[UPLOADTOP.G_CURRUPLOADER.newOrder[f]-1]=g}for(f=0;f<b;f++)d="",d+="1",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[f],d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[f],
d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelSize[f],d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[f],d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[f],d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a.isSettingLargFile()&&(d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[f]),d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f],d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[f],
d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",e[UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f]-1]=d;for(var d='{"type": [{0}], "uniqKey": [{1}], "originalName": [{2}], "uploadName": [{3}], "size": [{4}], "guid": [{5}], "uploadPath": [{6}], "status": [{7}], "logicalPath": [{8}], "extension": [{9}], "localPath": [{10}], "mark": [{11}], "responseCustomValue": [{12}]',d=a.isSettingLargFile()?d+', "largeFiles": [{13}], "order": [{14}], "customValue": [{15}], "groupId": [{16}]':d+', "order": [{13}], "customValue": [{14}], "groupId": [{15}]',
d=d+"}",p=g="",u="",n=b="",t="",k="",x="",q="",v="",l="",h="",r="",w="",y="",A="",D="",G=e.length,f=0;G>f;f++){0!=f&&(g+=",",p+=",",u+=",",b+=",",n+=",",t+=",",k+=",",x+=",",q+=",",v+=",",l+=",",h+=",",r+=",",a.isSettingLargFile()&&(w+=","),y+=",",A+=",",D+=",");var C=e[f].split(UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter),g=g+('"'+C[0]+'"'),p=p+('"'+C[1]+'"'),u=u+('"'+C[2]+'"');b+='"'+C[3]+'"';n+='"'+C[4]+'"';t+='"'+C[5]+'"';k+='"'+C[6].replace(/\\/g,"\\\\")+'"';x+='"'+C[7]+'"';if(""!=
C[8])var I=C[8],I=I.replace(/\\/g,"\\\\"),q=q+('"'+I+'"');else q+='""';v+='"'+C[9]+'"';l+='"'+C[10].replace(/\\/g,"\\\\")+'"';h+='"'+C[11]+'"';r+='"'+C[12]+'"';a.isSettingLargFile()?(w+='"'+C[13]+'"',y+='"'+C[14]+'"',A+='"'+C[15]+'"',D+='"'+C[16]+'"'):(y+='"'+C[13]+'"',A+='"'+C[14]+'"',D+='"'+C[15]+'"')}d=d.replace("{0}",g);d=d.replace("{1}",p);d=d.replace("{2}",u);d=d.replace("{3}",b);d=d.replace("{4}",n);d=d.replace("{5}",t);d=d.replace("{6}",k);d=d.replace("{7}",x);d=d.replace("{8}",q);d=d.replace("{9}",
v);d=d.replace("{10}",l);d=d.replace("{11}",h);d=d.replace("{12}",r);a.isSettingLargFile()?(d=d.replace("{13}",w),d=d.replace("{14}",y),d=d.replace("{15}",A),d=d.replace("{16}",D)):(d=d.replace("{13}",y),d=d.replace("{14}",A),d=d.replace("{15}",D));try{c=a.JSON.parse(d)}catch(H){c=null}}}return c},DEXT5UPLOAD.GetNewUploadListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;
if(0<a){for(var c='<?xml version="1.0" encoding="utf-8" ?><files>{0}</files>',d="",e=0;e<a;e++)d+="<file>",d+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOriginalName[e]+"]]\x3e</originalName>",d+="<uploadName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadName[e]+"]]\x3e</uploadName>",d+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newSize[e]+"]]\x3e</size>",d+="<guid><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGuid[e]+"]]\x3e</guid>",d+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadPath[e]+
"]]\x3e</uploadPath>",d+="<status><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newStatus[e]+"]]\x3e</status>",d+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[e]+"]]\x3e</logicalPath>",d+="<extension><![CDATA["+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[e])+"]]\x3e</extension>",d+="<localPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLocalPath[e]+"]]\x3e</localPath>",d+="<mark><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newMark[e]+"]]\x3e</mark>",d+="<responseCustomValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[e]+
"]]\x3e</responseCustomValue>",b.isSettingLargFile()&&(d+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[e]+"]]\x3e</largeFiles>"),d+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOrder[e]+"]]\x3e</order>",d+="<groupId><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGroupId[e]+"]]\x3e</groupId>",d+="</file>";c=c.replace("{0}",d);c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetSelectedNewUploadListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=
b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;if(0<a){for(var c='<?xml version="1.0" encoding="utf-8" ?><files>{0}</files>',d="",e=0;e<a;e++)"0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[e]&&(d+="<file>",d+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOriginalName[e]+"]]\x3e</originalName>",d+="<uploadName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadName[e]+"]]\x3e</uploadName>",d+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newSize[e]+
"]]\x3e</size>",d+="<guid><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGuid[e]+"]]\x3e</guid>",d+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadPath[e]+"]]\x3e</uploadPath>",d+="<status><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newStatus[e]+"]]\x3e</status>",d+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[e]+"]]\x3e</logicalPath>",d+="<extension><![CDATA["+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[e])+"]]\x3e</extension>",d+="<localPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLocalPath[e]+
"]]\x3e</localPath>",d+="<mark><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newMark[e]+"]]\x3e</mark>",d+="<responseCustomValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[e]+"]]\x3e</responseCustomValue>",b.isSettingLargFile()&&(d+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[e]+"]]\x3e</largeFiles>"),d+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOrder[e]+"]]\x3e</order>",d+="<groupId><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGroupId[e]+"]]\x3e</groupId>",d+="</file>");c=
c.replace("{0}",d);c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetAllFileListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var c='<?xml version="1.0" encoding="utf-8" ?><files><newFile>{0}</newFile><webFile>{1}</webFile></files>',e="",f=0;f<
a;f++)e+="<file>",e+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOriginalName[f]+"]]\x3e</originalName>",e+="<uploadName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadName[f]+"]]\x3e</uploadName>",e+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newSize[f]+"]]\x3e</size>",e+="<guid><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGuid[f]+"]]\x3e</guid>",e+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadPath[f]+"]]\x3e</uploadPath>",e+="<status><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newStatus[f]+
"]]\x3e</status>",e+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[f]+"]]\x3e</logicalPath>",e+="<extension><![CDATA["+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[f])+"]]\x3e</extension>",e+="<localPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLocalPath[f]+"]]\x3e</localPath>",e+="<mark><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newMark[f]+"]]\x3e</mark>",e+="<responseCustomValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[f]+"]]\x3e</responseCustomValue>",b.isSettingLargFile()&&
(e+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[f]+"]]\x3e</largeFiles>"),e+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOrder[f]+"]]\x3e</order>",e+="<groupId><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGroupId[f]+"]]\x3e</groupId>",e+="</file>";a="";for(f=0;f<d;f++)a+="<file>",a+="<uniqKey><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[f]+"]]\x3e</uniqKey>",a+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[f]+"]]\x3e</originalName>",a+="<size><![CDATA["+
UPLOADTOP.G_CURRUPLOADER.nonDelSize[f]+"]]\x3e</size>",b.isSettingLargFile()&&(a+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[f]+"]]\x3e</largeFiles>"),a+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f]+"]]\x3e</order>",a+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[f]+"]]\x3e</uploadPath>",a+="<customValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[f]+"]]\x3e</customValue>",a+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[f]+
"]]\x3e</logicalPath>",a+="</file>";c=c.replace("{0}",e);c=c.replace("{1}",a);c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetSelectedAllFileListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var c='<?xml version="1.0" encoding="utf-8" ?><files><newFile>{0}</newFile><webFile>{1}</webFile></files>',
e="",f=0;f<a;f++)"0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[f]&&(e+="<file>",e+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOriginalName[f]+"]]\x3e</originalName>",e+="<uploadName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadName[f]+"]]\x3e</uploadName>",e+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newSize[f]+"]]\x3e</size>",e+="<guid><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGuid[f]+"]]\x3e</guid>",e+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadPath[f]+"]]\x3e</uploadPath>",
e+="<status><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newStatus[f]+"]]\x3e</status>",e+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[f]+"]]\x3e</logicalPath>",e+="<extension><![CDATA["+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[f])+"]]\x3e</extension>",e+="<localPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLocalPath[f]+"]]\x3e</localPath>",e+="<mark><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newMark[f]+"]]\x3e</mark>",e+="<responseCustomValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[f]+
"]]\x3e</responseCustomValue>",b.isSettingLargFile()&&(e+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[f]+"]]\x3e</largeFiles>"),e+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOrder[f]+"]]\x3e</order>",e+="<groupId><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGroupId[f]+"]]\x3e</groupId>",e+="</file>");a="";for(f=0;f<d;f++)"0"!=UPLOADTOP.G_CURRUPLOADER.nonDelSelectFlag[f]&&(a+="<file>",a+="<uniqKey><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[f]+"]]\x3e</uniqKey>",a+="<originalName><![CDATA["+
UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[f]+"]]\x3e</originalName>",a+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelSize[f]+"]]\x3e</size>",b.isSettingLargFile()&&(a+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[f]+"]]\x3e</largeFiles>"),a+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f]+"]]\x3e</order>",a+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[f]+"]]\x3e</uploadPath>",a+="<customValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[f]+
"]]\x3e</customValue>",a+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[f]+"]]\x3e</logicalPath>",a+="</file>");c=c.replace("{0}",e);c=c.replace("{1}",a);c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetAllFileMergeListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=[],d=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,e=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;
if(0<d||0<e){for(var c='<?xml version="1.0" encoding="utf-8" ?><files>{0}</files>',f=0;f<d;f++){var g="",g=g+"<file>",g=g+"<type><![CDATA[0]]\x3e</type>",g=g+"<uniqKey></uniqKey>",g=g+("<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOriginalName[f]+"]]\x3e</originalName>"),g=g+("<uploadName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadName[f]+"]]\x3e</uploadName>"),g=g+("<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newSize[f]+"]]\x3e</size>"),g=g+("<guid><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGuid[f]+
"]]\x3e</guid>"),g=g+("<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newUploadPath[f]+"]]\x3e</uploadPath>"),g=g+("<status><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newStatus[f]+"]]\x3e</status>"),g=g+("<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[f]+"]]\x3e</logicalPath>"),g=g+("<extension><![CDATA["+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[f])+"]]\x3e</extension>"),g=g+("<localPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLocalPath[f]+"]]\x3e</localPath>"),g=g+("<mark><![CDATA["+
UPLOADTOP.G_CURRUPLOADER.newMark[f]+"]]\x3e</mark>"),g=g+("<responseCustomValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[f]+"]]\x3e</responseCustomValue>");b.isSettingLargFile()&&(g+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[f]+"]]\x3e</largeFiles>");g+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newOrder[f]+"]]\x3e</order>";g+="<customValue><![CDATA[]]\x3e</customValue>";g+="<groupId><![CDATA["+UPLOADTOP.G_CURRUPLOADER.newGroupId[f]+"]]\x3e</groupId>";g+="</file>";
a[UPLOADTOP.G_CURRUPLOADER.newOrder[f]-1]=g}for(f=0;f<e;f++)d="",d+="<file>",d+="<type><![CDATA[1]]\x3e</type>",d+="<uniqKey><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[f]+"]]\x3e</uniqKey>",d+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[f]+"]]\x3e</originalName>",d+="<uploadName></uploadName>",d+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelSize[f]+"]]\x3e</size>",d+="<guid><![CDATA[]]\x3e</guid>",d+="<uploadPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[f]+
"]]\x3e</uploadPath>",d+="<status><![CDATA[]]\x3e</status>",d+="<logicalPath><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[f]+"]]\x3e</logicalPath>",d+="<extension><![CDATA[]]\x3e</extension>",d+="<localPath><![CDATA[]]\x3e</localPath>",d+="<mark><![CDATA[]]\x3e</mark>",d+="<responseCustomValue><![CDATA[]]\x3e</responseCustomValue>",b.isSettingLargFile()&&(d+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[f]+"]]\x3e</largeFiles>"),d+="<order><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f]+
"]]\x3e</order>",d+="<customValue><![CDATA["+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[f]+"]]\x3e</customValue>",g+="<groupId><![CDATA[]]\x3e</groupId>",d+="</file>",a[UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f]-1]=d;c=c.replace("{0}",a.join(""));c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetDeleteListForXml=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.delOriginalName.length;
if(0<a){for(var c='<?xml version="1.0" encoding="utf-8" ?><files>{0}</files>',d="",e=0;e<a;e++)d+="<file>",d+="<uniqKey><![CDATA["+UPLOADTOP.G_CURRUPLOADER.delUniqKey[e]+"]]\x3e</uniqKey>",d+="<originalName><![CDATA["+UPLOADTOP.G_CURRUPLOADER.delOriginalName[e]+"]]\x3e</originalName>",d+="<size><![CDATA["+UPLOADTOP.G_CURRUPLOADER.delSize[e]+"]]\x3e</size>",b.isSettingLargFile()&&(d+="<largeFiles><![CDATA["+UPLOADTOP.G_CURRUPLOADER.delLargeFiles[e]+"]]\x3e</largeFiles>"),d+="</file>";c=c.replace("{0}",
d);c=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(c)}}return c},DEXT5UPLOAD.GetNewUploadListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var c="",a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;if(0<a)for(var d=0;d<a;d++)0!=d&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),c+=UPLOADTOP.G_CURRUPLOADER.newOriginalName[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[d],
c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newSize[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newStatus[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[d]),c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[d],b.isSettingLargFile()&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[d]),c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newOrder[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[d]}return c},DEXT5UPLOAD.GetSelectedNewUploadListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length;if(0<a)for(var c="",d=0,e=0;e<a;e++)"0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[e]&&(0!=d&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),
c+=UPLOADTOP.G_CURRUPLOADER.newOriginalName[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newSize[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newStatus[e],
c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[e]),c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[e],
b.isSettingLargFile()&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[e]),c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newOrder[e],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[e],d++)}return c},DEXT5UPLOAD.GetAllFileListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();
var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var c={newFile:"",webFile:""},e=0;e<a;e++)0!=e&&(c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),c.newFile+=UPLOADTOP.G_CURRUPLOADER.newOriginalName[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newSize[e],c.newFile+=
UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newStatus[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[e]),
c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[e],b.isSettingLargFile()&&(c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[e]),c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newOrder[e],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[e];for(e=0;e<d;e++)0!=e&&(c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),c.webFile+=UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[e],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[e],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelSize[e],b.isSettingLargFile()&&
(c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[e]),c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[e],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[e],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[e],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[e]}}return c},DEXT5UPLOAD.GetSelectedAllFileListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var c={newFile:"",webFile:""},e=0,f=0;f<a;f++)"0"!=UPLOADTOP.G_CURRUPLOADER.newSelectFlag[f]&&(0!=e&&(c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),
c.newFile+=UPLOADTOP.G_CURRUPLOADER.newOriginalName[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newSize[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newStatus[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[f]),c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[f],b.isSettingLargFile()&&(c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[f]),c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newOrder[f],c.newFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[f],e++);for(f=a=0;f<d;f++)"0"!=UPLOADTOP.G_CURRUPLOADER.nonDelSelectFlag[f]&&(0!=a&&(c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),
c.webFile+=UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[f],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[f],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelSize[f],b.isSettingLargFile()&&(c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[f]),c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[f],
c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[f],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[f],c.webFile+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[f],a++)}}return c},DEXT5UPLOAD.GetAllFileMergeListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();
var a=UPLOADTOP.G_CURRUPLOADER.newOriginalName.length,d=UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName.length;if(0<a||0<d){for(var c=[],e=0;e<a;e++){var f="";0!=UPLOADTOP.G_CURRUPLOADER.newOrder[e]-1&&(f+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter);f+="0";f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"";f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newOriginalName[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadName[e];
f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newSize[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGuid[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newUploadPath[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newStatus[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLogicalPath[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
b.getExtension(UPLOADTOP.G_CURRUPLOADER.newUploadName[e]);f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLocalPath[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newMark[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newResponseCustomValue[e];b.isSettingLargFile()&&(f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newLargeFiles[e]);f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.newOrder[e];f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"";f+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.newGroupId[e];c[UPLOADTOP.G_CURRUPLOADER.newOrder[e]-1]=f}for(e=0;e<d;e++)a="",0!=UPLOADTOP.G_CURRUPLOADER.nonDelOrder[e]-1&&(a+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),a+="1",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUniqKey[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.nonDelOriginalName[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelSize[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelUploadPath[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLogicalPath[e],
a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",b.isSettingLargFile()&&(a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelLargeFiles[e]),a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.nonDelOrder[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+
UPLOADTOP.G_CURRUPLOADER.nonDelCustomValue[e],a+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+"",c[UPLOADTOP.G_CURRUPLOADER.nonDelOrder[e]-1]=a}c&&(c=c.join(""))}return c},DEXT5UPLOAD.GetDeleteListForText=function(b){var c=null;if(b=DEXT5UPLOAD.util._setDext5Uploader(b))if(UPLOADTOP.G_CURRUPLOADER=b,b=UPLOADTOP.G_CURRUPLOADER.frameWin){b.setCurrFileStatus();var a=UPLOADTOP.G_CURRUPLOADER.delOriginalName.length;if(0<a)for(var c="",d=0;d<a;d++)0!=d&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),
c+=UPLOADTOP.G_CURRUPLOADER.delUniqKey[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.delOriginalName[d],c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.delSize[d],b.isSettingLargFile()&&(c+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+UPLOADTOP.G_CURRUPLOADER.delLargeFiles[d])}return c},DEXT5UPLOAD.GetItemInfoListForText=function(b,c,a){var d=null;if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=
a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){a.setCurrFileStatus();b=a.Dext5PL.GetItemInfoList(b,c);if(""==b)return null;b=b.split(G_vertical);c=b.length;if(0<c)for(d="",a=0;a<c;a++)0!=a&&(d+=UPLOADTOP.G_CURRUPLOADER._config.unitDelimiter),d+=a,d+=UPLOADTOP.G_CURRUPLOADER._config.unitAttributeDelimiter+decodeURIComponent(b[a]).replace(/\\/gi,"\\\\")}return d},DEXT5UPLOAD.GetItemInfoListForXml=function(b,c,a){var d=null;if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=
a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){a.setCurrFileStatus();b=a.Dext5PL.GetItemInfoList(b,c);if(""==b)return null;b=b.split(G_vertical);c=b.length;if(0<c){d='<?xml version="1.0" encoding="utf-8" ?><files>{0}</files>';a="";for(var e=0;e<c;e++)a+="<file>",a+="<index><![CDATA["+e+"]]\x3e</index>",a+="<path><![CDATA["+decodeURIComponent(b[e]).replace(/\\/gi,"\\\\")+"]]\x3e</path>",a+="</file>";d=d.replace("{0}",a);d=UPLOADTOP.DEXT5UPLOAD.util.stringToXML(d)}}return d},
DEXT5UPLOAD.GetItemInfoListForJson=function(b,c,a){var d=null;if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode){a.setCurrFileStatus();b=a.Dext5PL.GetItemInfoList(b,c);if(""==b)return null;b=b.split(G_vertical);c=b.length;if(0<c){for(var e='{"index": [{0}], "path": [{1}]}',f="",g="",p=0;p<c;p++)0!=p&&(f+=",",g+=","),f+='"'+p+'"',g+='"'+decodeURIComponent(b[p]).replace(/\\/gi,
"\\\\")+'"';e=e.replace("{0}",f);e=e.replace("{1}",g);try{d=a.JSON.parse(e)}catch(u){d=null}}}return d},DEXT5UPLOAD.FileSort=function(b,c,a){if(a=DEXT5UPLOAD.util._setDext5Uploader(a))if(UPLOADTOP.G_CURRUPLOADER=a,a=UPLOADTOP.G_CURRUPLOADER.frameWin)"ieplugin"==UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode?"0"==c?a.Dext5PL.SetSortCommand(b,"1"):"1"==c&&a.Dext5PL.SetSortCommand(b,"2"):(a.sortTotalFileList(b,c,!0),a.setListvalue())},DEXT5UPLOAD.GetUploadByName=function(b){var c=null;try{c=DEXT5UPLOAD.util._getUploaderByName(b),
void 0==c&&(c=null)}catch(a){}return c},DEXT5UPLOAD.SetViewModeChange=function(b,c){var a=DEXT5UPLOAD.util._setDext5Uploader(c);if(a&&(UPLOADTOP.G_CURRUPLOADER=a,"ieplugin"!=UPLOADTOP.G_CURRUPLOADER._config.userRunTimeMode&&(a=UPLOADTOP.G_CURRUPLOADER.frameWin)&&b!=UPLOADTOP.G_CURRUPLOADER._config.views)){UPLOADTOP.G_CURRUPLOADER._config.views=b;var d=UPLOADTOP.DEXT5UPLOAD.util.getElementsByClass("DEXT_fiVe_UP_uploadbox_tit",a.document,"div")[0];"thumbs"==b?(UPLOADTOP.G_CURRUPLOADER._config.showHeaderBar=
"0",d.style.display="none"):"list"==b&&"1"==UPLOADTOP.G_CURRUPLOADER._config.showHeaderBarOrg&&(UPLOADTOP.G_CURRUPLOADER._config.showHeaderBar="1",d.style.display="block");d=parseInt(UPLOADTOP.G_CURRUPLOADER._config.height);-1<UPLOADTOP.G_CURRUPLOADER._config.height.indexOf("%")&&(d=UPLOADTOP.DEXT5UPLOAD.util.getClientRect(UPLOADTOP.UPLOADDOC.getElementById("dext5uploader_holder_"+UPLOADTOP.G_CURRUPLOADER.ID)),d=d.bottom-d.top);a.setFileListHeight(d,UPLOADTOP.G_CURRUPLOADER._config,UPLOADTOP.G_CURRUPLOADER);
a.document.getElementById("DEXT_fiVe_UP_file_temp").style.height=UPLOADTOP.G_CURRUPLOADER.fileListHeight+"px";a.document.getElementById("file_list").innerHTML="";d=a.RESULTFILELIST.length;UPLOADTOP.G_CURRUPLOADER.checkThumbsArr=[];for(var e=0;e<d;e++)"n"==a.RESULTFILELIST[e].isDelete&&("y"==a.RESULTFILELIST[e].isWebFile?a.addFileList(a.RESULTFILELIST[e],!0,e):a.addFileList(a.RESULTFILELIST[e],!1,e));"thumbs"==b&&a.thumbsViewWithCanvas()}});
DEXT5UPLOAD.config={InitXml:"",InitServerXml:"",InitVisible:!0,SkinName:"",Width:"",Height:"",Lang:"",KeepLang:"",HeaderBar:"",StatusBar:"",ButtonBar:"",ButtonBarEdit:"",ButtonBarView:"",BorderStyle:"",MaxTotalFileCount:"",MaxTotalFileSize:"",MaxOneFileSize:"",MultiFileSelect:"",ExtensionAllowOrLimit:"",ExtensionArr:"",Mode:"",Views:"",Runtimes:"",RunTimes:"",UseWS:"",FolderNameRule:"",CustomHeaderColor:"",CustomFooterColor:"",CustomProgressBarColor:"",CustomTextColor:"",CustomWebFileColor:"",UploadHolder:"",
ResumeUpload:"",ResumeDownload:"",FolderTransfer:"",FolderDetailHeader:"",UseServerFileSize:"",UseAddEvent:"",UseDeleteEvent:"",UseViewOrOpenEvent:"",UseUploadingCancelEvent:"",UseDownloadEvent:"",UseAfterAddEvent:"",UseAfterAddEndTimeEvent:"",UseAfterDownloadEvent:"",UseDeleteEndTimeEvent:"",UseFinishDownloadedEvent:"",Timeout:"",AutomaticConnection:"",ShowFolderView:"",IgnoreSameUploadName:"0",HandlerUrl:"",WsHandlerUrl:"",WsChunkSize:"",DownloadHandlerUrl:"",MessageTitle:"",UseScriptEventControl:"",
DevelopLangage:"",ShowCheckBox:"",HideContextMenu:"",SizeColumnWidth:"",UnitDelimiter:"",UnitAttributeDelimiter:"",DownloadMulti:"",PluginZipDownload:"",CssRootPath:"",ViewerHandlerUrl:"",UseAutoHeight:"",RemoveContextItem:"",DisableDeleteConfirmMessage:"",DisplayFileSizeHtml4:"",ListViewDbclick:"",ListViewDragAndDrop:"",HideListInfo:"",NTLM:"",LargeFiles:"",CacheProtectMode:"",UseFileSort:"",FileSortField:"",FileSortAscDesc:"",AutoSort:"",ShowEditAlign:"",ShowViewAlign:"",FileMoveContextMenu:"",
CompleteEventResetUse:"",ImgPreView:"",ImgPreViewWidth:"",ImgPreViewHeight:"",StatusBarShowLimit:"",StatusBarShowStatus:"",ButtonBarPosition:"",UserMessage:"",UseSingleSelect:"",DisableAlertMessage:{Duplication:"",DeleteUnchosen:"",DownloadUnchosen:"",OpenUnchosen:"",DisableDeleteConfirm:"",FileExtensionDetect:""},AllowedZeroFileSize:"",ChunkSize:"",FileNameRule:"",FileNameRuleEx:null,AsyncUpload:"",SilentUpload:"",DirectDownload:"",ProductKey:"",LicenseKey:"",LicenseKeyEx:"",EnableThread:"",LicenseKeySE:"",
Security:{EncryptParam:"",FileExtensionDetector:"",FileIntegrity:"",FileEncrypt:""},HighSpeed:"",SizeForChunkUpload:"",UploadMethod:"",HeaderBarItem:"",HeaderBarItemWidth:"",HeaderBarItemAlign:"",WsWorkerCount:"",WsWorkerJobSize:"",WsMinSingleWorkerSize:"",UploadTransferWindow:{View:"",ViewWidth:"",ViewHeight:""},GroupId:"",PluginInstallType:"",PluginInstallUrl:"",DefaultDownloadPath:"",UploadMethodHtml4:"",TransferOpenFile:"",SelectByClicked:"",AftertUpload:{SetFileSize:""},SavePathSetting:"",TransferBackgroundStyle:"",
UseDropzone:"",AllowOpenExtension:"",FileFilterEx:"",ImageQuality:"",UseZipDownload:"",HybridDownload:"",EnforceIePlugin:"",UseInstallGuide:"",HybridWindowMode:"",DialogWindow:null,HybridUseSetCookie:"",HybridMethod:"",AllowedRealtimeDownloadAdd:"",HybridShowItemCount:"",DownloadPartialSize:"",Html4LimitFileSize:"",AutoDestroy:"",SkipSentFile:"",PartialSize:"",PluginKeepVersion:"",UsePluginInstallGuide:"",ForceOverwrite:"",Event:{TransferComplete:null}};
function Dext5Upload_Config(){this.licenseKey=this.productKey="";this.licenseKeySE="0";this.licenseKeyEx="";this.plugin_version=DEXT5UPLOAD.cabVersion;this.webPath={image:DEXT5UPLOAD.rootPath+"images/",skin:DEXT5UPLOAD.rootPath+"images/skin/",page:DEXT5UPLOAD.rootPath+"pages/",js:DEXT5UPLOAD.rootPath+"js/",jsDev:DEXT5UPLOAD.rootPath+"js_dev/",lang:DEXT5UPLOAD.rootPath+"js/lang/",langDev:DEXT5UPLOAD.rootPath+"js_dev/lang/",css:DEXT5UPLOAD.rootPath+"css/",cssDev:DEXT5UPLOAD.rootPath+"css_dev/",config:DEXT5UPLOAD.rootPath+
"config/",plugin:DEXT5UPLOAD.rootPath+"plugin/",hybrid:DEXT5UPLOAD.rootPath+"hybrid/",installGuide:DEXT5UPLOAD.rootPath+"hybrid/installguide/installguide.html",pluginInstallGuide:DEXT5UPLOAD.rootPath+"plugin/installguide/installguide.html"};this.initVisible=!0;this.configUrl=this.webPath.config+"dext5upload.config.xml";this.skinName="blue";this.customTextColor=this.customProgressBarColor=this.customFooterColor=this.customHeaderColor="";this.customWebFileColor="#f9f5f5";this.width="500px";this.height=
"316px";this.minHeight=120;this.statusBarHeight=26;this.buttonBarHeight=34;this.minHeaderBarHeight=28;this.originHeight="316px";this.customHeaderHeight="28";this.lang="ko-kr";this.keepLang="";this.isCrossDomain=!1;this.maxOneFileSize=this.maxTotalFileCount=this.maxTotalFileSize=0;this.useLogoImage={use:"0",logoPath:this.webPath.image+"dext5_logo.png",logoVer:""};this.hideListInfo="0";this.extension={allowOrLimit:"1",extArr:[],extToString:"",mimeAccept:""};this.showHeaderBarOrg=this.showHeaderBar=
this.showStatusBar="1";this.showButtonBarEdit=[];this.showButtonBarView=[];this.uploadBorderStyle="none";this.multiFileSelect=!0;this.listViewDragAndDrop=this.listViewDbclick="1";this.mode="upload";this.subMode="";this.views="list";this.canvasWidth=88;this.canvasHeight=60;this.userRunTimeMode="html5";this.hybridDownload=this.useWS="0";this.hybridParam="";this.hybridWindowMode=this.enforceIePlugin="1";this.useInstallGuide="0";this.installFileName="DEXT5_Hybrid50.exe";this.developLang="NET";this.handlerUrl=
DEXT5UPLOAD.rootPath+"handler/dext5handler.ashx";this.downloadHandlerUrl="";this.viewerUrl=DEXT5UPLOAD.rootPath+"handler/dext5viewer.aspx";this.folderNameRule="yyyy/mm";this.fileNameRule="REALFILENAME";this.fileNameRuleEx="_";this.asyncUpload=!1;this.chunkSize=1048576;this.useServerFileSize=this.folderDetailHeader=this.folderTransfer=this.resumeDownload=this.resumeUpload=this.multiUpload="0";this.useFinishDownloadedEvent=this.useDeleteEndTimeEvent=this.useAfterDownloadEvent=this.useAfterAddEndTimeEvent=
this.useAfterAddEvent=this.useDownloadEvent=this.useUploadingCancelEvent=this.useViewOrOpenEvent=this.useDeleteEvent=this.useAddEvent="1";this.addExtIcon=[];this.isPause=!1;this.automaticConnection=this.timeout="0";this.showFolderView="1";this.handlerUrlCheck=!0;this.messageTitle="";this.useScriptEventControl="1";this.uploadCancel=!1;this.hideContextMenu=this.showCheckBox="0";this.sizeColumnWidth="100";this.unitDelimiter="\x0B";this.unitAttributeDelimiter="\f";this.trans_unitDelimiter="\x0B";this.trans_unitAttributeDelimiter=
"\f";this.useAutoHeight=this.pluginZipDownload=this.downloadMulti="0";this.removeContextItem="";this.displayFileSizeHtml4=this.disableDeleteConfirmMessage="0";this.NTLM="";this.largeFiles={markSize:0,maxCount:0,maxTotalSize:0,text:"",color:"",markBaseTotalSize:0,customMode:0};this.forceReDraw=this.cacheProtectMode="0";this.use_file_sort="1";this.auto_sort=this.sort_ascdesc=this.sort_field="0";this.showViewAlign=this.showEditAlign="left";this.imgPreView=this.completeEventResetUse=this.fileMoveContextMenu=
"0";this.imgPreViewHeight=this.imgPreViewWidth="100px";this.statusBarShowStatus=this.statusBarShowLimit="1";this.buttonBarPosition="buttom";this.userMessage={edit:"",view:""};this.useSingleSelect="0";this.disableAlertMessage={duplication:"1",deleteUnchosen:"1",downloadUnchosen:"1",openUnchosen:"1",disableDeleteConfirm:"0",fileExtensionDetect:"0"};this.directDownload=this.silentUpload=this.allowedZeroFileSize="0";this.enableThread="1";this.security={encryptParam:"0",fileExtensionDetector:"0",fileIntegrity:"0",
fileEncrypt:"0",keyValue:DEXT5UPLOAD.ReleaseVer.substring(0,11)};this.highSpeed="0";this.sizeForChunkUpload=52428800;this.uploadMethod="1";this.headerBarItem=[];this.headerBarItemWidth=[];this.headerBarItemAlign=[];this.socketWorkerCount=2;this.socketWorkerJobSize=0;this.minSingleSocketWorkerSize=20971520;this.wsHandlerUrl=DEXT5UPLOAD.rootPath+"handler/dext5plushandler.ashx";5>=this.wsHandlerUrl.indexOf("//")&&(this.wsHandlerUrl="ws:"+this.wsHandlerUrl.substring(this.wsHandlerUrl.indexOf("//")));
this.wsChunkSize=1048576;this.uploadTransferWindow={view:"standard",viewWidth:"470px",viewHeight:"170px"};this.groupId="";this.pluginInstallType="0";this.pluginInstallUrl="";this.usePluginInstallGuide="0";this.pluginInstallFileName="dext5Setup.exe";this.defaultDownloadPath="";this.transferOpenFile=this.uploadMethodHtml4="1";this.selectByClicked="0";this.aftertUpload={setFileSize:"0"};this.savePathSetting="";this.transferBackgroundStyle="filter:alpha(opacity=50); opacity:0.5; -moz-opacity:0.5; -ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=50); -khtml-opacity: 0.5; background-color:black;";
this.useDropzone="0";this.fileFilterEx=this.allowOpenExtension="";this.imageQuality={quality:1,workerCount:7};this.useZipDownload="0";this.useSetCookie="1";this.hybridMethod=0;this.allowedRealtimeDownloadAdd=1;this.hybridShowItemCount=3;this.downloadPartialSize=0;this.skipSentFile=this.autoDestroy=this.html4LimitFileSize="0";this.pluginKeepVersion=this.partialSize="";this.forceOverwrite="0";this.event={transferComplete:null}}
function Dext5Upload(b){void 0==b&&(b=DEXT5UPLOAD.util.makeGuidTagName("dext5upload_"));DEXT5UPLOAD.CUploadID=b;var c=DEXT5UPLOAD.util.isExistUploaderName(b);if(1==c)alert("uploader's name is empty. Please, check uploader's name");else{if(2==c)if("1"==DEXT5UPLOAD.config.IgnoreSameUploadName)b=DEXT5UPLOAD.util.getNewNextUploaderName(b);else{alert("uploader's name is already exist. Please, check uploader's name");return}3!=c&&(DEXT5UPLOAD.DEXTMULTIPLEID.push(b),DEXT5UPLOAD.DEXTMULTIPLE["dext5uploader_frame_"+
b]=b,DEXT5UPLOAD.DEXTHOLDER[b]=DEXT5UPLOAD.config.UploadHolder);this.frameID="dext5uploader_frame_"+b;this.ID=b;var a=new Dext5Upload_Config;if(0<DEXT5UPLOAD.config.InitServerXml.length)if(-1<DEXT5UPLOAD.config.InitServerXml.indexOf("f="))a.configUrl=DEXT5UPLOAD.config.InitServerXml+"&dext5CMD=configRequest";else{alert("Error occurred reading the Upload Settings");return}else 0<DEXT5UPLOAD.config.InitXml.length&&(0==DEXT5UPLOAD.config.InitXml.indexOf("http")?a.configUrl=DEXT5UPLOAD.config.InitXml:
a.configUrl=DEXT5UPLOAD.rootPath+"config/"+DEXT5UPLOAD.config.InitXml);a.configUrl=DEXT5UPLOAD.util.set_handlerUrl(a.configUrl);-1<a.configUrl.indexOf("?")?a.configUrl+="&t="+(new Date).getTime():a.configUrl+="?t="+(new Date).getTime();var d=null,d=0<DEXT5UPLOAD.config.InitServerXml.length?DEXT5UPLOAD.ajax.load(a.configUrl):DEXT5UPLOAD.ajax.loadXml(a.configUrl);if(null==d)alert("Error occurred reading the Upload Settings");else{"object"!=typeof d&&(0==d.indexOf("[OK]")||0==d.indexOf("[OK-NE]")?(0==
d.indexOf("[OK]")?(d=d.substring(4),d=DEXT5UPLOAD.util.base64_decode(d)):0==d.indexOf("[OK-NE]")&&(d=d.substring(7),d=DEXT5UPLOAD.util.makeDecryptReponseMessage(d)),d=d.substring(d.indexOf("<?")),d=DEXT5UPLOAD.util.stringToXML(d)):0==d.indexOf("[FAIL]")?alert("Error occurred reading the Upload Settings"):0==d.indexOf("[FAIL-NE]")?alert("Error occurred reading the Upload Settings"):(d=DEXT5UPLOAD.util.stringToXML(d))||alert("Error occurred reading the Upload Settings"));0==DEXT5UPLOAD.config.InitVisible&&
(a.initVisible=DEXT5UPLOAD.config.InitVisible);var e=DEXT5UPLOAD.util.xml.getNode(d,"license");a.productKey=""!=DEXT5UPLOAD.config.ProductKey?DEXT5UPLOAD.config.ProductKey:DEXT5UPLOAD.util.xml.getNodeValue(e,"product_key");a.licenseKey=""!=DEXT5UPLOAD.config.LicenseKey?DEXT5UPLOAD.config.LicenseKey:DEXT5UPLOAD.util.xml.getNodeValue(e,"license_key");a.licenseKeyEx=""!=DEXT5UPLOAD.config.LicenseKeyEx?DEXT5UPLOAD.config.LicenseKeyEx:DEXT5UPLOAD.util.xml.getNodeValue(e,"license_key_ex");if(""!=DEXT5UPLOAD.config.LicenseKeySE)a.licenseKeySE=
DEXT5UPLOAD.config.LicenseKeySE;else{var f=DEXT5UPLOAD.util.xml.getNode(e,"license_key");f&&f.getAttribute("se")&&"1"==f.getAttribute("se")&&(a.licenseKeySE="1")}if(""!=DEXT5UPLOAD.config.PluginKeepVersion)a.pluginKeepVersion=DEXT5UPLOAD.config.PluginKeepVersion;else{var g=DEXT5UPLOAD.util.xml.getNodeValue(e,"plugin_keep_version");""!=g&&(a.pluginKeepVersion=g)}var p=DEXT5UPLOAD.util.xml.getNode(d,"uploader_setting");if(DEXT5UPLOAD.config.HighSpeed&&""!=DEXT5UPLOAD.config.HighSpeed)a.highSpeed="1"==
DEXT5UPLOAD.config.HighSpeed?"1":"0";else{var u=DEXT5UPLOAD.util.xml.getNodeValue(p,"high_speed");a.highSpeed="1"==u?"1":"0"}if(""!=DEXT5UPLOAD.config.DevelopLangage)a.developLang=DEXT5UPLOAD.config.DevelopLangage;else{var n=DEXT5UPLOAD.util.xml.getNodeValue(p,"develop_langage");0<n.length&&(a.developLang=n)}switch(a.developLang.toUpperCase()){case "JAVA":case "JSP":a.handlerUrl=DEXT5UPLOAD.rootPath+"handler/dext5handler.jsp";a.viewerUrl=DEXT5UPLOAD.rootPath+"handler/dext5viewer.jsp";break;case "PHP":a.handlerUrl=
DEXT5UPLOAD.rootPath+"handler/dext5handler.php";a.viewerUrl=DEXT5UPLOAD.rootPath+"handler/dext5viewer.php";break;case "NONE":a.handlerUrl="";a.viewerUrl="";break;default:a.handlerUrl=DEXT5UPLOAD.rootPath+"handler/dext5handler.ashx",a.viewerUrl=DEXT5UPLOAD.rootPath+"handler/dext5viewer.aspx"}var t;""!=DEXT5UPLOAD.config.HandlerUrl?a.handlerUrl=DEXT5UPLOAD.config.HandlerUrl:(t=DEXT5UPLOAD.util.xml.getNodeValue(p,"handler_url"),0<t.length&&(a.handlerUrl=t));a.handlerUrl=DEXT5UPLOAD.util.set_handlerUrl(a.handlerUrl);
4<a.handlerUrl.length&&"http"==a.handlerUrl.substring(0,4).toLowerCase()&&a.handlerUrl.match(/:\/\/(.[^\/]+)/)[1]!=window.location.host&&(a.isCrossDomain=!0);if(""!=DEXT5UPLOAD.config.ViewerHandlerUrl)a.viewerUrl=DEXT5UPLOAD.config.ViewerHandlerUrl;else{var k=DEXT5UPLOAD.util.xml.getNodeValue(p,"viewer_handler_url");""!=k&&(a.viewerUrl=k)}a.viewerUrl=DEXT5UPLOAD.util.set_handlerUrl(a.viewerUrl);if(""!=DEXT5UPLOAD.config.DownloadHandlerUrl)a.downloadHandlerUrl=DEXT5UPLOAD.config.DownloadHandlerUrl;
else{var x=DEXT5UPLOAD.util.xml.getNodeValue(p,"download_handler_url");""!=x&&(a.downloadHandlerUrl=x)}""!=a.downloadHandlerUrl&&(a.downloadHandlerUrl=DEXT5UPLOAD.util.set_handlerUrl(a.downloadHandlerUrl));if(""!=DEXT5UPLOAD.config.WsHandlerUrl)a.wsHandlerUrl=DEXT5UPLOAD.config.WsHandlerUrl;else{var q=DEXT5UPLOAD.util.xml.getNodeValue(p,"ws_handler_url");""!=q&&(a.wsHandlerUrl=q)}if(""!=DEXT5UPLOAD.config.WsChunkSize)a.wsChunkSize=parseInt(DEXT5UPLOAD.config.WsChunkSize,10);else{var v=DEXT5UPLOAD.util.xml.getNodeValue(p,
"ws_chunk_size");""!=v&&(a.wsChunkSize=parseInt(v,10))}if(""!=DEXT5UPLOAD.config.WsWorkerCount)a.socketWorkerCount=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.WsWorkerCount);else{var l=DEXT5UPLOAD.util.xml.getNodeValue(p,"ws_worker_count");""!=l&&(a.socketWorkerCount=DEXT5UPLOAD.util.parseIntOr0(l))}""!=DEXT5UPLOAD.config.WsWorkerJobSize?a.socketWorkerJobSize=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.WsWorkerJobSize):(l=DEXT5UPLOAD.util.xml.getNode(p,"ws_worker_count"))&&l.getAttribute("job_size")&&
""!=l.getAttribute("job_size")&&(a.socketWorkerJobSize=DEXT5UPLOAD.util.parseIntOr0(l.getAttribute("job_size")));""!=DEXT5UPLOAD.config.WsMinSingleWorkerSize?a.minSingleSocketWorkerSize=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.WsMinSingleWorkerSize):(l=DEXT5UPLOAD.util.xml.getNode(p,"ws_worker_count"))&&l.getAttribute("min_single_worker_size")&&""!=l.getAttribute("min_single_worker_size")&&(a.minSingleSocketWorkerSize=DEXT5UPLOAD.util.parseIntOr0(l.getAttribute("min_single_worker_size")));
if(""!=DEXT5UPLOAD.config.FolderNameRule)a.folderNameRule=DEXT5UPLOAD.config.FolderNameRule;else{var h=DEXT5UPLOAD.util.xml.getNodeValue(p,"folder_name_rule");a.folderNameRule=h}if(""!=DEXT5UPLOAD.config.FileNameRule)a.fileNameRule=DEXT5UPLOAD.config.FileNameRule;else{var r=DEXT5UPLOAD.util.xml.getNodeValue(p,"file_name_rule");""!=r&&(a.fileNameRule=r)}if(null!=DEXT5UPLOAD.config.FileNameRuleEx){if(""==DEXT5UPLOAD.config.FileNameRuleEx||"#"==DEXT5UPLOAD.config.FileNameRuleEx||"i"==DEXT5UPLOAD.config.FileNameRuleEx||
"_"==DEXT5UPLOAD.config.FileNameRuleEx)a.fileNameRuleEx=DEXT5UPLOAD.config.FileNameRuleEx}else{var w=DEXT5UPLOAD.util.xml.getNodeValue(p,"file_name_rule_ex");if("#"==w||"i"==w||""==w)a.fileNameRuleEx=w}""!=DEXT5UPLOAD.config.AsyncUpload?"1"==DEXT5UPLOAD.config.AsyncUpload&&(a.asyncUpload=!0):"1"==DEXT5UPLOAD.util.xml.getNodeValue(p,"async_upload")&&(a.asyncUpload=!0);if(""!=DEXT5UPLOAD.config.ChunkSize){var y=DEXT5UPLOAD.util.getUnit(DEXT5UPLOAD.config.ChunkSize),A=DEXT5UPLOAD.util.getUnitSize(y),
D=parseInt(DEXT5UPLOAD.config.ChunkSize,10);a.chunkSize=D*A}else{var G=DEXT5UPLOAD.util.xml.getNodeValue(p,"chunk_size");if(""!=G){var C=DEXT5UPLOAD.util.xml.getNode(p,"chunk_size");C.getAttribute("unit")&&""!=C.getAttribute("unit")&&(A=DEXT5UPLOAD.util.getUnitSize(C.getAttribute("unit")),a.chunkSize=parseInt(G,10)*A)}}""!=DEXT5UPLOAD.config.PartialSize?(y=DEXT5UPLOAD.util.getUnit(DEXT5UPLOAD.config.PartialSize),A=DEXT5UPLOAD.util.getUnitSize(y),D=parseInt(DEXT5UPLOAD.config.PartialSize,10),a.partialSize=
D*A):(G=DEXT5UPLOAD.util.xml.getNodeValue(p,"partial_size"),""!=G&&(C=DEXT5UPLOAD.util.xml.getNode(p,"partial_size"),C.getAttribute("unit")&&""!=C.getAttribute("unit")&&(A=DEXT5UPLOAD.util.getUnitSize(C.getAttribute("unit")),a.partialSize=parseInt(G,10)*A)));"1"==DEXT5UPLOAD.util.xml.getNodeValue(p,"multi_upload")&&(a.multiUpload="1");if(""!=DEXT5UPLOAD.config.UploadMethod)a.uploadMethod=DEXT5UPLOAD.config.UploadMethod;else{var I=DEXT5UPLOAD.util.xml.getNodeValue(p,"upload_method");""!=I&&(a.uploadMethod=
I)}if(""!=DEXT5UPLOAD.config.SizeForChunkUpload)a.sizeForChunkUpload=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.SizeForChunkUpload);else{var H=DEXT5UPLOAD.util.xml.getNodeValue(p,"size_for_chunk_upload");""!=H&&(a.sizeForChunkUpload=DEXT5UPLOAD.util.parseIntOr0(H))}var m=DEXT5UPLOAD.util.xml.getNode(d,"setting");if(""!=DEXT5UPLOAD.config.SkinName)a.skinName=DEXT5UPLOAD.config.SkinName;else{var B=DEXT5UPLOAD.util.xml.getNodeValue(m,"skin_name");0<B.length&&(a.skinName=B)}var z=DEXT5UPLOAD.util.xml.getNode(m,
"skin_name");""!=DEXT5UPLOAD.config.CustomHeaderColor?a.customHeaderColor=DEXT5UPLOAD.config.CustomHeaderColor:z&&z.getAttribute("color1")&&""!=z.getAttribute("color1")&&(a.customHeaderColor=z.getAttribute("color1"));""!=DEXT5UPLOAD.config.CustomFooterColor?a.customFooterColor=DEXT5UPLOAD.config.CustomFooterColor:z&&z.getAttribute("color2")&&""!=z.getAttribute("color2")&&(a.customFooterColor=z.getAttribute("color2"));""!=DEXT5UPLOAD.config.CustomProgressBarColor?a.customProgressBarColor=DEXT5UPLOAD.config.CustomProgressBarColor:
z&&z.getAttribute("color3")&&""!=z.getAttribute("color3")&&(a.customProgressBarColor=z.getAttribute("color3"));""!=DEXT5UPLOAD.config.CustomTextColor?a.customTextColor=DEXT5UPLOAD.config.CustomTextColor:z&&z.getAttribute("text_color")&&""!=z.getAttribute("text_color")&&(a.customTextColor=z.getAttribute("text_color"));""!=DEXT5UPLOAD.config.CustomWebFileColor?a.customWebFileColor=DEXT5UPLOAD.config.CustomWebFileColor:z&&z.getAttribute("webfile_color")&&""!=z.getAttribute("webfile_color")&&(a.customWebFileColor=
z.getAttribute("webfile_color"));""!=DEXT5UPLOAD.config.CssRootPath?(a.webPath.css=DEXT5UPLOAD.config.CssRootPath,a.webPath.cssDev=DEXT5UPLOAD.config.CssRootPath):z&&z.getAttribute("css_root_path")&&""!=z.getAttribute("css_root_path")&&(a.webPath.css=z.getAttribute("css_root_path"),a.webPath.cssDev=z.getAttribute("css_root_path"));a.webPath.css.lastIndexOf("/")<a.webPath.css.length-1&&(a.webPath.css+="/");a.webPath.cssDev.lastIndexOf("/")<a.webPath.cssDev.length-1&&(a.webPath.cssDev+="/");if(""!=
DEXT5UPLOAD.config.Width)a.width=DEXT5UPLOAD.config.Width;else{var M=DEXT5UPLOAD.util.xml.getNodeValue(m,"width");0<M.length&&(a.width=M)}if(""!=DEXT5UPLOAD.config.Height)a.height=DEXT5UPLOAD.config.Height,a.originHeight=DEXT5UPLOAD.config.Height;else{var qa=DEXT5UPLOAD.util.xml.getNodeValue(m,"height");0<qa.length&&(a.height=qa,a.originHeight=qa)}var Sa=DEXT5UPLOAD.util.xml.getNodeValue(m,"lang");""!=DEXT5UPLOAD.config.Lang?a.lang=DEXT5UPLOAD.util.getUserLang(DEXT5UPLOAD.config.Lang):0<Sa.length&&
(a.lang=DEXT5UPLOAD.util.getUserLang(Sa));if(""!=DEXT5UPLOAD.config.KeepLang)a.keepLang=DEXT5UPLOAD.config.KeepLang;else{var ra=DEXT5UPLOAD.util.xml.getNode(m,"lang");ra&&ra.getAttribute("keep_lang")&&""!=ra.getAttribute("keep_lang")&&(a.keepLang=ra.getAttribute("keep_lang"))}""==a.keepLang&&(a.keepLang=a.lang);if(""!=DEXT5UPLOAD.config.MaxTotalFileSize){var y=DEXT5UPLOAD.util.getUnit(DEXT5UPLOAD.config.MaxTotalFileSize),A=DEXT5UPLOAD.util.getUnitSize(y),ha=parseInt(DEXT5UPLOAD.config.MaxTotalFileSize,
10);a.maxTotalFileSize=ha*A}else{var Ta=DEXT5UPLOAD.util.xml.getNodeValue(m,"max_total_file_size");if(0<Ta.length){var Ba=DEXT5UPLOAD.util.xml.getNode(m,"max_total_file_size");Ba.getAttribute("unit")&&""!=Ba.getAttribute("unit")&&(A=DEXT5UPLOAD.util.getUnitSize(Ba.getAttribute("unit")),a.maxTotalFileSize=parseInt(Ta,10)*A)}}if(""!=DEXT5UPLOAD.config.MaxTotalFileCount)a.maxTotalFileCount=parseInt(DEXT5UPLOAD.config.MaxTotalFileCount,10);else{var Ua=DEXT5UPLOAD.util.xml.getNodeValue(m,"max_total_file_count");
0<parseInt(Ua,10)&&(a.maxTotalFileCount=parseInt(Ua,10))}if(""!=DEXT5UPLOAD.config.MaxOneFileSize)y=DEXT5UPLOAD.util.getUnit(DEXT5UPLOAD.config.MaxOneFileSize),A=DEXT5UPLOAD.util.getUnitSize(y),ha=parseInt(DEXT5UPLOAD.config.MaxOneFileSize,10),a.maxOneFileSize=ha*A;else{var Va=DEXT5UPLOAD.util.xml.getNodeValue(m,"max_one_file_size");if(0<Va.length){var Ca=DEXT5UPLOAD.util.xml.getNode(m,"max_one_file_size");Ca.getAttribute("unit")&&""!=Ca.getAttribute("unit")&&(A=DEXT5UPLOAD.util.getUnitSize(Ca.getAttribute("unit")),
a.maxOneFileSize=parseInt(Va,10)*A)}}var ia=DEXT5UPLOAD.util.xml.getNode(m,"upload_transfer_window"),Wa=DEXT5UPLOAD.util.xml.getNodeValue(ia,"view"),R=DEXT5UPLOAD.util.xml.getNode(ia,"view");DEXT5UPLOAD.config.UploadTransferWindow.View&&""!=DEXT5UPLOAD.config.UploadTransferWindow.View?a.uploadTransferWindow.view=DEXT5UPLOAD.config.UploadTransferWindow.View:""!=Wa&&(a.uploadTransferWindow.view=Wa);DEXT5UPLOAD.config.UploadTransferWindow.ViewWidth&&""!=DEXT5UPLOAD.config.UploadTransferWindow.ViewWidth?
a.uploadTransferWindow.viewWidth=DEXT5UPLOAD.config.UploadTransferWindow.ViewWidth:R&&R.getAttribute("width")&&""!=R.getAttribute("width")&&(a.uploadTransferWindow.viewWidth=R.getAttribute("width"),250>=DEXT5UPLOAD.util.parseIntOr0(a.uploadTransferWindow.viewWidth)&&(a.uploadTransferWindow.viewWidth="250px"));DEXT5UPLOAD.config.UploadTransferWindow.viewHeight&&""!=DEXT5UPLOAD.config.UploadTransferWindow.viewHeight?a.uploadTransferWindow.viewHeight=DEXT5UPLOAD.config.UploadTransferWindow.viewHeight:
R&&R.getAttribute("height")&&""!=R.getAttribute("height")&&(a.uploadTransferWindow.viewHeight=R.getAttribute("height"),150>=DEXT5UPLOAD.util.parseIntOr0(a.uploadTransferWindow.viewHeight)&&(a.uploadTransferWindow.viewHeight="150px"));var Xa=DEXT5UPLOAD.util.xml.getNodeValue(ia,"use_logo_image"),Y=DEXT5UPLOAD.util.xml.getNode(ia,"use_logo_image");if("1"==Xa)a.useLogoImage.use="1",Y.getAttribute("logo_path")&&""!=Y.getAttribute("logo_path")&&(a.useLogoImage.logoPath=Y.getAttribute("logo_path")),Y.getAttribute("logo_ver")&&
""!=Y.getAttribute("logo_ver")&&(a.useLogoImage.logoVer=Y.getAttribute("logo_ver"));else if("0"==Xa)a.useLogoImage.use="0";else if("1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_logo_image")){a.useLogoImage.use="1";var Z=DEXT5UPLOAD.util.xml.getNode(m,"use_logo_image");Z.getAttribute("logo_path")&&""!=Z.getAttribute("logo_path")&&(a.useLogoImage.logoPath=Z.getAttribute("logo_path"));Z.getAttribute("logo_ver")&&""!=Z.getAttribute("logo_ver")&&(a.useLogoImage.logoVer=Z.getAttribute("logo_ver"))}var Da=
DEXT5UPLOAD.util.xml.getNodeValue(ia,"silent_upload");if(""!=Da)""!=DEXT5UPLOAD.config.SilentUpload?a.silentUpload=DEXT5UPLOAD.config.SilentUpload:""!=Da&&(a.silentUpload=Da);else if(""!=DEXT5UPLOAD.config.SilentUpload)a.silentUpload=DEXT5UPLOAD.config.SilentUpload;else{var Ya=DEXT5UPLOAD.util.xml.getNodeValue(m,"silent_upload");""!=Ya&&(a.silentUpload=Ya)}""!=DEXT5UPLOAD.config.HideListInfo?a.hideListInfo=DEXT5UPLOAD.config.HideListInfo:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"hide_list_info")&&
(a.hideListInfo="1");if(""!=DEXT5UPLOAD.config.ExtensionAllowOrLimit.toString()&&""!=DEXT5UPLOAD.config.ExtensionArr){if("0"==DEXT5UPLOAD.config.ExtensionAllowOrLimit)a.extension.allowOrLimit="0";else{var Ea=DEXT5UPLOAD.util.getMimeFilter(DEXT5UPLOAD.config.ExtensionArr.toLowerCase());a.extension.mimeAccept=Ea}-1<DEXT5UPLOAD.config.ExtensionArr.indexOf("|")?a.extension.extToString=DEXT5UPLOAD.config.ExtensionArr:(a.extension.extArr=DEXT5UPLOAD.config.ExtensionArr.toLowerCase().split(","),a.extension.extToString=
a.extension.extArr.toString())}else{var ja=DEXT5UPLOAD.util.xml.getNodeValue(m,"extension").toLowerCase();if(""!=ja){var Za=DEXT5UPLOAD.util.xml.getNode(m,"extension");Za.getAttribute("allow_or_limit")&&"0"==Za.getAttribute("allow_or_limit")?a.extension.allowOrLimit="0":(Ea=DEXT5UPLOAD.util.getMimeFilter(ja),a.extension.mimeAccept=Ea);-1<ja.indexOf("|")?a.extension.extToString=ja:(a.extension.extArr=ja.split(","),a.extension.extToString=a.extension.extArr.toString())}}if(""!=DEXT5UPLOAD.config.BorderStyle)a.uploadBorderStyle=
DEXT5UPLOAD.config.BorderStyle;else{var $a=DEXT5UPLOAD.util.xml.getNodeValue(m,"border_style");""!=$a&&(a.uploadBorderStyle=$a)}""!=DEXT5UPLOAD.config.MultiFileSelect?(a.multiFileSelect=DEXT5UPLOAD.config.MultiFileSelect,"0"==a.multiFileSelect&&(a.multiFileSelect=!1)):"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"multi_file_select")&&(a.multiFileSelect=!1);if(""!=DEXT5UPLOAD.config.Mode)"form"==DEXT5UPLOAD.config.Mode?(a.subMode=DEXT5UPLOAD.config.Mode,a.mode="upload"):a.mode=DEXT5UPLOAD.config.Mode;
else{var S=DEXT5UPLOAD.util.xml.getNodeValue(m,"mode");""!=S.length&&("form"==S?(a.subMode=S,a.mode="upload"):a.mode=S)}"form"==a.subMode?a.listViewDbclick="0":""!=DEXT5UPLOAD.config.ListViewDbclick?a.listViewDbclick=DEXT5UPLOAD.config.ListViewDbclick:"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"list_view_dbclick")&&(a.listViewDbclick="0");"form"==a.subMode?a.listViewDragAndDrop="0":""!=DEXT5UPLOAD.config.ListViewDragAndDrop?a.listViewDragAndDrop=DEXT5UPLOAD.config.ListViewDragAndDrop:"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,
"list_view_drag_and_drop")&&(a.listViewDragAndDrop="0");if(""!=DEXT5UPLOAD.config.Runtimes)a.userRunTimeMode=DEXT5UPLOAD.config.Runtimes;else{var aa=DEXT5UPLOAD.util.xml.getNodeValue(m,"runtimes");""!=aa&&(a.userRunTimeMode=aa)}""!=DEXT5UPLOAD.config.RunTimes&&(a.userRunTimeMode=DEXT5UPLOAD.config.RunTimes);""!=DEXT5UPLOAD.config.UseWS?a.useWS=DEXT5UPLOAD.config.UseWS:(aa=DEXT5UPLOAD.util.xml.getNode(m,"runtimes"))&&aa.getAttribute("use_ws")&&""!=aa.getAttribute("use_ws")&&(a.useWS=aa.getAttribute("use_ws"),
"1"!=a.useWS&&(a.useWS="0"));"html5plus"==a.userRunTimeMode&&(a.useWS="1");if(""!=DEXT5UPLOAD.config.HybridDownload)"0"!=DEXT5UPLOAD.config.HybridDownload&&"windows"==DEXT5UPLOAD.UserAgent.os.name.toLowerCase()&&0==DEXT5UPLOAD.browser.mobile?a.hybridDownload=DEXT5UPLOAD.config.HybridDownload:a.hybridDownload="0";else{var sa=DEXT5UPLOAD.util.xml.getNode(m,"runtimes");sa&&sa.getAttribute("hybrid_download")&&"0"!=sa.getAttribute("hybrid_download")&&"windows"==DEXT5UPLOAD.UserAgent.os.name.toLowerCase()&&
0==DEXT5UPLOAD.browser.mobile&&(a.hybridDownload=sa.getAttribute("hybrid_download"))}var J=DEXT5UPLOAD.util.xml.getNode(m,"hybrid");""!=DEXT5UPLOAD.config.HybridWindowMode?a.hybridWindowMode=DEXT5UPLOAD.config.HybridWindowMode:J&&"0"==DEXT5UPLOAD.util.xml.getNodeValue(J,"window_mode")&&(a.hybridWindowMode="0");if(""!=DEXT5UPLOAD.config.UseInstallGuide)a.useInstallGuide=DEXT5UPLOAD.config.UseInstallGuide;else if(J){var ab=DEXT5UPLOAD.util.xml.getNodeValue(J,"use_install_guide");""!=ab&&(a.useInstallGuide=
ab)}if(""!=DEXT5UPLOAD.config.HybridUseSetCookie)a.useSetCookie=DEXT5UPLOAD.config.HybridUseSetCookie;else if(J){var bb=DEXT5UPLOAD.util.xml.getNodeValue(J,"use_set_cookie");""!=bb&&(a.useSetCookie=bb)}if(""!=DEXT5UPLOAD.config.HybridMethod)"1"==DEXT5UPLOAD.config.HybridMethod&&(a.hybridMethod=1);else if(J){var cb=DEXT5UPLOAD.util.xml.getNodeValue(J,"method");""!=cb&&"1"==cb&&(a.hybridMethod=1)}if(""!=DEXT5UPLOAD.config.AllowedRealtimeDownloadAdd)a.allowedRealtimeDownloadAdd=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.AllowedRealtimeDownloadAdd);
else if(J){var db=DEXT5UPLOAD.util.xml.getNodeValue(J,"allowed_realtime_download_add");""!=db&&"0"==db&&(a.allowedRealtimeDownloadAdd=0)}if(""!=DEXT5UPLOAD.config.HybridShowItemCount)a.hybridShowItemCount=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.HybridShowItemCount);else if(J){var eb=DEXT5UPLOAD.util.xml.getNodeValue(J,"show_item_count");""!=eb&&(a.hybridShowItemCount=DEXT5UPLOAD.util.parseIntOr0(eb))}3>a.hybridShowItemCount?a.hybridShowItemCount=3:15<a.hybridShowItemCount&&(a.hybridShowItemCount=
15);DEXT5UPLOAD.browser.safari&&(a.useInstallGuide=0);a.userRunTimeMode=DEXT5UPLOAD.util.GetUserRunTimeUpload(a.userRunTimeMode,a.useWS);if("html5"==a.userRunTimeMode&&DEXT5UPLOAD.browser.WorkerSupported){var ka="",O=DEXT5UPLOAD.ReleaseVer;"1"==a.cacheProtectMode&&(O=(new Date).getTime());ka=DEXT5UPLOAD.isRelease?a.webPath.js+"dext5upload.uploadchunk.min.js?ver="+O:a.webPath.jsDev+"dext5upload.uploadchunk.js?ver="+O;try{(new Worker(ka)).terminate()}catch(hc){DEXT5UPLOAD.browser.WorkerSupported=!1}}"ieplugin"!=
a.userRunTimeMode&&"0"!=a.hybridDownload&&DEXT5UPLOAD.browser.ie&&"html4"==a.userRunTimeMode&&(a.userRunTimeMode="ieplugin");if("ieplugin"!=a.userRunTimeMode&&""!=a.extension.extToString&&-1<a.extension.extToString.indexOf("|"))if(-1<a.extension.extToString.indexOf("*.*"))a.extension.extArr=[],a.extension.mimeAccept="";else{var Wb=DEXT5UPLOAD.util.getExtStringFromExtEx(a.extension.extToString);a.extension.extArr=Wb.split(",");"1"==a.extension.allowOrLimit&&(a.extension.mimeAccept=DEXT5UPLOAD.util.getMimeFilter(a.extension.extArr.toString()))}if(DEXT5UPLOAD.browser.WorkerSupported)if(""!=
DEXT5UPLOAD.config.EnableThread)a.enableThread="0"==DEXT5UPLOAD.config.EnableThread?"0":"1";else{var Xb=DEXT5UPLOAD.util.xml.getNodeValue(p,"enable_thread");a.enableThread="0"==Xb?"0":"1"}"0"==a.enableThread&&(DEXT5UPLOAD.browser.WorkerSupported=!1);if("html5plus"==a.userRunTimeMode&&DEXT5UPLOAD.browser.WorkerSupported){ka="";O=DEXT5UPLOAD.ReleaseVer;"1"==a.cacheProtectMode&&(O=(new Date).getTime());ka=DEXT5UPLOAD.isRelease?a.webPath.js+"dext5upload.uploadplus.min.js?ver="+O:a.webPath.jsDev+"dext5upload.uploadplus.js?ver="+
O;try{(new Worker(ka)).terminate()}catch(ic){DEXT5UPLOAD.browser.WorkerSupported=!1}}if(1==DEXT5UPLOAD.browser.WorkerSupported){var la=new Worker(DEXT5UPLOAD.isRelease?a.webPath.js+"dext5upload.processfile.min.js?ver="+O:a.webPath.jsDev+"dext5upload.processfile.js?ver="+O);la.onmessage=function(a){a=a.data;switch(a.type){case "check_formdata":DEXT5UPLOAD.browser.WorkerSupported=a.isFormDataSupport,la.terminate()}};la.onerror=function(a){la.terminate()};la.postMessage({type:"check_formdata"})}var fb=
DEXT5UPLOAD.util.xml.getNodeValue(m,"plugin_version");""!=fb&&(a.plugin_version=fb);a.mode=a.mode.toLowerCase();"edit"==a.mode&&(a.mode="upload");var Fa,T=DEXT5UPLOAD.util.xml.getNode(m,"show_status_bar");""!=DEXT5UPLOAD.config.StatusBar?a.showStatusBar=DEXT5UPLOAD.config.StatusBar:(Fa=DEXT5UPLOAD.util.xml.getNodeValue(m,"show_status_bar"),""!=Fa&&(a.showStatusBar=Fa));"1"==a.showStatusBar&&("0"==DEXT5UPLOAD.config.StatusBarShowLimit?a.statusBarShowLimit=DEXT5UPLOAD.config.StatusBarShowLimit:T&&T.getAttribute("show_limit")&&
""!=T.getAttribute("show_limit")&&(a.statusBarShowLimit=T.getAttribute("show_limit")),"0"==DEXT5UPLOAD.config.StatusBarShowStatus?a.statusBarShowStatus=DEXT5UPLOAD.config.StatusBarShowStatus:T&&T.getAttribute("show_status")&&""!=T.getAttribute("show_status")&&(a.statusBarShowStatus=T.getAttribute("show_status")));if(""!=DEXT5UPLOAD.config.HeaderBar)a.showHeaderBar=DEXT5UPLOAD.config.HeaderBar;else{"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"show_header_bar")&&(a.showHeaderBar="0");var ta=DEXT5UPLOAD.util.xml.getNode(m,
"show_header_bar");ta&&ta.getAttribute("header_height")&&""!=ta.getAttribute("header_height")&&(a.customHeaderHeight=ta.getAttribute("header_height"),DEXT5UPLOAD.util.parseIntOr0(a.customHeaderHeight)<a.minHeaderBarHeight&&(a.customHeaderHeight="28px"))}a.showHeaderBarOrg=a.showHeaderBar;if(""!=DEXT5UPLOAD.config.ButtonBar||""!=DEXT5UPLOAD.config.ButtonBarEdit){var ua="";""!=DEXT5UPLOAD.config.ButtonBar&&(ua=DEXT5UPLOAD.config.ButtonBar);""!=DEXT5UPLOAD.config.ButtonBarEdit&&(ua=DEXT5UPLOAD.config.ButtonBarEdit);
a.showButtonBarEdit="0"==ua?"":ua.split(",")}else{var ba=DEXT5UPLOAD.util.xml.getNodeValue(m,"show_button_bar_edit");""!=ba&&(a.showButtonBarEdit=ba.split(","))}""!=DEXT5UPLOAD.config.ShowEditAlign?a.showEditAlign=DEXT5UPLOAD.config.ShowEditAlign:(ba=DEXT5UPLOAD.util.xml.getNode(m,"show_button_bar_edit"))&&ba.getAttribute("align")&&""!=ba.getAttribute("align")&&(a.showEditAlign=ba.getAttribute("align"));if(""!=DEXT5UPLOAD.config.ButtonBarView)a.showButtonBarView="0"==DEXT5UPLOAD.config.ButtonBarView?
"":DEXT5UPLOAD.config.ButtonBarView.split(",");else{var ca=DEXT5UPLOAD.util.xml.getNodeValue(m,"show_button_bar_view");""!=ca&&(a.showButtonBarView=ca.split(","))}""!=DEXT5UPLOAD.config.ShowViewAlign?a.showViewAlign=DEXT5UPLOAD.config.ShowViewAlign:(ca=DEXT5UPLOAD.util.xml.getNode(m,"show_button_bar_view"))&&ca.getAttribute("align")&&""!=ca.getAttribute("align")&&(a.showViewAlign=ca.getAttribute("align"));if(""!=DEXT5UPLOAD.config.ButtonBarPosition)a.buttonBarPosition=DEXT5UPLOAD.config.ButtonBarPosition;
else{var gb=DEXT5UPLOAD.util.xml.getNodeValue(m,"button_bar_position");""!=gb&&(a.buttonBarPosition=gb)}var U=DEXT5UPLOAD.util.xml.getNode(m,"resume_mode");""!=DEXT5UPLOAD.config.ResumeUpload?a.resumeUpload=DEXT5UPLOAD.config.ResumeUpload:U&&U.getAttribute("upload")&&""!=U.getAttribute("upload")&&(a.resumeUpload=U.getAttribute("upload"));""!=DEXT5UPLOAD.config.ResumeDownload?a.resumeDownload=DEXT5UPLOAD.config.ResumeDownload:U&&U.getAttribute("download")&&""!=U.getAttribute("download")&&(a.resumeDownload=
U.getAttribute("download"));if(""!=DEXT5UPLOAD.config.FolderTransfer)a.folderTransfer=DEXT5UPLOAD.config.FolderTransfer;else{var ma=DEXT5UPLOAD.util.xml.getNodeValue(m,"folder_transfer");""!=ma&&(a.folderTransfer=ma)}"0"!=a.folderTransfer&&"ieplugin"==a.userRunTimeMode&&(""!=DEXT5UPLOAD.config.FolderDetailHeader?a.folderDetailHeader=DEXT5UPLOAD.config.FolderDetailHeader:(ma=DEXT5UPLOAD.util.xml.getNode(m,"folder_transfer"))&&ma.getAttribute("folder_detail_header")&&(a.folderDetailHeader=ma.getAttribute("folder_detail_header")));
if(""!=DEXT5UPLOAD.config.UseServerFileSize)a.useServerFileSize=DEXT5UPLOAD.config.UseServerFileSize;else{var hb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_server_file_size");""!=hb&&(a.useServerFileSize=hb)}if(""!=DEXT5UPLOAD.config.UseAddEvent)a.useAddEvent=DEXT5UPLOAD.config.UseAddEvent;else{var ib=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_add_event");""!=ib&&(a.useAddEvent=ib)}if(""!=DEXT5UPLOAD.config.UseDeleteEvent)a.useDeleteEvent=DEXT5UPLOAD.config.UseDeleteEvent;else{var jb=DEXT5UPLOAD.util.xml.getNodeValue(m,
"use_delete_event");""!=jb&&(a.useDeleteEvent=jb)}if(""!=DEXT5UPLOAD.config.UseViewOrOpenEvent)a.useViewOrOpenEvent=DEXT5UPLOAD.config.UseViewOrOpenEvent;else{var kb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_view_or_open_event");""!=kb&&(a.useViewOrOpenEvent=kb)}if(""!=DEXT5UPLOAD.config.UseUploadingCancelEvent)a.useUploadingCancelEvent=DEXT5UPLOAD.config.UseUploadingCancelEvent;else{var lb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_uploading_cancel_event");""!=lb&&(a.useUploadingCancelEvent=lb)}if(""!=
DEXT5UPLOAD.config.UseDownloadEvent)a.useDownloadEvent=DEXT5UPLOAD.config.UseDownloadEvent;else{var mb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_download_event");""!=mb&&(a.useDownloadEvent=mb)}if(""!=DEXT5UPLOAD.config.UseAfterAddEvent)a.useAfterAddEvent=DEXT5UPLOAD.config.UseAfterAddEvent;else{var nb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_after_add_event");""!=nb&&(a.useAfterAddEvent=nb)}if(""!=DEXT5UPLOAD.config.UseAfterAddEndTimeEvent)a.useAfterAddEndTimeEvent=DEXT5UPLOAD.config.UseAfterAddEndTimeEvent;
else{var ob=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_after_add_end_time_event");""!=ob&&(a.useAfterAddEndTimeEvent=ob)}if(""!=DEXT5UPLOAD.config.UseAfterDownloadEvent)a.useAfterDownloadEvent=DEXT5UPLOAD.config.UseAfterDownloadEvent;else{var pb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_after_download_event");""!=pb&&(a.useAfterDownloadEvent=pb)}if(""!=DEXT5UPLOAD.config.UseDeleteEndTimeEvent)a.useDeleteEndTimeEvent=DEXT5UPLOAD.config.UseDeleteEndTimeEvent;else{var qb=DEXT5UPLOAD.util.xml.getNodeValue(m,
"use_delete_end_time_event");""!=qb&&(a.useDeleteEndTimeEvent=qb)}if(""!=DEXT5UPLOAD.config.UseFinishDownloadedEvent)a.useFinishDownloadedEvent=DEXT5UPLOAD.config.UseFinishDownloadedEvent;else{var rb=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_finish_downloaded_event");""!=rb&&(a.useFinishDownloadedEvent=rb)}if(""!=DEXT5UPLOAD.config.Timeout)a.timeout=DEXT5UPLOAD.config.Timeout;else{var sb=DEXT5UPLOAD.util.xml.getNodeValue(m,"timeout");""!=sb&&(a.timeout=sb)}if(""!=DEXT5UPLOAD.config.AutomaticConnection)a.automaticConnection=
DEXT5UPLOAD.config.automaticConnection;else{var tb=DEXT5UPLOAD.util.xml.getNodeValue(m,"automatic_connection");""!=tb&&(a.automaticConnection=tb)}""!=DEXT5UPLOAD.config.ShowFolderView?a.showFolderView=DEXT5UPLOAD.config.ShowFolderView:"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"show_folder_view")&&(a.showFolderView="0");if(""!=DEXT5UPLOAD.config.MessageTitle)a.messageTitle=DEXT5UPLOAD.config.MessageTitle;else{var ub=DEXT5UPLOAD.util.xml.getNodeValue(m,"message_title");""!=ub&&(a.messageTitle=ub)}""!=
DEXT5UPLOAD.config.UseScriptEventControl?a.useScriptEventControl=DEXT5UPLOAD.config.UseScriptEventControl:"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_script_event_control")&&(a.useScriptEventControl="0");for(var vb=DEXT5UPLOAD.util.xml.getNode(d,"add_ext_icon"),Yb=DEXT5UPLOAD.util.xml.count(vb,"icon"),N=0;N<Yb;N++){var Zb=DEXT5UPLOAD.util.xml.getNodeValueIdx(vb,"icon",N);a.addExtIcon[N]=Zb}""!=DEXT5UPLOAD.config.SkinName?a.skinName=DEXT5UPLOAD.config.SkinName:(B=DEXT5UPLOAD.util.xml.getNodeValue(m,
"skin_name"),0<B.length&&(a.skinName=B));if(""!=DEXT5UPLOAD.config.ShowCheckBox)a.showCheckBox=DEXT5UPLOAD.config.ShowCheckBox;else{var wb=DEXT5UPLOAD.util.xml.getNodeValue(m,"show_checkbox");""!=wb&&(a.showCheckBox=wb)}if(""!=DEXT5UPLOAD.config.HideContextMenu)a.hideContextMenu=DEXT5UPLOAD.config.HideContextMenu;else{var xb=DEXT5UPLOAD.util.xml.getNodeValue(m,"hide_context_menu");""!=xb&&(a.hideContextMenu=xb)}"0"==DEXT5UPLOAD.config.SizeColumnWidth?a.sizeColumnWidth=DEXT5UPLOAD.config.SizeColumnWidth:
"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_size_column")&&(a.sizeColumnWidth="0");var V=DEXT5UPLOAD.util.xml.getNode(m,"file_delimiter");""!=DEXT5UPLOAD.config.UnitDelimiter?a.unitDelimiter=String.fromCharCode(DEXT5UPLOAD.config.UnitDelimiter):V&&V.getAttribute("unit_delimiter")&&""!=V.getAttribute("unit_delimiter")&&(a.unitDelimiter=String.fromCharCode(V.getAttribute("unit_delimiter")));""!=DEXT5UPLOAD.config.UnitDelimiter?a.unitAttributeDelimiter=String.fromCharCode(DEXT5UPLOAD.config.UnitAttributeDelimiter):
V&&V.getAttribute("unit_attribute_delimiter")&&""!=V.getAttribute("unit_attribute_delimiter")&&(a.unitAttributeDelimiter=String.fromCharCode(V.getAttribute("unit_attribute_delimiter")));""!=DEXT5UPLOAD.config.UseAutoHeight?a.useAutoHeight=DEXT5UPLOAD.config.UseAutoHeight:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_auto_height")&&(a.useAutoHeight="1");if(""!=DEXT5UPLOAD.config.RemoveContextItem)a.removeContextItem=DEXT5UPLOAD.config.RemoveContextItem;else{var yb=DEXT5UPLOAD.util.xml.getNodeValue(m,
"remove_context_item");""!=yb&&(a.removeContextItem=yb)}"form"==a.subMode&&(a.hideContextMenu="1");""!=DEXT5UPLOAD.config.DisplayFileSizeHtml4?a.displayFileSizeHtml4=DEXT5UPLOAD.config.DisplayFileSizeHtml4:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"display_file_size_html4")&&(a.displayFileSizeHtml4="1");""!=DEXT5UPLOAD.config.AllowedZeroFileSize?a.allowedZeroFileSize=DEXT5UPLOAD.config.AllowedZeroFileSize:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"allowed_zero_file_size")&&(a.allowedZeroFileSize="1");
""!=DEXT5UPLOAD.config.UseDropzone?a.useDropzone=DEXT5UPLOAD.config.UseDropzone:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_dropzone")&&(a.useDropzone="1");""!=DEXT5UPLOAD.config.UseZipDownload?a.useZipDownload=DEXT5UPLOAD.config.UseZipDownload:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"use_zip_download")&&(a.useZipDownload="1");if(""!=DEXT5UPLOAD.config.AllowOpenExtension)a.allowOpenExtension=DEXT5UPLOAD.config.AllowOpenExtension;else{var Ga=DEXT5UPLOAD.util.xml.getNodeValue(m,"allow_open_extension");
Ga&&""!=Ga&&(a.allowOpenExtension=Ga)}""!=a.allowOpenExtension&&"1"==a.hybridDownload&&(a.allowOpenExtension="");if(""!=DEXT5UPLOAD.config.FileFilterEx)a.fileFilterEx=DEXT5UPLOAD.config.FileFilterEx;else{var Ha=DEXT5UPLOAD.util.xml.getNodeValue(m,"file_filter_ex");Ha&&""!=Ha&&(a.fileFilterEx=Ha)}if(""!=DEXT5UPLOAD.config.DownloadMulti)a.downloadMulti=DEXT5UPLOAD.config.DownloadMulti;else{var zb=DEXT5UPLOAD.util.xml.getNodeValue(p,"download_multi");0<zb.length&&(a.downloadMulti=zb)}if(""!=DEXT5UPLOAD.config.PluginZipDownload)a.pluginZipDownload=
DEXT5UPLOAD.config.PluginZipDownload;else{var va=DEXT5UPLOAD.util.xml.getNode(p,"download_multi");va&&va.getAttribute("plugin_zip")&&0<va.getAttribute("plugin_zip").length&&(a.pluginZipDownload=va)}""!=DEXT5UPLOAD.config.NTLM&&-1<DEXT5UPLOAD.config.NTLM.indexOf("Basic")&&(a.NTLM=DEXT5UPLOAD.config.NTLM);if(""!=DEXT5UPLOAD.config.LargeFiles){var Ab=DEXT5UPLOAD.config.LargeFiles.Size?DEXT5UPLOAD.config.LargeFiles.Size:"0",y=DEXT5UPLOAD.util.getUnit(Ab),A=DEXT5UPLOAD.util.getUnitSize(y),D=parseInt(Ab,
10);a.largeFiles.markSize=D*A;var Bb=DEXT5UPLOAD.config.LargeFiles.MaxTotalSize?DEXT5UPLOAD.config.LargeFiles.MaxTotalSize:"0",y=DEXT5UPLOAD.util.getUnit(Bb),A=DEXT5UPLOAD.util.getUnitSize(y),D=parseInt(Bb,10);a.largeFiles.maxTotalSize=D*A;a.largeFiles.maxCount=DEXT5UPLOAD.config.LargeFiles.MaxCount?DEXT5UPLOAD.config.LargeFiles.MaxCount:0;a.largeFiles.text=DEXT5UPLOAD.config.LargeFiles.Text?DEXT5UPLOAD.config.LargeFiles.Text:"";a.largeFiles.color=DEXT5UPLOAD.config.LargeFiles.Color?DEXT5UPLOAD.config.LargeFiles.Color:
"";var Cb=DEXT5UPLOAD.config.LargeFiles.BaseTotalSize?DEXT5UPLOAD.config.LargeFiles.BaseTotalSize:"0",y=DEXT5UPLOAD.util.getUnit(Cb),A=DEXT5UPLOAD.util.getUnitSize(y),D=parseInt(Cb,10);a.largeFiles.markBaseTotalSize=D*A;a.largeFiles.customMode=DEXT5UPLOAD.config.LargeFiles.CustomMode?DEXT5UPLOAD.config.LargeFiles.CustomMode:"0"}else{var Ia=DEXT5UPLOAD.util.xml.getNodeValue(m,"large_files"),E=DEXT5UPLOAD.util.xml.getNode(m,"large_files");if(0<Ia.length&&0<Ia||E&&E.getAttribute("base_total_size")&&
0<E.getAttribute("base_total_size").length||E&&E.getAttribute("custom_mode")&&0<E.getAttribute("custom_mode").length){if(E.getAttribute("unit")&&""!=E.getAttribute("unit")){var A=DEXT5UPLOAD.util.getUnitSize(E.getAttribute("unit")),$b=DEXT5UPLOAD.util.parseIntOr0(Ia,10)*A;a.largeFiles.markSize=$b}E.getAttribute("max_count")&&""!=E.getAttribute("max_count")&&(a.largeFiles.maxCount=DEXT5UPLOAD.util.parseIntOr0(E.getAttribute("max_count")));if(E.getAttribute("max_total_size")&&""!=E.getAttribute("max_total_size")){var y=
E.getAttribute("unit")?E.getAttribute("unit"):"b",A=DEXT5UPLOAD.util.getUnitSize(y),ac=DEXT5UPLOAD.util.parseIntOr0(E.getAttribute("max_total_size"))*A;a.largeFiles.maxTotalSize=ac}E.getAttribute("color")&&""!=E.getAttribute("color")&&(a.largeFiles.color=E.getAttribute("color"));E.getAttribute("text")&&""!=E.getAttribute("text")&&(a.largeFiles.text=E.getAttribute("text"));E.getAttribute("base_total_size")&&""!=E.getAttribute("base_total_size")&&(A=1,E.getAttribute("unit")&&""!=E.getAttribute("unit")&&
(A=DEXT5UPLOAD.util.getUnitSize(E.getAttribute("unit"))),a.largeFiles.markBaseTotalSize=DEXT5UPLOAD.util.parseIntOr0(E.getAttribute("base_total_size"))*A);E.getAttribute("custom_mode")&&"1"==E.getAttribute("custom_mode")&&(a.largeFiles.customMode="1")}}""!=DEXT5UPLOAD.config.Views?a.views=DEXT5UPLOAD.config.Views:(S=DEXT5UPLOAD.util.xml.getNode(m,"mode"))&&S.getAttribute("views")&&""!=S.getAttribute("views")&&(a.views=S.getAttribute("views"));if(""!=DEXT5UPLOAD.config.AutoDestroy)a.autoDestroy=DEXT5UPLOAD.config.AutoDestroy;
else{var Db=DEXT5UPLOAD.util.xml.getNodeValue(m,"auto_destroy");""!=Db&&(a.autoDestroy=Db)}if(""!=DEXT5UPLOAD.config.UploadMethodHtml4)a.uploadMethodHtml4=DEXT5UPLOAD.config.UploadMethodHtml4;else{var Eb=DEXT5UPLOAD.util.xml.getNodeValue(p,"upload_method_html4");"0"==Eb&&(a.uploadMethodHtml4=Eb)}if("html4"==a.userRunTimeMode&&"1"==a.uploadMethodHtml4)if(""!=DEXT5UPLOAD.config.Html4LimitFileSize)a.html4LimitFileSize=DEXT5UPLOAD.config.Html4LimitFileSize;else{var wa=DEXT5UPLOAD.util.xml.getNode(p,"upload_method_html4");
wa&&wa.getAttribute("html4_limit_file_size")&&""!=wa.getAttribute("html4_limit_file_size")&&(a.html4LimitFileSize=wa.getAttribute("html4_limit_file_size"))}"html4"==a.userRunTimeMode&&"0"==a.uploadMethodHtml4&&(a.imgPreView="0",a.views="list");"thumbs"==a.views&&(a.largeFiles={markSize:0,maxCount:0,maxTotalSize:0,text:"",color:""},a.showHeaderBar=0);""!=DEXT5UPLOAD.config.CacheProtectMode?a.cacheProtectMode=DEXT5UPLOAD.config.CacheProtectMode:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"cache_protect_mode")&&
(a.cacheProtectMode="1");"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"force_re_draw")&&(a.forceReDraw="1");var K=DEXT5UPLOAD.util.xml.getNode(m,"file_sort");if("form"==a.subMode)a.use_file_sort="0";else if("0"==DEXT5UPLOAD.config.UseFileSort||"1"==DEXT5UPLOAD.config.UseFileSort){if(a.use_file_sort=DEXT5UPLOAD.config.UseFileSort,"1"==DEXT5UPLOAD.config.FileSortField&&(a.sort_field=DEXT5UPLOAD.config.FileSortField),"1"==DEXT5UPLOAD.config.FileSortAscDesc&&(a.sort_ascdesc=DEXT5UPLOAD.config.FileSortAscDesc),
"1"==DEXT5UPLOAD.config.AutoSort||"2"==DEXT5UPLOAD.config.AutoSort)a.auto_sort=DEXT5UPLOAD.config.AutoSort}else"0"==DEXT5UPLOAD.util.xml.getNodeValue(m,"file_sort")&&(a.use_file_sort="0"),DEXT5UPLOAD.util.xml.getNode(K,"file_sort"),K&&K.getAttribute("sort_field")&&"1"==K.getAttribute("sort_field")&&(a.sort_field="1"),K&&K.getAttribute("sort_ascdesc")&&"1"==K.getAttribute("sort_ascdesc")&&(a.sort_ascdesc="1"),K&&K.getAttribute("auto_sort")&&("1"==K.getAttribute("auto_sort")||"2"==K.getAttribute("auto_sort"))&&
(a.auto_sort=K.getAttribute("auto_sort"));"1"==DEXT5UPLOAD.config.FileMoveContextMenu?a.fileMoveContextMenu=DEXT5UPLOAD.config.FileMoveContextMenu:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"file_move_context_menu")&&(a.fileMoveContextMenu="1");"1"==DEXT5UPLOAD.config.CompleteEventResetUse?a.completeEventResetUse=DEXT5UPLOAD.config.CompleteEventResetUse:"1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"complete_event_reset_use")&&(a.completeEventResetUse="1");"thumbs"==a.views&&(a.fileMoveContextMenu="0");
var Fb,W=DEXT5UPLOAD.util.xml.getNode(m,"img_preview");""!=DEXT5UPLOAD.config.ImgPreView?a.imgPreView=DEXT5UPLOAD.config.ImgPreView:(Fb=DEXT5UPLOAD.util.xml.getNodeValue(m,"img_preview"),"1"==Fb&&(a.imgPreView="1"));1==DEXT5UPLOAD.browser.mobile&&(a.imgPreView="0");"1"==a.imgPreView&&(""!=DEXT5UPLOAD.config.ImgPreViewWidth?(a.imgPreViewWidth=DEXT5UPLOAD.config.ImgPreViewWidth,100>=DEXT5UPLOAD.util.parseIntOr0(a.imgPreViewWidth)&&(a.imgPreViewWidth="100px")):W&&W.getAttribute("width")&&""!=W.getAttribute("width")&&
(a.imgPreViewWidth=W.getAttribute("width"),100>=DEXT5UPLOAD.util.parseIntOr0(a.imgPreViewWidth)&&(a.imgPreViewWidth="100px")),""!=DEXT5UPLOAD.config.ImgPreViewHeight?(a.imgPreViewHeight=DEXT5UPLOAD.config.ImgPreViewHeight,100>=DEXT5UPLOAD.util.parseIntOr0(a.imgPreViewHeight)&&(a.imgPreViewHeight="100px")):W&&W.getAttribute("height")&&""!=W.getAttribute("height")&&(a.imgPreViewHeight=W.getAttribute("height"),100>=DEXT5UPLOAD.util.parseIntOr0(a.imgPreViewHeight)&&(a.imgPreViewHeight="100px")));if(""!=
DEXT5UPLOAD.config.UserMessage)a.userMessage.edit=DEXT5UPLOAD.config.UserMessage.Edit?DEXT5UPLOAD.config.UserMessage.Edit:"",a.userMessage.view=DEXT5UPLOAD.config.UserMessage.View?DEXT5UPLOAD.config.UserMessage.View:"";else if("1"==DEXT5UPLOAD.util.xml.getNodeValue(m,"user_message")){var da=DEXT5UPLOAD.util.xml.getNode(m,"user_message");da.getAttribute("edit")&&""!=da.getAttribute("edit")&&(a.userMessage.edit=da.getAttribute("edit"));da.getAttribute("view")&&""!=da.getAttribute("view")&&(a.userMessage.view=
da.getAttribute("view"))}""!=DEXT5UPLOAD.config.UseSingleSelect?a.useSingleSelect=DEXT5UPLOAD.config.UseSingleSelect:(xmlUseSingleSelect=DEXT5UPLOAD.util.xml.getNodeValue(m,"use_single_select"),"1"==xmlUseSingleSelect&&(a.useSingleSelect="1"));var ea=DEXT5UPLOAD.util.xml.getNode(m,"disable_alert_message");if(DEXT5UPLOAD.config.DisableAlertMessage.Duplication&&""!=DEXT5UPLOAD.config.DisableAlertMessage.Duplication)a.disableAlertMessage.duplication=DEXT5UPLOAD.config.DisableAlertMessage.Duplication;
else{var Gb=DEXT5UPLOAD.util.xml.getNodeValue(ea,"duplication");"0"==Gb&&(a.disableAlertMessage.duplication=Gb)}if(DEXT5UPLOAD.config.DisableAlertMessage.DeleteUnchosen&&""!=DEXT5UPLOAD.config.DisableAlertMessage.DeleteUnchosen)a.disableAlertMessage.deleteUnchosen=DEXT5UPLOAD.config.DisableAlertMessage.DeleteUnchosen;else{var Hb=DEXT5UPLOAD.util.xml.getNodeValue(ea,"delete_unchosen");"0"==Hb&&(a.disableAlertMessage.deleteUnchosen=Hb)}if(DEXT5UPLOAD.config.DisableAlertMessage.DownloadUnchosen&&""!=
DEXT5UPLOAD.config.DisableAlertMessage.DownloadUnchosen)a.disableAlertMessage.downloadUnchosen=DEXT5UPLOAD.config.DisableAlertMessage.DownloadUnchosen;else{var Ib=DEXT5UPLOAD.util.xml.getNodeValue(ea,"download_unchosen");"0"==Ib&&(a.disableAlertMessage.downloadUnchosen=Ib)}if(DEXT5UPLOAD.config.DisableAlertMessage.OpenUnchosen&&""!=DEXT5UPLOAD.config.DisableAlertMessage.OpenUnchosen)a.disableAlertMessage.openUnchosen=DEXT5UPLOAD.config.DisableAlertMessage.OpenUnchosen;else{var Jb=DEXT5UPLOAD.util.xml.getNodeValue(ea,
"open_unchosen");"0"==Jb&&(a.disableAlertMessage.openUnchosen=Jb)}if(""!=DEXT5UPLOAD.config.DisableDeleteConfirmMessage)a.disableAlertMessage.disableDeleteConfirm=DEXT5UPLOAD.config.DisableDeleteConfirmMessage;else{var xa=DEXT5UPLOAD.util.xml.getNodeValue(m,"disable_delete_confirm_message");if("1"==xa||"2"==xa||"3"==xa)a.disableAlertMessage.disableDeleteConfirm=xa}if(DEXT5UPLOAD.config.DisableAlertMessage.DisableDeleteConfirm&&""!=DEXT5UPLOAD.config.DisableAlertMessage.DisableDeleteConfirm)a.disableAlertMessage.disableDeleteConfirm=
DEXT5UPLOAD.config.DisableAlertMessage.DisableDeleteConfirm;else{var na=DEXT5UPLOAD.util.xml.getNodeValue(ea,"disable_delete_confirm");if("0"==na||"1"==na||"2"==na||"3"==na)a.disableAlertMessage.disableDeleteConfirm=na}if(DEXT5UPLOAD.config.DisableAlertMessage.FileExtensionDetect&&""!=DEXT5UPLOAD.config.DisableAlertMessage.FileExtensionDetect)a.disableAlertMessage.fileExtensionDetect=DEXT5UPLOAD.config.DisableAlertMessage.FileExtensionDetect;else{var Kb=DEXT5UPLOAD.util.xml.getNodeValue(ea,"file_extension_detect");
"1"==Kb&&(a.disableAlertMessage.fileExtensionDetect=Kb)}if(""!=DEXT5UPLOAD.config.DirectDownload.toString())a.directDownload=DEXT5UPLOAD.config.DirectDownload;else{var Lb=DEXT5UPLOAD.util.xml.getNodeValue(m,"direct_download");""!=Lb&&(a.directDownload=Lb)}1==a.directDownload&&(a.downloadMulti=1);var bc=DEXT5UPLOAD.util.xml.getNode(m,"security");DEXT5UPLOAD.util.setSecuritySetting(bc,a,!1);if("1"==a.security.fileIntegrity||"1"==a.security.fileEncrypt)a.uploadMethod="0";"0"==a.uploadMethod&&(DEXT5UPLOAD.browser.ajaxOnProgressSupported=
!1);var ya=DEXT5UPLOAD.util.xml.getNode(d,"header_item");DEXT5UPLOAD.util.xml.getNode(ya,"item");var cc=DEXT5UPLOAD.util.xml.count(ya,"item");if(""!=DEXT5UPLOAD.config.HeaderBarItem){var Ja=DEXT5UPLOAD.config.HeaderBarItem.split(",");DEXT5UPLOAD.config.HeaderBarItemWidth=DEXT5UPLOAD.config.HeaderBarItemWidth.replace(/\%/gi,"px");var Ka=DEXT5UPLOAD.config.HeaderBarItemWidth.split(","),La=DEXT5UPLOAD.config.HeaderBarItemAlign.split(",");Ja&&""!=Ja&&(a.headerBarItem=Ja);Ka&&""!=Ka&&(a.headerBarItemWidth=
Ka);La&&""!==La&&(a.headerBarItemAlign=La)}else for(N=0;N<cc;N++){var dc=DEXT5UPLOAD.util.xml.getNodeValueIdx(ya,"item",N);a.headerBarItem.push(dc);var Ma=DEXT5UPLOAD.util.xml.getNodeIdx(ya,"item",N);a.headerBarItemWidth.push(Ma.getAttribute("width").replace(/\%/gi,"px"));Ma.getAttribute("align")?a.headerBarItemAlign[N]=Ma.getAttribute("align").toLowerCase():a.headerBarItemAlign[N]="left"}DEXT5UPLOAD.config.GroupId&&""!=DEXT5UPLOAD.config.GroupId&&(a.groupId=DEXT5UPLOAD.config.GroupId);if(""!=DEXT5UPLOAD.config.TransferOpenFile)a.transferOpenFile=
DEXT5UPLOAD.config.TransferOpenFile;else{var Mb=DEXT5UPLOAD.util.xml.getNodeValue(m,"transfer_open_file");""!=Mb&&(a.transferOpenFile=Mb)}if(""!=DEXT5UPLOAD.config.SelectByClicked)a.selectByClicked=DEXT5UPLOAD.config.SelectByClicked;else{var Nb=DEXT5UPLOAD.util.xml.getNodeValue(m,"select_by_clicked");""!=Nb&&(a.selectByClicked=Nb)}if(-1<a.userRunTimeMode.indexOf("html5"))if(""!=DEXT5UPLOAD.config.ImageQuality)DEXT5UPLOAD.config.ImageQuality.Quality&&""!=DEXT5UPLOAD.config.ImageQuality.Quality&&(a.imageQuality.quality=
parseFloat(DEXT5UPLOAD.config.ImageQuality.Quality),DEXT5UPLOAD.config.ImageQuality.WorkerCount&&""!=DEXT5UPLOAD.config.ImageQuality.WorkerCount&&(a.imageQuality.workerCount=DEXT5UPLOAD.util.parseIntOr0(DEXT5UPLOAD.config.ImageQuality.WorkerCount)));else{var Ob=DEXT5UPLOAD.util.xml.getNodeValue(m,"image_quality");if(""!=Ob){a.imageQuality.quality=parseFloat(Ob);var Na=DEXT5UPLOAD.util.xml.getNode(m,"image_quality");Na.getAttribute("worker_count")&&""!=Na.getAttribute("worker_count")&&(a.imageQuality.workerCount=
DEXT5UPLOAD.util.parseIntOr0(Na.getAttribute("worker_count")))}}if(""!=DEXT5UPLOAD.config.DownloadPartialSize)y=DEXT5UPLOAD.util.getUnit(DEXT5UPLOAD.config.DownloadPartialSize),A=DEXT5UPLOAD.util.getUnitSize(y),ha=parseInt(DEXT5UPLOAD.config.DownloadPartialSize,10),a.downloadPartialSize=ha*A;else{var Pb=DEXT5UPLOAD.util.xml.getNodeValue(m,"download_partial_size");if(0<Pb.length){var Oa=DEXT5UPLOAD.util.xml.getNode(m,"download_partial_size");Oa.getAttribute("unit")&&""!=Oa.getAttribute("unit")&&(A=
DEXT5UPLOAD.util.getUnitSize(Oa.getAttribute("unit")),a.downloadPartialSize=parseInt(Pb,10)*A)}}0<a.downloadPartialSize&&"1"==a.resumeDownload&&(a.downloadPartialSize=0);if(""!=DEXT5UPLOAD.config.SkipSentFile)a.skipSentFile=DEXT5UPLOAD.config.SkipSentFile;else{var Qb=DEXT5UPLOAD.util.xml.getNodeValue(m,"skip_Sent_File");""!=Qb&&(a.skipSentFile=Qb)}if(""!=DEXT5UPLOAD.config.ForceOverwrite)a.forceOverwrite=DEXT5UPLOAD.config.ForceOverwrite;else{var Rb=DEXT5UPLOAD.util.xml.getNodeValue(m,"force_overwrite");
""!=Rb&&(a.forceOverwrite=Rb)}"function"===typeof DEXT5UPLOAD.config.Event.TransferComplete&&(a.event.transferComplete=DEXT5UPLOAD.config.Event.TransferComplete);var X=document.location.href;-1<X.indexOf("?")&&(X=X.substring(0,X.indexOf("?")));if("NONE"==a.developLang.toUpperCase()&&""!=a.licenseKeyEx){if(DEXT5UPLOAD.util.setSecuritySetting(null,a,!0),a.sizeForChunkUpload=0,a.uploadMethod="1",0==a.maxOneFileSize||**********<a.maxOneFileSize)a.maxOneFileSize=**********}else{var fa=a.handlerUrl,fa=
-1<fa.indexOf("?")?fa+("&t="+(new Date).getTime()):fa+("?t="+(new Date).getTime()),ga=a.licenseKey;"1"==a.licenseKeySE&&"0"==a.security.encryptParam&&(ga=DEXT5UPLOAD.util.base64_encode(ga));if(a.isCrossDomain){var P=document.createElement("div"),ec=DEXT5UPLOAD.util.getDefaultIframeSrc();P.innerHTML='<iframe name="initCheckframe" id="initCheckframe" style="display:none;" src="'+ec+'"></iframe>';P.style.display="none";document.body.appendChild(P);var za;if("1"==a.security.encryptParam){var F="",F=F+
("d01"+a.trans_unitAttributeDelimiter+"initRequest"+a.trans_unitDelimiter),F=F+("d40"+a.trans_unitAttributeDelimiter+X+a.trans_unitDelimiter),F=F+("d02"+a.trans_unitAttributeDelimiter+a.productKey+a.trans_unitDelimiter),F=F+("d03"+a.trans_unitAttributeDelimiter+ga+a.trans_unitDelimiter),F=F+("d04"+a.trans_unitAttributeDelimiter+"1"+a.trans_unitDelimiter),F=F+("d06"+a.trans_unitAttributeDelimiter+"DEXT5U"+a.trans_unitDelimiter),F=DEXT5UPLOAD.util.makeEncryptParam(F);za=[["d00",F]]}else za=[["dext5CMD",
"initRequest"],["domain",X],["productKey",a.productKey],["licenseKey",ga],["cd","1"],["pn","DEXT5U"]],"1"==a.licenseKeySE&&za.push(["Se","Y"]);DEXT5UPLOAD.util.postFormData(document,fa,"initCheckframe",za);DEXT5UPLOAD.util.addEvent(P.firstChild,"load",function(){P.firstChild.contentWindow.postMessage("check","*")},!0);if(window.postMessage){var Sb=function(b){b=DEXT5UPLOAD.util.trim(b.data);DEXT5UPLOAD.util.initLicenseCheck(b,a);document.body.removeChild(P);DEXT5UPLOAD.util.removeEvent(window,"message",
Sb)};DEXT5UPLOAD.util.addEvent(window,"message",Sb)}}else{var oa="";"1"==a.security.encryptParam?(F="",F+="d01"+a.trans_unitAttributeDelimiter+"initRequest"+a.trans_unitDelimiter,F+="d40"+a.trans_unitAttributeDelimiter+X+a.trans_unitDelimiter,F+="d02"+a.trans_unitAttributeDelimiter+a.productKey+a.trans_unitDelimiter,F+="d03"+a.trans_unitAttributeDelimiter+ga+a.trans_unitDelimiter,F+="d06"+a.trans_unitAttributeDelimiter+"DEXT5U"+a.trans_unitDelimiter,F=DEXT5UPLOAD.util.makeEncryptParam(F),oa="d00="+
F):(oa="dext5CMD=initRequest&domain="+X+"&productKey="+a.productKey+"&licenseKey="+ga+"&pn=DEXT5U","1"==a.licenseKeySE&&(oa+="&Se=Y"));DEXT5UPLOAD.ajax.postData(fa,oa,function(c){DEXT5UPLOAD.util.initLicenseCheck(c,a,b)})}}if(DEXT5UPLOAD.config.PluginInstallType&&""!=DEXT5UPLOAD.config.PluginInstallType.toString())a.pluginInstallType=DEXT5UPLOAD.config.PluginInstallType;else{var Q=DEXT5UPLOAD.util.xml.getNodeValue(m,"plugin_install_type");a.pluginInstallType=Q}"0"!=a.pluginInstallType&&(DEXT5UPLOAD.config.PluginInstallUrl&&
""!=DEXT5UPLOAD.config.PluginInstallUrl.toString()?a.pluginInstallUrl=DEXT5UPLOAD.config.PluginInstallUrl:(Q=DEXT5UPLOAD.util.xml.getNode(m,"plugin_install_type"))&&Q.getAttribute("install_url")&&""!=Q.getAttribute("install_url")&&(a.pluginInstallUrl=Q.getAttribute("install_url")));DEXT5UPLOAD.config.UsePluginInstallGuide&&""!=DEXT5UPLOAD.config.UsePluginInstallGuide.toString()?a.usePluginInstallGuide=DEXT5UPLOAD.config.UsePluginInstallGuide:(Q=DEXT5UPLOAD.util.xml.getNode(m,"plugin_install_type"))&&
Q.getAttribute("use_install_guide")&&""!=Q.getAttribute("use_install_guide")&&(a.usePluginInstallGuide=Q.getAttribute("use_install_guide"));"1"==a.usePluginInstallGuide&&"2"==a.pluginInstallType&&(a.pluginInstallFileName="dext5SetupSL.exe");if(DEXT5UPLOAD.config.DefaultDownloadPath&&""!=DEXT5UPLOAD.config.DefaultDownloadPath.toString())a.defaultDownloadPath=DEXT5UPLOAD.config.DefaultDownloadPath;else{var Pa=DEXT5UPLOAD.util.xml.getNodeValue(m,"default_download_path");Pa&&""!=Pa&&(a.defaultDownloadPath=
Pa)}if(DEXT5UPLOAD.config.SavePathSetting&&""!=DEXT5UPLOAD.config.SavePathSetting.toString())a.savePathSetting=DEXT5UPLOAD.config.SavePathSetting;else{var Qa=DEXT5UPLOAD.util.xml.getNodeValue(m,"save_path_setting");Qa&&""!=Qa&&(a.savePathSetting=Qa)}var fc=DEXT5UPLOAD.util.xml.getNode(m,"after_upload");if(DEXT5UPLOAD.config.AftertUpload.SetFileSize&&""!=DEXT5UPLOAD.config.AftertUpload.SetFileSize.toString())a.aftertUpload.setFileSize=DEXT5UPLOAD.config.AftertUpload.SetFileSize;else{var Ra=DEXT5UPLOAD.util.xml.getNodeValue(fc,
"set_file_size");Ra&&""!=Ra.toString()&&(a.aftertUpload.setFileSize=Ra)}if(""!=DEXT5UPLOAD.config.TransferBackgroundStyle)a.transferBackgroundStyle=DEXT5UPLOAD.config.TransferBackgroundStyle;else{var Tb=DEXT5UPLOAD.util.xml.getNodeValue(m,"transfer_background_style");""!=Tb&&(a.transferBackgroundStyle=Tb)}var Aa="",L=a.minHeight,L="1"==a.showHeaderBar?L+DEXT5UPLOAD.util.parseIntOr0(a.customHeaderHeight)-a.minHeaderBarHeight:L-a.minHeaderBarHeight;"0"==a.showStatusBar&&(L-=a.statusBarHeight);"upload"==
a.mode?0==a.showButtonBarEdit.length&&"html4"!=a.userRunTimeMode&&(L-=a.buttonBarHeight):0==a.showButtonBarView.length&&(L-=a.buttonBarHeight);a.minHeight=L;0>a.height.indexOf("%")&&L>DEXT5UPLOAD.util.parseIntOr0(a.height)&&(a.height=L+"px");Aa=a.initVisible?'<div id="dext5uploader_holder_'+b+'" style="width:'+a.width+"; height:"+a.height+'"></div>':'<div id="dext5uploader_holder_'+b+'" style="width:'+a.width+'; height:0px; display:none;"></div>';if(""!=DEXT5UPLOAD.config.UploadHolder){var Ub=document.getElementById(DEXT5UPLOAD.config.UploadHolder);
Ub?Ub.innerHTML=Aa:document.write(Aa)}else document.write(Aa);var gc=document.getElementById("dext5uploader_holder_"+b);DEXT5UPLOAD.util.createUploaderIframe(a,gc,this,"dext5uploader_frame_"+b,"",b);if("1"==a.useDropzone&&"ieplugin"==a.userRunTimeMode&&null==document.getElementById("dext5dropzonePL")){var pa='<object id="dext5dropzonePL" name="dext5dropzonePL" classid="CLSID:F7FEF85C-B9A4-421C-BD01-099E534ABFB0" width="0" height="0" style="width:0px; height:0px;">',pa=pa+('<param name="nUseDropzone" value="'+
a.useDropzone+'">'),pa=pa+"</object>",P=document.createElement("div");P.innerHTML=pa;var Vb=P.firstChild;document.body.appendChild(Vb);11<=DEXT5UPLOAD.browser.ieVersion?DEXT5UPLOAD.util.createEvent("dext5dropzonePL","d5_event_dropZone_drop(strID, strFullPath, bFolder, nDropCount)","dext5_dropZone_drop(strID, strFullPath, bFolder, nDropCount)"):Vb.attachEvent("d5_event_dropZone_drop",dext5_dropZone_drop)}}}}
function dext5_dropZone_drop(b,c,a,d){try{DEXT5UPLOAD_DropZoneAddItem(null,c,b)}catch(e){}};

﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.web>
    <customErrors mode="Off" />
    <!--
        httpRuntime Attributes:
        executionTimeout="[seconds]" - time in seconds before request is automatically timed out
        maxRequestLength="[KBytes]" - KBytes size of maximum request length to accept
        useFullyQualifiedRedirectUrl="[true|false]" - Fully qualifiy the URL for client redirects
        minFreeThreads="[count]" - minimum number of free thread to allow execution of new requests
        minLocalRequestFreeThreads="[count]" - minimum number of free thread to allow execution of new local requests
        appRequestQueueLimit="[count]" - maximum number of requests queued for the application
    -->
    <httpRuntime executionTimeout="3600" maxRequestLength="2048000" />    
    <!-- <identity impersonate="true" userName="dext5" password="1"/> -->    
    <!--<globalization requestEncoding="utf-8" responseEncoding="utf-8" responseHeaderEncoding="utf-8" />-->    
  </system.web>
  <system.webServer>
    <asp enableChunkedEncoding="true"/>
    <security>
      <requestFiltering>
        <!-- 2GB: allowed request length(bytes), IIS 7 -->
        <requestLimits maxAllowedContentLength="2048000000" />
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <add name="Access-Control-Allow-Methods" value="OPTIONS,POST"/>
        <add name="Access-Control-Allow-Headers" value="content-type, dext5-encoded"/>
        <add name="Access-Control-Allow-Origin" value="*" />
      </customHeaders>
    </httpProtocol>   
    
  </system.webServer>
 
</configuration>
﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Hosting;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5
{
	// Token: 0x02000039 RID: 57
	public class UploadOnProgress : Base
	{
		// Token: 0x0600032C RID: 812 RVA: 0x00024B70 File Offset: 0x00022D70
		public UploadOnProgress(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pAllowExtensionSpecialSymbol) : base(context)
		{
			this.tempPath = pTempPath;
			this.physicalPath = pPhysicalPath;
			this.virtualPath = pVirtualPath;
			this.fileWhiteList = pFileWhiteList;
			this.fileBlackList = pFileBlackList;
			this.fileBlackWordList = pFileBlackWordList;
			this.allowExtensionSpecialSymbol = pAllowExtensionSpecialSymbol;
		}

		// Token: 0x0600032D RID: 813 RVA: 0x00024BF4 File Offset: 0x00022DF4
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			string str = string.Empty;
			string text = string.Empty;
			string text2 = string.Empty;
			string text3 = string.Empty;
			if (!base.CheckCaller("html5"))
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|013|Bad Request Type"));
				return null;
			}
			if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, null))
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
				return null;
			}
			if (!base.CheckBlackWord(this.fileBlackWordList, null))
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
				return null;
			}
			this.tempPath = base.GetTempPath(this.tempPath);
			text = base.GetFileLocationInfo(this.tempPath, this.physicalPath, this.virtualPath, false, null, null, null, null, null, null, null);
			if (string.IsNullOrEmpty(text))
			{
				throw new Exception("Error occured on the server side");
			}
			if (text.IndexOf("error") == 0)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter(text));
				return null;
			}
			string[] array = text.Split(new char[]
			{
				'|'
			});
			text2 = array[0];
			text3 = array[1];
			bool flag = false;
			string text4 = text2;
			try
			{
				pBeforeInitializeEvent(this.hContext, ref text2, ref text3);
				if (!text4.Equals(text2))
				{
					flag = true;
				}
			}
			catch
			{
			}
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = this.hContext;
				uploadEventEntity.NewFileLocation = text2;
				uploadEventEntity.ResponseFileName = text3;
				pBeforeInitializeEventEx(uploadEventEntity);
				text2 = uploadEventEntity.NewFileLocation;
				text3 = uploadEventEntity.ResponseFileName;
				if (!text4.Equals(text2))
				{
					flag = true;
				}
			}
			catch
			{
			}
			if (flag)
			{
				string[] array2 = base.InitializeEventFileExec(text2, text3);
				text2 = array2[0];
				text3 = array2[1];
			}
			string text5 = string.Empty;
			if (pCustomError == null)
			{
				this.hContext.Request.Files[0].SaveAs(text2);
				bool flag2 = true;
				string fileDataIntegrity = this._entity_dextParam.fileDataIntegrity;
				if (!string.IsNullOrEmpty(fileDataIntegrity))
				{
					string integrityHashValue = base.getIntegrityHashValue(text2);
					if (integrityHashValue != fileDataIntegrity)
					{
						flag2 = false;
					}
				}
				if (!flag2)
				{
					File.Delete(text2);
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|024|After the completion of file data transfer and file data before transmitting do not match"));
					return null;
				}
				if (!string.IsNullOrEmpty(this.physicalPath))
				{
					text5 = text2;
				}
				else if (!string.IsNullOrEmpty(this.virtualPath))
				{
					string oldValue = HostingEnvironment.MapPath("~/");
					text5 = "/" + text2.Replace(oldValue, "");
					text5 = text5.Replace(this.m_PathChar, '/');
				}
				try
				{
					pCompleteBeforeEvent(this.hContext, ref text2, ref text5, ref text3, ref this._str_ResponseCustomValue);
				}
				catch
				{
				}
				try
				{
					UploadEventEntity uploadEventEntity2 = new UploadEventEntity();
					uploadEventEntity2.Context = this.hContext;
					uploadEventEntity2.NewFileLocation = text2;
					uploadEventEntity2.ResponseFileServerPath = text5;
					uploadEventEntity2.ResponseFileName = text3;
					uploadEventEntity2.ResponseGroupId = this._entity_dextParam.fileGroupID;
					uploadEventEntity2.FileIndex = this._entity_dextParam.fileIndex;
					pCompleteBeforeEventEx(uploadEventEntity2);
					text2 = uploadEventEntity2.NewFileLocation;
					text5 = uploadEventEntity2.ResponseFileServerPath;
					text3 = uploadEventEntity2.ResponseFileName;
					this._str_ResponseCustomValue = uploadEventEntity2.ResponseCustomValue;
					this._str_ResponseGroupId = uploadEventEntity2.ResponseGroupId;
				}
				catch
				{
				}
				string text6 = "";
				if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('\b') == -1)
				{
					text6 = text6 + "|" + this._str_ResponseCustomValue;
				}
				if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('\b') == -1)
				{
					text6 = text6 + "\b" + this._str_ResponseGroupId;
				}
				FileInfo fileInfo = new FileInfo(text2);
				string text7 = fileInfo.Length.ToString();
				string text8 = this._entity_dextParam.fileName;
				text8 = text8.Normalize(NormalizationForm.FormC);
				str = string.Concat(new string[]
				{
					"success|",
					text8,
					"::",
					text5,
					"|",
					text3,
					"|",
					text7,
					text6
				});
			}
			if (pCustomError != null)
			{
				str = "error|" + pCustomError.ErrorCode + "|" + pCustomError.ErrorMessage;
			}
			if (!string.IsNullOrEmpty(text) && text.IndexOf("error") != 0 && pCustomError == null)
			{
				try
				{
					pCompleteEvent(this.hContext, text2, text5, text3);
				}
				catch
				{
				}
				try
				{
					pCompleteEventEx(new UploadEventEntity
					{
						Context = this.hContext,
						NewFileLocation = text2,
						ResponseFileServerPath = text5,
						ResponseFileName = text3
					});
				}
				catch
				{
				}
			}
			string text9 = string.Empty;
			text9 += Dext5Parameter.MakeParameter(str);
			this.hContext.Response.Write(text9);
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - Html5 UploadOnProgress, " + Dext5Encoding.Base64Decoding(text9), this._str_DebugFilePath);
			}
			return null;
		}

		// Token: 0x040001B5 RID: 437
		private string physicalPath = string.Empty;

		// Token: 0x040001B6 RID: 438
		private string virtualPath = string.Empty;

		// Token: 0x040001B7 RID: 439
		private string fileWhiteList = string.Empty;

		// Token: 0x040001B8 RID: 440
		private string fileBlackList = string.Empty;

		// Token: 0x040001B9 RID: 441
		private string[] fileBlackWordList;

		// Token: 0x040001BA RID: 442
		private string allowExtensionSpecialSymbol = string.Empty;
	}
}

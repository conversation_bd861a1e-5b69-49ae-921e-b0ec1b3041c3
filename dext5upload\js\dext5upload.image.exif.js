var ExifRestorer=function(){return{KEY_STR:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode64:function(c){var b="",a,d,e="",h,f,g="",k=0;do a=c[k++],d=c[k++],e=c[k++],h=a>>2,a=(a&3)<<4|d>>4,f=(d&15)<<2|e>>6,g=e&63,isNaN(d)?f=g=64:isNaN(e)&&(g=64),b=b+this.KEY_STR.charAt(h)+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(f)+this.KEY_STR.charAt(g);while(k<c.length);return b},restore:function(c,b){if(!c.match("data:image/jpeg;base64,"))return b;var a=this.decode64(c.replace("data:image/jpeg;base64,",
"")),a=this.slice2Segments(a),a=this.exifManipulation(b,a);return this.encode64(a)},restore2:function(c,b){var a=this.slice2Segments(c),a=this.exifManipulation(b,a);return this.encode64(a)},exifManipulation:function(c,b){var a=this.getExifArray(b),a=this.insertExif(c,a);return new Uint8Array(a)},getExifArray:function(c){for(var b,a=0;a<c.length;a++)if(b=c[a],255==b[0]&225==b[1])return b;return[]},insertExif:function(c,b){var a=c.replace("data:image/jpeg;base64,",""),d=this.decode64(a),e=d.indexOf(255,
3),a=d.slice(0,e),d=d.slice(e),a=a.concat(b);return a=a.concat(d)},slice2Segments:function(c){for(var b=0,a=[];!(255==c[b]&218==c[b+1]);){if(255==c[b]&216==c[b+1])b+=2;else{var d=b+(256*c[b+2]+c[b+3])+2,b=c.slice(b,d);a.push(b);b=d}if(b>c.length)break}return a},decode64:function(c){var b,a,d="",e,h="",f=0,g=[];/[^A-Za-z0-9\+\/\=]/g.exec(c)&&alert("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding.");c=c.replace(/[^A-Za-z0-9\+\/\=]/g,
"");do b=this.KEY_STR.indexOf(c.charAt(f++)),a=this.KEY_STR.indexOf(c.charAt(f++)),e=this.KEY_STR.indexOf(c.charAt(f++)),h=this.KEY_STR.indexOf(c.charAt(f++)),b=b<<2|a>>4,a=(a&15)<<4|e>>2,d=(e&3)<<6|h,g.push(b),64!=e&&g.push(a),64!=h&&g.push(d);while(f<c.length);return g}}}();

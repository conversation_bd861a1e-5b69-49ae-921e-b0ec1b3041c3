﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process;
using Raonwiz.Dext5.Process.Common;
using Raonwiz.Dext5.Process.Entity;
using Raonwiz.Dext5.Process.Html4;
using Raonwiz.Dext5.Process.Html5;
using Raonwiz.Dext5.Process.Html5Plus;
using Raonwiz.Dext5.Process.Plugin;

namespace Raonwiz.Dext5
{
	// Token: 0x02000046 RID: 70
	public class UploadHandler : IDisposable
	{
		// Token: 0x14000008 RID: 8
		// (add) Token: 0x06000361 RID: 865 RVA: 0x000271C0 File Offset: 0x000253C0
		// (remove) Token: 0x06000362 RID: 866 RVA: 0x000271F8 File Offset: 0x000253F8
		public event UploadHandlerBeforeInitializeDelegate UploadBeforeInitializeEvent;

		// Token: 0x14000009 RID: 9
		// (add) Token: 0x06000363 RID: 867 RVA: 0x00027230 File Offset: 0x00025430
		// (remove) Token: 0x06000364 RID: 868 RVA: 0x00027268 File Offset: 0x00025468
		public event UploadHandlerBeforeInitializeDelegateEx UploadBeforeInitializeEventEx;

		// Token: 0x1400000A RID: 10
		// (add) Token: 0x06000365 RID: 869 RVA: 0x000272A0 File Offset: 0x000254A0
		// (remove) Token: 0x06000366 RID: 870 RVA: 0x000272D8 File Offset: 0x000254D8
		public event UploadHandlerBeforeCompleteDelegate UploadCompleteBeforeEvent;

		// Token: 0x1400000B RID: 11
		// (add) Token: 0x06000367 RID: 871 RVA: 0x00027310 File Offset: 0x00025510
		// (remove) Token: 0x06000368 RID: 872 RVA: 0x00027348 File Offset: 0x00025548
		public event UploadHandlerBeforeCompleteDelegateEx UploadCompleteBeforeEventEx;

		// Token: 0x1400000C RID: 12
		// (add) Token: 0x06000369 RID: 873 RVA: 0x00027380 File Offset: 0x00025580
		// (remove) Token: 0x0600036A RID: 874 RVA: 0x000273B8 File Offset: 0x000255B8
		public event UploadHandlerDelegate UploadCompleteEvent;

		// Token: 0x1400000D RID: 13
		// (add) Token: 0x0600036B RID: 875 RVA: 0x000273F0 File Offset: 0x000255F0
		// (remove) Token: 0x0600036C RID: 876 RVA: 0x00027428 File Offset: 0x00025628
		public event UploadHandlerDelegateEx UploadCompleteEventEx;

		// Token: 0x1400000E RID: 14
		// (add) Token: 0x0600036D RID: 877 RVA: 0x00027460 File Offset: 0x00025660
		// (remove) Token: 0x0600036E RID: 878 RVA: 0x00027498 File Offset: 0x00025698
		public event OpenDownloadHandlerDelegate OpenDownloadCompleteEvent;

		// Token: 0x1400000F RID: 15
		// (add) Token: 0x0600036F RID: 879 RVA: 0x000274D0 File Offset: 0x000256D0
		// (remove) Token: 0x06000370 RID: 880 RVA: 0x00027508 File Offset: 0x00025708
		public event OpenDownloadHandlerDelegateEx OpenDownloadCompleteEventEx;

		// Token: 0x14000010 RID: 16
		// (add) Token: 0x06000371 RID: 881 RVA: 0x00027540 File Offset: 0x00025740
		// (remove) Token: 0x06000372 RID: 882 RVA: 0x00027578 File Offset: 0x00025778
		public event OpenDownloadBeforeInitializeDelegateEx OpenDownloadBeforeInitializeEventEx;

		// Token: 0x170000FC RID: 252
		// (set) Token: 0x06000373 RID: 883 RVA: 0x000275AD File Offset: 0x000257AD
		public int SetScriptTimeout
		{
			set
			{
				this._i_ScriptTimeout = value;
			}
		}

		// Token: 0x170000FD RID: 253
		// (set) Token: 0x06000374 RID: 884 RVA: 0x000275B6 File Offset: 0x000257B6
		public string SetEnableZip64
		{
			set
			{
				this._str_enableZip64 = value;
			}
		}

		// Token: 0x170000FE RID: 254
		// (set) Token: 0x06000375 RID: 885 RVA: 0x000275BF File Offset: 0x000257BF
		public bool SetExternalWebFileDirectDownload
		{
			set
			{
				this._b_externalWebFileDirectDownload = value;
			}
		}

		// Token: 0x170000FF RID: 255
		// (set) Token: 0x06000376 RID: 886 RVA: 0x000275C8 File Offset: 0x000257C8
		public bool SetUseExternalDownload
		{
			set
			{
				this._b_useExternalDownload = value;
			}
		}

		// Token: 0x17000100 RID: 256
		// (set) Token: 0x06000377 RID: 887 RVA: 0x000275D1 File Offset: 0x000257D1
		public string SetLicenseKeyPath
		{
			set
			{
				this._str_licenseKeyPath = value;
			}
		}

		// Token: 0x17000101 RID: 257
		// (set) Token: 0x06000378 RID: 888 RVA: 0x000275DA File Offset: 0x000257DA
		public int SetGarbageCleanDay
		{
			set
			{
				this._i_garbageCleanDay = value;
			}
		}

		// Token: 0x17000102 RID: 258
		// (set) Token: 0x06000379 RID: 889 RVA: 0x000275E3 File Offset: 0x000257E3
		public string SetZipFileName
		{
			set
			{
				this._str_zipFileName = value;
			}
		}

		// Token: 0x17000103 RID: 259
		// (set) Token: 0x0600037A RID: 890 RVA: 0x000275EC File Offset: 0x000257EC
		public bool SetDownLoadVirtualPath
		{
			set
			{
				this._b_downloadVirtualPath = value;
			}
		}

		// Token: 0x17000104 RID: 260
		// (set) Token: 0x0600037B RID: 891 RVA: 0x000275F5 File Offset: 0x000257F5
		public string SetTempPath
		{
			set
			{
				this._str_tempPath = value;
			}
		}

		// Token: 0x17000105 RID: 261
		// (set) Token: 0x0600037C RID: 892 RVA: 0x000275FE File Offset: 0x000257FE
		public string SetConfigPhysicalPath
		{
			set
			{
				this._str_ConfigPhysicalPath = value;
			}
		}

		// Token: 0x17000106 RID: 262
		// (set) Token: 0x0600037D RID: 893 RVA: 0x00027607 File Offset: 0x00025807
		public string SetVirtualPath
		{
			set
			{
				this._str_virtualPath = value;
			}
		}

		// Token: 0x17000107 RID: 263
		// (set) Token: 0x0600037E RID: 894 RVA: 0x00027610 File Offset: 0x00025810
		public string SetPhysicalPath
		{
			set
			{
				this._str_physicalPath = value;
			}
		}

		// Token: 0x17000108 RID: 264
		// (set) Token: 0x0600037F RID: 895 RVA: 0x00027619 File Offset: 0x00025819
		public string SetFileBlackList
		{
			set
			{
				this._str_fileBlackList = value;
			}
		}

		// Token: 0x17000109 RID: 265
		// (set) Token: 0x06000380 RID: 896 RVA: 0x00027622 File Offset: 0x00025822
		public string SetFileWhiteList
		{
			set
			{
				this._str_fileWhiteList = value;
			}
		}

		// Token: 0x1700010A RID: 266
		// (set) Token: 0x06000381 RID: 897 RVA: 0x0002762B File Offset: 0x0002582B
		public string[] SetFileBlackWordList
		{
			set
			{
				this._strary_fileBlackWordList = value;
			}
		}

		// Token: 0x1700010B RID: 267
		// (set) Token: 0x06000382 RID: 898 RVA: 0x00027634 File Offset: 0x00025834
		public string SetViewerJsPath
		{
			set
			{
				this._str_viewerJsPath = value;
			}
		}

		// Token: 0x1700010C RID: 268
		// (set) Token: 0x06000383 RID: 899 RVA: 0x0002763D File Offset: 0x0002583D
		public string SetViewerJs2Path
		{
			set
			{
				this._str_viewerJs2Path = value;
			}
		}

		// Token: 0x1700010D RID: 269
		// (set) Token: 0x06000384 RID: 900 RVA: 0x00027646 File Offset: 0x00025846
		public string SetViewerCssPath
		{
			set
			{
				this._str_viewerCssPath = value;
			}
		}

		// Token: 0x1700010E RID: 270
		// (set) Token: 0x06000385 RID: 901 RVA: 0x0002764F File Offset: 0x0002584F
		public string SetViewerLoadingImagePath
		{
			set
			{
				this._str_viewerLoadingImagePath = value;
			}
		}

		// Token: 0x1700010F RID: 271
		// (set) Token: 0x06000386 RID: 902 RVA: 0x00027658 File Offset: 0x00025858
		public string SetViewerBrokenImagePath
		{
			set
			{
				this._str_downloadRootPath = value;
			}
		}

		// Token: 0x17000110 RID: 272
		// (set) Token: 0x06000387 RID: 903 RVA: 0x00027661 File Offset: 0x00025861
		public bool SetHtml5SliceAppend
		{
			set
			{
				this._b_html5SliceAppend = value;
			}
		}

		// Token: 0x17000111 RID: 273
		// (set) Token: 0x06000388 RID: 904 RVA: 0x0002766A File Offset: 0x0002586A
		public string SetDownloadRootPath
		{
			set
			{
				this._str_downloadRootPath = value;
			}
		}

		// Token: 0x17000112 RID: 274
		// (set) Token: 0x06000389 RID: 905 RVA: 0x00027673 File Offset: 0x00025873
		public string SetAllowExtensionSpecialSymbol
		{
			set
			{
				this._str_allowExtensionSpecialSymbol = value;
			}
		}

		// Token: 0x0600038A RID: 906 RVA: 0x0002767C File Offset: 0x0002587C
		public void SetUploadCheckFileExtension(int iAllowOrLimit, string sFileExtensionList)
		{
			if (iAllowOrLimit == 0)
			{
				this._str_fileBlackList_upload = sFileExtensionList;
				return;
			}
			if (iAllowOrLimit == 1)
			{
				this._str_fileWhiteList_upload = sFileExtensionList;
			}
		}

		// Token: 0x0600038B RID: 907 RVA: 0x00027694 File Offset: 0x00025894
		public void SetDownloadCheckFileExtension(int iAllowOrLimit, string sFileExtensionList)
		{
			if (iAllowOrLimit == 0)
			{
				this._str_fileBlackList_download = sFileExtensionList;
				return;
			}
			if (iAllowOrLimit == 1)
			{
				this._str_fileWhiteList_download = sFileExtensionList;
			}
		}

		// Token: 0x0600038C RID: 908 RVA: 0x000276AC File Offset: 0x000258AC
		public void SetCustomError(string errorMessage)
		{
			this._CustomError = new Dext5CustomError(errorMessage);
		}

		// Token: 0x0600038D RID: 909 RVA: 0x000276BA File Offset: 0x000258BA
		public void SetCustomError(string errorCode, string errorMessage)
		{
			this._CustomError = new Dext5CustomError(errorCode, errorMessage);
		}

		// Token: 0x0600038E RID: 910 RVA: 0x000276C9 File Offset: 0x000258C9
		public void SetNetworkCredentials(string userID, string userPassword, string domain)
		{
			this._str_Credentials_UserID = userID;
			this._str_Credentials_UserPassword = userPassword;
			this._str_Credentials_Domain = domain;
		}

		// Token: 0x0600038F RID: 911 RVA: 0x000276E0 File Offset: 0x000258E0
		public void SetDebugMode(bool isDebug, string debugFilePath)
		{
			this._b_IsDebug = isDebug;
			this._str_DebugFilePath = debugFilePath;
		}

		// Token: 0x06000390 RID: 912 RVA: 0x000276F0 File Offset: 0x000258F0
		public void Process(HttpContext context)
		{
			context.Server.ScriptTimeout = this._i_ScriptTimeout;
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			if (string.IsNullOrEmpty(paramEntity.dext5CMD))
			{
				context.Response.Clear();
				context.Response.Write(Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("Error - Wrong operation type", this._str_DebugFilePath);
				}
				return;
			}
			string dext5CMD = paramEntity.dext5CMD;
			Base @base = null;
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - " + dext5CMD + " Process Start", this._str_DebugFilePath);
			}
			string key;
			if ((key = dext5CMD) != null)
			{
				if (<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000388-1 == null)
				{
					<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000388-1 = new Dictionary<string, int>(31)
					{
						{
							"initRequest",
							0
						},
						{
							"configRequest",
							1
						},
						{
							"uploadRequest",
							2
						},
						{
							"uploadPRequest",
							3
						},
						{
							"uploadHtml4Request",
							4
						},
						{
							"uploadHtml4SWFRequest",
							5
						},
						{
							"uh4fc",
							6
						},
						{
							"uh4s",
							7
						},
						{
							"uzr",
							8
						},
						{
							"uploadPlugIn",
							9
						},
						{
							"uploadPLResumeEnd",
							10
						},
						{
							"mergeRequest",
							11
						},
						{
							"downloadRequest",
							12
						},
						{
							"mdr",
							13
						},
						{
							"mzf",
							14
						},
						{
							"zs",
							15
						},
						{
							"openRequest",
							16
						},
						{
							"ofc",
							17
						},
						{
							"gcRequest",
							18
						},
						{
							"cfs",
							19
						},
						{
							"partialSize",
							20
						},
						{
							"partialDelete",
							21
						},
						{
							"deleteFile",
							22
						},
						{
							"h5pbe",
							23
						},
						{
							"h5pae",
							24
						},
						{
							"gfs",
							25
						},
						{
							"vdc",
							26
						},
						{
							"h5rur",
							27
						},
						{
							"h5rer",
							28
						},
						{
							"h5ps",
							29
						},
						{
							"h5pdf",
							30
						}
					};
				}
				int num;
				if (<PrivateImplementationDetails>{118569B2-5524-447F-8977-984A9B08067F}.$$method0x6000388-1.TryGetValue(key, out num))
				{
					switch (num)
					{
					case 0:
						@base = new LicenseCheck(context);
						break;
					case 1:
						@base = new ConfigXML(context, this._str_ConfigPhysicalPath);
						break;
					case 2:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new Raonwiz.Dext5.Process.Html5.Upload(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetHtml5SliceAppend(this._b_html5SliceAppend);
						break;
					case 3:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new UploadOnProgress(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 4:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						if (paramEntity.displayFileSize.Equals("1"))
						{
							@base = new UploadProgress(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						}
						else
						{
							@base = new Raonwiz.Dext5.Process.Html4.Upload(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						}
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 5:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new UploadSWF(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 6:
						@base = new FileCheck(context);
						break;
					case 7:
						@base = new UploadStatus(context);
						break;
					case 8:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new UploadZeroFile(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 9:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new Raonwiz.Dext5.Process.Plugin.Upload(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 10:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new ResumeEnd(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 11:
						@base = new Merge(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetHtml5SliceAppend(this._b_html5SliceAppend);
						break;
					case 12:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						@base = new Download(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_downloadRootPath, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetExternalWebFileDirectDownload(this._b_externalWebFileDirectDownload);
						@base.SetUseExternalDownload(this._b_useExternalDownload);
						break;
					case 13:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						@base = new MultiDownload(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_downloadRootPath, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetExternalWebFileDirectDownload(this._b_externalWebFileDirectDownload);
						@base.SetUseExternalDownload(this._b_useExternalDownload);
						@base.SetEnableZip64(this._str_enableZip64);
						break;
					case 14:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						@base = new MakeZip(context, this._str_tempPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_downloadRootPath, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetExternalWebFileDirectDownload(false);
						@base.SetUseExternalDownload(this._b_useExternalDownload);
						break;
					case 15:
						@base = new ZipStatus(context, this._str_tempPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 16:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						@base = new Open(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_downloadRootPath, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetExternalWebFileDirectDownload(this._b_externalWebFileDirectDownload);
						@base.SetUseExternalDownload(this._b_useExternalDownload);
						break;
					case 17:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						@base = new OpenFileCheck(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_downloadRootPath, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						@base.SetExternalWebFileDirectDownload(this._b_externalWebFileDirectDownload);
						@base.SetUseExternalDownload(this._b_useExternalDownload);
						break;
					case 18:
						@base = new Garbage(context, this._str_tempPath, this._i_garbageCleanDay, this._str_physicalPath, this._str_virtualPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 19:
						@base = new Utils(context, "ProcessCheckFileSize", this._str_tempPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 20:
						@base = new Utils(context, "ProcessGetPartialSize", this._str_tempPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 21:
						@base = new Utils(context, "ProcessPartialDelete", this._str_tempPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 22:
						@base = new Utils(context, "ProcessDeleteFile", this._str_tempPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 23:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new UploadBeforeEvent(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 24:
						@base = new UploadAfterEvent(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 25:
						@base = new GetFileSize(context);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 26:
						@base = new ViewerDownloadComplete(context);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					case 27:
					case 28:
					case 29:
					case 30:
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_upload;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_upload;
						}
						@base = new ResumeUpload(context, this._str_tempPath, this._str_physicalPath, this._str_virtualPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
						@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						break;
					default:
						goto IL_E88;
					}
					@base.SetDextParam(paramEntity);
					@base.SetDebugMode(this._b_IsDebug, this._str_DebugFilePath);
					try
					{
						object obj = @base.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("CMD - " + dext5CMD + " Process End", this._str_DebugFilePath);
							LogUtil.DextDebug("", this._str_DebugFilePath);
						}
						if ((((dext5CMD == "downloadRequest" || dext5CMD == "openRequest") && string.IsNullOrEmpty(paramEntity.zipFileName)) || dext5CMD == "mzf" || dext5CMD == "mdr") && context.Response.StatusCode == 200 && obj != null && (obj.GetType() != typeof(string) || !((string)obj).StartsWith("error")))
						{
							this.DownloadEvent(context, obj);
						}
					}
					catch (Exception ex)
					{
						context.Response.Clear();
						string text = string.Empty;
						if (this.CheckCaller(context, "ieplugin") || dext5CMD == "downloadRequest" || dext5CMD == "mdr" || dext5CMD == "mzf")
						{
							text = "[FAIL]";
						}
						text += Dext5Parameter.MakeParameter("error|020|Error occured on the server side");
						context.Response.Write(text);
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("Error - " + ex.Message, this._str_DebugFilePath);
						}
					}
					@base.ImpersonatorDispose();
					return;
				}
			}
			IL_E88:
			context.Response.Clear();
			context.Response.Write(Dext5Parameter.MakeParameter("error|001|Wrong operation type"));
		}

		// Token: 0x06000391 RID: 913 RVA: 0x000287A4 File Offset: 0x000269A4
		private void DownloadEvent(HttpContext pContext, object lPhysicalPath)
		{
			List<string> list = (List<string>)lPhysicalPath;
			string[] array = new string[list.Count];
			string[] array2 = new string[list.Count];
			for (int i = 0; i < list.Count; i++)
			{
				string[] array3 = list[i].Split(new char[]
				{
					'\f'
				});
				array[i] = array3[0];
				array2[i] = array3[1];
			}
			string[] requestValue = this.GetRequestValue(pContext, "customValue");
			try
			{
				this.OpenDownloadCompleteEvent(pContext, array, array2, requestValue);
			}
			catch
			{
			}
			try
			{
				UploadEventEntity uploadEventEntity = new UploadEventEntity();
				uploadEventEntity.Context = pContext;
				uploadEventEntity.DownloadFilePath = array;
				uploadEventEntity.DownloadFileName = array2;
				uploadEventEntity.DownloadCustomValue = requestValue;
				this.OpenDownloadCompleteEventEx(uploadEventEntity);
			}
			catch
			{
			}
		}

		// Token: 0x06000392 RID: 914 RVA: 0x00028888 File Offset: 0x00026A88
		public void Viewer(HttpContext context)
		{
			HttpRequest request = context.Request;
			HttpResponse response = context.Response;
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			StringBuilder stringBuilder = new StringBuilder();
			string str = "../js/dext5upload.viewer.js";
			string str2 = "../js/dext5upload.base64.js";
			string str3 = "../css/dext5viewer.min.css";
			string text = "../images/loading.gif";
			string text2 = "../images/broken.png";
			if (!string.IsNullOrEmpty(this._str_viewerJsPath))
			{
				str = this._str_viewerJsPath;
			}
			if (!string.IsNullOrEmpty(this._str_viewerJs2Path))
			{
				str2 = this._str_viewerJs2Path;
			}
			if (!string.IsNullOrEmpty(this._str_viewerCssPath))
			{
				str3 = this._str_viewerCssPath;
			}
			if (!string.IsNullOrEmpty(this._str_viewerLoadingImagePath))
			{
				text = this._str_viewerLoadingImagePath;
			}
			if (!string.IsNullOrEmpty(this._str_downloadRootPath))
			{
				text2 = this._str_downloadRootPath;
			}
			string text3 = paramEntity.documentDomain;
			string text4 = "";
			if (!string.IsNullOrEmpty(text3))
			{
				text4 = "<script> document.domain = '" + text3 + "'; </script>";
			}
			else
			{
				text3 = "";
			}
			string viewerPopupInit = paramEntity.viewerPopupInit;
			if (string.IsNullOrEmpty(viewerPopupInit))
			{
				bool flag = true;
				try
				{
					string fileExt = paramEntity.fileExt;
					string empty = string.Empty;
					string text5 = string.Empty;
					string value = request["d00"];
					string text6 = string.Empty;
					string text7 = Guid.NewGuid().ToString();
					string text8;
					if (!string.IsNullOrEmpty(value))
					{
						text5 = paramEntity.fileOrgName;
						text6 = paramEntity.downloadUrl;
						text6 = ((text6.IndexOf('?') < 0) ? (text6 + "?") : (text6 + "&"));
						text8 = text6;
						string text9 = "";
						string text10 = text9;
						text9 = string.Concat(new string[]
						{
							text10,
							"d01",
							Dext5Parameter.trans_unitAttributeDelimiter,
							"openRequest",
							Dext5Parameter.trans_unitDelimiter
						});
						string text11 = text9;
						text9 = string.Concat(new string[]
						{
							text11,
							"d10",
							Dext5Parameter.trans_unitAttributeDelimiter,
							paramEntity.fileNameRuleEx,
							Dext5Parameter.trans_unitDelimiter
						});
						string text12 = text9;
						text9 = string.Concat(new string[]
						{
							text12,
							"d25",
							Dext5Parameter.trans_unitAttributeDelimiter,
							paramEntity.fileVirtualPath,
							Dext5Parameter.trans_unitDelimiter
						});
						string text13 = text9;
						text9 = string.Concat(new string[]
						{
							text13,
							"d26",
							Dext5Parameter.trans_unitAttributeDelimiter,
							paramEntity.fileOrgName,
							Dext5Parameter.trans_unitDelimiter
						});
						string text14 = text9;
						text9 = string.Concat(new string[]
						{
							text14,
							"d29",
							Dext5Parameter.trans_unitAttributeDelimiter,
							text7,
							Dext5Parameter.trans_unitDelimiter
						});
						text9 = Dext5Parameter.MakeParameter(text9);
						text6 = text6 + "d00=" + text9;
					}
					else
					{
						text5 = HttpUtility.UrlDecode(paramEntity.fileOrgName);
						text6 = HttpUtility.UrlDecode(paramEntity.downloadUrl);
						text6 = ((text6.IndexOf('?') < 0) ? (text6 + "?") : (text6 + "&"));
						text8 = text6;
						text6 = text6 + "dext5CMD=openRequest&fileNameRuleEx=" + paramEntity.fileNameRuleEx;
						text6 = text6 + "&fileVirtualPath=" + HttpUtility.UrlEncode(paramEntity.fileVirtualPath);
						text6 = text6 + "&fileOrgName=" + HttpUtility.UrlEncode(paramEntity.fileOrgName);
						text6 = text6 + "&viewerguid=" + text7;
					}
					text6 = text6 + "&customValue=" + HttpUtility.UrlEncode(request["customValue"]);
					string skinName = paramEntity.skinName;
					flag = Convert.ToBoolean(paramEntity.dext5Release);
					if ("bmp,gif,jpeg,jpg,png,tif,tiff".IndexOf(fileExt) > -1)
					{
						stringBuilder.AppendFormat("<script type='text/javascript'>\r\n    function ResizingImage(img, limit_width, limit_height) {{\r\n\r\n        setTimeout( function() {{\r\n    \r\n            if (img.width > limit_width || img.height > limit_height) {{\r\n\t\t\t\tif ((img.width / img.height) > (limit_width / limit_height)) {{\r\n\t\t\t\t\timg.width = limit_width;\r\n\t\t\t\t}} else {{\r\n\t\t\t\t\timg.height = limit_height;\r\n\t\t\t\t}}\r\n\t\t\t}}\r\n\r\n            var widthGap = 46;\r\n            var heightGap = 150;\r\n\r\n            if (dext5uploadBrowser.ie == true) {{ // ie\r\n                heightGap = 130;//102;\r\n            }} else if (dext5uploadBrowser.chrome == true) {{ // chrome\r\n                //default\r\n            }} else if (dext5uploadBrowser.gecko == true) {{ // firefox\r\n                //default\r\n            }}  else if (dext5uploadBrowser.opera == true) {{ // opera\r\n                heightGap = 165;\r\n            }} else if (dext5uploadBrowser.safari == true) {{ // safari\r\n                heightGap = 125;\r\n            }}\r\n\r\n            windowResize(img.width + widthGap, img.height + heightGap);\r\n            windowResize(img.width + widthGap, img.height + heightGap);\r\n        }}, 300);\r\n\r\n    }}\r\n\r\n    function ErrorImage(img) {{\r\n        img.src = '{0}';\r\n        LoadDext5ViewerCss();\r\n    }}\r\n</script>\r\n<strong id='header' class='skin_{1}'>{2}</strong>\r\n<table id='wrapper'>\r\n    <tr>\r\n        <td><img id='dext5Obj' src=\"{3}\" onerror='javascript:ErrorImage(this);' onload='javascript:ResizingImage(this, screen.availWidth-120, screen.availHeight-270);' onclick='javascript:window.close();' alt='클릭하면 창이 닫힙니다.' title='클릭하면 창이 닫힙니다.' /></td>\r\n    </tr>\r\n</table>", new object[]
						{
							text2,
							skinName,
							text5,
							text6
						});
					}
					else if (fileExt == "swf")
					{
						stringBuilder.AppendFormat("<strong id='header' class='skin_{0}'>{1}</strong>\r\n<table id='wrapper'>\r\n    <tr>        \r\n        <td>\r\n            <script type='text/javascript'>\r\n                function Flash(id, url, w, h, bg, t) {{\r\n                    document.writeln(\"<object classid='clsid:d27cdb6e-ae6d-11cf-96b8-444553540000' codebase='http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=8,0,0,0' width=\" + w + \" height=\" + h + \" id=\" + id + \">\");\r\n                    document.writeln(\"<param name='movie' value=\" + url + \" />\");\r\n                    document.writeln(\"<param name='wmode' value=\" + t + \" />\");\r\n                    document.writeln(\"<param name='bgcolor' value=\" + bg + \" />\");\r\n                    document.writeln(\"<param name='allowScriptAccess' value='always' />\");\r\n                    document.writeln(\"<param name='quality' value='high' />\");\r\n                    document.writeln(\"<param name='menu' value='false' />\");\r\n                    document.writeln(\"<embed src=\" + url + \" wmode=\" + t + \" width=\" + w + \" height=\" + h + \" name=\" + id + \" bgcolor=\" + bg + \" allowScriptAccess='always' quality='high' type='application/x-shockwave-flash' pluginspage='http://www.macromedia.com/go/getflashplayer' />\");\r\n                    document.writeln(\"</object>\");\r\n                }}\r\n                Flash(\"dext5Obj\", \"{2}\", \"400\", \"400\", \"#ffffff\", \"transparent\");\r\n                windowResize(500, 500)\r\n            </script>\r\n        </td>\r\n    </tr>\r\n</table>", skinName, text5, text6);
					}
					else if (fileExt == "pdf" || fileExt == "htm" || fileExt == "html")
					{
						stringBuilder.AppendFormat("<script type=\"text/javascript\">\r\n    LoadDext5ViewerCss();\r\n</script>\r\n<strong id=\"header\" class=\"skin_{0}\">{1}</strong>\r\n<iframe src=\"{2}\" width=\"100%\" height=\"97%\" scrolling=\"{3}\" frameborder=\"0\"></iframe>", new object[]
						{
							skinName,
							text5,
							text6,
							(fileExt == "pdf") ? "no" : "yes"
						});
					}
					else if (fileExt == "xml" || fileExt == "txt")
					{
						if (!string.IsNullOrEmpty(text3))
						{
							stringBuilder.AppendFormat("<script type='text/javascript'>\r\n    function populateIframe(data) {{\r\n\r\n        var iframe = document.getElementById('dext5Iframe');\r\n        \r\n        var srcUrl = 'document.open();';\r\n        srcUrl += ' document.domain=\"{0}\";';\r\n        srcUrl += ' document.close();';\r\n        srcUrl = 'javascript:void(function(){{' + encodeURIComponent(srcUrl) + '}}())';\r\n        iframe.src = srcUrl;\r\n\r\n        var idoc = iframe.contentDocument || iframe.contentWindow.document;\r\n\r\n        if (dext5uploadBrowser.gecko == true && extension == 'xml') // firefox\r\n            idoc.open('text/html');\r\n        else if (extension == 'xml')\r\n            idoc.open('text/xml');\r\n\r\n        idoc.write(\"<style>body {{ text-align:center; }}</style>\");\r\n        idoc.write(\"<textarea name='data' style='padding:10px; width:99%; height:99%; margin:0 auto;' readonly></textarea>\");\r\n        idoc.getElementsByTagName('textarea')[0].value = data;\r\n\r\n        idoc.close();\r\n    }}\r\n    dext5uploadAjaxText(\"{1}\",\r\n        function (data) {{ populateIframe(data); }}\r\n    );\r\n\r\n    LoadDext5ViewerCss();        \r\n</script>\r\n<strong id='header' class='skin_{2}'>{3}</strong>        \r\n<iframe id='dext5Iframe' width='100%' height='97%' scrolling='no' frameborder='0'></iframe>", new object[]
							{
								text3,
								text6,
								skinName,
								text5
							});
						}
						else
						{
							stringBuilder.AppendFormat("<script type='text/javascript'>\r\n    function populateIframe(data) {{\r\n\r\n        var iframe = document.getElementById('dext5Iframe');\r\n        var idoc = iframe.contentDocument || iframe.contentWindow.document;\r\n\r\n        if (dext5uploadBrowser.gecko == true && extension == 'xml') // firefox\r\n            idoc.open('text/html');\r\n        else if (extension == 'xml')\r\n            idoc.open('text/xml');\r\n\r\n        idoc.write(\"<style>body {{ text-align:center; }}</style>\");\r\n        idoc.write(\"<textarea name='data' style='padding:10px; width:99%; height:99%; margin:0 auto;' readonly></textarea>\");\r\n        idoc.getElementsByTagName('textarea')[0].value = data;\r\n\r\n        idoc.close();\r\n    }}\r\n    dext5uploadAjaxText(\"{0}\",\r\n        function (data) {{ populateIframe(data); }}\r\n    );\r\n\r\n    LoadDext5ViewerCss();        \r\n</script>\r\n<strong id='header' class='skin_{1}'>{2}</strong>        \r\n<iframe id='dext5Iframe' width='100%' height='97%' scrolling='no' frameborder='0'></iframe>", text6, skinName, text5);
						}
					}
					else
					{
						stringBuilder.AppendFormat("<script src='{6}' type='text/javascript'></script>\r\n<script type='text/javascript'>\r\n    LoadDext5ViewerCss();    \r\n</script>\r\n<strong id='header' class='skin_{0}'>{1}</strong>\r\n<p class='loading_box'><span><img src='{2}' alt='로딩 이미지' /></span></p>\r\n<script type='text/javascript'>\r\n    location.href = \"{3}\";\r\n    \r\n    //setTimeout(function () {{\r\n    //    window.close();\r\n    //}}, 5000);    \r\n\r\n    dext5uploadCheckDownloadComplete('{4}', {5}, '{7}');\r\n</script>", new object[]
						{
							skinName,
							text5,
							text,
							text6,
							text7,
							string.IsNullOrEmpty(request["d00"]) ? "false" : "true",
							flag ? (str2 + "?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion) : ("../js_dev/dext5upload.base64.js?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion),
							text8
						});
					}
					response.Write(string.Format("<html>\r\n<head>\r\n    <title>DEXT5 Viewer</title>\r\n    {3}\r\n    <script src='{4}' type='text/javascript'></script>\r\n    <script type='text/javascript'>\r\n        var extension = '{0}';\r\n\r\n        function windowResize(width, height) {{\r\n            window.resizeTo(width, height);\r\n            window.moveTo(((screen.width - width) / 2), ((screen.height - height) / 2));      \r\n\r\n            LoadDext5ViewerCss();\r\n        }}\r\n        function LoadDext5ViewerCss() {{\r\n            var header = document.getElementsByTagName('head')[0];\r\n\r\n            var newCss = document.createElement('link');\r\n            newCss.type = 'text/css';\r\n            newCss.rel = 'stylesheet';\r\n            newCss.href = \"{1}\";\r\n            header.appendChild(newCss);\r\n        }}\r\n    </script>\r\n</head>\r\n<body>\r\n{2}\r\n</body>\r\n</html>", new object[]
					{
						fileExt,
						flag ? (str3 + "?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion) : ("../css_dev/dext5viewer.css?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion),
						stringBuilder.ToString(),
						text4,
						flag ? (str + "?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion) : ("../js_dev/dext5upload.viewer.js?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion)
					}));
				}
				catch (Exception ex)
				{
					response.Write(string.Format("<html>\r\n<head>\r\n    <title>DEXT5 Viewer</title>\r\n    <meta http-equiv='Content-Type' content='text/html; charset=utf-8'>\r\n    <link href='{0}' rel='stylesheet' type='text/css'>\r\n</head>\r\n<body>\r\n<strong id='header' class='skin_blue'>Error</strong>\r\n{1}\r\n</body>\r\n</html>", flag ? (str3 + "?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion) : ("../css_dev/dext5viewer.css?ver=" + Raonwiz.Dext5.Process.Version._UploadServerVersion), ex.Message));
				}
				if (this._b_IsDebug)
				{
					LogUtil.DextDebug("CMD - DEXT5 Viewer Process End", this._str_DebugFilePath);
				}
				return;
			}
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("CMD - DEXT5 Viewer Process Start", this._str_DebugFilePath);
			}
			string text15 = string.Empty;
			string text16 = string.Empty;
			string value2 = request["d00"];
			text15 = HttpUtility.UrlEncode(HttpUtility.UrlDecode(paramEntity.fileVirtualPath));
			text16 = HttpUtility.UrlEncode(HttpUtility.UrlDecode(paramEntity.fileOrgName));
			string text17 = paramEntity.viewerUrl;
			if (string.IsNullOrEmpty(text17))
			{
				text17 = request.Path;
			}
			if (!string.IsNullOrEmpty(value2))
			{
				string text18 = "";
				string text19 = text18;
				text18 = string.Concat(new string[]
				{
					text19,
					"d01",
					Dext5Parameter.trans_unitAttributeDelimiter,
					"openRequest",
					Dext5Parameter.trans_unitDelimiter
				});
				string text20 = text18;
				text18 = string.Concat(new string[]
				{
					text20,
					"d10",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.fileNameRuleEx,
					Dext5Parameter.trans_unitDelimiter
				});
				string text21 = text18;
				text18 = string.Concat(new string[]
				{
					text21,
					"d25",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.fileVirtualPath,
					Dext5Parameter.trans_unitDelimiter
				});
				string text22 = text18;
				text18 = string.Concat(new string[]
				{
					text22,
					"d26",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.fileOrgName,
					Dext5Parameter.trans_unitDelimiter
				});
				string text23 = text18;
				text18 = string.Concat(new string[]
				{
					text23,
					"d31",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.skinName,
					Dext5Parameter.trans_unitDelimiter
				});
				string text24 = text18;
				text18 = string.Concat(new string[]
				{
					text24,
					"d41",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.dext5Release,
					Dext5Parameter.trans_unitDelimiter
				});
				string text25 = text18;
				text18 = string.Concat(new string[]
				{
					text25,
					"d42",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.downloadUrl,
					Dext5Parameter.trans_unitDelimiter
				});
				string text26 = text18;
				text18 = string.Concat(new string[]
				{
					text26,
					"d43",
					Dext5Parameter.trans_unitAttributeDelimiter,
					paramEntity.fileExt,
					Dext5Parameter.trans_unitDelimiter
				});
				string text27 = text18;
				text18 = string.Concat(new string[]
				{
					text27,
					"d44",
					Dext5Parameter.trans_unitAttributeDelimiter,
					text3,
					Dext5Parameter.trans_unitDelimiter
				});
				text18 = Dext5Parameter.MakeParameter(text18);
				stringBuilder.AppendFormat("<html>\r\n    <head>\r\n        <title>DEXT5 Viewer</title>\r\n        <meta http-equiv='Content-Type' content='text/html; charset=utf-8'>\r\n        {3}\r\n        <script>\r\n            function FormSubmit() {{\r\n                var dext5Form = document.getElementById('dext5Form');\r\n                dext5Form.submit();\r\n            }}\r\n        </script>\r\n    </head>\r\n    <body onload='FormSubmit();'>\r\n        <form id='dext5Form' method='post' target='_self' action=\"{0}\">\r\n            <input type='hidden' name='d00' value=\"{1}\" />\r\n            <input type='hidden' name='customValue' value=\"{2}\" />\r\n        </form>\r\n    </body>\r\n</html>", new object[]
				{
					text17,
					text18,
					HttpUtility.UrlEncode(HttpUtility.UrlDecode(request["customValue"])),
					text4,
					paramEntity.fileNameRuleEx,
					paramEntity.fileVirtualPath,
					paramEntity.fileOrgName,
					request["customValue"]
				});
			}
			else
			{
				stringBuilder.AppendFormat("<html>\r\n    <head>\r\n        <title>DEXT5 Viewer</title>\r\n        <meta http-equiv='Content-Type' content='text/html; charset=utf-8'>\r\n        {9}\r\n        <script>\r\n            function FormSubmit() {{\r\n                var dext5Form = document.getElementById('dext5Form');\r\n                dext5Form.submit();\r\n            }}\r\n        </script>\r\n    </head>\r\n    <body onload='FormSubmit();'>\r\n        <form id='dext5Form' method='post' target='_self' action=\"{0}\">\r\n            <input type='hidden' name='dext5CMD' value=\"openRequest\" />\r\n            <input type='hidden' name='fileNameRuleEx' value=\"{1}\" />\r\n            <input type='hidden' name='fileVirtualPath' value=\"{2}\" />\r\n            <input type='hidden' name='fileOrgName' value=\"{3}\" />\r\n            <input type='hidden' name='customValue' value=\"{4}\" />\r\n            <input type='hidden' name='skinName' value=\"{5}\" />\r\n            <input type='hidden' name='dext5Release' value=\"{6}\" />\r\n            <input type='hidden' name='downloadUrl' value=\"{7}\" />\r\n            <input type='hidden' name='fileExt' value=\"{8}\" />\r\n            <input type='hidden' name='documentDomain' value=\"{10}\" />\r\n        </form>\r\n    </body>\r\n</html>", new object[]
				{
					text17,
					paramEntity.fileNameRuleEx,
					text15,
					text16,
					HttpUtility.UrlEncode(HttpUtility.UrlDecode(request["customValue"])),
					paramEntity.skinName,
					paramEntity.dext5Release,
					HttpUtility.UrlDecode(paramEntity.downloadUrl),
					paramEntity.fileExt,
					text4,
					text3
				});
			}
			response.Clear();
			response.Write(stringBuilder.ToString());
		}

		// Token: 0x06000393 RID: 915 RVA: 0x0002930C File Offset: 0x0002750C
		public void ProcessCustomDownload(HttpContext context, List<string> downloadList)
		{
			string userAgent = context.Request.UserAgent;
			if (string.IsNullOrEmpty(userAgent))
			{
				this.ProcessCustomDownloadWeb(context, downloadList);
				return;
			}
			if (userAgent.ToLower().IndexOf("dext5") > -1)
			{
				this.ProcessCustomDownloadPlugin(context, downloadList);
				return;
			}
			this.ProcessCustomDownloadWeb(context, downloadList);
		}

		// Token: 0x06000394 RID: 916 RVA: 0x0002935C File Offset: 0x0002755C
		public void ProcessCustomDownload(HttpContext context, List<Stream> downloadList)
		{
			string userAgent = context.Request.UserAgent;
			if (string.IsNullOrEmpty(userAgent))
			{
				this.ProcessCustomDownloadWeb(context, downloadList);
				return;
			}
			if (userAgent.ToLower().IndexOf("dext5") > -1)
			{
				this.ProcessCustomDownloadPlugin(context, downloadList);
				return;
			}
			this.ProcessCustomDownloadWeb(context, downloadList);
		}

		// Token: 0x06000395 RID: 917 RVA: 0x000293AC File Offset: 0x000275AC
		private void ProcessCustomDownloadWeb(HttpContext context, List<string> downloadList)
		{
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			string dext5CMD = paramEntity.dext5CMD;
			if (dext5CMD == "mzf")
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				MakeZip makeZip = new MakeZip(context, this._str_tempPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, "", this._str_allowExtensionSpecialSymbol);
				makeZip.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				makeZip.SetDextParam(paramEntity);
				object obj = makeZip.Run2(this.UploadCompleteBeforeEvent, this.UploadCompleteEvent, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError, downloadList);
				if (context.Response.StatusCode == 200 && obj != null && (obj.GetType() != typeof(string) || !((string)obj).StartsWith("error")))
				{
					this.DownloadEvent(context, obj);
					return;
				}
			}
			else
			{
				if (dext5CMD == "zs")
				{
					Base @base = new ZipStatus(context, this._str_tempPath);
					@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					@base.SetDextParam(paramEntity);
					@base.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					return;
				}
				if (dext5CMD == "mdr")
				{
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileWhiteList = this._str_fileWhiteList_download;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileBlackList = this._str_fileBlackList_download;
					}
					MultiDownload multiDownload = new MultiDownload(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
					multiDownload.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					multiDownload.SetDextParam(paramEntity);
					object obj2 = multiDownload.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					if (context.Response.StatusCode == 200 && obj2 != null && (obj2.GetType() != typeof(string) || !((string)obj2).StartsWith("error")))
					{
						this.DownloadEvent(context, obj2);
						return;
					}
				}
				else
				{
					if (dext5CMD == "ofc")
					{
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						Base base2 = new OpenFileCheck(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
						base2.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						base2.SetDextParam(paramEntity);
						base2.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
						return;
					}
					if (dext5CMD == "vdc")
					{
						Base base3 = new ViewerDownloadComplete(context);
						base3.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						base3.SetDextParam(paramEntity);
						base3.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
						return;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileWhiteList = this._str_fileWhiteList_download;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileBlackList = this._str_fileBlackList_download;
					}
					Base base4 = new CustomDownload(context, this._str_tempPath, this._str_zipFileName, "ProcessCustomDownload", downloadList, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
					base4.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					base4.SetDextParam(paramEntity);
					object obj3 = base4.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					if (context.Response.ContentType != "text/html" && (dext5CMD == "downloadRequest" || dext5CMD == "openRequest") && string.IsNullOrEmpty(paramEntity.zipFileName) && context.Response.StatusCode == 200 && obj3 != null && (obj3.GetType() != typeof(string) || !((string)obj3).StartsWith("error")))
					{
						this.DownloadEvent(context, obj3);
					}
				}
			}
		}

		// Token: 0x06000396 RID: 918 RVA: 0x00029970 File Offset: 0x00027B70
		private void ProcessCustomDownloadWeb(HttpContext context, List<Stream> downloadList)
		{
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			string dext5CMD = paramEntity.dext5CMD;
			if (dext5CMD == "mzf")
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				MakeZip makeZip = new MakeZip(context, this._str_tempPath, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, "", this._str_allowExtensionSpecialSymbol);
				makeZip.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				makeZip.SetDextParam(paramEntity);
				object obj = makeZip.Run2(this.UploadCompleteBeforeEvent, this.UploadCompleteEvent, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError, downloadList);
				if (context.Response.StatusCode == 200 && obj != null && (obj.GetType() != typeof(string) || !((string)obj).StartsWith("error")))
				{
					this.DownloadEvent(context, obj);
					return;
				}
			}
			else
			{
				if (dext5CMD == "zs")
				{
					Base @base = new ZipStatus(context, this._str_tempPath);
					@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					@base.SetDextParam(paramEntity);
					@base.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					return;
				}
				if (dext5CMD == "mdr")
				{
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileWhiteList = this._str_fileWhiteList_download;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileBlackList = this._str_fileBlackList_download;
					}
					MultiDownload multiDownload = new MultiDownload(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
					multiDownload.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					multiDownload.SetDextParam(paramEntity);
					object obj2 = multiDownload.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					if (context.Response.StatusCode == 200 && obj2 != null && (obj2.GetType() != typeof(string) || !((string)obj2).StartsWith("error")))
					{
						this.DownloadEvent(context, obj2);
						return;
					}
				}
				else
				{
					if (dext5CMD == "ofc")
					{
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileWhiteList = this._str_fileWhiteList_download;
						}
						if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
						{
							this._str_fileBlackList = this._str_fileBlackList_download;
						}
						Base base2 = new OpenFileCheck(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
						base2.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						base2.SetDextParam(paramEntity);
						base2.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
						return;
					}
					if (dext5CMD == "vdc")
					{
						Base base3 = new ViewerDownloadComplete(context);
						base3.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
						base3.SetDextParam(paramEntity);
						base3.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
						return;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileWhiteList = this._str_fileWhiteList_download;
					}
					if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
					{
						this._str_fileBlackList = this._str_fileBlackList_download;
					}
					Base base4 = new CustomDownload(context, this._str_tempPath, this._str_zipFileName, "ProcessCustomDownload", downloadList, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
					base4.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
					base4.SetDextParam(paramEntity);
					object obj3 = base4.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
					if (context.Response.ContentType != "text/html" && (dext5CMD == "downloadRequest" || dext5CMD == "openRequest") && string.IsNullOrEmpty(paramEntity.zipFileName) && context.Response.StatusCode == 200 && obj3 != null && (obj3.GetType() != typeof(string) || !((string)obj3).StartsWith("error")))
					{
						this.DownloadEvent(context, obj3);
					}
				}
			}
		}

		// Token: 0x06000397 RID: 919 RVA: 0x00029F34 File Offset: 0x00028134
		private void ProcessCustomDownloadPlugin(HttpContext context, List<string> downloadList)
		{
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			string dext5CMD = paramEntity.dext5CMD;
			if (dext5CMD == "mdr")
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				MultiDownload multiDownload = new MultiDownload(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
				multiDownload.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				multiDownload.SetDextParam(paramEntity);
				object obj = multiDownload.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
				if (context.Response.StatusCode == 200 && obj != null && (obj.GetType() != typeof(string) || !((string)obj).StartsWith("error")))
				{
					this.DownloadEvent(context, obj);
					return;
				}
			}
			else
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				Base @base = new CustomDownload(context, this._str_tempPath, this._str_zipFileName, "ProcessCustomDownloadPlugin", downloadList, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
				@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				@base.SetDextParam(paramEntity);
				object obj2 = @base.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
				if (context.Response.ContentType != "text/html" && context.Response.StatusCode == 200 && obj2 != null && (obj2.GetType() != typeof(string) || !((string)obj2).StartsWith("error")))
				{
					this.DownloadEvent(context, obj2);
				}
			}
		}

		// Token: 0x06000398 RID: 920 RVA: 0x0002A1D8 File Offset: 0x000283D8
		private void ProcessCustomDownloadPlugin(HttpContext context, List<Stream> downloadList)
		{
			ParamEntity paramEntity = new ParamEntity(context);
			try
			{
				Dext5Parameter.SetParameter(context, paramEntity);
			}
			catch (Exception)
			{
				return;
			}
			string dext5CMD = paramEntity.dext5CMD;
			if (dext5CMD == "mdr")
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				MultiDownload multiDownload = new MultiDownload(context, this._str_tempPath, this._str_zipFileName, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, downloadList, this._str_allowExtensionSpecialSymbol);
				multiDownload.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				multiDownload.SetDextParam(paramEntity);
				object obj = multiDownload.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
				if (context.Response.StatusCode == 200 && obj != null && (obj.GetType() != typeof(string) || !((string)obj).StartsWith("error")))
				{
					this.DownloadEvent(context, obj);
					return;
				}
			}
			else
			{
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileWhiteList = this._str_fileWhiteList_download;
				}
				if (string.IsNullOrEmpty(this._str_fileWhiteList) && string.IsNullOrEmpty(this._str_fileBlackList))
				{
					this._str_fileBlackList = this._str_fileBlackList_download;
				}
				Base @base = new CustomDownload(context, this._str_tempPath, this._str_zipFileName, "ProcessCustomDownloadPlugin", downloadList, this._str_fileWhiteList, this._str_fileBlackList, this._strary_fileBlackWordList, this._str_allowExtensionSpecialSymbol);
				@base.SetNetworkCredentials(this._str_Credentials_Method, this._str_Credentials_UserID, this._str_Credentials_UserPassword, this._str_Credentials_Domain);
				@base.SetDextParam(paramEntity);
				object obj2 = @base.Run(this.UploadBeforeInitializeEvent, this.UploadBeforeInitializeEventEx, this.UploadCompleteBeforeEvent, this.UploadCompleteBeforeEventEx, this.UploadCompleteEvent, this.UploadCompleteEventEx, this.OpenDownloadBeforeInitializeEventEx, ref this._CustomError);
				if (context.Response.ContentType != "text/html" && context.Response.StatusCode == 200 && obj2 != null && (obj2.GetType() != typeof(string) || !((string)obj2).StartsWith("error")))
				{
					this.DownloadEvent(context, obj2);
				}
			}
		}

		// Token: 0x06000399 RID: 921 RVA: 0x0002A47C File Offset: 0x0002867C
		public string[] GetRequestValue(HttpContext context, string keyName)
		{
			string[] array = null;
			string value = context.Request[keyName];
			if (!string.IsNullOrEmpty(value))
			{
				array = context.Request.Form.GetValues(keyName);
				if (array == null)
				{
					array = context.Request.QueryString.GetValues(keyName);
				}
			}
			if (array == null && !string.IsNullOrEmpty(context.Request["d00"]) && !keyName.StartsWith("customValue"))
			{
				ParamEntity paramEntity = new ParamEntity(context);
				try
				{
					Dext5Parameter.SetParameter(context, paramEntity);
					Type type = paramEntity.GetType();
					PropertyInfo property = type.GetProperty(keyName);
					if (property == null)
					{
						return null;
					}
					object value2 = property.GetValue(paramEntity, null);
					string[] array2 = value2.ToString().Split(new string[]
					{
						"|"
					}, StringSplitOptions.RemoveEmptyEntries);
					array = array2;
				}
				catch
				{
					array = null;
				}
				return array;
			}
			return array;
		}

		// Token: 0x0600039A RID: 922 RVA: 0x0002A564 File Offset: 0x00028764
		private bool CheckCaller(HttpContext context, string mode)
		{
			bool result = false;
			if (mode == "html5")
			{
				string text = string.Empty;
				string text2 = string.Empty;
				try
				{
					text = context.Request.UrlReferrer.LocalPath;
					text2 = context.Request.UserAgent;
				}
				catch
				{
					try
					{
						text2 = context.Request.UserAgent;
						if (text2.IndexOf("Mozilla") > -1 || text2.IndexOf("AppleWebKit") > -1)
						{
							text = "hybird";
						}
					}
					catch
					{
					}
				}
				if (!string.IsNullOrEmpty(text))
				{
					if (text.ToLower().IndexOf("dext5upload.uploadchunk.min.js") > -1)
					{
						result = true;
					}
					else if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(text2))
					{
						result = true;
					}
				}
			}
			else if (mode == "html4")
			{
				string value = string.Empty;
				string text3 = string.Empty;
				try
				{
					value = context.Request.UrlReferrer.LocalPath;
					text3 = context.Request.UserAgent;
				}
				catch
				{
					try
					{
						text3 = context.Request.UserAgent;
						if (text3.IndexOf("Mozilla") > -1 || text3.IndexOf("AppleWebKit") > -1)
						{
							value = "hybird";
						}
					}
					catch
					{
					}
				}
				if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(text3))
				{
					result = true;
				}
			}
			else if (mode == "ieplugin")
			{
				string userAgent = context.Request.UserAgent;
				if (!string.IsNullOrEmpty(userAgent) && userAgent.ToLower().IndexOf("dext5") > -1)
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x0600039B RID: 923 RVA: 0x0002A71C File Offset: 0x0002891C
		public string GetCustomErrorMessage(HttpContext context, string sCustomErrorCode, string sCustomErrorMessage)
		{
			string str = string.Empty;
			string text = context.Request["dext5CMD"];
			if (this._b_IsDebug)
			{
				LogUtil.DextDebug("Error - code : " + sCustomErrorCode + " message - " + sCustomErrorMessage, this._str_DebugFilePath);
			}
			if (this.CheckCaller(context, "ieplugin") || (!string.IsNullOrEmpty(text) && (text == "downloadRequest" || text == "mdr" || text == "mzf" || text == "openRequest")))
			{
				str = "[FAIL]";
			}
			return str + Dext5Parameter.MakeParameter("error|" + sCustomErrorCode + "|" + sCustomErrorMessage);
		}

		// Token: 0x0600039C RID: 924 RVA: 0x0002A7D2 File Offset: 0x000289D2
		public void Dispose()
		{
			this.Dispose(true);
			GC.SuppressFinalize(this);
		}

		// Token: 0x0600039D RID: 925 RVA: 0x0002A7E1 File Offset: 0x000289E1
		protected virtual void Dispose(bool disposing)
		{
			if (this.disposed)
			{
				return;
			}
			this.disposed = true;
		}

		// Token: 0x040001D3 RID: 467
		private bool disposed;

		// Token: 0x040001D4 RID: 468
		private char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x040001D5 RID: 469
		private bool _b_IsDebug;

		// Token: 0x040001D6 RID: 470
		private string _str_DebugFilePath = string.Empty;

		// Token: 0x040001D7 RID: 471
		private int _i_ScriptTimeout = 30000000;

		// Token: 0x040001D8 RID: 472
		private string _str_licenseKeyPath = string.Empty;

		// Token: 0x040001D9 RID: 473
		private string _str_tempPath = string.Empty;

		// Token: 0x040001DA RID: 474
		private string _str_virtualPath = string.Empty;

		// Token: 0x040001DB RID: 475
		private string _str_physicalPath = string.Empty;

		// Token: 0x040001DC RID: 476
		private string _str_ConfigPhysicalPath = string.Empty;

		// Token: 0x040001DD RID: 477
		private bool _b_downloadVirtualPath = true;

		// Token: 0x040001DE RID: 478
		private string _str_zipFileName = "download.zip";

		// Token: 0x040001DF RID: 479
		private int _i_garbageCleanDay = 2;

		// Token: 0x040001E0 RID: 480
		private string _str_fileBlackList = string.Empty;

		// Token: 0x040001E1 RID: 481
		private string _str_fileWhiteList = string.Empty;

		// Token: 0x040001E2 RID: 482
		private string[] _strary_fileBlackWordList;

		// Token: 0x040001E3 RID: 483
		private string _str_Credentials_Method = "impersonate";

		// Token: 0x040001E4 RID: 484
		private string _str_Credentials_UserID = string.Empty;

		// Token: 0x040001E5 RID: 485
		private string _str_Credentials_UserPassword = string.Empty;

		// Token: 0x040001E6 RID: 486
		private string _str_Credentials_Domain = string.Empty;

		// Token: 0x040001E7 RID: 487
		private string _str_viewerJsPath = string.Empty;

		// Token: 0x040001E8 RID: 488
		private string _str_viewerJs2Path = string.Empty;

		// Token: 0x040001E9 RID: 489
		private string _str_viewerCssPath = string.Empty;

		// Token: 0x040001EA RID: 490
		private string _str_viewerLoadingImagePath = string.Empty;

		// Token: 0x040001EB RID: 491
		private string _str_viewerBrokenImagePath = string.Empty;

		// Token: 0x040001EC RID: 492
		private string _str_fileBlackList_upload = string.Empty;

		// Token: 0x040001ED RID: 493
		private string _str_fileWhiteList_upload = string.Empty;

		// Token: 0x040001EE RID: 494
		private string _str_fileBlackList_download = string.Empty;

		// Token: 0x040001EF RID: 495
		private string _str_fileWhiteList_download = string.Empty;

		// Token: 0x040001F0 RID: 496
		private bool _b_externalWebFileDirectDownload = true;

		// Token: 0x040001F1 RID: 497
		private bool _b_useExternalDownload = true;

		// Token: 0x040001F2 RID: 498
		private string _str_enableZip64 = "asnecessary";

		// Token: 0x040001F3 RID: 499
		private bool _b_html5SliceAppend = true;

		// Token: 0x040001F4 RID: 500
		private string _str_downloadRootPath = string.Empty;

		// Token: 0x040001F5 RID: 501
		private string _str_allowExtensionSpecialSymbol = string.Empty;

		// Token: 0x040001F6 RID: 502
		private Dext5CustomError _CustomError;
	}
}

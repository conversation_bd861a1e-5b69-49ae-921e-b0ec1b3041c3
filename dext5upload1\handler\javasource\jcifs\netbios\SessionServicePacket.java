package jcifs.netbios;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/SessionServicePacket.class */
public abstract class SessionServicePacket {
    static final int SESSION_MESSAGE = 0;
    static final int SESSION_REQUEST = 129;
    public static final int POSITIVE_SESSION_RESPONSE = 130;
    public static final int NEGATIVE_SESSION_RESPONSE = 131;
    static final int SESSION_RETARGET_RESPONSE = 132;
    static final int SESSION_KEEP_ALIVE = 133;
    static final int MAX_MESSAGE_SIZE = 131071;
    static final int HEADER_LENGTH = 4;
    int type;
    int length;

    abstract int writeTrailerWireFormat(byte[] bArr, int i);

    abstract int readTrailerWireFormat(InputStream inputStream, byte[] bArr, int i) throws IOException;

    static void writeInt2(int val, byte[] dst, int dstIndex) {
        dst[dstIndex] = (byte) ((val >> 8) & 255);
        dst[dstIndex + 1] = (byte) (val & 255);
    }

    static void writeInt4(int val, byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = (byte) ((val >> 24) & 255);
        int dstIndex3 = dstIndex2 + 1;
        dst[dstIndex2] = (byte) ((val >> 16) & 255);
        dst[dstIndex3] = (byte) ((val >> 8) & 255);
        dst[dstIndex3 + 1] = (byte) (val & 255);
    }

    static int readInt2(byte[] src, int srcIndex) {
        return ((src[srcIndex] & 255) << 8) + (src[srcIndex + 1] & 255);
    }

    static int readInt4(byte[] src, int srcIndex) {
        return ((src[srcIndex] & 255) << 24) + ((src[srcIndex + 1] & 255) << 16) + ((src[srcIndex + 2] & 255) << 8) + (src[srcIndex + 3] & 255);
    }

    static int readLength(byte[] src, int srcIndex) {
        int srcIndex2 = srcIndex + 1;
        int srcIndex3 = srcIndex2 + 1;
        int i = (src[srcIndex2] & 1) << 16;
        int srcIndex4 = srcIndex3 + 1;
        int i2 = i + ((src[srcIndex3] & 255) << 8);
        int i3 = srcIndex4 + 1;
        return i2 + (src[srcIndex4] & 255);
    }

    static int readn(InputStream in, byte[] b, int off, int len) throws IOException {
        int i;
        int n;
        int i2 = 0;
        while (true) {
            i = i2;
            if (i >= len || (n = in.read(b, off + i, len - i)) <= 0) {
                break;
            }
            i2 = i + n;
        }
        return i;
    }

    static int readPacketType(InputStream in, byte[] buffer, int bufferIndex) throws IOException {
        int n = readn(in, buffer, bufferIndex, 4);
        if (n != 4) {
            if (n == -1) {
                return -1;
            }
            throw new IOException("unexpected EOF reading netbios session header");
        }
        int t = buffer[bufferIndex] & 255;
        return t;
    }

    public int writeWireFormat(byte[] dst, int dstIndex) {
        this.length = writeTrailerWireFormat(dst, dstIndex + 4);
        writeHeaderWireFormat(dst, dstIndex);
        return 4 + this.length;
    }

    int readWireFormat(InputStream in, byte[] buffer, int bufferIndex) throws IOException {
        readHeaderWireFormat(in, buffer, bufferIndex);
        return 4 + readTrailerWireFormat(in, buffer, bufferIndex);
    }

    int writeHeaderWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = (byte) this.type;
        if (this.length > 65535) {
            dst[dstIndex2] = 1;
        }
        writeInt2(this.length, dst, dstIndex2 + 1);
        return 4;
    }

    int readHeaderWireFormat(InputStream in, byte[] buffer, int bufferIndex) throws IOException {
        int bufferIndex2 = bufferIndex + 1;
        this.type = buffer[bufferIndex] & 255;
        this.length = ((buffer[bufferIndex2] & 1) << 16) + readInt2(buffer, bufferIndex2 + 1);
        return 4;
    }
}

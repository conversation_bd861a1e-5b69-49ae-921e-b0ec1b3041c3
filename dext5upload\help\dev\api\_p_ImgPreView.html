﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: ImgPreView</h3>
    <p class="ttl">config.ImgPreView</p>
    <p class="txt">
        이미지 미리보기 창 기능을 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0"이고 사용안함, "1"로 설정시 이미지 미리보기 창 사용입니다.<br/><br />
        <span class="txt">
            <span class="firebrick">ImgPreViewWidth</span>&nbsp;&nbsp;이미지 미리보기 창의 넓이를 설정합니다.<br />
            <span style="padding-left:104px">기본값은 "320px" 입니다.</span><br />
            <span style="padding-left:104px">넓이는 "px"(픽셀)만 가능합니다.</span>
        </span><br /><br />
        <span class="txt">
            <span class="firebrick">ImgPreViewHeight</span>&nbsp;&nbsp;이미지 미리보기 창의 높이를 설정합니다.<br />
            <span style="padding-left:107px">기본값은 "280px" 입니다.</span><br />
            <span style="padding-left:107px">높이는 "px"(픽셀)만 가능합니다.</span>
        </span><br />   
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 이미지 미리보기 창 사용으로 설정합니다.
        DEXT5UPLOAD.config.ImgPreView = '1';
        
        // 이미지 미리보기 창 넓이를 250px 로 설정합니다.
        DEXT5UPLOAD.config.ImgPreViewWidth = '250px';

        // 이미지 미리보기 창 넓이를 200px 로 설정합니다.
        DEXT5UPLOAD.config.ImgPreViewHeight = '200px';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


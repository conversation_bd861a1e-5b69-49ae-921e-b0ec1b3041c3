a.cheditor-tag-path-elem {
    text-decoration: none;
    color: #0033cc;
    font-size: 8pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", dotum, monospace;
    cursor: pointer;
}
a.cheditor-tag-path-elem:hover {
    color: #0033cc;
    text-decoration: underline;
    cursor: pointer;
}
.cheditor-container {
    border-top: 1px #ccc solid;
    position: relative;
    text-align: left;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
.cheditor-tb-wrapper {
    border-right: 1px #ccc solid;
    border-left: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    position: relative;
    display: block;
    -webkit-box-sizing: inherit;
    box-sizing: inherit;
    background-color: #f0f0f0;
}
.cheditor-tb-wrapper-readonly {
    border-right: 1px #ccc solid;
    border-left: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    position: relative;
    display: block;
    background: #f0f0f0 url(../icons/readonlymode.png) no-repeat 10px center;
}
.cheditor-tb-wrapper-code {
    border-right: 1px #ccc solid;
    border-left: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    position: relative;
    display: block;
    background: #f0f0f0 url(../icons/viewmode_code.png) no-repeat 10px center;
}
.cheditor-tb-wrapper-preview {
    border-right: 1px #ccc solid;
    border-left: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    position: relative;
    display: block;
    background: #f0f0f0 url(../icons/viewmode_preview.png) no-repeat 10px center;
}
.cheditor-tb-wrapper-readonly div, .cheditor-tb-wrapper-code div, .cheditor-tb-wrapper-preview div {
    display: none;
}
.cheditor-tb-fullscreen {
    width: 16px;
    height: 16px;
    float: right;
    margin-top: 3px;
    cursor: pointer;
    background: transparent url(../icons/fullscreen.png) no-repeat center center;
}
.cheditor-tb-fullscreen-disable {
    display: none;
}
.cheditor-tb-fullscreen-actual {
    width: 16px;
    height: 16px;
    float: right;
    margin-top: 3px;
    cursor: pointer;
    background: transparent url(../icons/fullscreen_actual.png) no-repeat center center;
}
.cheditor-editarea-wrapper {
    border-right: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    border-left: 1px #ccc solid;
    width: auto;
    overflow: hidden;
}
.cheditor-editarea {
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    display: block;
}
@font-face {
    font-family: 'SourceCodePro';
    src: url('SourceCodePro.eot');
    src: url('SourceCodePro.woff') format('woff');
}
.cheditor-editarea-text-content {
    overflow-x: hidden;
    overflow-y: scroll;
    margin: 0;
    border: 1px solid transparent;
    padding: 7px 10px;
    display: none;
    resize: none;
    outline: none;
    font-family: SourceCodePro, monospace;
    font-size: 12px;
}
.cheditor-modify-block .cheditor-ico {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
}
.cheditor-modify-block select {
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    font-size: 9pt;
    color: #000;
}
.cheditor-modify-block div {
    padding: 5px 10px 5px 10px;
    color: #000;
    display: block;
}
.cheditor-modify-block div .wrap-text-desc {
    line-height: 1em;
    color: #000;
}
.cheditor-modify-block div .user-input-alt {
    width: 120px;
    margin: 3px 10px 0 5px;
    height: 15px;
    line-height: 15px;
    padding-top: 1px;
    font-size: 9pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    color: #000;
}
.cheditor-modify-block div .user-input-caption {
    width: 350px;
    margin: 7px 10px 0 5px;
    height: 15px;
    line-height: 15px;
    padding-top: 1px;
    font-size: 9pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    color: #000;
}
.cheditor-modify-block div .caption-align {
    margin: 7px 0 0 4px;
    font-size: 9pt;
    vertical-align: top;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    color: #000;
}
.cheditor-modify-block div .wrap-checked {
    vertical-align: middle;
    padding: 0;
    margin-right: 2px;
}
.cheditor-modify-block div .input-submit {
    cursor: pointer;
    vertical-align: middle;
    margin-top: -2px;
    margin-left: 10px;
    height: 20px;
    width: 64px;
}
.cheditor-modify-block div .color-picker {
    cursor: pointer;
    vertical-align: middle;
    height: 20px;
    width: 20px;
}
.cheditor-modify-block div .delete-submit {
    cursor: pointer;
    vertical-align: middle;
    margin-top: -2px;
    margin-left: 3px;
    height: 20px;
    width: 64px;
}
.cheditor-modify-block div .edit-table-ico {
    cursor: pointer;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    margin: 0 3px;
}
.cheditor-modify-block {
    display: none;
    border-right: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    border-left: 1px #ccc solid;
    padding: 2px;
    background-color: #eee;
    font-size: 9pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    text-align: center;
}
.cheditor-status-bar {
    font-size: 8pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", dotum, monospace;
    color: #333;
}
.cheditor-tag-path {
    border-right: 1px #ccc solid;
    border-bottom: 1px #ccc solid;
    border-left: 1px #ccc solid;
    padding: 0 2px 0 2px;
    display: none;
    height: 18px;
    line-height: 18px;
    overflow: hidden;
}
.cheditor-viewmode {
    padding: 0 4px 0 4px;
    height: 16px;
    background: transparent url(../icons/statusbar_bgline.gif) repeat-x 0 0;
}
.cheditor-viewmode div {
    width: 24px;
    height: 16px;
    cursor: pointer;
}
.cheditor-tab-rich {
    background: transparent url(../icons/edit_mode_rich_a.png) no-repeat 0 0;
    float: left;
}
.cheditor-tab-rich-off {
    background: transparent url(../icons/edit_mode_rich_b.png) no-repeat 0 0;
    float: left;
}
.cheditor-tab-code {
    background: transparent url(../icons/edit_mode_code_a.png) no-repeat 0 0;
    float: left;
}
.cheditor-tab-code-off {
    background: transparent url(../icons/edit_mode_code_b.png) no-repeat 0 0;
    float: left;
}
.cheditor-tab-preview {
    background: transparent url(../icons/edit_mode_view_a.png) no-repeat 0 0;
    float: left;
}
.cheditor-tab-preview-off {
    background: transparent url(../icons/edit_mode_view_b.png) no-repeat 0 0;
    float: left;
}
.cheditor-resizebar {
    height: 11px;
    overflow: hidden;
    border-left: 1px #ccc solid;
    border-right: 1px #ccc solid;
    cursor: s-resize;
    background: #eee url(../icons/splitter.gif) no-repeat center top;
}
.cheditor-resizebar-off {
    height: 11px;
    overflow: hidden;
    border-left: 1px #ccc solid;
    border-right: 1px #ccc solid;
    cursor: default;
    background-color: #eee;
}
.cheditor_mode_icon { width: 24px; height: 16px; cursor: pointer; vertical-align: top }
.cheditor-pulldown-container {
    border: #7d8db0 1px solid;
    background-color: #fff;
    padding: 1px;
    position: relative;
    -moz-box-shadow: 0 0 5px #aaa;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 1px 1px 10px #bbb;
}
.cheditor-pulldown-container div {
    padding: 2px 2px 2px 15px;
    font-size: 9pt;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    color: #222;
    position: relative;
    display: block;
    line-height: 1.2;
    margin: 2px 2px;
}
.cheditor-pulldown-color-container {
    border: #7d8db0 1px solid;
    -moz-box-shadow: 0 0 5px #aaa;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 1px 1px 10px #bbb;
    background-color: #fff;
    padding: 2px;
}
.cheditor-pulldown-container div label, .cheditor-pulldown-textblock-container div label {
    display: block;
}
.cheditor-pulldown-textblock-container div div {
    text-align: center;
    padding: 2px;
    font-size: 9pt;
    color: #000;
    line-height: 1.2;
}
.cheditor-pulldown-textblock-container div {
    padding: 1px;
}
.cheditor-pulldown-textblock-out {
    border: #fff 1px solid;
}
.cheditor-pulldown-textblock-over {
    border: #ccc 1px solid;
}
.cheditor-pulldown-textblock-container {
    border: #7d8db0 1px solid;
    -moz-box-shadow: 0 0 5px #aaa;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 1px 1px 10px #bbb;
    background-color: #fff;
    padding: 2px;
}
.cheditor-pulldown-mouseout {
    border: #fff 1px solid;
}
.cheditor-pulldown-mouseover {
    background-color: #f0f0f0;
    border: #e0e0e0 1px solid;
}
.cheditor-pulldown-frame {
    position: absolute;
    visibility: hidden;
    z-index: -1;
    width: 1px;
    height: 1px;
    line-height: 12px;
}
.cheditor-pulldown-color-cell-over, .cheditor-pulldown-color-cell {
    float: left;
    width: 14px;
    height: 14px;
    margin: 1px;
    cursor: pointer;
}
.cheditor-pulldown-color-cell-over {
    border: 1px #000 solid;
}
.cheditor-pulldown-color-cell-over span {
    width: 12px;
    height: 12px;
    border: 1px #fff solid;
    display: block;
}
.cheditor-pulldown-color-cell {
    border: 1px #999 solid;
}
.cheditor-pulldown-color-cell span {
    width: 14px;
    height: 14px;
    display: block;
    border: none;
}
.cheditor-pulldown-color-selected {
    border: 1px solid #999;
    text-align: left;
    margin: 4px 0 0 1px;
    padding-left: 5px;
    height: 15px;
    line-height: 15px;
    font-size: 11px;
    font-family: verdana, monospace;
    width: 55px;
    vertical-align: -10%;
}
.cheditor-pulldown-color-reset, .cheditor-pulldown-color-show-picker {
    height: 19px;
    width: 16px;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    margin-left: 3px;
}
.cheditor-pulldown-color-reset {
    background: #fff url(../icons/color_picker_reset.png) no-repeat center center;
}
.cheditor-pulldown-color-show-picker {
    background: #fff url(../icons/color_picker.png) no-repeat center center;
}
.cheditor-pulldown-color-submit {
    vertical-align: middle;
    margin: 4px 1px 0 0;
    height: 19px;
    width: 40px;
    cursor: pointer;
    right: 0;
    position: absolute;
}
.cheditor-container-fullscreen {
    position: fixed;
    left: 0;
	top: 0;
	_position: absolute;
    z-index: 1000;
    text-align: left;
    background-color: #fff;
}
.cheditor-popupModalBackground {
	background-color: #fff;
    position: fixed;
    _position: absolute;
	display: none;
	top: 0;
	left: 0;
	width: 100%;
    height: 100%;
}
.cheditor-popup-window {
    border: 1px solid #0078D7;
    border-radius: 5px;
    background-color: #0078D7;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    -moz-box-shadow: 0 0 5px #aaa;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 0 0 10px #bbb;
    padding: 0;
    overflow: hidden;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
.cheditor-popup-cmd-button {
    width: 64px;
    height: 22px;
    margin: 5px 2px;
    cursor: pointer;
    vertical-align: middle;
}
.cheditor-popup-cframe {
    background-color: #fff;
    margin: 0;
    padding: 10px;
    border: none;
    text-align: center;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}

.cheditor-popup-cframe iframe {
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.cheditor-popup-drag-handle {
    height: 31px;
}
.cheditor-popup-titlebar {
    padding-left: 10px;
    line-height: 30px;
}
.cheditor-popup-title {
    font-size: 12px;
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    font-weight: bold;
    color: #fff;
}
.cheditor-dragWindowTransparent {
    background-color: #fff;
    position: absolute;
    display: block;
    left: 0px;
    top: 27px;
}
.cheditor-pulldown-wrapper {
    line-height: 1;
}
.cheditor-toolbar-icon-wrapper {
    margin: 0 2px 0 0;
    float: left;
    height: 24px;
    overflow: hidden;
}
.cheditor-tb-icon {
    height: 22px;
    width: 16px;
    overflow: hidden;
}
.cheditor-tb-icon-disable {
    height: 22px;
    width: 16px;
    overflow: hidden;
    filter: alpha(opacity=40) gray;
    opacity: 0.4;
}
.cheditor-tb-text {
    padding: 0;
    margin: 0;
    color: #333;
    height: 20px;
    line-height: 20px;
}
.cheditor-tb-text-disable {
    padding: 0;
    margin: 0;
    height: 20px;
    line-height: 20px;
    color: #333;
    overflow: hidden;
    filter: alpha(opacity=40) gray;
    -webkit-filter: grayscale(100%);
    opacity: 0.4;
}
.cheditor-tb-text span, .cheditor-tb-text-disable span {
    font-family: "Malgun Gothic", "Apple SD Gothic Neo", gulim, monospace;
    margin: 0 0 0 1px;
    padding: 0;
    width: 41px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: block;
    font-size: 12px;
}
.cheditor-tb-icon23 {
    height: 22px;
    width: 23px;
    margin-left: 3px;
    overflow: hidden;
}
.cheditor-tb-icon23-disable {
    height: 22px;
    width: 23px;
    margin-left: 3px;
    overflow: hidden;
    filter: alpha(opacity=40) gray;
    opacity: 0.4;
}
.cheditor-tb-icon36 {
    height: 22px;
    width: 36px;
    overflow: hidden;
}
.cheditor-tb-icon36-disable {
    height: 22px;
    width: 36px;
    overflow: hidden;
    filter: alpha(opacity=40) gray;
    opacity: 0.4;
}
.cheditor-tb-combo {
    height: 22px;
    width: 10px;
    overflow: hidden;
}
.cheditor-tb-combo-disable {
    height: 22px;
    width: 10px;
    overflow: hidden;
    filter: alpha(opacity=40) gray;
    opacity: 0.4;
}
.cheditor-tb-bg55 {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -483px;
    position: relative;
}
.cheditor-tb-bg40 {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -552px;
    position: relative;
}
.cheditor-tb-bg44 {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -621px;
    position: relative;
}
.cheditor-tb-bg30-first {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -276px;
    position: relative;
}
.cheditor-tb-bg30 {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -345px;
    position: relative;
}
.cheditor-tb-bg30-last {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -414px;
    position: relative;
}
.cheditor-tb-bgcombo {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -690px;
    position: relative;
}
.cheditor-tb-bgcombo-first {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left top;
    position: relative;
}
.cheditor-tb-bgcombo-last {
    float: left;
    overflow: hidden;
    background: url(../icons/toolbar-background.png) no-repeat left -759px;
    position: relative;
}
.cheditor-tb-bg {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -69px;
    position: relative;
}
.cheditor-tb-bg-first {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left top;
    position: relative;
}
.cheditor-tb-bg-last {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -138px;
    position: relative;
    background-clip: border-box;
}
.cheditor-tb-bg-single {
    float: left;
    overflow: hidden;
    background: transparent url(../icons/toolbar-background.png) no-repeat left -207px;
    position: relative;
}
.cheditor-tb-color-btn {
	width: 16px;
	height: 3px;
	overflow: hidden;
	position: absolute;
	top: 16px;
	left: 3px;
}
.cheditor-tb-button-spacer {
    overflow: hidden;
    width: 4px;
    height: 4px;
    float: left;
}
.cheditor-tb-split {
	overflow: hidden;
    height: 2px;
    width: 3px;
    clear: both;
}
package jcifs.dcerpc.ndr;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/ndr/NdrSmall.class */
public class NdrSmall extends NdrObject {
    public int value;

    public NdrSmall(int value) {
        this.value = value & 255;
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void encode(NdrBuffer dst) throws NdrException {
        dst.enc_ndr_small(this.value);
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void decode(NdrBuffer src) throws NdrException {
        this.value = src.dec_ndr_small();
    }
}

﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">               
    <h3 class="title">DEXT5 Upload :: Config :: FolderNameRule</h3>
    <p class="ttl">config.FolderNameRule</p>
    <p class="txt">
        dext5handler에서 설정된 폴더 하위들의 저장 체계를 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        일반 폴더 또는 년월일폴더를 사용할 수 있습니다.<br /><br />
        일반폴더는 "사용자 설정값" 형태로 사용할 수 있습니다.<br />
        년월일폴더는<br />사용하지 않음 => "/",<br />년도 => "YYYY/",<br />년도/월 => "YYYY/MM/",<br />년도/월/일 => "YYYY/MM/DD/" 중 하나의 값을 사용합니다.<br /><br />
        폴더가 존재하지 않으면 자동으로 생성되며, 서버의 날짜가 기준이 됩니다. 
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 할 폴더를 uploadfolder 으로 설정합니다.
        DEXT5UPLOAD.config.FolderNameRule = 'uploadfolder';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


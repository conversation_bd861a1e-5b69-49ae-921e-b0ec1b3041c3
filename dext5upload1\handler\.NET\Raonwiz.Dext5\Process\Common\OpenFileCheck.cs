﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000007 RID: 7
	public class OpenFileCheck : Download
	{
		// Token: 0x06000042 RID: 66 RVA: 0x0000B56C File Offset: 0x0000976C
		public OpenFileCheck(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, string pDownloadRootPath, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, pDownloadRootPath, pAllowExtensionSpecialSymbol)
		{
		}

		// Token: 0x06000043 RID: 67 RVA: 0x0000B58C File Offset: 0x0000978C
		public OpenFileCheck(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, List<string> pDownloadList, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, "", pAllowExtensionSpecialSymbol)
		{
			this.strDownloadList = pDownloadList;
			this.isCustom = true;
		}

		// Token: 0x06000044 RID: 68 RVA: 0x0000B5C0 File Offset: 0x000097C0
		public OpenFileCheck(HttpContext context, string pTempPath, string pZipFileName, string pFileWhiteList, string pFileBlackList, string[] pFileBlackWordList, List<Stream> pDownloadList, string pAllowExtensionSpecialSymbol) : base(context, pTempPath, pZipFileName, pFileWhiteList, pFileBlackList, pFileBlackWordList, "", pAllowExtensionSpecialSymbol)
		{
			this.sDownloadList = pDownloadList;
			this.isCustom = true;
		}

		// Token: 0x06000045 RID: 69 RVA: 0x0000B5F4 File Offset: 0x000097F4
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			if (!this.isCustom)
			{
				this.tempPath = base.GetTempPath(this.tempPath);
				string fileVirtualPath = this._entity_dextParam.fileVirtualPath;
				string empty = string.Empty;
				string empty2 = string.Empty;
				if (string.IsNullOrEmpty(fileVirtualPath))
				{
					string text = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text += "<html><head>";
						text += "<script type=\"text/javascript\">";
						text += "if (window.postMessage) {";
						text += "if (window.addEventListener) {";
						text += "window.addEventListener('message', function (e) {";
						text += "var sendUrl = e.origin;";
						text += "var data = document.body.innerHTML;";
						text += "e.source.postMessage(data, sendUrl);";
						text += "}, false);";
						text += "}";
						text += "else if (window.attachEvent) {";
						text += "window.attachEvent('onmessage', function (e) {";
						text += "var sendUrl = e.origin;";
						text += "var data = document.body.innerHTML;";
						text += "e.source.postMessage(data, sendUrl);";
						text += "});";
						text += "}";
						text += "}";
						text += "</script>";
						text += "</head>";
						text += "<body>";
						text += "{0}";
						text += "</body>";
						text += "</html>";
					}
					else
					{
						text = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
					}
					text = text.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text);
					return "error|009|Invalid parameter on server";
				}
				string[] fileVirtualPathAry = this._entity_dextParam.fileVirtualPathAry;
				string[] fileOrgNameAry = this._entity_dextParam.fileOrgNameAry;
				if (this._entity_dextParam.mode == "normal")
				{
					for (int i = 0; i < fileVirtualPathAry.Length; i++)
					{
						fileVirtualPathAry[i] = HttpUtility.UrlDecode(fileVirtualPathAry[i], Encoding.UTF8);
						fileOrgNameAry[i] = HttpUtility.UrlDecode(fileOrgNameAry[i], Encoding.UTF8);
					}
				}
				List<string> list = new List<string>();
				List<string> list2 = new List<string>();
				string text2 = string.Empty;
				string empty3 = string.Empty;
				string str = this.hContext.Request.Url.Scheme + "://" + this.hContext.Request.Url.Authority;
				for (int j = 0; j < fileVirtualPathAry.Length; j++)
				{
					text2 = fileVirtualPathAry[j];
					if (!string.IsNullOrEmpty(this.downloadRootPath) && File.Exists(this.downloadRootPath + text2))
					{
						list.Add(this.downloadRootPath + text2);
						list2.Add(fileOrgNameAry[j]);
					}
					else if (File.Exists(text2))
					{
						list.Add(text2);
						list2.Add(fileOrgNameAry[j]);
					}
					else
					{
						string text3 = text2.Replace("http://", "");
						text3 = text3.Replace("https://", "");
						text3 = text3.Substring(text3.IndexOf("/"));
						if (!string.IsNullOrEmpty(this.downloadRootPath))
						{
							text3 = this.hContext.Request.MapPath(this.downloadRootPath + text3);
						}
						else
						{
							text3 = this.hContext.Request.MapPath(text3);
						}
						if (File.Exists(text3))
						{
							list.Add(text3);
							list2.Add(fileOrgNameAry[j]);
						}
						else
						{
							if (this._b_useExternalDownload)
							{
								if (text2.IndexOf("http://") == -1 && text2.IndexOf("https://") == -1)
								{
									text2 = str + text2.Substring(text2.IndexOf("/"));
								}
								text2 = base.CheckExternalWebFile(text2);
							}
							else
							{
								text2 = "";
							}
							if (string.IsNullOrEmpty(text2))
							{
								string text4 = "";
								if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
								{
									text4 += "<html><head>";
									text4 += "<script type=\"text/javascript\">";
									text4 += "if (window.postMessage) {";
									text4 += "if (window.addEventListener) {";
									text4 += "window.addEventListener('message', function (e) {";
									text4 += "var sendUrl = e.origin;";
									text4 += "var data = document.body.innerHTML;";
									text4 += "e.source.postMessage(data, sendUrl);";
									text4 += "}, false);";
									text4 += "}";
									text4 += "else if (window.attachEvent) {";
									text4 += "window.attachEvent('onmessage', function (e) {";
									text4 += "var sendUrl = e.origin;";
									text4 += "var data = document.body.innerHTML;";
									text4 += "e.source.postMessage(data, sendUrl);";
									text4 += "});";
									text4 += "}";
									text4 += "}";
									text4 += "</script>";
									text4 += "</head>";
									text4 += "<body>";
									text4 += "{0}";
									text4 += "</body>";
									text4 += "</html>";
								}
								else
								{
									text4 = "{0}";
								}
								if (this._b_IsDebug)
								{
									LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
								}
								text4 = text4.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
								this.hContext.Response.Clear();
								this.hContext.Response.Write(text4);
								return "error|010|Not found file on server";
							}
							list.Add(text2);
							list2.Add(fileOrgNameAry[j]);
						}
					}
					if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list2[j]))
					{
						string text5 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text5 += "<html><head>";
							text5 += "<script type=\"text/javascript\">";
							text5 += "if (window.postMessage) {";
							text5 += "if (window.addEventListener) {";
							text5 += "window.addEventListener('message', function (e) {";
							text5 += "var sendUrl = e.origin;";
							text5 += "var data = document.body.innerHTML;";
							text5 += "e.source.postMessage(data, sendUrl);";
							text5 += "}, false);";
							text5 += "}";
							text5 += "else if (window.attachEvent) {";
							text5 += "window.attachEvent('onmessage', function (e) {";
							text5 += "var sendUrl = e.origin;";
							text5 += "var data = document.body.innerHTML;";
							text5 += "e.source.postMessage(data, sendUrl);";
							text5 += "});";
							text5 += "}";
							text5 += "}";
							text5 += "</script>";
							text5 += "</head>";
							text5 += "<body>";
							text5 += "{0}";
							text5 += "</body>";
							text5 += "</html>";
						}
						else
						{
							text5 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
						}
						text5 = text5.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text5);
						return "error|012|Not allowed file extension";
					}
					if (!base.CheckBlackWord(this.fileBlackWordList, list2[j]))
					{
						string text6 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text6 += "<html><head>";
							text6 += "<script type=\"text/javascript\">";
							text6 += "if (window.postMessage) {";
							text6 += "if (window.addEventListener) {";
							text6 += "window.addEventListener('message', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "}, false);";
							text6 += "}";
							text6 += "else if (window.attachEvent) {";
							text6 += "window.attachEvent('onmessage', function (e) {";
							text6 += "var sendUrl = e.origin;";
							text6 += "var data = document.body.innerHTML;";
							text6 += "e.source.postMessage(data, sendUrl);";
							text6 += "});";
							text6 += "}";
							text6 += "}";
							text6 += "</script>";
							text6 += "</head>";
							text6 += "<body>";
							text6 += "{0}";
							text6 += "</body>";
							text6 += "</html>";
						}
						else
						{
							text6 = "{0}";
						}
						if (this._b_IsDebug)
						{
							LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
						}
						text6 = text6.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text6);
						return "error|018|There does not allow the string included in file name";
					}
				}
				if (string.IsNullOrEmpty(empty2))
				{
					if (list.Count > 0)
					{
						string text7 = "";
						if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
						{
							text7 += "<html><head>";
							text7 += "<script type=\"text/javascript\">";
							text7 += "if (window.postMessage) {";
							text7 += "if (window.addEventListener) {";
							text7 += "window.addEventListener('message', function (e) {";
							text7 += "var sendUrl = e.origin;";
							text7 += "var data = document.body.innerHTML;";
							text7 += "e.source.postMessage(data, sendUrl);";
							text7 += "}, false);";
							text7 += "}";
							text7 += "else if (window.attachEvent) {";
							text7 += "window.attachEvent('onmessage', function (e) {";
							text7 += "var sendUrl = e.origin;";
							text7 += "var data = document.body.innerHTML;";
							text7 += "e.source.postMessage(data, sendUrl);";
							text7 += "});";
							text7 += "}";
							text7 += "}";
							text7 += "</script>";
							text7 += "</head>";
							text7 += "<body>";
							text7 += "{0}";
							text7 += "</body>";
							text7 += "</html>";
						}
						else
						{
							text7 = "{0}";
						}
						text7 = text7.Replace("{0}", "[OK]");
						this.hContext.Response.Clear();
						this.hContext.Response.Write(text7);
						return null;
					}
					string text8 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text8 += "<html><head>";
						text8 += "<script type=\"text/javascript\">";
						text8 += "if (window.postMessage) {";
						text8 += "if (window.addEventListener) {";
						text8 += "window.addEventListener('message', function (e) {";
						text8 += "var sendUrl = e.origin;";
						text8 += "var data = document.body.innerHTML;";
						text8 += "e.source.postMessage(data, sendUrl);";
						text8 += "}, false);";
						text8 += "}";
						text8 += "else if (window.attachEvent) {";
						text8 += "window.attachEvent('onmessage', function (e) {";
						text8 += "var sendUrl = e.origin;";
						text8 += "var data = document.body.innerHTML;";
						text8 += "e.source.postMessage(data, sendUrl);";
						text8 += "});";
						text8 += "}";
						text8 += "}";
						text8 += "</script>";
						text8 += "</head>";
						text8 += "<body>";
						text8 += "{0}";
						text8 += "</body>";
						text8 += "</html>";
					}
					else
					{
						text8 = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
					}
					text8 = text8.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text8);
					return "error|010|Not found file on server";
				}
			}
			else
			{
				string fileOrgName = this._entity_dextParam.fileOrgName;
				string empty4 = string.Empty;
				if (this.sDownloadList == null && (this.strDownloadList == null || this.strDownloadList.Count <= 0))
				{
					string text9 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text9 += "<html><head>";
						text9 += "<script type=\"text/javascript\">";
						text9 += "if (window.postMessage) {";
						text9 += "if (window.addEventListener) {";
						text9 += "window.addEventListener('message', function (e) {";
						text9 += "var sendUrl = e.origin;";
						text9 += "var data = document.body.innerHTML;";
						text9 += "e.source.postMessage(data, sendUrl);";
						text9 += "}, false);";
						text9 += "}";
						text9 += "else if (window.attachEvent) {";
						text9 += "window.attachEvent('onmessage', function (e) {";
						text9 += "var sendUrl = e.origin;";
						text9 += "var data = document.body.innerHTML;";
						text9 += "e.source.postMessage(data, sendUrl);";
						text9 += "});";
						text9 += "}";
						text9 += "}";
						text9 += "</script>";
						text9 += "</head>";
						text9 += "<body>";
						text9 += "{0}";
						text9 += "</body>";
						text9 += "</html>";
					}
					else
					{
						text9 = "{0}";
					}
					if (this._b_IsDebug)
					{
						LogUtil.DextDebug("error|009|Invalid parameter on server", this._str_DebugFilePath);
					}
					text9 = text9.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|009|Invalid parameter on server"));
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text9);
					return "error|009|Invalid parameter on server";
				}
				string[] array = this._entity_dextParam.fileOrgNameAry;
				if (!string.IsNullOrEmpty(this._entity_dextParam.dext5CMD) || array == null)
				{
					string dext5CMD = this._entity_dextParam.dext5CMD;
					if (array == null)
					{
						array = new string[]
						{
							this._entity_dextParam.fileOrgName
						};
					}
				}
				List<string> list3 = new List<string>();
				List<Stream> list4 = new List<Stream>();
				List<string> list5 = new List<string>();
				List<string> list6 = new List<string>();
				if (this._entity_dextParam.mode == "normal")
				{
					for (int k = 0; k < array.Length; k++)
					{
						array[k] = HttpUtility.UrlDecode(array[k], Encoding.UTF8);
					}
				}
				string empty5 = string.Empty;
				if (this.sDownloadList != null)
				{
					for (int l = 0; l < this.sDownloadList.Count; l++)
					{
						list4.Add(this.sDownloadList[l]);
						list5.Add(array[l]);
						list6.Add("");
						if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list5[l]))
						{
							string text10 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text10 += "<html><head>";
								text10 += "<script type=\"text/javascript\">";
								text10 += "if (window.postMessage) {";
								text10 += "if (window.addEventListener) {";
								text10 += "window.addEventListener('message', function (e) {";
								text10 += "var sendUrl = e.origin;";
								text10 += "var data = document.body.innerHTML;";
								text10 += "e.source.postMessage(data, sendUrl);";
								text10 += "}, false);";
								text10 += "}";
								text10 += "else if (window.attachEvent) {";
								text10 += "window.attachEvent('onmessage', function (e) {";
								text10 += "var sendUrl = e.origin;";
								text10 += "var data = document.body.innerHTML;";
								text10 += "e.source.postMessage(data, sendUrl);";
								text10 += "});";
								text10 += "}";
								text10 += "}";
								text10 += "</script>";
								text10 += "</head>";
								text10 += "<body>";
								text10 += "{0}";
								text10 += "</body>";
								text10 += "</html>";
							}
							else
							{
								text10 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
							}
							text10 = text10.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text10);
							return "error|012|Not allowed file extension";
						}
						if (!base.CheckBlackWord(this.fileBlackWordList, list5[l]))
						{
							string text11 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text11 += "<html><head>";
								text11 += "<script type=\"text/javascript\">";
								text11 += "if (window.postMessage) {";
								text11 += "if (window.addEventListener) {";
								text11 += "window.addEventListener('message', function (e) {";
								text11 += "var sendUrl = e.origin;";
								text11 += "var data = document.body.innerHTML;";
								text11 += "e.source.postMessage(data, sendUrl);";
								text11 += "}, false);";
								text11 += "}";
								text11 += "else if (window.attachEvent) {";
								text11 += "window.attachEvent('onmessage', function (e) {";
								text11 += "var sendUrl = e.origin;";
								text11 += "var data = document.body.innerHTML;";
								text11 += "e.source.postMessage(data, sendUrl);";
								text11 += "});";
								text11 += "}";
								text11 += "}";
								text11 += "</script>";
								text11 += "</head>";
								text11 += "<body>";
								text11 += "{0}";
								text11 += "</body>";
								text11 += "</html>";
							}
							else
							{
								text11 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
							}
							text11 = text11.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text11);
							return "error|018|There does not allow the string included in file name";
						}
					}
				}
				else if (this.strDownloadList != null)
				{
					for (int m = 0; m < this.strDownloadList.Count; m++)
					{
						if (!File.Exists(this.strDownloadList[m]))
						{
							string text12 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text12 += "<html><head>";
								text12 += "<script type=\"text/javascript\">";
								text12 += "if (window.postMessage) {";
								text12 += "if (window.addEventListener) {";
								text12 += "window.addEventListener('message', function (e) {";
								text12 += "var sendUrl = e.origin;";
								text12 += "var data = document.body.innerHTML;";
								text12 += "e.source.postMessage(data, sendUrl);";
								text12 += "}, false);";
								text12 += "}";
								text12 += "else if (window.attachEvent) {";
								text12 += "window.attachEvent('onmessage', function (e) {";
								text12 += "var sendUrl = e.origin;";
								text12 += "var data = document.body.innerHTML;";
								text12 += "e.source.postMessage(data, sendUrl);";
								text12 += "});";
								text12 += "}";
								text12 += "}";
								text12 += "</script>";
								text12 += "</head>";
								text12 += "<body>";
								text12 += "{0}";
								text12 += "</body>";
								text12 += "</html>";
							}
							else
							{
								text12 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|010|Not found file on server", this._str_DebugFilePath);
							}
							text12 = text12.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|010|Not found file on server"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text12);
							return "error|010|Not found file on server";
						}
						list3.Add(this.strDownloadList[m]);
						list5.Add(array[m]);
						list6.Add("");
						if (!base.CheckFileExtension(this.fileWhiteList, this.fileBlackList, this.allowExtensionSpecialSymbol, list5[m]))
						{
							string text13 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text13 += "<html><head>";
								text13 += "<script type=\"text/javascript\">";
								text13 += "if (window.postMessage) {";
								text13 += "if (window.addEventListener) {";
								text13 += "window.addEventListener('message', function (e) {";
								text13 += "var sendUrl = e.origin;";
								text13 += "var data = document.body.innerHTML;";
								text13 += "e.source.postMessage(data, sendUrl);";
								text13 += "}, false);";
								text13 += "}";
								text13 += "else if (window.attachEvent) {";
								text13 += "window.attachEvent('onmessage', function (e) {";
								text13 += "var sendUrl = e.origin;";
								text13 += "var data = document.body.innerHTML;";
								text13 += "e.source.postMessage(data, sendUrl);";
								text13 += "});";
								text13 += "}";
								text13 += "}";
								text13 += "</script>";
								text13 += "</head>";
								text13 += "<body>";
								text13 += "{0}";
								text13 += "</body>";
								text13 += "</html>";
							}
							else
							{
								text13 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|012|Not allowed file extension", this._str_DebugFilePath);
							}
							text13 = text13.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|012|Not allowed file extension"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text13);
							return "error|012|Not allowed file extension";
						}
						if (!base.CheckBlackWord(this.fileBlackWordList, list5[m]))
						{
							string text14 = "";
							if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
							{
								text14 += "<html><head>";
								text14 += "<script type=\"text/javascript\">";
								text14 += "if (window.postMessage) {";
								text14 += "if (window.addEventListener) {";
								text14 += "window.addEventListener('message', function (e) {";
								text14 += "var sendUrl = e.origin;";
								text14 += "var data = document.body.innerHTML;";
								text14 += "e.source.postMessage(data, sendUrl);";
								text14 += "}, false);";
								text14 += "}";
								text14 += "else if (window.attachEvent) {";
								text14 += "window.attachEvent('onmessage', function (e) {";
								text14 += "var sendUrl = e.origin;";
								text14 += "var data = document.body.innerHTML;";
								text14 += "e.source.postMessage(data, sendUrl);";
								text14 += "});";
								text14 += "}";
								text14 += "}";
								text14 += "</script>";
								text14 += "</head>";
								text14 += "<body>";
								text14 += "{0}";
								text14 += "</body>";
								text14 += "</html>";
							}
							else
							{
								text14 = "{0}";
							}
							if (this._b_IsDebug)
							{
								LogUtil.DextDebug("error|018|There does not allow the string included in file name", this._str_DebugFilePath);
							}
							text14 = text14.Replace("{0}", "[FAIL]" + Dext5Parameter.MakeParameter("error|018|There does not allow the string included in file name"));
							this.hContext.Response.Clear();
							this.hContext.Response.Write(text14);
							return "error|018|There does not allow the string included in file name";
						}
					}
				}
				if (!string.IsNullOrEmpty(empty4))
				{
					return null;
				}
				if ((list4 != null && list4.Count > 0) || (list3 != null && list3.Count > 0))
				{
					string text15 = "";
					if (!string.IsNullOrEmpty(this._entity_dextParam.crossDomain))
					{
						text15 += "<html><head>";
						text15 += "<script type=\"text/javascript\">";
						text15 += "if (window.postMessage) {";
						text15 += "if (window.addEventListener) {";
						text15 += "window.addEventListener('message', function (e) {";
						text15 += "var sendUrl = e.origin;";
						text15 += "var data = document.body.innerHTML;";
						text15 += "e.source.postMessage(data, sendUrl);";
						text15 += "}, false);";
						text15 += "}";
						text15 += "else if (window.attachEvent) {";
						text15 += "window.attachEvent('onmessage', function (e) {";
						text15 += "var sendUrl = e.origin;";
						text15 += "var data = document.body.innerHTML;";
						text15 += "e.source.postMessage(data, sendUrl);";
						text15 += "});";
						text15 += "}";
						text15 += "}";
						text15 += "</script>";
						text15 += "</head>";
						text15 += "<body>";
						text15 += "{0}";
						text15 += "</body>";
						text15 += "</html>";
					}
					else
					{
						text15 = "{0}";
					}
					text15 = text15.Replace("{0}", "[OK]");
					this.hContext.Response.Clear();
					this.hContext.Response.Write(text15);
					return null;
				}
			}
			return null;
		}

		// Token: 0x0400002E RID: 46
		private List<string> strDownloadList;

		// Token: 0x0400002F RID: 47
		private List<Stream> sDownloadList;

		// Token: 0x04000030 RID: 48
		private bool isCustom;
	}
}

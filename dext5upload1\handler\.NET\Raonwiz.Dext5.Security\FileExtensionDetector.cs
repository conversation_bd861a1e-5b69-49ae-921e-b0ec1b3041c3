﻿using System;
using System.IO;

namespace Raonwiz.Dext5.Security
{
	// Token: 0x02000002 RID: 2
	public class FileExtensionDetector
	{
		// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
		private byte[] ArrSliceFormTo(byte[] arrSource, int offset, int length)
		{
			byte[] array = new byte[length];
			Array.Copy(arrSource, offset, array, 0, length);
			return array;
		}

		// Token: 0x06000002 RID: 2 RVA: 0x00002070 File Offset: 0x00000270
		private int is_match(byte[] arrFirst, byte[] arrSecond)
		{
			int result = 1;
			if (arrFirst.Length == 0)
			{
				return 0;
			}
			int num = arrSecond.Length;
			for (int i = 0; i < num; i++)
			{
				if (arrFirst[i] != arrSecond[i])
				{
					result = 0;
					break;
				}
			}
			return result;
		}

		// Token: 0x06000003 RID: 3 RVA: 0x000020B0 File Offset: 0x000002B0
		private int is_compoundDocument(byte[] arrFileCont)
		{
			byte[] array = new byte[]
			{
				208,
				207,
				17,
				224,
				161,
				177,
				26,
				225
			};
			byte[] arrFirst = this.ArrSliceFormTo(arrFileCont, 0, array.Length);
			return this.is_match(arrFirst, array);
		}

		// Token: 0x06000004 RID: 4 RVA: 0x000020F0 File Offset: 0x000002F0
		private int is_zipDocument(byte[] arrFileCont)
		{
			byte[] array = new byte[]
			{
				80,
				75,
				3,
				4,
				20
			};
			byte[] arrFirst = this.ArrSliceFormTo(arrFileCont, 0, array.Length);
			return this.is_match(arrFirst, array);
		}

		// Token: 0x06000005 RID: 5 RVA: 0x00002128 File Offset: 0x00000328
		private string GetFileExtension(string strSourceFile, bool bLower)
		{
			string text = "";
			if (strSourceFile.Length > 0)
			{
				int num = strSourceFile.LastIndexOf(".");
				if (num > -1)
				{
					text = strSourceFile.Substring(num + 1);
				}
			}
			if (bLower)
			{
				text = text.ToLower();
			}
			return text;
		}

		// Token: 0x06000006 RID: 6 RVA: 0x0000216C File Offset: 0x0000036C
		public int checkFileExtension(string sFilePath, string sFileExtName)
		{
			string text;
			if (sFileExtName.Length == 0 || string.IsNullOrEmpty(sFileExtName))
			{
				text = this.GetFileExtension(sFilePath, true);
			}
			else
			{
				text = sFileExtName;
			}
			if (text == FileExtensionDetector.FileExts.tiff.ToString() || text == FileExtensionDetector.FileExts.chmi.ToString() || text == FileExtensionDetector.FileExts.html.ToString() || text == FileExtensionDetector.FileExts.midi.ToString())
			{
				text = text.Substring(0, 3);
			}
			else if (text == FileExtensionDetector.FileExts.jpeg.ToString() || text == FileExtensionDetector.FileExts.mpeg.ToString())
			{
				text = text.Replace("e", "");
			}
			else if (text == "7z")
			{
				text = "z" + text;
			}
			else if (text == "3gg" || text == "3gp" || text == "3g2")
			{
				text = "z" + text;
			}
			int result;
			if (FileExtensionDetector.FileExts.doc.ToString() == text)
			{
				result = this.is_doc(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.docx.ToString() == text)
			{
				result = this.is_docx(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.xls.ToString() == text)
			{
				result = this.is_xls(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.xlsx.ToString() == text)
			{
				result = this.is_xlsx(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.ppt.ToString() == text)
			{
				result = this.is_ppt(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.pptx.ToString() == text)
			{
				result = this.is_pptx(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.hwp.ToString() == text)
			{
				result = this.is_hwp(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.hwpx.ToString() == text)
			{
				result = this.is_hwpx(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.bmp.ToString() == text || FileExtensionDetector.FileExts.jpg.ToString() == text || FileExtensionDetector.FileExts.png.ToString() == text || FileExtensionDetector.FileExts.tif.ToString() == text || FileExtensionDetector.FileExts.gif.ToString() == text || FileExtensionDetector.FileExts.dwg.ToString() == text || FileExtensionDetector.FileExts.wmf.ToString() == text || FileExtensionDetector.FileExts.emf.ToString() == text || FileExtensionDetector.FileExts.psd.ToString() == text)
			{
				result = this.is_imageFormat(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.asp.ToString() == text || FileExtensionDetector.FileExts.aspx.ToString() == text || FileExtensionDetector.FileExts.jsp.ToString() == text || FileExtensionDetector.FileExts.txt.ToString() == text || FileExtensionDetector.FileExts.htm.ToString() == text || FileExtensionDetector.FileExts.java.ToString() == text || FileExtensionDetector.FileExts.cs.ToString() == text || FileExtensionDetector.FileExts.php.ToString() == text)
			{
				result = this.is_textFormat(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.jar.ToString() == text || FileExtensionDetector.FileExts.rar.ToString() == text || FileExtensionDetector.FileExts.tar.ToString() == text || FileExtensionDetector.FileExts.tgz.ToString() == text || FileExtensionDetector.FileExts.gz.ToString() == text || FileExtensionDetector.FileExts.z7z.ToString() == text || FileExtensionDetector.FileExts.bz2.ToString() == text || FileExtensionDetector.FileExts.zip.ToString() == text || FileExtensionDetector.FileExts.egg.ToString() == text)
			{
				result = this.is_zipFormat(sFilePath, text);
			}
			else if (FileExtensionDetector.FileExts.mp4.ToString() == text || FileExtensionDetector.FileExts.swf.ToString() == text || FileExtensionDetector.FileExts.flv.ToString() == text || FileExtensionDetector.FileExts.avi.ToString() == text || FileExtensionDetector.FileExts.wmv.ToString() == text || FileExtensionDetector.FileExts.wma.ToString() == text || FileExtensionDetector.FileExts.asf.ToString() == text || FileExtensionDetector.FileExts.mp3.ToString() == text || FileExtensionDetector.FileExts.mov.ToString() == text || FileExtensionDetector.FileExts.mid.ToString() == text || FileExtensionDetector.FileExts.mpg.ToString() == text || FileExtensionDetector.FileExts.ra.ToString() == text || FileExtensionDetector.FileExts.ram.ToString() == text || FileExtensionDetector.FileExts.iso.ToString() == text || FileExtensionDetector.FileExts.z3gg.ToString() == text || FileExtensionDetector.FileExts.z3gp.ToString() == text || FileExtensionDetector.FileExts.z3g2.ToString() == text || FileExtensionDetector.FileExts.m4v.ToString() == text || FileExtensionDetector.FileExts.rmi.ToString() == text || FileExtensionDetector.FileExts.cda.ToString() == text || FileExtensionDetector.FileExts.wav.ToString() == text || FileExtensionDetector.FileExts.qcp.ToString() == text || FileExtensionDetector.FileExts.ogg.ToString() == text || FileExtensionDetector.FileExts.oga.ToString() == text || FileExtensionDetector.FileExts.ogv.ToString() == text || FileExtensionDetector.FileExts.ogx.ToString() == text || FileExtensionDetector.FileExts.isz.ToString() == text || FileExtensionDetector.FileExts.flac.ToString() == text || FileExtensionDetector.FileExts.aiff.ToString() == text || FileExtensionDetector.FileExts.mkv.ToString() == text || FileExtensionDetector.FileExts.mka.ToString() == text || FileExtensionDetector.FileExts.mds.ToString() == text || FileExtensionDetector.FileExts.mpc.ToString() == text || FileExtensionDetector.FileExts.vob.ToString() == text || FileExtensionDetector.FileExts.acc.ToString() == text || FileExtensionDetector.FileExts.ape.ToString() == text || FileExtensionDetector.FileExts.mmf.ToString() == text || FileExtensionDetector.FileExts.dmg.ToString() == text || FileExtensionDetector.FileExts.img.ToString() == text)
			{
				result = this.is_moveAndMediaFormat(sFilePath, text);
			}
			else
			{
				result = this.is_otherFormat(sFilePath, text);
			}
			return result;
		}

		// Token: 0x06000007 RID: 7 RVA: 0x00002948 File Offset: 0x00000B48
		private byte[] FileToByteArray(string path, long offset, int length, bool bMustRead)
		{
			byte[] array = null;
			try
			{
				using (BinaryReader binaryReader = new BinaryReader(File.Open(path, FileMode.Open, FileAccess.Read)))
				{
					binaryReader.BaseStream.Seek(offset, SeekOrigin.Begin);
					array = binaryReader.ReadBytes(length);
					if (length != array.Length && bMustRead)
					{
						array = null;
					}
				}
			}
			catch (Exception)
			{
			}
			return array;
		}

		// Token: 0x06000008 RID: 8 RVA: 0x000029F0 File Offset: 0x00000BF0
		private int is_doc(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] arrSecond = new byte[]
			{
				236,
				165,
				193,
				0
			};
			byte[] arrSecond2 = new byte[]
			{
				219,
				165,
				45,
				0
			};
			byte[] arrSecond3 = new byte[]
			{
				127,
				254,
				52,
				10
			};
			byte[] arrSecond4 = new byte[]
			{
				18,
				52,
				86,
				120,
				144,
				byte.MaxValue
			};
			byte[] arrSecond5 = new byte[]
			{
				49,
				190,
				0,
				0,
				0,
				171,
				0,
				0
			};
			byte[] arrSecond6 = new byte[]
			{
				13,
				68,
				79,
				67
			};
			byte[] arrSecond7 = new byte[]
			{
				207,
				17,
				224,
				161,
				177,
				26,
				225,
				0
			};
			byte[] array = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array != null && array.Length >= 1024)
			{
				if (this.is_compoundDocument(array) == 1)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond);
				}
				if (num == 1)
				{
					return num;
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond2);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond3);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond4);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond5);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond6);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond7);
				}
			}
			return num;
		}

		// Token: 0x06000009 RID: 9 RVA: 0x00002B78 File Offset: 0x00000D78
		private int is_docx(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				119,
				111,
				114,
				100,
				47
			};
			byte[] array2 = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array2 != null && array2.Length >= 1024)
			{
				if (this.is_zipDocument(array2) == 1)
				{
					long num2 = 0L;
					int num3 = this._BUFFSIZE;
					for (;;)
					{
						num3 = this._BUFFSIZE;
						array2 = this.FileToByteArray(sFilePath, num2, num3, false);
						if (array2 == null)
						{
							break;
						}
						num3 = array2.Length;
						for (int i = 0; i < num3 - array.Length; i++)
						{
							if (array2[i] == array[0] && array2[i + 1] == array[1])
							{
								num = this.is_match(this.ArrSliceFormTo(array2, i, array.Length), array);
								if (num == 1)
								{
									break;
								}
							}
						}
						if (num == 1 || num3 == array.Length)
						{
							break;
						}
						num2 += (long)(num3 - array.Length);
					}
				}
				else
				{
					num = this.is_compoundDocument(array2);
				}
			}
			return num;
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002CA8 File Offset: 0x00000EA8
		private int is_xls(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] arrSecond = new byte[]
			{
				9,
				8,
				16,
				0,
				0,
				6,
				5,
				0
			};
			byte[] arrSecond2 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				16
			};
			byte[] arrSecond3 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				31
			};
			byte[] arrSecond4 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				34
			};
			byte[] arrSecond5 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				35
			};
			byte[] arrSecond6 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				40
			};
			byte[] arrSecond7 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				41
			};
			byte[] arrSecond8 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				32,
				0,
				0,
				0
			};
			byte[] array = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array != null && array.Length >= 1024 && this.is_compoundDocument(array) == 1)
			{
				num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond);
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond2);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond3);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond4);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond5);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond6);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond7);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond8);
				}
				if (num == 0)
				{
					byte[] array2 = new byte[]
					{
						87,
						0,
						111,
						0,
						114,
						0,
						107,
						0,
						98,
						0,
						111,
						0,
						111,
						0,
						107,
						0
					};
					array = this.FileToByteArray(sFilePath, 1152L, array2.Length, true);
					if (array != null && array.Length >= array2.Length)
					{
						num = this.is_match(array, array2);
					}
				}
			}
			return num;
		}

		// Token: 0x0600000B RID: 11 RVA: 0x00002E94 File Offset: 0x00001094
		private int is_xlsx(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				120,
				108,
				47
			};
			byte[] array2 = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array2 != null && array2.Length >= 1024)
			{
				if (this.is_zipDocument(array2) == 1)
				{
					long num2 = 0L;
					int num3 = this._BUFFSIZE;
					for (;;)
					{
						num3 = this._BUFFSIZE;
						array2 = this.FileToByteArray(sFilePath, num2, num3, false);
						if (array2 == null)
						{
							break;
						}
						num3 = array2.Length;
						for (int i = 0; i < num3 - array.Length; i++)
						{
							if (array2[i] == array[0] && array2[i + 1] == array[1])
							{
								num = this.is_match(this.ArrSliceFormTo(array2, i, array.Length), array);
								if (num == 1)
								{
									break;
								}
							}
						}
						if (num == 1 || num3 == array.Length)
						{
							break;
						}
						num2 += (long)(num3 - array.Length);
					}
				}
				else
				{
					num = this.is_compoundDocument(array2);
				}
			}
			return num;
		}

		// Token: 0x0600000C RID: 12 RVA: 0x00002FB4 File Offset: 0x000011B4
		private int is_ppt(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] arrSecond = new byte[]
			{
				0,
				110,
				30,
				240
			};
			byte[] arrSecond2 = new byte[]
			{
				15,
				0,
				232,
				3
			};
			byte[] arrSecond3 = new byte[]
			{
				160,
				70,
				29,
				240
			};
			byte[] arrSecond4 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				14,
				0,
				0,
				0
			};
			byte[] arrSecond5 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				28,
				0,
				0,
				0
			};
			byte[] arrSecond6 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				38,
				0,
				0,
				0
			};
			byte[] arrSecond7 = new byte[]
			{
				253,
				byte.MaxValue,
				byte.MaxValue,
				byte.MaxValue,
				67,
				0,
				0,
				0
			};
			byte[] array = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array != null && array.Length >= 1024 && this.is_compoundDocument(array) == 1)
			{
				num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond);
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond2);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond3);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond4);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond5);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond6);
				}
				if (num == 0)
				{
					num = this.is_match(this.ArrSliceFormTo(array, 512, 8), arrSecond7);
				}
				if (num == 0)
				{
					byte[] array2 = new byte[]
					{
						80,
						111,
						119,
						101,
						114,
						80,
						111,
						105,
						110
					};
					long num2 = 0L;
					int num3 = this._BUFFSIZE;
					for (;;)
					{
						num3 = this._BUFFSIZE;
						array = this.FileToByteArray(sFilePath, num2, num3, false);
						if (array == null)
						{
							break;
						}
						num3 = array.Length;
						for (int i = 0; i < num3 - array2.Length; i++)
						{
							if (array[i] == array2[0] && array[i + 1] == array2[1])
							{
								num = this.is_match(this.ArrSliceFormTo(array, i, array2.Length), array2);
								if (num == 1)
								{
									break;
								}
							}
						}
						if (num == 1 || num3 == array2.Length)
						{
							break;
						}
						num2 += (long)(num3 - array2.Length);
					}
				}
			}
			return num;
		}

		// Token: 0x0600000D RID: 13 RVA: 0x000031E4 File Offset: 0x000013E4
		private int is_pptx(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				112,
				112,
				116,
				47
			};
			byte[] array2 = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array2 != null && array2.Length >= 1024)
			{
				if (this.is_zipDocument(array2) == 1)
				{
					long num2 = 0L;
					int num3 = this._BUFFSIZE;
					for (;;)
					{
						num3 = this._BUFFSIZE;
						array2 = this.FileToByteArray(sFilePath, num2, num3, false);
						if (array2 == null)
						{
							break;
						}
						num3 = array2.Length;
						for (int i = 0; i < num3 - array.Length; i++)
						{
							if (array2[i] == array[0] && array2[i + 1] == array[1])
							{
								num = this.is_match(this.ArrSliceFormTo(array2, i, array.Length), array);
								if (num == 1)
								{
									break;
								}
							}
						}
						if (num == 1 || num3 == array.Length)
						{
							break;
						}
						num2 += (long)(num3 - array.Length);
					}
				}
				else
				{
					num = this.is_compoundDocument(array2);
				}
			}
			return num;
		}

		// Token: 0x0600000E RID: 14 RVA: 0x000032D4 File Offset: 0x000014D4
		private int is_hwp(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				72,
				87,
				80,
				32,
				68,
				111,
				99,
				117,
				109,
				101,
				110,
				116,
				32,
				70,
				105,
				108,
				101
			};
			byte[] array2 = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array2 != null && array2.Length >= 1024 && this.is_compoundDocument(array2) == 1)
			{
				num = this.is_match(this.ArrSliceFormTo(array2, 0, 17), array);
				if (num == 1)
				{
					return num;
				}
				long num2 = 0L;
				int num3 = this._BUFFSIZE;
				for (;;)
				{
					num3 = this._BUFFSIZE;
					array2 = this.FileToByteArray(sFilePath, num2, num3, false);
					if (array2 == null)
					{
						break;
					}
					num3 = array2.Length;
					for (int i = 0; i < num3 - array.Length; i++)
					{
						if (array2[i] == array[0] && array2[i + 1] == array[1])
						{
							num = this.is_match(this.ArrSliceFormTo(array2, i, array.Length), array);
							if (num == 1)
							{
								break;
							}
						}
					}
					if (num == 1 || num3 == array.Length)
					{
						break;
					}
					num2 += (long)(num3 - array.Length);
				}
			}
			return num;
		}

		// Token: 0x0600000F RID: 15 RVA: 0x000033C4 File Offset: 0x000015C4
		private int is_hwpx(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				47,
				104,
				119,
				112
			};
			byte[] array2 = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array2 != null && array2.Length >= 1024)
			{
				if (this.is_zipDocument(array2) == 1)
				{
					long num2 = 0L;
					int num3 = this._BUFFSIZE;
					for (;;)
					{
						num3 = this._BUFFSIZE;
						array2 = this.FileToByteArray(sFilePath, num2, num3, false);
						if (array2 == null)
						{
							break;
						}
						num3 = array2.Length;
						for (int i = 0; i < num3 - array.Length; i++)
						{
							if (array2[i] == array[0] && array2[i + 1] == array[1])
							{
								num = this.is_match(this.ArrSliceFormTo(array2, i, array.Length), array);
								if (num == 1)
								{
									break;
								}
							}
						}
						if (num == 1 || num3 == array.Length)
						{
							break;
						}
						num2 += (long)(num3 - array.Length);
					}
				}
				else
				{
					num = this.is_compoundDocument(array2);
				}
			}
			return num;
		}

		// Token: 0x06000010 RID: 16 RVA: 0x000034D4 File Offset: 0x000016D4
		private int is_imageFormat(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = new byte[]
			{
				66,
				77
			};
			byte[] array2 = new byte[]
			{
				byte.MaxValue,
				216
			};
			byte[] array3 = new byte[]
			{
				137,
				80,
				78,
				71
			};
			byte[] array4 = new byte[]
			{
				73,
				32,
				73
			};
			byte[] array5 = new byte[]
			{
				73,
				73,
				42
			};
			byte[] array6 = new byte[]
			{
				77,
				77
			};
			byte[] array7 = new byte[]
			{
				71,
				73,
				70,
				56
			};
			byte[] array8 = new byte[]
			{
				65,
				67,
				49,
				48
			};
			byte[] array9 = new byte[]
			{
				215,
				205,
				198,
				154
			};
			byte[] array10 = new byte[4];
			array10[0] = 1;
			byte[] array11 = array10;
			byte[] array12 = new byte[]
			{
				56,
				66,
				80,
				83
			};
			byte[] array13 = this.FileToByteArray(sFilePath, 0L, 10, true);
			if (array13 != null && array13.Length >= 10)
			{
				if (FileExtensionDetector.FileExts.bmp.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array.Length), array);
				}
				else if (FileExtensionDetector.FileExts.jpg.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array2.Length), array2);
				}
				else if (FileExtensionDetector.FileExts.png.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array3.Length), array3);
				}
				else if (FileExtensionDetector.FileExts.tif.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array4.Length), array4);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array13, 0, array5.Length), array5);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array13, 0, array6.Length), array6);
					}
				}
				else if (FileExtensionDetector.FileExts.gif.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array7.Length), array7);
				}
				else if (FileExtensionDetector.FileExts.dwg.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array8.Length), array8);
				}
				else if (FileExtensionDetector.FileExts.wmf.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array9.Length), array9);
				}
				else if (FileExtensionDetector.FileExts.emf.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array11.Length), array11);
				}
				else if (FileExtensionDetector.FileExts.psd.ToString() == sFileExt)
				{
					num = this.is_match(this.ArrSliceFormTo(array13, 0, array12.Length), array12);
				}
			}
			return num;
		}

		// Token: 0x06000011 RID: 17 RVA: 0x000038C8 File Offset: 0x00001AC8
		private int is_moveAndMediaFormat(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = this.FileToByteArray(sFilePath, 0L, 20, true);
			if (array != null && array.Length >= 20)
			{
				if (FileExtensionDetector.FileExts.mp4.ToString() == sFileExt || FileExtensionDetector.FileExts.z3gg.ToString() == sFileExt || FileExtensionDetector.FileExts.z3gp.ToString() == sFileExt || FileExtensionDetector.FileExts.z3g2.ToString() == sFileExt || FileExtensionDetector.FileExts.m4v.ToString() == sFileExt)
				{
					byte[] array2 = new byte[]
					{
						102,
						116,
						121,
						112
					};
					num = this.is_match(this.ArrSliceFormTo(array, 4, array2.Length), array2);
				}
				else if (FileExtensionDetector.FileExts.swf.ToString() == sFileExt)
				{
					byte[] array3 = new byte[]
					{
						67,
						87,
						83
					};
					byte[] array4 = new byte[]
					{
						70,
						87,
						83
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array3.Length), array3);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array4.Length), array4);
					}
				}
				else if (FileExtensionDetector.FileExts.flv.ToString() == sFileExt)
				{
					byte[] array5 = new byte[]
					{
						70,
						76,
						86
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array5.Length), array5);
				}
				else if (FileExtensionDetector.FileExts.avi.ToString() == sFileExt || FileExtensionDetector.FileExts.rmi.ToString() == sFileExt || FileExtensionDetector.FileExts.cda.ToString() == sFileExt || FileExtensionDetector.FileExts.wav.ToString() == sFileExt || FileExtensionDetector.FileExts.qcp.ToString() == sFileExt)
				{
					byte[] array6 = new byte[]
					{
						82,
						73,
						70,
						70
					};
					byte[] array7 = new byte[]
					{
						65,
						86,
						73,
						32
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array6.Length), array6);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array7.Length), array7);
					}
				}
				else if (FileExtensionDetector.FileExts.wmv.ToString() == sFileExt || FileExtensionDetector.FileExts.wma.ToString() == sFileExt || FileExtensionDetector.FileExts.asf.ToString() == sFileExt)
				{
					byte[] array8 = new byte[]
					{
						48,
						38,
						178,
						117,
						142,
						102,
						207,
						17
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array8.Length), array8);
				}
				else if (FileExtensionDetector.FileExts.mp3.ToString() == sFileExt)
				{
					byte[] array9 = new byte[]
					{
						73,
						68,
						51
					};
					byte[] array10 = new byte[]
					{
						byte.MaxValue,
						250
					};
					byte[] array11 = new byte[]
					{
						byte.MaxValue,
						251
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array9.Length), array9);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array10.Length), array10);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array11.Length), array11);
					}
				}
				else if (FileExtensionDetector.FileExts.mov.ToString() == sFileExt)
				{
					byte[] array12 = new byte[]
					{
						109,
						111,
						111,
						118
					};
					byte[] array13 = new byte[]
					{
						102,
						114,
						101,
						101
					};
					byte[] array14 = new byte[]
					{
						109,
						100,
						97,
						116
					};
					byte[] array15 = new byte[]
					{
						119,
						105,
						100,
						101
					};
					byte[] array16 = new byte[]
					{
						112,
						110,
						111,
						116
					};
					byte[] array17 = new byte[]
					{
						115,
						107,
						105,
						112
					};
					byte[] array18 = new byte[]
					{
						102,
						116,
						121,
						112,
						116
					};
					num = this.is_match(this.ArrSliceFormTo(array, 4, array12.Length), array12);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array13.Length), array13);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array14.Length), array14);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array15.Length), array15);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array16.Length), array16);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array17.Length), array17);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 4, array18.Length), array18);
					}
				}
				else if (FileExtensionDetector.FileExts.mid.ToString() == sFileExt)
				{
					byte[] array19 = new byte[]
					{
						77,
						84,
						104,
						100
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array19.Length), array19);
				}
				else if (FileExtensionDetector.FileExts.mpg.ToString() == sFileExt)
				{
					byte[] array20 = new byte[]
					{
						0,
						0,
						1,
						186
					};
					byte[] array21 = new byte[]
					{
						0,
						0,
						1,
						179
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array20.Length), array20);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array21.Length), array21);
					}
				}
				else if (FileExtensionDetector.FileExts.ra.ToString() == sFileExt)
				{
					byte[] array22 = new byte[]
					{
						46,
						114,
						97,
						253,
						0
					};
					byte[] array23 = new byte[]
					{
						46,
						82,
						77,
						70,
						0,
						0,
						0,
						18
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array22.Length), array22);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array23.Length), array23);
					}
				}
				else if (FileExtensionDetector.FileExts.ram.ToString() == sFileExt)
				{
					byte[] array24 = new byte[]
					{
						114,
						116,
						115,
						112,
						58,
						47,
						47
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array24.Length), array24);
				}
				else if (FileExtensionDetector.FileExts.iso.ToString() == sFileExt)
				{
					byte[] array25 = new byte[]
					{
						67,
						68,
						48,
						48,
						49
					};
					byte[] array26 = new byte[]
					{
						0,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						byte.MaxValue,
						0,
						0,
						2,
						0
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array25.Length), array25);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array26.Length), array26);
					}
					if (num == 0)
					{
						array = this.FileToByteArray(sFilePath, 32769L, 15, true);
						if (array != null && array.Length >= 15)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array25.Length), array25);
						}
						if (num == 0)
						{
							array = this.FileToByteArray(sFilePath, 34817L, 15, true);
							if (array != null && array.Length >= 15)
							{
								num = this.is_match(this.ArrSliceFormTo(array, 0, array25.Length), array25);
							}
						}
						if (num == 0)
						{
							array = this.FileToByteArray(sFilePath, 36865L, 15, true);
							if (array != null && array.Length >= 15)
							{
								num = this.is_match(this.ArrSliceFormTo(array, 0, array25.Length), array25);
							}
						}
					}
				}
				else if (FileExtensionDetector.FileExts.isz.ToString() == sFileExt)
				{
					byte[] array27 = new byte[]
					{
						73,
						115,
						90,
						33
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array27.Length), array27);
				}
				else if (FileExtensionDetector.FileExts.ogg.ToString() == sFileExt || FileExtensionDetector.FileExts.oga.ToString() == sFileExt || FileExtensionDetector.FileExts.ogv.ToString() == sFileExt || FileExtensionDetector.FileExts.ogx.ToString() == sFileExt)
				{
					byte[] array28 = new byte[]
					{
						79,
						103,
						103,
						83
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array28.Length), array28);
				}
				else if (FileExtensionDetector.FileExts.flac.ToString() == sFileExt)
				{
					byte[] array29 = new byte[]
					{
						102,
						76,
						97,
						67
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array29.Length), array29);
				}
				else if (FileExtensionDetector.FileExts.aiff.ToString() == sFileExt)
				{
					byte[] array30 = new byte[]
					{
						70,
						79,
						82,
						77,
						0
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array30.Length), array30);
				}
				else if (FileExtensionDetector.FileExts.mkv.ToString() == sFileExt || FileExtensionDetector.FileExts.mka.ToString() == sFileExt)
				{
					byte[] array31 = new byte[]
					{
						26,
						69,
						223,
						163
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array31.Length), array31);
				}
				else if (FileExtensionDetector.FileExts.mds.ToString() == sFileExt)
				{
					byte[] array32 = new byte[]
					{
						77,
						69,
						68,
						73,
						65,
						32,
						68,
						69,
						83,
						67,
						82,
						73,
						80,
						84,
						79,
						82
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array32.Length), array32);
				}
				else if (FileExtensionDetector.FileExts.mpc.ToString() == sFileExt)
				{
					byte[] array33 = new byte[]
					{
						77,
						80,
						43
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array33.Length), array33);
				}
				else if (FileExtensionDetector.FileExts.vob.ToString() == sFileExt)
				{
					byte[] array34 = new byte[]
					{
						0,
						0,
						1,
						186
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array34.Length), array34);
				}
				else if (FileExtensionDetector.FileExts.acc.ToString() == sFileExt)
				{
					byte[] array35 = new byte[]
					{
						65,
						65,
						67,
						0,
						1,
						0
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array35.Length), array35);
				}
				else if (FileExtensionDetector.FileExts.ape.ToString() == sFileExt)
				{
					byte[] array36 = new byte[]
					{
						77,
						65,
						67,
						32
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array36.Length), array36);
				}
				else if (FileExtensionDetector.FileExts.mmf.ToString() == sFileExt)
				{
					byte[] array37 = new byte[]
					{
						77,
						77,
						77,
						68
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array37.Length), array37);
				}
				else if (FileExtensionDetector.FileExts.dmg.ToString() == sFileExt)
				{
					byte[] array38 = new byte[]
					{
						120
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array38.Length), array38);
				}
				else if (FileExtensionDetector.FileExts.img.ToString() == sFileExt)
				{
					byte[] array39 = new byte[]
					{
						81,
						70,
						73,
						251
					};
					byte[] array40 = new byte[]
					{
						80,
						73,
						67,
						84,
						0,
						8
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array39.Length), array39);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array40.Length), array40);
					}
				}
			}
			return num;
		}

		// Token: 0x06000012 RID: 18 RVA: 0x000043A0 File Offset: 0x000025A0
		private int is_textFormat(string sFilePath, string sFileExt)
		{
			int result = -1;
			byte[] array = this.FileToByteArray(sFilePath, 0L, 1024, true);
			if (array != null && array.Length >= 1024)
			{
				if (this.is_compoundDocument(array) == 1)
				{
					return 0;
				}
				if (this.is_zipDocument(array) == 1)
				{
					return 0;
				}
			}
			for (int i = 9; i < Enum.GetNames(typeof(FileExtensionDetector.FileExts)).Length; i++)
			{
				FileExtensionDetector.FileExts fileExts = (FileExtensionDetector.FileExts)i;
				int num = this.checkFileExtension(sFilePath, fileExts.ToString());
				if (num == 1)
				{
					result = 0;
					break;
				}
			}
			return result;
		}

		// Token: 0x06000013 RID: 19 RVA: 0x00004474 File Offset: 0x00002674
		private int is_zipFormat(string sFilePath, string sFileExt)
		{
			int num = 0;
			byte[] array = this.FileToByteArray(sFilePath, 0L, 15, true);
			if (array != null && array.Length >= 15)
			{
				if (FileExtensionDetector.FileExts.zip.ToString() == sFileExt)
				{
					byte[] array2 = new byte[]
					{
						80,
						75
					};
					byte[] array3 = new byte[]
					{
						87,
						105,
						110,
						90,
						105,
						112
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array2.Length), array2);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array3.Length), array3);
					}
				}
				else if (FileExtensionDetector.FileExts.jar.ToString() == sFileExt)
				{
					byte[] array4 = new byte[]
					{
						80,
						75,
						3,
						4
					};
					byte[] array5 = new byte[]
					{
						95,
						39,
						168,
						137
					};
					byte[] array6 = new byte[]
					{
						74,
						65,
						82,
						67,
						83,
						0
					};
					byte[] array7 = new byte[]
					{
						80,
						75,
						3,
						4,
						20,
						0,
						8,
						0
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array4.Length), array4);
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array5.Length), array5);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array6.Length), array6);
					}
					if (num == 0)
					{
						num = this.is_match(this.ArrSliceFormTo(array, 0, array7.Length), array7);
					}
				}
				else if (FileExtensionDetector.FileExts.rar.ToString() == sFileExt)
				{
					byte[] array8 = new byte[]
					{
						82,
						97,
						114,
						33,
						26,
						7,
						0
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array8.Length), array8);
				}
				else if (FileExtensionDetector.FileExts.tar.ToString() == sFileExt || FileExtensionDetector.FileExts.tgz.ToString() == sFileExt)
				{
					byte[] array9 = new byte[]
					{
						31,
						139
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array9.Length), array9);
				}
				else if (FileExtensionDetector.FileExts.gz.ToString() == sFileExt)
				{
					byte[] array10 = new byte[]
					{
						31,
						139,
						8
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array10.Length), array10);
				}
				else if (FileExtensionDetector.FileExts.z7z.ToString() == sFileExt)
				{
					byte[] array11 = new byte[]
					{
						55,
						122,
						188,
						175,
						39,
						28
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array11.Length), array11);
				}
				else if (FileExtensionDetector.FileExts.bz2.ToString() == sFileExt)
				{
					byte[] array12 = new byte[]
					{
						66,
						90,
						104
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array12.Length), array12);
				}
				else if (FileExtensionDetector.FileExts.egg.ToString() == sFileExt)
				{
					byte[] array13 = new byte[]
					{
						69,
						71,
						71,
						65
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array13.Length), array13);
				}
			}
			return num;
		}

		// Token: 0x06000014 RID: 20 RVA: 0x00004800 File Offset: 0x00002A00
		private int is_otherFormat(string sFilePath, string sFileExt)
		{
			int num = -1;
			byte[] array = this.FileToByteArray(sFilePath, 0L, 15, true);
			if (array != null && array.Length >= 15)
			{
				if (FileExtensionDetector.FileExts.pdf.ToString() == sFileExt)
				{
					byte[] array2 = new byte[]
					{
						37,
						80,
						68,
						70
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array2.Length), array2);
				}
				else if (FileExtensionDetector.FileExts.mdb.ToString() == sFileExt)
				{
					byte[] array3 = new byte[]
					{
						83,
						116,
						97,
						110,
						100,
						97,
						114,
						100,
						32,
						74,
						101,
						116
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array3.Length), array3);
				}
				else if (FileExtensionDetector.FileExts.rtf.ToString() == sFileExt)
				{
					byte[] array4 = new byte[]
					{
						123,
						92,
						114,
						116,
						102,
						49
					};
					num = this.is_match(this.ArrSliceFormTo(array, 0, array4.Length), array4);
				}
				else if (FileExtensionDetector.FileExts.msg.ToString() == sFileExt)
				{
					num = this.is_compoundDocument(array);
				}
				else if (!(FileExtensionDetector.FileExts.pst.ToString() == sFileExt))
				{
					if (FileExtensionDetector.FileExts.cab.ToString() == sFileExt)
					{
						byte[] array5 = new byte[]
						{
							73,
							83,
							99,
							40
						};
						byte[] array6 = new byte[]
						{
							77,
							83,
							67,
							70
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array5.Length), array5);
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array6.Length), array6);
						}
					}
					else if (FileExtensionDetector.FileExts.chm.ToString() == sFileExt)
					{
						byte[] array7 = new byte[]
						{
							73,
							84,
							83,
							70
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array7.Length), array7);
					}
					else if (FileExtensionDetector.FileExts.eml.ToString() == sFileExt)
					{
						byte[] array8 = new byte[]
						{
							88,
							45
						};
						byte[] array9 = new byte[]
						{
							82,
							101,
							116,
							117,
							114,
							110,
							45,
							80
						};
						byte[] array10 = new byte[]
						{
							70,
							114,
							111,
							109
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array8.Length), array8);
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array9.Length), array9);
						}
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array10.Length), array10);
						}
					}
					else if (FileExtensionDetector.FileExts.hlp.ToString() == sFileExt)
					{
						byte[] array11 = new byte[]
						{
							0,
							0,
							byte.MaxValue,
							byte.MaxValue,
							byte.MaxValue,
							byte.MaxValue
						};
						byte[] array12 = new byte[]
						{
							63,
							95,
							3,
							0
						};
						byte[] array13 = new byte[]
						{
							76,
							78,
							2,
							0
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array11.Length), array11);
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array12.Length), array12);
						}
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array13.Length), array13);
						}
					}
					else if (FileExtensionDetector.FileExts.lnk.ToString() == sFileExt)
					{
						byte[] array14 = new byte[]
						{
							76,
							0,
							0,
							0,
							1,
							20,
							2,
							0
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array14.Length), array14);
					}
					else if (FileExtensionDetector.FileExts.ttf.ToString() == sFileExt)
					{
						byte[] array15 = new byte[5];
						array15[1] = 1;
						byte[] array16 = array15;
						num = this.is_match(this.ArrSliceFormTo(array, 0, array16.Length), array16);
					}
					else if (FileExtensionDetector.FileExts.mdf.ToString() == sFileExt)
					{
						byte[] array17 = new byte[4];
						array17[0] = 1;
						array17[1] = 15;
						byte[] array18 = array17;
						num = this.is_match(this.ArrSliceFormTo(array, 0, array18.Length), array18);
					}
					else if (FileExtensionDetector.FileExts.ico.ToString() == sFileExt)
					{
						byte[] array19 = new byte[4];
						array19[2] = 1;
						byte[] array20 = array19;
						num = this.is_match(this.ArrSliceFormTo(array, 0, array20.Length), array20);
					}
					else if (FileExtensionDetector.FileExts.cur.ToString() == sFileExt)
					{
						byte[] array21 = new byte[4];
						array21[2] = 2;
						byte[] array22 = array21;
						num = this.is_match(this.ArrSliceFormTo(array, 0, array22.Length), array22);
					}
					else if (FileExtensionDetector.FileExts.xmp.ToString() == sFileExt)
					{
						byte[] array23 = new byte[]
						{
							47,
							42,
							32,
							88,
							80,
							77,
							32,
							42,
							47
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array23.Length), array23);
					}
					else if (FileExtensionDetector.FileExts.com.ToString() == sFileExt)
					{
						byte[] array24 = new byte[]
						{
							232
						};
						byte[] array25 = new byte[]
						{
							233
						};
						byte[] array26 = new byte[]
						{
							235
						};
						byte[] array27 = new byte[]
						{
							77,
							90
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array24.Length), array24);
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array25.Length), array25);
						}
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array26.Length), array26);
						}
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array27.Length), array27);
						}
					}
					else if (FileExtensionDetector.FileExts.exe.ToString() == sFileExt || FileExtensionDetector.FileExts.dll.ToString() == sFileExt || FileExtensionDetector.FileExts.ocx.ToString() == sFileExt)
					{
						byte[] array28 = new byte[]
						{
							77,
							90
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array28.Length), array28);
					}
					else if (FileExtensionDetector.FileExts.lib.ToString() == sFileExt)
					{
						byte[] array29 = new byte[]
						{
							33,
							60,
							97,
							114,
							99,
							104,
							62,
							10
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array29.Length), array29);
					}
					else if (FileExtensionDetector.FileExts.msi.ToString() == sFileExt)
					{
						num = this.is_compoundDocument(array);
					}
					else if (FileExtensionDetector.FileExts.obj.ToString() == sFileExt)
					{
						byte[] array30 = new byte[]
						{
							76,
							1
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array30.Length), array30);
					}
					else if (FileExtensionDetector.FileExts.ps.ToString() == sFileExt)
					{
						byte[] array31 = new byte[]
						{
							37,
							33
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array31.Length), array31);
					}
					else if (FileExtensionDetector.FileExts.eps.ToString() == sFileExt)
					{
						byte[] array32 = new byte[]
						{
							197,
							208,
							211,
							198
						};
						byte[] array33 = new byte[]
						{
							37,
							33,
							80,
							83,
							45,
							65,
							100,
							111
						};
						num = this.is_match(this.ArrSliceFormTo(array, 0, array32.Length), array32);
						if (num == 0)
						{
							num = this.is_match(this.ArrSliceFormTo(array, 0, array33.Length), array33);
						}
					}
				}
			}
			return num;
		}

		// Token: 0x04000001 RID: 1
		private int _BUFFSIZE = 3145728;

		// Token: 0x02000003 RID: 3
		private enum FileExts
		{
			// Token: 0x04000003 RID: 3
			asp,
			// Token: 0x04000004 RID: 4
			aspx,
			// Token: 0x04000005 RID: 5
			jsp,
			// Token: 0x04000006 RID: 6
			txt,
			// Token: 0x04000007 RID: 7
			htm,
			// Token: 0x04000008 RID: 8
			html,
			// Token: 0x04000009 RID: 9
			java,
			// Token: 0x0400000A RID: 10
			cs,
			// Token: 0x0400000B RID: 11
			php,
			// Token: 0x0400000C RID: 12
			doc,
			// Token: 0x0400000D RID: 13
			docx,
			// Token: 0x0400000E RID: 14
			xls,
			// Token: 0x0400000F RID: 15
			xlsx,
			// Token: 0x04000010 RID: 16
			ppt,
			// Token: 0x04000011 RID: 17
			pptx,
			// Token: 0x04000012 RID: 18
			hwp,
			// Token: 0x04000013 RID: 19
			hwpx,
			// Token: 0x04000014 RID: 20
			bmp,
			// Token: 0x04000015 RID: 21
			jpg,
			// Token: 0x04000016 RID: 22
			jpeg,
			// Token: 0x04000017 RID: 23
			png,
			// Token: 0x04000018 RID: 24
			tif,
			// Token: 0x04000019 RID: 25
			tiff,
			// Token: 0x0400001A RID: 26
			gif,
			// Token: 0x0400001B RID: 27
			dwg,
			// Token: 0x0400001C RID: 28
			wmf,
			// Token: 0x0400001D RID: 29
			emf,
			// Token: 0x0400001E RID: 30
			psd,
			// Token: 0x0400001F RID: 31
			jar,
			// Token: 0x04000020 RID: 32
			rar,
			// Token: 0x04000021 RID: 33
			tar,
			// Token: 0x04000022 RID: 34
			tgz,
			// Token: 0x04000023 RID: 35
			gz,
			// Token: 0x04000024 RID: 36
			z7z,
			// Token: 0x04000025 RID: 37
			bz2,
			// Token: 0x04000026 RID: 38
			zip,
			// Token: 0x04000027 RID: 39
			egg,
			// Token: 0x04000028 RID: 40
			mp4,
			// Token: 0x04000029 RID: 41
			swf,
			// Token: 0x0400002A RID: 42
			flv,
			// Token: 0x0400002B RID: 43
			avi,
			// Token: 0x0400002C RID: 44
			wmv,
			// Token: 0x0400002D RID: 45
			wma,
			// Token: 0x0400002E RID: 46
			asf,
			// Token: 0x0400002F RID: 47
			mp3,
			// Token: 0x04000030 RID: 48
			mov,
			// Token: 0x04000031 RID: 49
			mid,
			// Token: 0x04000032 RID: 50
			mpg,
			// Token: 0x04000033 RID: 51
			mpeg,
			// Token: 0x04000034 RID: 52
			ra,
			// Token: 0x04000035 RID: 53
			ram,
			// Token: 0x04000036 RID: 54
			iso,
			// Token: 0x04000037 RID: 55
			z3gg,
			// Token: 0x04000038 RID: 56
			z3gp,
			// Token: 0x04000039 RID: 57
			z3g2,
			// Token: 0x0400003A RID: 58
			m4v,
			// Token: 0x0400003B RID: 59
			rmi,
			// Token: 0x0400003C RID: 60
			cda,
			// Token: 0x0400003D RID: 61
			wav,
			// Token: 0x0400003E RID: 62
			qcp,
			// Token: 0x0400003F RID: 63
			ogg,
			// Token: 0x04000040 RID: 64
			oga,
			// Token: 0x04000041 RID: 65
			ogv,
			// Token: 0x04000042 RID: 66
			ogx,
			// Token: 0x04000043 RID: 67
			midi,
			// Token: 0x04000044 RID: 68
			isz,
			// Token: 0x04000045 RID: 69
			flac,
			// Token: 0x04000046 RID: 70
			aiff,
			// Token: 0x04000047 RID: 71
			mkv,
			// Token: 0x04000048 RID: 72
			mka,
			// Token: 0x04000049 RID: 73
			mds,
			// Token: 0x0400004A RID: 74
			mpc,
			// Token: 0x0400004B RID: 75
			vob,
			// Token: 0x0400004C RID: 76
			acc,
			// Token: 0x0400004D RID: 77
			ape,
			// Token: 0x0400004E RID: 78
			mmf,
			// Token: 0x0400004F RID: 79
			dmg,
			// Token: 0x04000050 RID: 80
			img,
			// Token: 0x04000051 RID: 81
			pdf,
			// Token: 0x04000052 RID: 82
			mdb,
			// Token: 0x04000053 RID: 83
			rtf,
			// Token: 0x04000054 RID: 84
			msg,
			// Token: 0x04000055 RID: 85
			pst,
			// Token: 0x04000056 RID: 86
			cab,
			// Token: 0x04000057 RID: 87
			chm,
			// Token: 0x04000058 RID: 88
			chmi,
			// Token: 0x04000059 RID: 89
			eml,
			// Token: 0x0400005A RID: 90
			hlp,
			// Token: 0x0400005B RID: 91
			lnk,
			// Token: 0x0400005C RID: 92
			ttf,
			// Token: 0x0400005D RID: 93
			mdf,
			// Token: 0x0400005E RID: 94
			ico,
			// Token: 0x0400005F RID: 95
			cur,
			// Token: 0x04000060 RID: 96
			xmp,
			// Token: 0x04000061 RID: 97
			com,
			// Token: 0x04000062 RID: 98
			exe,
			// Token: 0x04000063 RID: 99
			dll,
			// Token: 0x04000064 RID: 100
			ocx,
			// Token: 0x04000065 RID: 101
			lib,
			// Token: 0x04000066 RID: 102
			msi,
			// Token: 0x04000067 RID: 103
			obj,
			// Token: 0x04000068 RID: 104
			ps,
			// Token: 0x04000069 RID: 105
			eps
		}
	}
}

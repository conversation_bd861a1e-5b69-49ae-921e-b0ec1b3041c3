﻿using System;

namespace Ionic.Zlib
{
	// Token: 0x02000062 RID: 98
	public enum CompressionLevel
	{
		// Token: 0x04000373 RID: 883
		None,
		// Token: 0x04000374 RID: 884
		Level0 = 0,
		// Token: 0x04000375 RID: 885
		BestSpeed,
		// Token: 0x04000376 RID: 886
		Level1 = 1,
		// Token: 0x04000377 RID: 887
		Level2,
		// Token: 0x04000378 RID: 888
		Level3,
		// Token: 0x04000379 RID: 889
		Level4,
		// Token: 0x0400037A RID: 890
		Level5,
		// Token: 0x0400037B RID: 891
		Default,
		// Token: 0x0400037C RID: 892
		Level6 = 6,
		// Token: 0x0400037D RID: 893
		Level7,
		// Token: 0x0400037E RID: 894
		Level8,
		// Token: 0x0400037F RID: 895
		BestCompression,
		// Token: 0x04000380 RID: 896
		Level9 = 9
	}
}

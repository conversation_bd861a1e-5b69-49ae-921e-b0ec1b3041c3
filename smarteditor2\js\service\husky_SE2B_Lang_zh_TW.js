/*
Copyright (C) NAVER corp.  

This library is free software; you can redistribute it and/or  
modify it under the terms of the GNU Lesser General Public  
License as published by the Free Software Foundation; either  
version 2.1 of the License, or (at your option) any later version.  

This library is distributed in the hope that it will be useful,  
but WITHOUT ANY WARRANTY; without even the implied warranty of  
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU  
Lesser General Public License for more details.  

You should have received a copy of the GNU Lesser General Public  
License along with this library; if not, write to the Free Software  
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA  
*/
if(typeof window.nhn=='undefined'){window.nhn = {};}
/**
 * @fileOverview This file contains a message mapping(Korean), which is used to map the message code to the actual message
 * @name husky_SE2B_Lang_zh_TW.js
 * @ unescape
 */
var oMessageMap_zh_TW = {
	'SE_EditingAreaManager.onExit' : '內容已被更改。',
	'SE_Color.invalidColorCode' : '請輸入正確的顔色代碼。\n\n例子）#000000, #FF0000, #FFFFFF, #ffffff, ffffff',
	'SE_Hyperlink.invalidURL' : '輸錯URL',
	'SE_FindReplace.keywordMissing' : '請輸入要查詢的單詞。',
	'SE_FindReplace.keywordNotFound' : '要查詢的單詞不存在。',
	'SE_FindReplace.replaceAllResultP1' : '一致的內容總有',
	'SE_FindReplace.replaceAllResultP2' : '件已被更改。',
	'SE_FindReplace.notSupportedBrowser' : '當前的浏覽器上無法使用此功能。\n\n很抱歉給你添麻煩了。',
	'SE_FindReplace.replaceKeywordNotFound' : '要更改的詞彙不存在。',
	'SE_LineHeight.invalidLineHeight' : '爲有錯誤的值。',
	'SE_Footnote.defaultText' : '請輸入注腳內容。',
	'SE.failedToLoadFlash' : 'Flash被屏蔽，導致無法使用此功能。',
	'SE2M_EditingModeChanger.confirmTextMode' : '如轉換爲文本模式，雖然維持已寫成的內容，\n\n但字號等編輯效果和圖像等附加內容都會被消失。\n\n是否轉換？',
	'SE2M_FontNameWithLayerUI.sSampleText' : 'ABCD'
};

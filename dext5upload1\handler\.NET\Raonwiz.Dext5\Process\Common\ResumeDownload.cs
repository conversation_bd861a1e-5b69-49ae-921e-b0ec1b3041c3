﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x02000023 RID: 35
	public class ResumeDownload
	{
		// Token: 0x06000246 RID: 582 RVA: 0x0001CBCC File Offset: 0x0001ADCC
		public static void DownloadFile(HttpContext httpContext, string filePath, string fileName, string headerFileName, string tempPath, string uploadServerVersion, bool bUseDownloadServerFileName, int iBufferSize)
		{
			ResumeDownload._iBufferSize = iBufferSize;
			if (!ResumeDownload.IsFileExists(filePath))
			{
				httpContext.Response.StatusCode = 404;
				return;
			}
			HttpResponseHeader responseHeader = ResumeDownload.GetResponseHeader(httpContext.Request, filePath, headerFileName);
			if (responseHeader == null)
			{
				return;
			}
			try
			{
				ResumeDownload.SendDownloadFile(httpContext.Request, httpContext.Response, responseHeader, filePath, fileName, tempPath, uploadServerVersion, bUseDownloadServerFileName);
			}
			catch (HttpException ex)
			{
				httpContext.Response.StatusCode = ex.GetHttpCode();
			}
		}

		// Token: 0x06000247 RID: 583 RVA: 0x0001CC4C File Offset: 0x0001AE4C
		private static bool IsFileExists(string filePath)
		{
			bool result = false;
			if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
			{
				result = true;
			}
			return result;
		}

		// Token: 0x06000248 RID: 584 RVA: 0x0001CC70 File Offset: 0x0001AE70
		private static HttpResponseHeader GetResponseHeader(HttpRequest httpRequest, string filePath, string headerFileName)
		{
			if (httpRequest == null)
			{
				return null;
			}
			if (string.IsNullOrEmpty(filePath))
			{
				return null;
			}
			FileInfo fileInfo = new FileInfo(filePath);
			long num = 0L;
			long num2 = -1L;
			string name = fileInfo.Name;
			long length = fileInfo.Length;
			string text = fileInfo.LastWriteTimeUtc.ToString();
			string text2 = HttpUtility.UrlEncode(name, Encoding.UTF8) + " " + text;
			string contentDisposition = "attachment;filename=\"" + headerFileName + "\"";
			if (httpRequest.Headers["Range"] != null)
			{
				string[] array = httpRequest.Headers["Range"].Split(new char[]
				{
					'=',
					'-'
				});
				num = Convert.ToInt64(array[1]);
				if (num < 0L || num >= length)
				{
					return null;
				}
				if (array.Length == 3 && !string.IsNullOrEmpty(array[2]))
				{
					num2 = Convert.ToInt64(array[2]);
					if (num2 <= 0L || num2 >= length)
					{
						if (num != 0L)
						{
							return null;
						}
						num2 = length - 1L;
					}
				}
			}
			if (httpRequest.Headers["If-Range"] != null && httpRequest.Headers["If-Range"].Replace("\"", "") != text2)
			{
				num = 0L;
			}
			string contentLength = string.Empty;
			if (num2 == -1L)
			{
				contentLength = (length - num).ToString();
			}
			else
			{
				contentLength = (num2 - num + 1L).ToString();
			}
			string contentRange;
			if (num2 == -1L)
			{
				contentRange = string.Format(" bytes {0}-{1}/{2}", num, length - 1L, length);
			}
			else
			{
				contentRange = string.Format(" bytes {0}-{1}/{2}", num, num2, length);
			}
			return new HttpResponseHeader
			{
				AcceptRanges = "bytes",
				Connection = "Keep-Alive",
				ContentDisposition = contentDisposition,
				ContentEncoding = Encoding.UTF8,
				ContentLength = contentLength,
				ContentRange = contentRange,
				ContentType = Dext5Mime.GetMimeType(headerFileName),
				Etag = text2,
				LastModified = text
			};
		}

		// Token: 0x06000249 RID: 585 RVA: 0x0001CEA0 File Offset: 0x0001B0A0
		private static void SendDownloadFile(HttpRequest httpRequest, HttpResponse httpResponse, HttpResponseHeader responseHeader, string filePath, string fileName, string tempPath, string uploadServerVersion, bool bUseDownloadServerFileName)
		{
			if (httpResponse == null || responseHeader == null)
			{
				return;
			}
			FileInfo fileInfo = new FileInfo(filePath);
			httpResponse.Clear();
			httpResponse.BufferOutput = false;
			if (bUseDownloadServerFileName)
			{
				httpResponse.AddHeader("X-Raon-FName", HttpUtility.UrlEncode(fileName).Replace("+", "%20"));
			}
			string text = httpRequest.Headers["X-Raon-Fdi"];
			if (!string.IsNullOrEmpty(text) && text == "hmc-sha256")
			{
				string integrityHashValue = ResumeDownload.getIntegrityHashValue(filePath, uploadServerVersion);
				httpResponse.AppendHeader("X-Raon-Fdi", integrityHashValue);
			}
			httpResponse.AppendHeader("Accept-Ranges", responseHeader.AcceptRanges);
			httpResponse.AppendHeader("Connection", responseHeader.Connection);
			httpResponse.AppendHeader("Content-Disposition", responseHeader.ContentDisposition);
			httpResponse.ContentEncoding = responseHeader.ContentEncoding;
			httpResponse.ContentType = responseHeader.ContentType;
			httpResponse.AppendHeader("Etag", "\"" + responseHeader.Etag + "\"");
			httpResponse.AppendHeader("Last-Modified", responseHeader.LastModified);
			try
			{
				string text2 = httpRequest.Headers["X-Raon-Fde"];
				if (!string.IsNullOrEmpty(text2) && text2 == "securedAes")
				{
					string text3 = httpRequest.Headers["X-Raon-Guid"];
					string name = fileInfo.Name;
					int num = name.LastIndexOf(".");
					string text4 = name;
					if (num > -1)
					{
						text4 = name.Substring(0, num);
					}
					string extension = fileInfo.Extension;
					string text5 = string.Concat(new object[]
					{
						tempPath,
						ResumeDownload.m_PathChar,
						text3,
						ResumeDownload.m_PathChar,
						text4,
						"_encrypt",
						extension
					});
					if (!File.Exists(text5))
					{
						string s = uploadServerVersion.Substring(0, 11);
						byte[] array = new byte[ResumeDownload._cipherRoanKeySize];
						byte[] bytes = Encoding.UTF8.GetBytes(s);
						Array.Copy(bytes, array, Math.Min(array.Length, bytes.Length));
						ICryptoTransform transform = new RijndaelManaged
						{
							KeySize = 128,
							BlockSize = 128,
							Mode = CipherMode.CBC,
							Padding = PaddingMode.PKCS7,
							Key = array,
							IV = array
						}.CreateEncryptor();
						using (FileStream fileStream = new FileStream(text5, FileMode.Create))
						{
							using (CryptoStream cryptoStream = new CryptoStream(fileStream, transform, CryptoStreamMode.Write))
							{
								using (FileStream fileStream2 = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
								{
									byte[] array2 = new byte[ResumeDownload._iBufferSize];
									for (int i = fileStream2.Read(array2, 0, array2.Length); i > 0; i = fileStream2.Read(array2, 0, array2.Length))
									{
										cryptoStream.Write(array2, 0, i);
									}
									fileStream2.Close();
								}
								cryptoStream.FlushFinalBlock();
								cryptoStream.Close();
							}
						}
					}
					if (httpRequest.Headers["Range"] != null && !string.IsNullOrEmpty(responseHeader.ContentRange))
					{
						httpResponse.StatusCode = 206;
					}
					FileInfo fileInfo2 = new FileInfo(text5);
					long num2 = 0L;
					long num3 = -1L;
					if (httpRequest.Headers["Range"] != null)
					{
						string[] array3 = httpRequest.Headers["Range"].Split(new char[]
						{
							'=',
							'-'
						});
						num2 = Convert.ToInt64(array3[1]);
						if (num2 < 0L || num2 >= fileInfo2.Length)
						{
							num2 = 0L;
						}
						if (array3.Length == 3 && !string.IsNullOrEmpty(array3[2]))
						{
							num3 = Convert.ToInt64(array3[2]);
							if (num3 <= 0L || num3 >= fileInfo2.Length)
							{
								if (num2 == 0L)
								{
									num3 = fileInfo2.Length - 1L;
								}
								else
								{
									num3 = -1L;
								}
							}
						}
						if (num3 == -1L)
						{
							httpResponse.AppendHeader("Content-Range", string.Format(" bytes {0}-{1}/{2}", num2, fileInfo2.Length - 1L, fileInfo2.Length));
						}
						else
						{
							httpResponse.AppendHeader("Content-Range", string.Format(" bytes {0}-{1}/{2}", num2, num3, fileInfo2.Length));
						}
					}
					if (num3 == -1L)
					{
						httpResponse.AppendHeader("Content-Length", (fileInfo2.Length - num2).ToString());
						httpResponse.AppendHeader("X-Raon-Cl", (fileInfo2.Length - num2).ToString());
					}
					else
					{
						httpResponse.AppendHeader("Content-Length", (num3 - num2 + 1L).ToString());
						httpResponse.AppendHeader("X-Raon-Cl", (num3 - num2 + 1L).ToString());
					}
					Stream stream = new FileStream(text5, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
					if (num2 > 0L)
					{
						stream.Position = num2;
					}
					byte[] array4 = new byte[ResumeDownload._iBufferSize];
					long num4 = fileInfo2.Length;
					while (num4 > 0L)
					{
						if (httpResponse.IsClientConnected)
						{
							int num5 = stream.Read(array4, 0, array4.Length);
							httpResponse.OutputStream.Write(array4, 0, num5);
							num4 -= (long)num5;
						}
						else
						{
							num4 = -1L;
						}
					}
					httpResponse.Flush();
				}
				else
				{
					if (httpRequest.Headers["Range"] != null && !string.IsNullOrEmpty(responseHeader.ContentRange))
					{
						httpResponse.AppendHeader("Content-Range", responseHeader.ContentRange);
					}
					Stream stream2 = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
					if (!string.IsNullOrEmpty(responseHeader.ContentRange))
					{
						if (httpRequest.Headers["Range"] != null)
						{
							httpResponse.StatusCode = 206;
						}
						string[] array5 = responseHeader.ContentRange.Split(new char[]
						{
							' ',
							'=',
							'-'
						});
						stream2.Position = Convert.ToInt64(array5[2]);
					}
					httpResponse.AppendHeader("Content-Length", responseHeader.ContentLength);
					httpResponse.AppendHeader("X-Raon-Cl", responseHeader.ContentLength);
					byte[] array6 = new byte[ResumeDownload._iBufferSize];
					long num6 = Convert.ToInt64(responseHeader.ContentLength);
					while (num6 > 0L)
					{
						if (httpResponse.IsClientConnected)
						{
							int num7 = stream2.Read(array6, 0, array6.Length);
							httpResponse.OutputStream.Write(array6, 0, num7);
							num6 -= (long)num7;
						}
						else
						{
							num6 = -1L;
						}
					}
					httpResponse.Flush();
				}
			}
			catch
			{
			}
		}

		// Token: 0x0600024A RID: 586 RVA: 0x0001D558 File Offset: 0x0001B758
		protected static string getIntegrityHashValue(string filePath, string uploadServerVersion)
		{
			string s = uploadServerVersion.Substring(0, 11);
			ASCIIEncoding asciiencoding = new ASCIIEncoding();
			byte[] bytes = asciiencoding.GetBytes(s);
			string text = string.Empty;
			using (HMACSHA256 hmacsha = new HMACSHA256(bytes))
			{
				using (FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
				{
					byte[] array = hmacsha.ComputeHash(fileStream);
					foreach (byte b in array)
					{
						text += string.Format("{0:x2}", b);
					}
					fileStream.Close();
				}
			}
			return text;
		}

		// Token: 0x0400013A RID: 314
		protected static int _cipherRoanKeySize = 16;

		// Token: 0x0400013B RID: 315
		protected static char m_PathChar = Path.DirectorySeparatorChar;

		// Token: 0x0400013C RID: 316
		protected static int _iBufferSize;
	}
}

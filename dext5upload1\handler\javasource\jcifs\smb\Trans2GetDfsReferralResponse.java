package jcifs.smb;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2GetDfsReferralResponse.class */
class Trans2GetDfsReferralResponse extends SmbComTransactionResponse {
    int pathConsumed;
    int numReferrals;
    int flags;
    Referral[] referrals;

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/Trans2GetDfsReferralResponse$Referral.class */
    class Referral {
        private int version;
        private int size;
        private int serverType;
        private int flags;
        private int proximity;
        private int pathOffset;
        private int altPathOffset;
        private int nodeOffset;
        private String altPath;
        int ttl;
        String path = null;
        String node = null;

        Referral() {
        }

        int readWireFormat(byte[] buffer, int bufferIndex, int len) {
            this.version = ServerMessageBlock.readInt2(buffer, bufferIndex);
            if (this.version != 3 && this.version != 1) {
                throw new RuntimeException("Version " + this.version + " referral not supported. Please report this to jcifs at samba dot org.");
            }
            int bufferIndex2 = bufferIndex + 2;
            this.size = ServerMessageBlock.readInt2(buffer, bufferIndex2);
            int bufferIndex3 = bufferIndex2 + 2;
            this.serverType = ServerMessageBlock.readInt2(buffer, bufferIndex3);
            int bufferIndex4 = bufferIndex3 + 2;
            this.flags = ServerMessageBlock.readInt2(buffer, bufferIndex4);
            int bufferIndex5 = bufferIndex4 + 2;
            if (this.version == 3) {
                this.proximity = ServerMessageBlock.readInt2(buffer, bufferIndex5);
                int bufferIndex6 = bufferIndex5 + 2;
                this.ttl = ServerMessageBlock.readInt2(buffer, bufferIndex6);
                int bufferIndex7 = bufferIndex6 + 2;
                this.pathOffset = ServerMessageBlock.readInt2(buffer, bufferIndex7);
                int bufferIndex8 = bufferIndex7 + 2;
                this.altPathOffset = ServerMessageBlock.readInt2(buffer, bufferIndex8);
                int bufferIndex9 = bufferIndex8 + 2;
                this.nodeOffset = ServerMessageBlock.readInt2(buffer, bufferIndex9);
                int i = bufferIndex9 + 2;
                this.path = Trans2GetDfsReferralResponse.this.readString(buffer, bufferIndex + this.pathOffset, len, (Trans2GetDfsReferralResponse.this.flags2 & 32768) != 0);
                if (this.nodeOffset > 0) {
                    this.node = Trans2GetDfsReferralResponse.this.readString(buffer, bufferIndex + this.nodeOffset, len, (Trans2GetDfsReferralResponse.this.flags2 & 32768) != 0);
                }
            } else if (this.version == 1) {
                this.node = Trans2GetDfsReferralResponse.this.readString(buffer, bufferIndex5, len, (Trans2GetDfsReferralResponse.this.flags2 & 32768) != 0);
            }
            return this.size;
        }

        public String toString() {
            return new String("Referral[version=" + this.version + ",size=" + this.size + ",serverType=" + this.serverType + ",flags=" + this.flags + ",proximity=" + this.proximity + ",ttl=" + this.ttl + ",pathOffset=" + this.pathOffset + ",altPathOffset=" + this.altPathOffset + ",nodeOffset=" + this.nodeOffset + ",path=" + this.path + ",altPath=" + this.altPath + ",node=" + this.node + "]");
        }
    }

    Trans2GetDfsReferralResponse() {
        this.subCommand = (byte) 16;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeSetupWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeParametersWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int writeDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readSetupWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readParametersWireFormat(byte[] buffer, int bufferIndex, int len) {
        return 0;
    }

    @Override // jcifs.smb.SmbComTransactionResponse
    int readDataWireFormat(byte[] buffer, int bufferIndex, int len) {
        this.pathConsumed = readInt2(buffer, bufferIndex);
        int bufferIndex2 = bufferIndex + 2;
        if ((this.flags2 & 32768) != 0) {
            this.pathConsumed /= 2;
        }
        this.numReferrals = readInt2(buffer, bufferIndex2);
        int bufferIndex3 = bufferIndex2 + 2;
        this.flags = readInt2(buffer, bufferIndex3);
        int bufferIndex4 = bufferIndex3 + 4;
        this.referrals = new Referral[this.numReferrals];
        for (int ri = 0; ri < this.numReferrals; ri++) {
            this.referrals[ri] = new Referral();
            bufferIndex4 += this.referrals[ri].readWireFormat(buffer, bufferIndex4, len);
        }
        return bufferIndex4 - bufferIndex;
    }

    @Override // jcifs.smb.SmbComTransactionResponse, jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("Trans2GetDfsReferralResponse[" + super.toString() + ",pathConsumed=" + this.pathConsumed + ",numReferrals=" + this.numReferrals + ",flags=" + this.flags + "]");
    }
}

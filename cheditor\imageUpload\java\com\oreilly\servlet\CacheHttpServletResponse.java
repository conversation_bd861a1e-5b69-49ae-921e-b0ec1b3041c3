package com.oreilly.servlet;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Locale;
import java.util.Vector;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

/* compiled from: CacheHttpServlet.java */
/* loaded from: cos.jar:com/oreilly/servlet/CacheHttpServletResponse.class */
class CacheHttpServletResponse implements HttpServletResponse {
    private int status;
    private Hashtable headers;
    private int contentLength;
    private String contentType;
    private Locale locale;
    private Vector cookies;
    private boolean didError;
    private boolean didRedirect;
    private boolean gotStream;
    private boolean gotWriter;
    private HttpServletResponse delegate;
    private CacheServletOutputStream out;
    private PrintWriter writer;

    CacheHttpServletResponse(HttpServletResponse httpServletResponse) {
        this.delegate = httpServletResponse;
        try {
            this.out = new CacheServletOutputStream(httpServletResponse.getOutputStream());
        } catch (IOException e) {
            System.out.println(new StringBuffer().append("Got IOException constructing cached response: ").append(e.getMessage()).toString());
        }
        internalReset();
    }

    private void internalReset() {
        this.status = 200;
        this.headers = new Hashtable();
        this.contentLength = -1;
        this.contentType = null;
        this.locale = null;
        this.cookies = new Vector();
        this.didError = false;
        this.didRedirect = false;
        this.gotStream = false;
        this.gotWriter = false;
        this.out.getBuffer().reset();
    }

    public boolean isValid() {
        return (this.didError || this.didRedirect) ? false : true;
    }

    private void internalSetHeader(String str, Object obj) {
        Vector vector = new Vector();
        vector.addElement(obj);
        this.headers.put(str, vector);
    }

    private void internalAddHeader(String str, Object obj) {
        Vector vector = (Vector) this.headers.get(str);
        if (vector == null) {
            vector = new Vector();
        }
        vector.addElement(obj);
        this.headers.put(str, vector);
    }

    public void writeTo(HttpServletResponse httpServletResponse) {
        httpServletResponse.setStatus(this.status);
        if (this.contentType != null) {
            httpServletResponse.setContentType(this.contentType);
        }
        if (this.locale != null) {
            httpServletResponse.setLocale(this.locale);
        }
        Enumeration elements = this.cookies.elements();
        while (elements.hasMoreElements()) {
            httpServletResponse.addCookie((Cookie) elements.nextElement());
        }
        Enumeration keys = this.headers.keys();
        while (keys.hasMoreElements()) {
            String str = (String) keys.nextElement();
            Enumeration elements2 = ((Vector) this.headers.get(str)).elements();
            while (elements2.hasMoreElements()) {
                Object nextElement = elements2.nextElement();
                if (nextElement instanceof String) {
                    httpServletResponse.setHeader(str, (String) nextElement);
                }
                if (nextElement instanceof Integer) {
                    httpServletResponse.setIntHeader(str, ((Integer) nextElement).intValue());
                }
                if (nextElement instanceof Long) {
                    httpServletResponse.setDateHeader(str, ((Long) nextElement).longValue());
                }
            }
        }
        httpServletResponse.setContentLength(this.out.getBuffer().size());
        try {
            this.out.getBuffer().writeTo(httpServletResponse.getOutputStream());
        } catch (IOException e) {
            System.out.println(new StringBuffer().append("Got IOException writing cached response: ").append(e.getMessage()).toString());
        }
    }

    public ServletOutputStream getOutputStream() throws IOException {
        if (this.gotWriter) {
            throw new IllegalStateException("Cannot get output stream after getting writer");
        }
        this.gotStream = true;
        return this.out;
    }

    public PrintWriter getWriter() throws UnsupportedEncodingException {
        if (this.gotStream) {
            throw new IllegalStateException("Cannot get writer after getting output stream");
        }
        this.gotWriter = true;
        if (this.writer == null) {
            this.writer = new PrintWriter((Writer) new OutputStreamWriter((OutputStream) this.out, getCharacterEncoding()), true);
        }
        return this.writer;
    }

    public void setContentLength(int i) {
        this.delegate.setContentLength(i);
    }

    public void setContentType(String str) {
        this.delegate.setContentType(str);
        this.contentType = str;
    }

    public String getCharacterEncoding() {
        return this.delegate.getCharacterEncoding();
    }

    public void setBufferSize(int i) throws IllegalStateException {
        this.delegate.setBufferSize(i);
    }

    public int getBufferSize() {
        return this.delegate.getBufferSize();
    }

    public void reset() throws IllegalStateException {
        this.delegate.reset();
        internalReset();
    }

    public void resetBuffer() throws IllegalStateException {
        this.delegate.resetBuffer();
        this.contentLength = -1;
        this.out.getBuffer().reset();
    }

    public boolean isCommitted() {
        return this.delegate.isCommitted();
    }

    public void flushBuffer() throws IOException {
        this.delegate.flushBuffer();
    }

    public void setLocale(Locale locale) {
        this.delegate.setLocale(locale);
        this.locale = locale;
    }

    public Locale getLocale() {
        return this.delegate.getLocale();
    }

    public void addCookie(Cookie cookie) {
        this.delegate.addCookie(cookie);
        this.cookies.addElement(cookie);
    }

    public boolean containsHeader(String str) {
        return this.delegate.containsHeader(str);
    }

    public void setStatus(int i, String str) {
        this.delegate.setStatus(i, str);
        this.status = i;
    }

    public void setStatus(int i) {
        this.delegate.setStatus(i);
        this.status = i;
    }

    public void setHeader(String str, String str2) {
        this.delegate.setHeader(str, str2);
        internalSetHeader(str, str2);
    }

    public void setIntHeader(String str, int i) {
        this.delegate.setIntHeader(str, i);
        internalSetHeader(str, new Integer(i));
    }

    public void setDateHeader(String str, long j) {
        this.delegate.setDateHeader(str, j);
        internalSetHeader(str, new Long(j));
    }

    public void sendError(int i, String str) throws IOException {
        this.delegate.sendError(i, str);
        this.didError = true;
    }

    public void sendError(int i) throws IOException {
        this.delegate.sendError(i);
        this.didError = true;
    }

    public void sendRedirect(String str) throws IOException {
        this.delegate.sendRedirect(str);
        this.didRedirect = true;
    }

    public String encodeURL(String str) {
        return this.delegate.encodeURL(str);
    }

    public String encodeRedirectURL(String str) {
        return this.delegate.encodeRedirectURL(str);
    }

    public void addHeader(String str, String str2) {
        internalAddHeader(str, str2);
    }

    public void addIntHeader(String str, int i) {
        internalAddHeader(str, new Integer(i));
    }

    public void addDateHeader(String str, long j) {
        internalAddHeader(str, new Long(j));
    }

    public String encodeUrl(String str) {
        return encodeURL(str);
    }

    public String encodeRedirectUrl(String str) {
        return encodeRedirectURL(str);
    }
}

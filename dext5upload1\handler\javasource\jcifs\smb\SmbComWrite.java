package jcifs.smb;

import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComWrite.class */
class SmbComWrite extends ServerMessageBlock {
    private int fid;
    private int count;
    private int offset;
    private int remaining;
    private int off;
    private byte[] b;

    SmbComWrite() {
        this.command = (byte) 11;
    }

    SmbComWrite(int fid, int offset, int remaining, byte[] b, int off, int len) {
        this.fid = fid;
        this.count = len;
        this.offset = offset;
        this.remaining = remaining;
        this.b = b;
        this.off = off;
        this.command = (byte) 11;
    }

    void setParam(int fid, long offset, int remaining, byte[] b, int off, int len) {
        this.fid = fid;
        this.offset = (int) (offset & InternalZipConstants.ZIP_64_LIMIT);
        this.remaining = remaining;
        this.b = b;
        this.off = off;
        this.count = len;
        this.digest = null;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.fid, dst, dstIndex);
        int dstIndex2 = dstIndex + 2;
        writeInt2(this.count, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 2;
        writeInt4(this.offset, dst, dstIndex3);
        int dstIndex4 = dstIndex3 + 4;
        writeInt2(this.remaining, dst, dstIndex4);
        return (dstIndex4 + 2) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = 1;
        writeInt2(this.count, dst, dstIndex2);
        int dstIndex3 = dstIndex2 + 2;
        System.arraycopy(this.b, this.off, dst, dstIndex3, this.count);
        return (dstIndex3 + this.count) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComWrite[" + super.toString() + ",fid=" + this.fid + ",count=" + this.count + ",offset=" + this.offset + ",remaining=" + this.remaining + "]");
    }
}

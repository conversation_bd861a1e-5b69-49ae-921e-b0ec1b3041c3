﻿using System;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000014 RID: 20
	public class ExifEntity
	{
		// Token: 0x17000049 RID: 73
		// (get) Token: 0x06000117 RID: 279 RVA: 0x00012369 File Offset: 0x00010569
		// (set) Token: 0x06000118 RID: 280 RVA: 0x00012371 File Offset: 0x00010571
		public string ImageWidth
		{
			get
			{
				return this._ImageWidth;
			}
			set
			{
				this._ImageWidth = value;
			}
		}

		// Token: 0x1700004A RID: 74
		// (get) Token: 0x06000119 RID: 281 RVA: 0x0001237A File Offset: 0x0001057A
		// (set) Token: 0x0600011A RID: 282 RVA: 0x00012382 File Offset: 0x00010582
		public string ImageHeight
		{
			get
			{
				return this._ImageHeight;
			}
			set
			{
				this._ImageHeight = value;
			}
		}

		// Token: 0x1700004B RID: 75
		// (get) Token: 0x0600011B RID: 283 RVA: 0x0001238B File Offset: 0x0001058B
		// (set) Token: 0x0600011C RID: 284 RVA: 0x00012393 File Offset: 0x00010593
		public string GPSVersionID
		{
			get
			{
				return this._GPSVersionID;
			}
			set
			{
				this._GPSVersionID = value;
			}
		}

		// Token: 0x1700004C RID: 76
		// (get) Token: 0x0600011D RID: 285 RVA: 0x0001239C File Offset: 0x0001059C
		// (set) Token: 0x0600011E RID: 286 RVA: 0x000123A4 File Offset: 0x000105A4
		public string GPSAltitudeRef
		{
			get
			{
				return this._GPSAltitudeRef;
			}
			set
			{
				this._GPSAltitudeRef = value;
			}
		}

		// Token: 0x1700004D RID: 77
		// (get) Token: 0x0600011F RID: 287 RVA: 0x000123AD File Offset: 0x000105AD
		// (set) Token: 0x06000120 RID: 288 RVA: 0x000123B5 File Offset: 0x000105B5
		public string StripOffsets
		{
			get
			{
				return this._StripOffsets;
			}
			set
			{
				this._StripOffsets = value;
			}
		}

		// Token: 0x1700004E RID: 78
		// (get) Token: 0x06000121 RID: 289 RVA: 0x000123BE File Offset: 0x000105BE
		// (set) Token: 0x06000122 RID: 290 RVA: 0x000123C6 File Offset: 0x000105C6
		public string RowsPerStrip
		{
			get
			{
				return this._RowsPerStrip;
			}
			set
			{
				this._RowsPerStrip = value;
			}
		}

		// Token: 0x1700004F RID: 79
		// (get) Token: 0x06000123 RID: 291 RVA: 0x000123CF File Offset: 0x000105CF
		// (set) Token: 0x06000124 RID: 292 RVA: 0x000123D7 File Offset: 0x000105D7
		public string StripByteCounts
		{
			get
			{
				return this._StripByteCounts;
			}
			set
			{
				this._StripByteCounts = value;
			}
		}

		// Token: 0x17000050 RID: 80
		// (get) Token: 0x06000125 RID: 293 RVA: 0x000123E0 File Offset: 0x000105E0
		// (set) Token: 0x06000126 RID: 294 RVA: 0x000123E8 File Offset: 0x000105E8
		public string PixelXDimension
		{
			get
			{
				return this._PixelXDimension;
			}
			set
			{
				this._PixelXDimension = value;
			}
		}

		// Token: 0x17000051 RID: 81
		// (get) Token: 0x06000127 RID: 295 RVA: 0x000123F1 File Offset: 0x000105F1
		// (set) Token: 0x06000128 RID: 296 RVA: 0x000123F9 File Offset: 0x000105F9
		public string PixelYDimension
		{
			get
			{
				return this._PixelYDimension;
			}
			set
			{
				this._PixelYDimension = value;
			}
		}

		// Token: 0x17000052 RID: 82
		// (get) Token: 0x06000129 RID: 297 RVA: 0x00012402 File Offset: 0x00010602
		// (set) Token: 0x0600012A RID: 298 RVA: 0x0001240A File Offset: 0x0001060A
		public string BitsPerSample
		{
			get
			{
				return this._BitsPerSample;
			}
			set
			{
				this._BitsPerSample = value;
			}
		}

		// Token: 0x17000053 RID: 83
		// (get) Token: 0x0600012B RID: 299 RVA: 0x00012413 File Offset: 0x00010613
		// (set) Token: 0x0600012C RID: 300 RVA: 0x0001241B File Offset: 0x0001061B
		public string Compression
		{
			get
			{
				return this._Compression;
			}
			set
			{
				this._Compression = value;
			}
		}

		// Token: 0x17000054 RID: 84
		// (get) Token: 0x0600012D RID: 301 RVA: 0x00012424 File Offset: 0x00010624
		// (set) Token: 0x0600012E RID: 302 RVA: 0x0001242C File Offset: 0x0001062C
		public string PhotometricInterpretation
		{
			get
			{
				return this._PhotometricInterpretation;
			}
			set
			{
				this._PhotometricInterpretation = value;
			}
		}

		// Token: 0x17000055 RID: 85
		// (get) Token: 0x0600012F RID: 303 RVA: 0x00012435 File Offset: 0x00010635
		// (set) Token: 0x06000130 RID: 304 RVA: 0x0001243D File Offset: 0x0001063D
		public string Orientation
		{
			get
			{
				return this._Orientation;
			}
			set
			{
				this._Orientation = value;
			}
		}

		// Token: 0x17000056 RID: 86
		// (get) Token: 0x06000131 RID: 305 RVA: 0x00012446 File Offset: 0x00010646
		// (set) Token: 0x06000132 RID: 306 RVA: 0x0001244E File Offset: 0x0001064E
		public string SamplesPerPixel
		{
			get
			{
				return this._SamplesPerPixel;
			}
			set
			{
				this._SamplesPerPixel = value;
			}
		}

		// Token: 0x17000057 RID: 87
		// (get) Token: 0x06000133 RID: 307 RVA: 0x00012457 File Offset: 0x00010657
		// (set) Token: 0x06000134 RID: 308 RVA: 0x0001245F File Offset: 0x0001065F
		public string PlanarConfiguration
		{
			get
			{
				return this._PlanarConfiguration;
			}
			set
			{
				this._PlanarConfiguration = value;
			}
		}

		// Token: 0x17000058 RID: 88
		// (get) Token: 0x06000135 RID: 309 RVA: 0x00012468 File Offset: 0x00010668
		// (set) Token: 0x06000136 RID: 310 RVA: 0x00012470 File Offset: 0x00010670
		public string YCbCrSubSampling
		{
			get
			{
				return this._YCbCrSubSampling;
			}
			set
			{
				this._YCbCrSubSampling = value;
			}
		}

		// Token: 0x17000059 RID: 89
		// (get) Token: 0x06000137 RID: 311 RVA: 0x00012479 File Offset: 0x00010679
		// (set) Token: 0x06000138 RID: 312 RVA: 0x00012481 File Offset: 0x00010681
		public string YCbCrPositioning
		{
			get
			{
				return this._YCbCrPositioning;
			}
			set
			{
				this._YCbCrPositioning = value;
			}
		}

		// Token: 0x1700005A RID: 90
		// (get) Token: 0x06000139 RID: 313 RVA: 0x0001248A File Offset: 0x0001068A
		// (set) Token: 0x0600013A RID: 314 RVA: 0x00012492 File Offset: 0x00010692
		public string ResolutionUnit
		{
			get
			{
				return this._ResolutionUnit;
			}
			set
			{
				this._ResolutionUnit = value;
			}
		}

		// Token: 0x1700005B RID: 91
		// (get) Token: 0x0600013B RID: 315 RVA: 0x0001249B File Offset: 0x0001069B
		// (set) Token: 0x0600013C RID: 316 RVA: 0x000124A3 File Offset: 0x000106A3
		public string TransferFunction
		{
			get
			{
				return this._TransferFunction;
			}
			set
			{
				this._TransferFunction = value;
			}
		}

		// Token: 0x1700005C RID: 92
		// (get) Token: 0x0600013D RID: 317 RVA: 0x000124AC File Offset: 0x000106AC
		// (set) Token: 0x0600013E RID: 318 RVA: 0x000124B4 File Offset: 0x000106B4
		public string ColorSpace
		{
			get
			{
				return this._ColorSpace;
			}
			set
			{
				this._ColorSpace = value;
			}
		}

		// Token: 0x1700005D RID: 93
		// (get) Token: 0x0600013F RID: 319 RVA: 0x000124BD File Offset: 0x000106BD
		// (set) Token: 0x06000140 RID: 320 RVA: 0x000124C5 File Offset: 0x000106C5
		public string ExposureProgram
		{
			get
			{
				return this._ExposureProgram;
			}
			set
			{
				this._ExposureProgram = value;
			}
		}

		// Token: 0x1700005E RID: 94
		// (get) Token: 0x06000141 RID: 321 RVA: 0x000124CE File Offset: 0x000106CE
		// (set) Token: 0x06000142 RID: 322 RVA: 0x000124D6 File Offset: 0x000106D6
		public string ISOSpeedRatings
		{
			get
			{
				return this._ISOSpeedRatings;
			}
			set
			{
				this._ISOSpeedRatings = value;
			}
		}

		// Token: 0x1700005F RID: 95
		// (get) Token: 0x06000143 RID: 323 RVA: 0x000124DF File Offset: 0x000106DF
		// (set) Token: 0x06000144 RID: 324 RVA: 0x000124E7 File Offset: 0x000106E7
		public string MeteringMode
		{
			get
			{
				return this._MeteringMode;
			}
			set
			{
				this._MeteringMode = value;
			}
		}

		// Token: 0x17000060 RID: 96
		// (get) Token: 0x06000145 RID: 325 RVA: 0x000124F0 File Offset: 0x000106F0
		// (set) Token: 0x06000146 RID: 326 RVA: 0x000124F8 File Offset: 0x000106F8
		public string LightSource
		{
			get
			{
				return this._LightSource;
			}
			set
			{
				this._LightSource = value;
			}
		}

		// Token: 0x17000061 RID: 97
		// (get) Token: 0x06000147 RID: 327 RVA: 0x00012501 File Offset: 0x00010701
		// (set) Token: 0x06000148 RID: 328 RVA: 0x00012509 File Offset: 0x00010709
		public string Flash
		{
			get
			{
				return this._Flash;
			}
			set
			{
				this._Flash = value;
			}
		}

		// Token: 0x17000062 RID: 98
		// (get) Token: 0x06000149 RID: 329 RVA: 0x00012512 File Offset: 0x00010712
		// (set) Token: 0x0600014A RID: 330 RVA: 0x0001251A File Offset: 0x0001071A
		public string SubjectArea
		{
			get
			{
				return this._SubjectArea;
			}
			set
			{
				this._SubjectArea = value;
			}
		}

		// Token: 0x17000063 RID: 99
		// (get) Token: 0x0600014B RID: 331 RVA: 0x00012523 File Offset: 0x00010723
		// (set) Token: 0x0600014C RID: 332 RVA: 0x0001252B File Offset: 0x0001072B
		public string FocalPlaneResolutionUnit
		{
			get
			{
				return this._FocalPlaneResolutionUnit;
			}
			set
			{
				this._FocalPlaneResolutionUnit = value;
			}
		}

		// Token: 0x17000064 RID: 100
		// (get) Token: 0x0600014D RID: 333 RVA: 0x00012534 File Offset: 0x00010734
		// (set) Token: 0x0600014E RID: 334 RVA: 0x0001253C File Offset: 0x0001073C
		public string SubjectLocation
		{
			get
			{
				return this._SubjectLocation;
			}
			set
			{
				this._SubjectLocation = value;
			}
		}

		// Token: 0x17000065 RID: 101
		// (get) Token: 0x0600014F RID: 335 RVA: 0x00012545 File Offset: 0x00010745
		// (set) Token: 0x06000150 RID: 336 RVA: 0x0001254D File Offset: 0x0001074D
		public string SensingMethod
		{
			get
			{
				return this._SensingMethod;
			}
			set
			{
				this._SensingMethod = value;
			}
		}

		// Token: 0x17000066 RID: 102
		// (get) Token: 0x06000151 RID: 337 RVA: 0x00012556 File Offset: 0x00010756
		// (set) Token: 0x06000152 RID: 338 RVA: 0x0001255E File Offset: 0x0001075E
		public string CustomRendered
		{
			get
			{
				return this._CustomRendered;
			}
			set
			{
				this._CustomRendered = value;
			}
		}

		// Token: 0x17000067 RID: 103
		// (get) Token: 0x06000153 RID: 339 RVA: 0x00012567 File Offset: 0x00010767
		// (set) Token: 0x06000154 RID: 340 RVA: 0x0001256F File Offset: 0x0001076F
		public string ExposureMode
		{
			get
			{
				return this._ExposureMode;
			}
			set
			{
				this._ExposureMode = value;
			}
		}

		// Token: 0x17000068 RID: 104
		// (get) Token: 0x06000155 RID: 341 RVA: 0x00012578 File Offset: 0x00010778
		// (set) Token: 0x06000156 RID: 342 RVA: 0x00012580 File Offset: 0x00010780
		public string WhiteBalance
		{
			get
			{
				return this._WhiteBalance;
			}
			set
			{
				this._WhiteBalance = value;
			}
		}

		// Token: 0x17000069 RID: 105
		// (get) Token: 0x06000157 RID: 343 RVA: 0x00012589 File Offset: 0x00010789
		// (set) Token: 0x06000158 RID: 344 RVA: 0x00012591 File Offset: 0x00010791
		public string FocalLengthIn35mmFilm
		{
			get
			{
				return this._FocalLengthIn35mmFilm;
			}
			set
			{
				this._FocalLengthIn35mmFilm = value;
			}
		}

		// Token: 0x1700006A RID: 106
		// (get) Token: 0x06000159 RID: 345 RVA: 0x0001259A File Offset: 0x0001079A
		// (set) Token: 0x0600015A RID: 346 RVA: 0x000125A2 File Offset: 0x000107A2
		public string SceneCaptureType
		{
			get
			{
				return this._SceneCaptureType;
			}
			set
			{
				this._SceneCaptureType = value;
			}
		}

		// Token: 0x1700006B RID: 107
		// (get) Token: 0x0600015B RID: 347 RVA: 0x000125AB File Offset: 0x000107AB
		// (set) Token: 0x0600015C RID: 348 RVA: 0x000125B3 File Offset: 0x000107B3
		public string Contrast
		{
			get
			{
				return this._Contrast;
			}
			set
			{
				this._Contrast = value;
			}
		}

		// Token: 0x1700006C RID: 108
		// (get) Token: 0x0600015D RID: 349 RVA: 0x000125BC File Offset: 0x000107BC
		// (set) Token: 0x0600015E RID: 350 RVA: 0x000125C4 File Offset: 0x000107C4
		public string Saturation
		{
			get
			{
				return this._Saturation;
			}
			set
			{
				this._Saturation = value;
			}
		}

		// Token: 0x1700006D RID: 109
		// (get) Token: 0x0600015F RID: 351 RVA: 0x000125CD File Offset: 0x000107CD
		// (set) Token: 0x06000160 RID: 352 RVA: 0x000125D5 File Offset: 0x000107D5
		public string Sharpness
		{
			get
			{
				return this._Sharpness;
			}
			set
			{
				this._Sharpness = value;
			}
		}

		// Token: 0x1700006E RID: 110
		// (get) Token: 0x06000161 RID: 353 RVA: 0x000125DE File Offset: 0x000107DE
		// (set) Token: 0x06000162 RID: 354 RVA: 0x000125E6 File Offset: 0x000107E6
		public string SubjectDistanceRange
		{
			get
			{
				return this._SubjectDistanceRange;
			}
			set
			{
				this._SubjectDistanceRange = value;
			}
		}

		// Token: 0x1700006F RID: 111
		// (get) Token: 0x06000163 RID: 355 RVA: 0x000125EF File Offset: 0x000107EF
		// (set) Token: 0x06000164 RID: 356 RVA: 0x000125F7 File Offset: 0x000107F7
		public string GPSDifferential
		{
			get
			{
				return this._GPSDifferential;
			}
			set
			{
				this._GPSDifferential = value;
			}
		}

		// Token: 0x17000070 RID: 112
		// (get) Token: 0x06000165 RID: 357 RVA: 0x00012600 File Offset: 0x00010800
		// (set) Token: 0x06000166 RID: 358 RVA: 0x00012608 File Offset: 0x00010808
		public string ShutterSpeedValue
		{
			get
			{
				return this._ShutterSpeedValue;
			}
			set
			{
				this._ShutterSpeedValue = value;
			}
		}

		// Token: 0x17000071 RID: 113
		// (get) Token: 0x06000167 RID: 359 RVA: 0x00012611 File Offset: 0x00010811
		// (set) Token: 0x06000168 RID: 360 RVA: 0x00012619 File Offset: 0x00010819
		public string BrightnessValue
		{
			get
			{
				return this._BrightnessValue;
			}
			set
			{
				this._BrightnessValue = value;
			}
		}

		// Token: 0x17000072 RID: 114
		// (get) Token: 0x06000169 RID: 361 RVA: 0x00012622 File Offset: 0x00010822
		// (set) Token: 0x0600016A RID: 362 RVA: 0x0001262A File Offset: 0x0001082A
		public string ExposureBiasValue
		{
			get
			{
				return this._ExposureBiasValue;
			}
			set
			{
				this._ExposureBiasValue = value;
			}
		}

		// Token: 0x17000073 RID: 115
		// (get) Token: 0x0600016B RID: 363 RVA: 0x00012633 File Offset: 0x00010833
		// (set) Token: 0x0600016C RID: 364 RVA: 0x0001263B File Offset: 0x0001083B
		public string JPEGInterchangeFormat
		{
			get
			{
				return this._JPEGInterchangeFormat;
			}
			set
			{
				this._JPEGInterchangeFormat = value;
			}
		}

		// Token: 0x17000074 RID: 116
		// (get) Token: 0x0600016D RID: 365 RVA: 0x00012644 File Offset: 0x00010844
		// (set) Token: 0x0600016E RID: 366 RVA: 0x0001264C File Offset: 0x0001084C
		public string JPEGInterchangeFormatLength
		{
			get
			{
				return this._JPEGInterchangeFormatLength;
			}
			set
			{
				this._JPEGInterchangeFormatLength = value;
			}
		}

		// Token: 0x17000075 RID: 117
		// (get) Token: 0x0600016F RID: 367 RVA: 0x00012655 File Offset: 0x00010855
		// (set) Token: 0x06000170 RID: 368 RVA: 0x0001265D File Offset: 0x0001085D
		public string XResolution
		{
			get
			{
				return this._XResolution;
			}
			set
			{
				this._XResolution = value;
			}
		}

		// Token: 0x17000076 RID: 118
		// (get) Token: 0x06000171 RID: 369 RVA: 0x00012666 File Offset: 0x00010866
		// (set) Token: 0x06000172 RID: 370 RVA: 0x0001266E File Offset: 0x0001086E
		public string YResolution
		{
			get
			{
				return this._YResolution;
			}
			set
			{
				this._YResolution = value;
			}
		}

		// Token: 0x17000077 RID: 119
		// (get) Token: 0x06000173 RID: 371 RVA: 0x00012677 File Offset: 0x00010877
		// (set) Token: 0x06000174 RID: 372 RVA: 0x0001267F File Offset: 0x0001087F
		public string WhitePoint
		{
			get
			{
				return this._WhitePoint;
			}
			set
			{
				this._WhitePoint = value;
			}
		}

		// Token: 0x17000078 RID: 120
		// (get) Token: 0x06000175 RID: 373 RVA: 0x00012688 File Offset: 0x00010888
		// (set) Token: 0x06000176 RID: 374 RVA: 0x00012690 File Offset: 0x00010890
		public string PrimaryChromaticities
		{
			get
			{
				return this._PrimaryChromaticities;
			}
			set
			{
				this._PrimaryChromaticities = value;
			}
		}

		// Token: 0x17000079 RID: 121
		// (get) Token: 0x06000177 RID: 375 RVA: 0x00012699 File Offset: 0x00010899
		// (set) Token: 0x06000178 RID: 376 RVA: 0x000126A1 File Offset: 0x000108A1
		public string YCbCrCoefficients
		{
			get
			{
				return this._YCbCrCoefficients;
			}
			set
			{
				this._YCbCrCoefficients = value;
			}
		}

		// Token: 0x1700007A RID: 122
		// (get) Token: 0x06000179 RID: 377 RVA: 0x000126AA File Offset: 0x000108AA
		// (set) Token: 0x0600017A RID: 378 RVA: 0x000126B2 File Offset: 0x000108B2
		public string ReferenceBlackWhite
		{
			get
			{
				return this._ReferenceBlackWhite;
			}
			set
			{
				this._ReferenceBlackWhite = value;
			}
		}

		// Token: 0x1700007B RID: 123
		// (get) Token: 0x0600017B RID: 379 RVA: 0x000126BB File Offset: 0x000108BB
		// (set) Token: 0x0600017C RID: 380 RVA: 0x000126C3 File Offset: 0x000108C3
		public string CompressedBitsPerPixel
		{
			get
			{
				return this._CompressedBitsPerPixel;
			}
			set
			{
				this._CompressedBitsPerPixel = value;
			}
		}

		// Token: 0x1700007C RID: 124
		// (get) Token: 0x0600017D RID: 381 RVA: 0x000126CC File Offset: 0x000108CC
		// (set) Token: 0x0600017E RID: 382 RVA: 0x000126D4 File Offset: 0x000108D4
		public string ExposureTime
		{
			get
			{
				return this._ExposureTime;
			}
			set
			{
				this._ExposureTime = value;
			}
		}

		// Token: 0x1700007D RID: 125
		// (get) Token: 0x0600017F RID: 383 RVA: 0x000126DD File Offset: 0x000108DD
		// (set) Token: 0x06000180 RID: 384 RVA: 0x000126E5 File Offset: 0x000108E5
		public string FNumber
		{
			get
			{
				return this._FNumber;
			}
			set
			{
				this._FNumber = value;
			}
		}

		// Token: 0x1700007E RID: 126
		// (get) Token: 0x06000181 RID: 385 RVA: 0x000126EE File Offset: 0x000108EE
		// (set) Token: 0x06000182 RID: 386 RVA: 0x000126F6 File Offset: 0x000108F6
		public string ApertureValue
		{
			get
			{
				return this._ApertureValue;
			}
			set
			{
				this._ApertureValue = value;
			}
		}

		// Token: 0x1700007F RID: 127
		// (get) Token: 0x06000183 RID: 387 RVA: 0x000126FF File Offset: 0x000108FF
		// (set) Token: 0x06000184 RID: 388 RVA: 0x00012707 File Offset: 0x00010907
		public string MaxApertureValue
		{
			get
			{
				return this._MaxApertureValue;
			}
			set
			{
				this._MaxApertureValue = value;
			}
		}

		// Token: 0x17000080 RID: 128
		// (get) Token: 0x06000185 RID: 389 RVA: 0x00012710 File Offset: 0x00010910
		// (set) Token: 0x06000186 RID: 390 RVA: 0x00012718 File Offset: 0x00010918
		public string SubjectDistance
		{
			get
			{
				return this._SubjectDistance;
			}
			set
			{
				this._SubjectDistance = value;
			}
		}

		// Token: 0x17000081 RID: 129
		// (get) Token: 0x06000187 RID: 391 RVA: 0x00012721 File Offset: 0x00010921
		// (set) Token: 0x06000188 RID: 392 RVA: 0x00012729 File Offset: 0x00010929
		public string FocalLength
		{
			get
			{
				return this._FocalLength;
			}
			set
			{
				this._FocalLength = value;
			}
		}

		// Token: 0x17000082 RID: 130
		// (get) Token: 0x06000189 RID: 393 RVA: 0x00012732 File Offset: 0x00010932
		// (set) Token: 0x0600018A RID: 394 RVA: 0x0001273A File Offset: 0x0001093A
		public string FlashEnergy
		{
			get
			{
				return this._FlashEnergy;
			}
			set
			{
				this._FlashEnergy = value;
			}
		}

		// Token: 0x17000083 RID: 131
		// (get) Token: 0x0600018B RID: 395 RVA: 0x00012743 File Offset: 0x00010943
		// (set) Token: 0x0600018C RID: 396 RVA: 0x0001274B File Offset: 0x0001094B
		public string FocalPlaneXResolution
		{
			get
			{
				return this._FocalPlaneXResolution;
			}
			set
			{
				this._FocalPlaneXResolution = value;
			}
		}

		// Token: 0x17000084 RID: 132
		// (get) Token: 0x0600018D RID: 397 RVA: 0x00012754 File Offset: 0x00010954
		// (set) Token: 0x0600018E RID: 398 RVA: 0x0001275C File Offset: 0x0001095C
		public string FocalPlaneYResolution
		{
			get
			{
				return this._FocalPlaneYResolution;
			}
			set
			{
				this._FocalPlaneYResolution = value;
			}
		}

		// Token: 0x17000085 RID: 133
		// (get) Token: 0x0600018F RID: 399 RVA: 0x00012765 File Offset: 0x00010965
		// (set) Token: 0x06000190 RID: 400 RVA: 0x0001276D File Offset: 0x0001096D
		public string ExposureIndex
		{
			get
			{
				return this._ExposureIndex;
			}
			set
			{
				this._ExposureIndex = value;
			}
		}

		// Token: 0x17000086 RID: 134
		// (get) Token: 0x06000191 RID: 401 RVA: 0x00012776 File Offset: 0x00010976
		// (set) Token: 0x06000192 RID: 402 RVA: 0x0001277E File Offset: 0x0001097E
		public string DigitalZoomRatio
		{
			get
			{
				return this._DigitalZoomRatio;
			}
			set
			{
				this._DigitalZoomRatio = value;
			}
		}

		// Token: 0x17000087 RID: 135
		// (get) Token: 0x06000193 RID: 403 RVA: 0x00012787 File Offset: 0x00010987
		// (set) Token: 0x06000194 RID: 404 RVA: 0x0001278F File Offset: 0x0001098F
		public string GainControl
		{
			get
			{
				return this._GainControl;
			}
			set
			{
				this._GainControl = value;
			}
		}

		// Token: 0x17000088 RID: 136
		// (get) Token: 0x06000195 RID: 405 RVA: 0x00012798 File Offset: 0x00010998
		// (set) Token: 0x06000196 RID: 406 RVA: 0x000127A0 File Offset: 0x000109A0
		public string GPSLatitude
		{
			get
			{
				return this._GPSLatitude;
			}
			set
			{
				this._GPSLatitude = value;
			}
		}

		// Token: 0x17000089 RID: 137
		// (get) Token: 0x06000197 RID: 407 RVA: 0x000127A9 File Offset: 0x000109A9
		// (set) Token: 0x06000198 RID: 408 RVA: 0x000127B1 File Offset: 0x000109B1
		public string GPSLongitude
		{
			get
			{
				return this._GPSLongitude;
			}
			set
			{
				this._GPSLongitude = value;
			}
		}

		// Token: 0x1700008A RID: 138
		// (get) Token: 0x06000199 RID: 409 RVA: 0x000127BA File Offset: 0x000109BA
		// (set) Token: 0x0600019A RID: 410 RVA: 0x000127C2 File Offset: 0x000109C2
		public string GPSAltitude
		{
			get
			{
				return this._GPSAltitude;
			}
			set
			{
				this._GPSAltitude = value;
			}
		}

		// Token: 0x1700008B RID: 139
		// (get) Token: 0x0600019B RID: 411 RVA: 0x000127CB File Offset: 0x000109CB
		// (set) Token: 0x0600019C RID: 412 RVA: 0x000127D3 File Offset: 0x000109D3
		public string GPSTimeStamp
		{
			get
			{
				return this._GPSTimeStamp;
			}
			set
			{
				this._GPSTimeStamp = value;
			}
		}

		// Token: 0x1700008C RID: 140
		// (get) Token: 0x0600019D RID: 413 RVA: 0x000127DC File Offset: 0x000109DC
		// (set) Token: 0x0600019E RID: 414 RVA: 0x000127E4 File Offset: 0x000109E4
		public string GPSDOP
		{
			get
			{
				return this._GPSDOP;
			}
			set
			{
				this._GPSDOP = value;
			}
		}

		// Token: 0x1700008D RID: 141
		// (get) Token: 0x0600019F RID: 415 RVA: 0x000127ED File Offset: 0x000109ED
		// (set) Token: 0x060001A0 RID: 416 RVA: 0x000127F5 File Offset: 0x000109F5
		public string GPSSpeed
		{
			get
			{
				return this._GPSSpeed;
			}
			set
			{
				this._GPSSpeed = value;
			}
		}

		// Token: 0x1700008E RID: 142
		// (get) Token: 0x060001A1 RID: 417 RVA: 0x000127FE File Offset: 0x000109FE
		// (set) Token: 0x060001A2 RID: 418 RVA: 0x00012806 File Offset: 0x00010A06
		public string GPSTrack
		{
			get
			{
				return this._GPSTrack;
			}
			set
			{
				this._GPSTrack = value;
			}
		}

		// Token: 0x1700008F RID: 143
		// (get) Token: 0x060001A3 RID: 419 RVA: 0x0001280F File Offset: 0x00010A0F
		// (set) Token: 0x060001A4 RID: 420 RVA: 0x00012817 File Offset: 0x00010A17
		public string GPSImgDirection
		{
			get
			{
				return this._GPSImgDirection;
			}
			set
			{
				this._GPSImgDirection = value;
			}
		}

		// Token: 0x17000090 RID: 144
		// (get) Token: 0x060001A5 RID: 421 RVA: 0x00012820 File Offset: 0x00010A20
		// (set) Token: 0x060001A6 RID: 422 RVA: 0x00012828 File Offset: 0x00010A28
		public string GPSDestLatitude
		{
			get
			{
				return this._GPSDestLatitude;
			}
			set
			{
				this._GPSDestLatitude = value;
			}
		}

		// Token: 0x17000091 RID: 145
		// (get) Token: 0x060001A7 RID: 423 RVA: 0x00012831 File Offset: 0x00010A31
		// (set) Token: 0x060001A8 RID: 424 RVA: 0x00012839 File Offset: 0x00010A39
		public string GPSDestLongitude
		{
			get
			{
				return this._GPSDestLongitude;
			}
			set
			{
				this._GPSDestLongitude = value;
			}
		}

		// Token: 0x17000092 RID: 146
		// (get) Token: 0x060001A9 RID: 425 RVA: 0x00012842 File Offset: 0x00010A42
		// (set) Token: 0x060001AA RID: 426 RVA: 0x0001284A File Offset: 0x00010A4A
		public string GPSDestBearing
		{
			get
			{
				return this._GPSDestBearing;
			}
			set
			{
				this._GPSDestBearing = value;
			}
		}

		// Token: 0x17000093 RID: 147
		// (get) Token: 0x060001AB RID: 427 RVA: 0x00012853 File Offset: 0x00010A53
		// (set) Token: 0x060001AC RID: 428 RVA: 0x0001285B File Offset: 0x00010A5B
		public string GPSDestDistance
		{
			get
			{
				return this._GPSDestDistance;
			}
			set
			{
				this._GPSDestDistance = value;
			}
		}

		// Token: 0x17000094 RID: 148
		// (get) Token: 0x060001AD RID: 429 RVA: 0x00012864 File Offset: 0x00010A64
		// (set) Token: 0x060001AE RID: 430 RVA: 0x0001286C File Offset: 0x00010A6C
		public string DateTime
		{
			get
			{
				return this._DateTime;
			}
			set
			{
				this._DateTime = value;
			}
		}

		// Token: 0x17000095 RID: 149
		// (get) Token: 0x060001AF RID: 431 RVA: 0x00012875 File Offset: 0x00010A75
		// (set) Token: 0x060001B0 RID: 432 RVA: 0x0001287D File Offset: 0x00010A7D
		public string ImageDescription
		{
			get
			{
				return this._ImageDescription;
			}
			set
			{
				this._ImageDescription = value;
			}
		}

		// Token: 0x17000096 RID: 150
		// (get) Token: 0x060001B1 RID: 433 RVA: 0x00012886 File Offset: 0x00010A86
		// (set) Token: 0x060001B2 RID: 434 RVA: 0x0001288E File Offset: 0x00010A8E
		public string Make
		{
			get
			{
				return this._Make;
			}
			set
			{
				this._Make = value;
			}
		}

		// Token: 0x17000097 RID: 151
		// (get) Token: 0x060001B3 RID: 435 RVA: 0x00012897 File Offset: 0x00010A97
		// (set) Token: 0x060001B4 RID: 436 RVA: 0x0001289F File Offset: 0x00010A9F
		public string Model
		{
			get
			{
				return this._Model;
			}
			set
			{
				this._Model = value;
			}
		}

		// Token: 0x17000098 RID: 152
		// (get) Token: 0x060001B5 RID: 437 RVA: 0x000128A8 File Offset: 0x00010AA8
		// (set) Token: 0x060001B6 RID: 438 RVA: 0x000128B0 File Offset: 0x00010AB0
		public string Software
		{
			get
			{
				return this._Software;
			}
			set
			{
				this._Software = value;
			}
		}

		// Token: 0x17000099 RID: 153
		// (get) Token: 0x060001B7 RID: 439 RVA: 0x000128B9 File Offset: 0x00010AB9
		// (set) Token: 0x060001B8 RID: 440 RVA: 0x000128C1 File Offset: 0x00010AC1
		public string Artist
		{
			get
			{
				return this._Artist;
			}
			set
			{
				this._Artist = value;
			}
		}

		// Token: 0x1700009A RID: 154
		// (get) Token: 0x060001B9 RID: 441 RVA: 0x000128CA File Offset: 0x00010ACA
		// (set) Token: 0x060001BA RID: 442 RVA: 0x000128D2 File Offset: 0x00010AD2
		public string Copyright
		{
			get
			{
				return this._Copyright;
			}
			set
			{
				this._Copyright = value;
			}
		}

		// Token: 0x1700009B RID: 155
		// (get) Token: 0x060001BB RID: 443 RVA: 0x000128DB File Offset: 0x00010ADB
		// (set) Token: 0x060001BC RID: 444 RVA: 0x000128E3 File Offset: 0x00010AE3
		public string RelatedSoundFile
		{
			get
			{
				return this._RelatedSoundFile;
			}
			set
			{
				this._RelatedSoundFile = value;
			}
		}

		// Token: 0x1700009C RID: 156
		// (get) Token: 0x060001BD RID: 445 RVA: 0x000128EC File Offset: 0x00010AEC
		// (set) Token: 0x060001BE RID: 446 RVA: 0x000128F4 File Offset: 0x00010AF4
		public string DateTimeOriginal
		{
			get
			{
				return this._DateTimeOriginal;
			}
			set
			{
				this._DateTimeOriginal = value;
			}
		}

		// Token: 0x1700009D RID: 157
		// (get) Token: 0x060001BF RID: 447 RVA: 0x000128FD File Offset: 0x00010AFD
		// (set) Token: 0x060001C0 RID: 448 RVA: 0x00012905 File Offset: 0x00010B05
		public string DateTimeDigitized
		{
			get
			{
				return this._DateTimeDigitized;
			}
			set
			{
				this._DateTimeDigitized = value;
			}
		}

		// Token: 0x1700009E RID: 158
		// (get) Token: 0x060001C1 RID: 449 RVA: 0x0001290E File Offset: 0x00010B0E
		// (set) Token: 0x060001C2 RID: 450 RVA: 0x00012916 File Offset: 0x00010B16
		public string SubSecTime
		{
			get
			{
				return this._SubSecTime;
			}
			set
			{
				this._SubSecTime = value;
			}
		}

		// Token: 0x1700009F RID: 159
		// (get) Token: 0x060001C3 RID: 451 RVA: 0x0001291F File Offset: 0x00010B1F
		// (set) Token: 0x060001C4 RID: 452 RVA: 0x00012927 File Offset: 0x00010B27
		public string SubSecTimeOriginal
		{
			get
			{
				return this._SubSecTimeOriginal;
			}
			set
			{
				this._SubSecTimeOriginal = value;
			}
		}

		// Token: 0x170000A0 RID: 160
		// (get) Token: 0x060001C5 RID: 453 RVA: 0x00012930 File Offset: 0x00010B30
		// (set) Token: 0x060001C6 RID: 454 RVA: 0x00012938 File Offset: 0x00010B38
		public string SubSecTimeDigitized
		{
			get
			{
				return this._SubSecTimeDigitized;
			}
			set
			{
				this._SubSecTimeDigitized = value;
			}
		}

		// Token: 0x170000A1 RID: 161
		// (get) Token: 0x060001C7 RID: 455 RVA: 0x00012941 File Offset: 0x00010B41
		// (set) Token: 0x060001C8 RID: 456 RVA: 0x00012949 File Offset: 0x00010B49
		public string ImageUniqueID
		{
			get
			{
				return this._ImageUniqueID;
			}
			set
			{
				this._ImageUniqueID = value;
			}
		}

		// Token: 0x170000A2 RID: 162
		// (get) Token: 0x060001C9 RID: 457 RVA: 0x00012952 File Offset: 0x00010B52
		// (set) Token: 0x060001CA RID: 458 RVA: 0x0001295A File Offset: 0x00010B5A
		public string SpectralSensitivity
		{
			get
			{
				return this._SpectralSensitivity;
			}
			set
			{
				this._SpectralSensitivity = value;
			}
		}

		// Token: 0x170000A3 RID: 163
		// (get) Token: 0x060001CB RID: 459 RVA: 0x00012963 File Offset: 0x00010B63
		// (set) Token: 0x060001CC RID: 460 RVA: 0x0001296B File Offset: 0x00010B6B
		public string GPSLatitudeRef
		{
			get
			{
				return this._GPSLatitudeRef;
			}
			set
			{
				this._GPSLatitudeRef = value;
			}
		}

		// Token: 0x170000A4 RID: 164
		// (get) Token: 0x060001CD RID: 461 RVA: 0x00012974 File Offset: 0x00010B74
		// (set) Token: 0x060001CE RID: 462 RVA: 0x0001297C File Offset: 0x00010B7C
		public string GPSLongitudeRef
		{
			get
			{
				return this._GPSLongitudeRef;
			}
			set
			{
				this._GPSLongitudeRef = value;
			}
		}

		// Token: 0x170000A5 RID: 165
		// (get) Token: 0x060001CF RID: 463 RVA: 0x00012985 File Offset: 0x00010B85
		// (set) Token: 0x060001D0 RID: 464 RVA: 0x0001298D File Offset: 0x00010B8D
		public string GPSSatellites
		{
			get
			{
				return this._GPSSatellites;
			}
			set
			{
				this._GPSSatellites = value;
			}
		}

		// Token: 0x170000A6 RID: 166
		// (get) Token: 0x060001D1 RID: 465 RVA: 0x00012996 File Offset: 0x00010B96
		// (set) Token: 0x060001D2 RID: 466 RVA: 0x0001299E File Offset: 0x00010B9E
		public string GPSStatus
		{
			get
			{
				return this._GPSStatus;
			}
			set
			{
				this._GPSStatus = value;
			}
		}

		// Token: 0x170000A7 RID: 167
		// (get) Token: 0x060001D3 RID: 467 RVA: 0x000129A7 File Offset: 0x00010BA7
		// (set) Token: 0x060001D4 RID: 468 RVA: 0x000129AF File Offset: 0x00010BAF
		public string GPSMeasureMode
		{
			get
			{
				return this._GPSMeasureMode;
			}
			set
			{
				this._GPSMeasureMode = value;
			}
		}

		// Token: 0x170000A8 RID: 168
		// (get) Token: 0x060001D5 RID: 469 RVA: 0x000129B8 File Offset: 0x00010BB8
		// (set) Token: 0x060001D6 RID: 470 RVA: 0x000129C0 File Offset: 0x00010BC0
		public string GPSSpeedRef
		{
			get
			{
				return this._GPSSpeedRef;
			}
			set
			{
				this._GPSSpeedRef = value;
			}
		}

		// Token: 0x170000A9 RID: 169
		// (get) Token: 0x060001D7 RID: 471 RVA: 0x000129C9 File Offset: 0x00010BC9
		// (set) Token: 0x060001D8 RID: 472 RVA: 0x000129D1 File Offset: 0x00010BD1
		public string GPSTrackRef
		{
			get
			{
				return this._GPSTrackRef;
			}
			set
			{
				this._GPSTrackRef = value;
			}
		}

		// Token: 0x170000AA RID: 170
		// (get) Token: 0x060001D9 RID: 473 RVA: 0x000129DA File Offset: 0x00010BDA
		// (set) Token: 0x060001DA RID: 474 RVA: 0x000129E2 File Offset: 0x00010BE2
		public string GPSImgDirectionRef
		{
			get
			{
				return this._GPSImgDirectionRef;
			}
			set
			{
				this._GPSImgDirectionRef = value;
			}
		}

		// Token: 0x170000AB RID: 171
		// (get) Token: 0x060001DB RID: 475 RVA: 0x000129EB File Offset: 0x00010BEB
		// (set) Token: 0x060001DC RID: 476 RVA: 0x000129F3 File Offset: 0x00010BF3
		public string GPSMapDatum
		{
			get
			{
				return this._GPSMapDatum;
			}
			set
			{
				this._GPSMapDatum = value;
			}
		}

		// Token: 0x170000AC RID: 172
		// (get) Token: 0x060001DD RID: 477 RVA: 0x000129FC File Offset: 0x00010BFC
		// (set) Token: 0x060001DE RID: 478 RVA: 0x00012A04 File Offset: 0x00010C04
		public string GPSDestLatitudeRef
		{
			get
			{
				return this._GPSDestLatitudeRef;
			}
			set
			{
				this._GPSDestLatitudeRef = value;
			}
		}

		// Token: 0x170000AD RID: 173
		// (get) Token: 0x060001DF RID: 479 RVA: 0x00012A0D File Offset: 0x00010C0D
		// (set) Token: 0x060001E0 RID: 480 RVA: 0x00012A15 File Offset: 0x00010C15
		public string GPSDestLongitudeRef
		{
			get
			{
				return this._GPSDestLongitudeRef;
			}
			set
			{
				this._GPSDestLongitudeRef = value;
			}
		}

		// Token: 0x170000AE RID: 174
		// (get) Token: 0x060001E1 RID: 481 RVA: 0x00012A1E File Offset: 0x00010C1E
		// (set) Token: 0x060001E2 RID: 482 RVA: 0x00012A26 File Offset: 0x00010C26
		public string GPSDestBearingRef
		{
			get
			{
				return this._GPSDestBearingRef;
			}
			set
			{
				this._GPSDestBearingRef = value;
			}
		}

		// Token: 0x170000AF RID: 175
		// (get) Token: 0x060001E3 RID: 483 RVA: 0x00012A2F File Offset: 0x00010C2F
		// (set) Token: 0x060001E4 RID: 484 RVA: 0x00012A37 File Offset: 0x00010C37
		public string GPSDestDistanceRef
		{
			get
			{
				return this._GPSDestDistanceRef;
			}
			set
			{
				this._GPSDestDistanceRef = value;
			}
		}

		// Token: 0x170000B0 RID: 176
		// (get) Token: 0x060001E5 RID: 485 RVA: 0x00012A40 File Offset: 0x00010C40
		// (set) Token: 0x060001E6 RID: 486 RVA: 0x00012A48 File Offset: 0x00010C48
		public string GPSDateStamp
		{
			get
			{
				return this._GPSDateStamp;
			}
			set
			{
				this._GPSDateStamp = value;
			}
		}

		// Token: 0x170000B1 RID: 177
		// (get) Token: 0x060001E7 RID: 487 RVA: 0x00012A51 File Offset: 0x00010C51
		// (set) Token: 0x060001E8 RID: 488 RVA: 0x00012A59 File Offset: 0x00010C59
		public string OECF
		{
			get
			{
				return this._OECF;
			}
			set
			{
				this._OECF = value;
			}
		}

		// Token: 0x170000B2 RID: 178
		// (get) Token: 0x060001E9 RID: 489 RVA: 0x00012A62 File Offset: 0x00010C62
		// (set) Token: 0x060001EA RID: 490 RVA: 0x00012A6A File Offset: 0x00010C6A
		public string SpatialFrequencyResponse
		{
			get
			{
				return this._SpatialFrequencyResponse;
			}
			set
			{
				this._SpatialFrequencyResponse = value;
			}
		}

		// Token: 0x170000B3 RID: 179
		// (get) Token: 0x060001EB RID: 491 RVA: 0x00012A73 File Offset: 0x00010C73
		// (set) Token: 0x060001EC RID: 492 RVA: 0x00012A7B File Offset: 0x00010C7B
		public string FileSource
		{
			get
			{
				return this._FileSource;
			}
			set
			{
				this._FileSource = value;
			}
		}

		// Token: 0x170000B4 RID: 180
		// (get) Token: 0x060001ED RID: 493 RVA: 0x00012A84 File Offset: 0x00010C84
		// (set) Token: 0x060001EE RID: 494 RVA: 0x00012A8C File Offset: 0x00010C8C
		public string SceneType
		{
			get
			{
				return this._SceneType;
			}
			set
			{
				this._SceneType = value;
			}
		}

		// Token: 0x170000B5 RID: 181
		// (get) Token: 0x060001EF RID: 495 RVA: 0x00012A95 File Offset: 0x00010C95
		// (set) Token: 0x060001F0 RID: 496 RVA: 0x00012A9D File Offset: 0x00010C9D
		public string CFAPattern
		{
			get
			{
				return this._CFAPattern;
			}
			set
			{
				this._CFAPattern = value;
			}
		}

		// Token: 0x170000B6 RID: 182
		// (get) Token: 0x060001F1 RID: 497 RVA: 0x00012AA6 File Offset: 0x00010CA6
		// (set) Token: 0x060001F2 RID: 498 RVA: 0x00012AAE File Offset: 0x00010CAE
		public string DeviceSettingDescription
		{
			get
			{
				return this._DeviceSettingDescription;
			}
			set
			{
				this._DeviceSettingDescription = value;
			}
		}

		// Token: 0x170000B7 RID: 183
		// (get) Token: 0x060001F3 RID: 499 RVA: 0x00012AB7 File Offset: 0x00010CB7
		// (set) Token: 0x060001F4 RID: 500 RVA: 0x00012ABF File Offset: 0x00010CBF
		public string ExifVersion
		{
			get
			{
				return this._ExifVersion;
			}
			set
			{
				this._ExifVersion = value;
			}
		}

		// Token: 0x170000B8 RID: 184
		// (get) Token: 0x060001F5 RID: 501 RVA: 0x00012AC8 File Offset: 0x00010CC8
		// (set) Token: 0x060001F6 RID: 502 RVA: 0x00012AD0 File Offset: 0x00010CD0
		public string FlashpixVersion
		{
			get
			{
				return this._FlashpixVersion;
			}
			set
			{
				this._FlashpixVersion = value;
			}
		}

		// Token: 0x170000B9 RID: 185
		// (get) Token: 0x060001F7 RID: 503 RVA: 0x00012AD9 File Offset: 0x00010CD9
		// (set) Token: 0x060001F8 RID: 504 RVA: 0x00012AE1 File Offset: 0x00010CE1
		public string ComponentsConfiguration
		{
			get
			{
				return this._ComponentsConfiguration;
			}
			set
			{
				this._ComponentsConfiguration = value;
			}
		}

		// Token: 0x170000BA RID: 186
		// (get) Token: 0x060001F9 RID: 505 RVA: 0x00012AEA File Offset: 0x00010CEA
		// (set) Token: 0x060001FA RID: 506 RVA: 0x00012AF2 File Offset: 0x00010CF2
		public string MakerNote
		{
			get
			{
				return this._MakerNote;
			}
			set
			{
				this._MakerNote = value;
			}
		}

		// Token: 0x170000BB RID: 187
		// (get) Token: 0x060001FB RID: 507 RVA: 0x00012AFB File Offset: 0x00010CFB
		// (set) Token: 0x060001FC RID: 508 RVA: 0x00012B03 File Offset: 0x00010D03
		public string UserComment
		{
			get
			{
				return this._UserComment;
			}
			set
			{
				this._UserComment = value;
			}
		}

		// Token: 0x170000BC RID: 188
		// (get) Token: 0x060001FD RID: 509 RVA: 0x00012B0C File Offset: 0x00010D0C
		// (set) Token: 0x060001FE RID: 510 RVA: 0x00012B14 File Offset: 0x00010D14
		public string GPSProcessingMethod
		{
			get
			{
				return this._GPSProcessingMethod;
			}
			set
			{
				this._GPSProcessingMethod = value;
			}
		}

		// Token: 0x170000BD RID: 189
		// (get) Token: 0x060001FF RID: 511 RVA: 0x00012B1D File Offset: 0x00010D1D
		// (set) Token: 0x06000200 RID: 512 RVA: 0x00012B25 File Offset: 0x00010D25
		public string GPSAreaInformation
		{
			get
			{
				return this._GPSAreaInformation;
			}
			set
			{
				this._GPSAreaInformation = value;
			}
		}

		// Token: 0x040000A5 RID: 165
		private string _ImageWidth = string.Empty;

		// Token: 0x040000A6 RID: 166
		private string _ImageHeight = string.Empty;

		// Token: 0x040000A7 RID: 167
		private string _GPSVersionID = string.Empty;

		// Token: 0x040000A8 RID: 168
		private string _GPSAltitudeRef = string.Empty;

		// Token: 0x040000A9 RID: 169
		private string _StripOffsets = string.Empty;

		// Token: 0x040000AA RID: 170
		private string _RowsPerStrip = string.Empty;

		// Token: 0x040000AB RID: 171
		private string _StripByteCounts = string.Empty;

		// Token: 0x040000AC RID: 172
		private string _PixelXDimension = string.Empty;

		// Token: 0x040000AD RID: 173
		private string _PixelYDimension = string.Empty;

		// Token: 0x040000AE RID: 174
		private string _BitsPerSample = string.Empty;

		// Token: 0x040000AF RID: 175
		private string _Compression = string.Empty;

		// Token: 0x040000B0 RID: 176
		private string _PhotometricInterpretation = string.Empty;

		// Token: 0x040000B1 RID: 177
		private string _Orientation = string.Empty;

		// Token: 0x040000B2 RID: 178
		private string _SamplesPerPixel = string.Empty;

		// Token: 0x040000B3 RID: 179
		private string _PlanarConfiguration = string.Empty;

		// Token: 0x040000B4 RID: 180
		private string _YCbCrSubSampling = string.Empty;

		// Token: 0x040000B5 RID: 181
		private string _YCbCrPositioning = string.Empty;

		// Token: 0x040000B6 RID: 182
		private string _ResolutionUnit = string.Empty;

		// Token: 0x040000B7 RID: 183
		private string _TransferFunction = string.Empty;

		// Token: 0x040000B8 RID: 184
		private string _ColorSpace = string.Empty;

		// Token: 0x040000B9 RID: 185
		private string _ExposureProgram = string.Empty;

		// Token: 0x040000BA RID: 186
		private string _ISOSpeedRatings = string.Empty;

		// Token: 0x040000BB RID: 187
		private string _MeteringMode = string.Empty;

		// Token: 0x040000BC RID: 188
		private string _LightSource = string.Empty;

		// Token: 0x040000BD RID: 189
		private string _Flash = string.Empty;

		// Token: 0x040000BE RID: 190
		private string _SubjectArea = string.Empty;

		// Token: 0x040000BF RID: 191
		private string _FocalPlaneResolutionUnit = string.Empty;

		// Token: 0x040000C0 RID: 192
		private string _SubjectLocation = string.Empty;

		// Token: 0x040000C1 RID: 193
		private string _SensingMethod = string.Empty;

		// Token: 0x040000C2 RID: 194
		private string _CustomRendered = string.Empty;

		// Token: 0x040000C3 RID: 195
		private string _ExposureMode = string.Empty;

		// Token: 0x040000C4 RID: 196
		private string _WhiteBalance = string.Empty;

		// Token: 0x040000C5 RID: 197
		private string _FocalLengthIn35mmFilm = string.Empty;

		// Token: 0x040000C6 RID: 198
		private string _SceneCaptureType = string.Empty;

		// Token: 0x040000C7 RID: 199
		private string _Contrast = string.Empty;

		// Token: 0x040000C8 RID: 200
		private string _Saturation = string.Empty;

		// Token: 0x040000C9 RID: 201
		private string _Sharpness = string.Empty;

		// Token: 0x040000CA RID: 202
		private string _SubjectDistanceRange = string.Empty;

		// Token: 0x040000CB RID: 203
		private string _GPSDifferential = string.Empty;

		// Token: 0x040000CC RID: 204
		private string _ShutterSpeedValue = string.Empty;

		// Token: 0x040000CD RID: 205
		private string _BrightnessValue = string.Empty;

		// Token: 0x040000CE RID: 206
		private string _ExposureBiasValue = string.Empty;

		// Token: 0x040000CF RID: 207
		private string _JPEGInterchangeFormat = string.Empty;

		// Token: 0x040000D0 RID: 208
		private string _JPEGInterchangeFormatLength = string.Empty;

		// Token: 0x040000D1 RID: 209
		private string _XResolution = string.Empty;

		// Token: 0x040000D2 RID: 210
		private string _YResolution = string.Empty;

		// Token: 0x040000D3 RID: 211
		private string _WhitePoint = string.Empty;

		// Token: 0x040000D4 RID: 212
		private string _PrimaryChromaticities = string.Empty;

		// Token: 0x040000D5 RID: 213
		private string _YCbCrCoefficients = string.Empty;

		// Token: 0x040000D6 RID: 214
		private string _ReferenceBlackWhite = string.Empty;

		// Token: 0x040000D7 RID: 215
		private string _CompressedBitsPerPixel = string.Empty;

		// Token: 0x040000D8 RID: 216
		private string _ExposureTime = string.Empty;

		// Token: 0x040000D9 RID: 217
		private string _FNumber = string.Empty;

		// Token: 0x040000DA RID: 218
		private string _ApertureValue = string.Empty;

		// Token: 0x040000DB RID: 219
		private string _MaxApertureValue = string.Empty;

		// Token: 0x040000DC RID: 220
		private string _SubjectDistance = string.Empty;

		// Token: 0x040000DD RID: 221
		private string _FocalLength = string.Empty;

		// Token: 0x040000DE RID: 222
		private string _FlashEnergy = string.Empty;

		// Token: 0x040000DF RID: 223
		private string _FocalPlaneXResolution = string.Empty;

		// Token: 0x040000E0 RID: 224
		private string _FocalPlaneYResolution = string.Empty;

		// Token: 0x040000E1 RID: 225
		private string _ExposureIndex = string.Empty;

		// Token: 0x040000E2 RID: 226
		private string _DigitalZoomRatio = string.Empty;

		// Token: 0x040000E3 RID: 227
		private string _GainControl = string.Empty;

		// Token: 0x040000E4 RID: 228
		private string _GPSLatitude = string.Empty;

		// Token: 0x040000E5 RID: 229
		private string _GPSLongitude = string.Empty;

		// Token: 0x040000E6 RID: 230
		private string _GPSAltitude = string.Empty;

		// Token: 0x040000E7 RID: 231
		private string _GPSTimeStamp = string.Empty;

		// Token: 0x040000E8 RID: 232
		private string _GPSDOP = string.Empty;

		// Token: 0x040000E9 RID: 233
		private string _GPSSpeed = string.Empty;

		// Token: 0x040000EA RID: 234
		private string _GPSTrack = string.Empty;

		// Token: 0x040000EB RID: 235
		private string _GPSImgDirection = string.Empty;

		// Token: 0x040000EC RID: 236
		private string _GPSDestLatitude = string.Empty;

		// Token: 0x040000ED RID: 237
		private string _GPSDestLongitude = string.Empty;

		// Token: 0x040000EE RID: 238
		private string _GPSDestBearing = string.Empty;

		// Token: 0x040000EF RID: 239
		private string _GPSDestDistance = string.Empty;

		// Token: 0x040000F0 RID: 240
		private string _DateTime = string.Empty;

		// Token: 0x040000F1 RID: 241
		private string _ImageDescription = string.Empty;

		// Token: 0x040000F2 RID: 242
		private string _Make = string.Empty;

		// Token: 0x040000F3 RID: 243
		private string _Model = string.Empty;

		// Token: 0x040000F4 RID: 244
		private string _Software = string.Empty;

		// Token: 0x040000F5 RID: 245
		private string _Artist = string.Empty;

		// Token: 0x040000F6 RID: 246
		private string _Copyright = string.Empty;

		// Token: 0x040000F7 RID: 247
		private string _RelatedSoundFile = string.Empty;

		// Token: 0x040000F8 RID: 248
		private string _DateTimeOriginal = string.Empty;

		// Token: 0x040000F9 RID: 249
		private string _DateTimeDigitized = string.Empty;

		// Token: 0x040000FA RID: 250
		private string _SubSecTime = string.Empty;

		// Token: 0x040000FB RID: 251
		private string _SubSecTimeOriginal = string.Empty;

		// Token: 0x040000FC RID: 252
		private string _SubSecTimeDigitized = string.Empty;

		// Token: 0x040000FD RID: 253
		private string _ImageUniqueID = string.Empty;

		// Token: 0x040000FE RID: 254
		private string _SpectralSensitivity = string.Empty;

		// Token: 0x040000FF RID: 255
		private string _GPSLatitudeRef = string.Empty;

		// Token: 0x04000100 RID: 256
		private string _GPSLongitudeRef = string.Empty;

		// Token: 0x04000101 RID: 257
		private string _GPSSatellites = string.Empty;

		// Token: 0x04000102 RID: 258
		private string _GPSStatus = string.Empty;

		// Token: 0x04000103 RID: 259
		private string _GPSMeasureMode = string.Empty;

		// Token: 0x04000104 RID: 260
		private string _GPSSpeedRef = string.Empty;

		// Token: 0x04000105 RID: 261
		private string _GPSTrackRef = string.Empty;

		// Token: 0x04000106 RID: 262
		private string _GPSImgDirectionRef = string.Empty;

		// Token: 0x04000107 RID: 263
		private string _GPSMapDatum = string.Empty;

		// Token: 0x04000108 RID: 264
		private string _GPSDestLatitudeRef = string.Empty;

		// Token: 0x04000109 RID: 265
		private string _GPSDestLongitudeRef = string.Empty;

		// Token: 0x0400010A RID: 266
		private string _GPSDestBearingRef = string.Empty;

		// Token: 0x0400010B RID: 267
		private string _GPSDestDistanceRef = string.Empty;

		// Token: 0x0400010C RID: 268
		private string _GPSDateStamp = string.Empty;

		// Token: 0x0400010D RID: 269
		private string _OECF = string.Empty;

		// Token: 0x0400010E RID: 270
		private string _SpatialFrequencyResponse = string.Empty;

		// Token: 0x0400010F RID: 271
		private string _FileSource = string.Empty;

		// Token: 0x04000110 RID: 272
		private string _SceneType = string.Empty;

		// Token: 0x04000111 RID: 273
		private string _CFAPattern = string.Empty;

		// Token: 0x04000112 RID: 274
		private string _DeviceSettingDescription = string.Empty;

		// Token: 0x04000113 RID: 275
		private string _ExifVersion = string.Empty;

		// Token: 0x04000114 RID: 276
		private string _FlashpixVersion = string.Empty;

		// Token: 0x04000115 RID: 277
		private string _ComponentsConfiguration = string.Empty;

		// Token: 0x04000116 RID: 278
		private string _MakerNote = string.Empty;

		// Token: 0x04000117 RID: 279
		private string _UserComment = string.Empty;

		// Token: 0x04000118 RID: 280
		private string _GPSProcessingMethod = string.Empty;

		// Token: 0x04000119 RID: 281
		private string _GPSAreaInformation = string.Empty;
	}
}

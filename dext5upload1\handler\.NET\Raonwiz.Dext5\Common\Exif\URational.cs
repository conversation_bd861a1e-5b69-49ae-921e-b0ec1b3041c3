﻿using System;

namespace Raonwiz.Dext5.Common.Exif
{
	// Token: 0x02000019 RID: 25
	internal sealed class URational
	{
		// Token: 0x06000216 RID: 534 RVA: 0x0001570C File Offset: 0x0001390C
		public URational(byte[] bytes)
		{
			byte[] array = new byte[4];
			byte[] array2 = new byte[4];
			Array.Copy(bytes, 0, array, 0, 4);
			Array.Copy(bytes, 4, array2, 0, 4);
			this._num = BitConverter.ToUInt32(array, 0);
			this._denom = BitConverter.ToUInt32(array2, 0);
		}

		// Token: 0x06000217 RID: 535 RVA: 0x0001575B File Offset: 0x0001395B
		public double ToDouble()
		{
			return Math.Round(Convert.ToDouble(this._num) / Convert.ToDouble(this._denom), 2);
		}

		// Token: 0x06000218 RID: 536 RVA: 0x0001577A File Offset: 0x0001397A
		public override string ToString()
		{
			return this.ToString("/");
		}

		// Token: 0x06000219 RID: 537 RVA: 0x00015787 File Offset: 0x00013987
		public string ToString(string separator)
		{
			return this._num.ToString() + separator + this._denom.ToString();
		}

		// Token: 0x04000121 RID: 289
		private uint _num;

		// Token: 0x04000122 RID: 290
		private uint _denom;
	}
}

package jcifs.smb;

import java.io.UnsupportedEncodingException;
import jcifs.Config;
import jcifs.util.Hexdump;
import org.apache.commons.fileupload.MultipartStream;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComTreeConnectAndX.class */
class SmbComTreeConnectAndX extends AndXServerMessageBlock {
    private SmbSession session;
    private boolean disconnectTid;
    private String service;
    private byte[] password;
    private int passwordLength;
    String path;
    private static final boolean DISABLE_PLAIN_TEXT_PASSWORDS = Config.getBoolean("jcifs.smb.client.disablePlainTextPasswords", true);
    private static byte[] batchLimits = {1, 1, 1, 1, 1, 1, 1, 1, 0};

    static {
        String s = Config.getProperty("jcifs.smb.client.TreeConnectAndX.CheckDirectory");
        if (s != null) {
            batchLimits[0] = Byte.parseByte(s);
        }
        String s2 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.CreateDirectory");
        if (s2 != null) {
            batchLimits[2] = Byte.parseByte(s2);
        }
        String s3 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.Delete");
        if (s3 != null) {
            batchLimits[3] = Byte.parseByte(s3);
        }
        String s4 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.DeleteDirectory");
        if (s4 != null) {
            batchLimits[4] = Byte.parseByte(s4);
        }
        String s5 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.OpenAndX");
        if (s5 != null) {
            batchLimits[5] = Byte.parseByte(s5);
        }
        String s6 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.Rename");
        if (s6 != null) {
            batchLimits[6] = Byte.parseByte(s6);
        }
        String s7 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.Transaction");
        if (s7 != null) {
            batchLimits[7] = Byte.parseByte(s7);
        }
        String s8 = Config.getProperty("jcifs.smb.client.TreeConnectAndX.QueryInformation");
        if (s8 != null) {
            batchLimits[8] = Byte.parseByte(s8);
        }
    }

    SmbComTreeConnectAndX(SmbSession session, String path, String service, ServerMessageBlock andx) {
        super(andx);
        this.disconnectTid = false;
        this.session = session;
        this.path = path;
        this.service = service;
        this.command = (byte) 117;
    }

    @Override // jcifs.smb.AndXServerMessageBlock
    int getBatchLimit(byte command) {
        int c = command & 255;
        switch (c) {
            case 0:
                return batchLimits[2];
            case 1:
                return batchLimits[4];
            case 6:
                return batchLimits[3];
            case 7:
                return batchLimits[6];
            case 8:
                return batchLimits[8];
            case 16:
                return batchLimits[0];
            case 37:
                return batchLimits[7];
            case MultipartStream.DASH /* 45 */:
                return batchLimits[5];
            default:
                return 0;
        }
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        if (this.session.transport.server.security == 0 && (this.session.auth.hashesExternal || this.session.auth.password.length() > 0)) {
            if (this.session.transport.server.encryptedPasswords) {
                this.password = this.session.auth.getAnsiHash(this.session.transport.server.encryptionKey);
                this.passwordLength = this.password.length;
            } else {
                if (DISABLE_PLAIN_TEXT_PASSWORDS) {
                    throw new RuntimeException("Plain text passwords are disabled");
                }
                this.password = new byte[(this.session.auth.password.length() + 1) * 2];
                this.passwordLength = writeString(this.session.auth.password, this.password, 0);
            }
        } else {
            this.passwordLength = 1;
        }
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = this.disconnectTid ? (byte) 1 : (byte) 0;
        dst[dstIndex2] = 0;
        writeInt2(this.passwordLength, dst, dstIndex2 + 1);
        return 4;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2;
        if (this.session.transport.server.security == 0 && (this.session.auth.hashesExternal || this.session.auth.password.length() > 0)) {
            System.arraycopy(this.password, 0, dst, dstIndex, this.passwordLength);
            dstIndex2 = dstIndex + this.passwordLength;
        } else {
            dstIndex2 = dstIndex + 1;
            dst[dstIndex] = 0;
        }
        int dstIndex3 = dstIndex2 + writeString(this.path, dst, dstIndex2);
        try {
            System.arraycopy(this.service.getBytes("ASCII"), 0, dst, dstIndex3, this.service.length());
            int dstIndex4 = dstIndex3 + this.service.length();
            dst[dstIndex4] = 0;
            return (dstIndex4 + 1) - dstIndex;
        } catch (UnsupportedEncodingException e) {
            return 0;
        }
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.AndXServerMessageBlock, jcifs.smb.ServerMessageBlock
    public String toString() {
        String result = new String("SmbComTreeConnectAndX[" + super.toString() + ",disconnectTid=" + this.disconnectTid + ",passwordLength=" + this.passwordLength + ",password=" + Hexdump.toHexString(this.password, this.passwordLength, 0) + ",path=" + this.path + ",service=" + this.service + "]");
        return result;
    }
}

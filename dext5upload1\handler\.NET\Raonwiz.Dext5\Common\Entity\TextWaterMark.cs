﻿using System;

namespace Raonwiz.Dext5.Common.Entity
{
	// Token: 0x02000013 RID: 19
	public class TextWaterMark
	{
		// Token: 0x17000045 RID: 69
		// (get) Token: 0x0600010E RID: 270 RVA: 0x000122D4 File Offset: 0x000104D4
		// (set) Token: 0x0600010F RID: 271 RVA: 0x000122DC File Offset: 0x000104DC
		public string FontName
		{
			get
			{
				return this._fontName;
			}
			set
			{
				this._fontName = value;
			}
		}

		// Token: 0x17000046 RID: 70
		// (get) Token: 0x06000110 RID: 272 RVA: 0x000122E5 File Offset: 0x000104E5
		// (set) Token: 0x06000111 RID: 273 RVA: 0x000122ED File Offset: 0x000104ED
		public int FontSize
		{
			get
			{
				return this._fontSize;
			}
			set
			{
				this._fontSize = value;
			}
		}

		// Token: 0x17000047 RID: 71
		// (get) Token: 0x06000112 RID: 274 RVA: 0x000122F6 File Offset: 0x000104F6
		// (set) Token: 0x06000113 RID: 275 RVA: 0x000122FE File Offset: 0x000104FE
		public string FontColor
		{
			get
			{
				return this._fontColor;
			}
			set
			{
				this._fontColor = value;
			}
		}

		// Token: 0x17000048 RID: 72
		// (get) Token: 0x06000114 RID: 276 RVA: 0x00012307 File Offset: 0x00010507
		// (set) Token: 0x06000115 RID: 277 RVA: 0x0001230F File Offset: 0x0001050F
		public string WaterMarkText
		{
			get
			{
				return this._waterMarkText;
			}
			set
			{
				this._waterMarkText = value;
			}
		}

		// Token: 0x06000116 RID: 278 RVA: 0x00012318 File Offset: 0x00010518
		public TextWaterMark(string pWaterMarkText, string pFontName, int pFontSize, string pFontColor)
		{
			this._waterMarkText = pWaterMarkText;
			this._fontName = pFontName;
			this._fontSize = pFontSize;
			this._fontColor = pFontColor;
		}

		// Token: 0x040000A1 RID: 161
		private string _fontName = string.Empty;

		// Token: 0x040000A2 RID: 162
		private int _fontSize;

		// Token: 0x040000A3 RID: 163
		private string _fontColor = string.Empty;

		// Token: 0x040000A4 RID: 164
		private string _waterMarkText = string.Empty;
	}
}

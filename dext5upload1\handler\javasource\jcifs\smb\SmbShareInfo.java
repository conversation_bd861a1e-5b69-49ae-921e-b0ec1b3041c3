package jcifs.smb;

import jcifs.util.Hexdump;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbShareInfo.class */
public class SmbShareInfo implements FileEntry {
    protected String netName;
    protected int type;
    protected String remark;

    public SmbShareInfo() {
    }

    public SmbShareInfo(String netName, int type, String remark) {
        this.netName = netName;
        this.type = type;
        this.remark = remark;
    }

    @Override // jcifs.smb.FileEntry
    public String getName() {
        return this.netName;
    }

    @Override // jcifs.smb.FileEntry
    public int getType() {
        switch (this.type & InternalZipConstants.MAX_ALLOWED_ZIP_COMMENT_LENGTH) {
            case 1:
                return 32;
            case 3:
                return 16;
            default:
                return 8;
        }
    }

    @Override // jcifs.smb.FileEntry
    public int getAttributes() {
        return 17;
    }

    @Override // jcifs.smb.FileEntry
    public long createTime() {
        return 0L;
    }

    @Override // jcifs.smb.FileEntry
    public long lastModified() {
        return 0L;
    }

    @Override // jcifs.smb.FileEntry
    public long length() {
        return 0L;
    }

    public boolean equals(Object obj) {
        if (obj instanceof SmbShareInfo) {
            SmbShareInfo si = (SmbShareInfo) obj;
            return this.netName.equals(si.netName);
        }
        return false;
    }

    public int hashCode() {
        int hashCode = this.netName.hashCode();
        return hashCode;
    }

    public String toString() {
        return new String("SmbShareInfo[netName=" + this.netName + ",type=0x" + Hexdump.toHexString(this.type, 8) + ",remark=" + this.remark + "]");
    }
}

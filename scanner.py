#!/usr/bin/env python3
"""
通用编辑器漏洞扫描器
支持多种编辑器的自动识别和漏洞检测
"""

import requests
import re
import urllib3
import sys
import json
import threading
import os
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import string

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def random_string(length=12):
    """生成随机字符串"""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

class UniversalEditorScanner:
    def __init__(self, timeout=10, max_workers=10):
        self.timeout = timeout
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # 编辑器特征定义
        self.editor_signatures = {
            'dext5upload': {
                'core_files': ['dext5upload.js', 'dext5upload.css'],
                'patterns': [
                    r'["\']([^"\']*dext5upload\.js[^"\']*)["\']',
                    r'["\']([^"\']*dext5upload\.css[^"\']*)["\']',
                    r'src=[\'"]*([^"\']*dext5upload\.js[^"\']*)',
                    r'href=[\'"]*([^"\']*dext5upload\.css[^"\']*)'
                ],
                'vuln_paths': [
                    {'path': 'handler/dext5handler.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'handler/dext5handler.ashx', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'dext5upload/handler/dext5handler.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'dext5upload/handler/dext5handler.ashx', 'type': 'upload', 'severity': 'critical'}
                ],
                'directories': [
                    'dext5upload/', 'Dext5Upload/', 'dext5/', 'upload/',
                    'js/component/dext5upload/', 'resources/dext5upload/'
                ]
            },
            'smarteditor': {
                'core_files': ['HuskyEZCreator.js', 'smart_editor2.js'],
                'patterns': [
                    r'["\']([^"\']*HuskyEZCreator\.js[^"\']*)["\']',
                    r'["\']([^"\']*smart_editor2\.js[^"\']*)["\']',
                    r'src=[\'"]*([^"\']*HuskyEZCreator\.js[^"\']*)'
                ],
                'vuln_paths': [
                    {'path': 'sample/photo_uploader/file_uploader.jsp', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/file_uploader.php', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/file_uploader_html5.jsp', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/photo_uploader.html', 'type': 'info', 'severity': 'medium'},
                    {'path': 'photo_uploader/popup/file_uploader.jsp', 'type': 'upload', 'severity': 'high'}
                ],
                'directories': [
                    'smarteditor/', 'SmartEditor/', 'SmartEditor2/', 'se2/',
                    'resources/js/SmartEditor2/', 'js/smarteditor/'
                ]
            },
            'cheditor': {
                'core_files': ['cheditor.js', 'cheditor.css'],
                'patterns': [
                    r'["\']([^"\']*cheditor\.js[^"\']*)["\']',
                    r'["\']([^"\']*cheditor\.css[^"\']*)["\']',
                    r'CHEditor\s*\(',
                    r'cheditor/cheditor\.js'
                ],
                'vuln_paths': [
                    {'path': 'imageUpload/upload.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'imageUpload/delete.jsp', 'type': 'delete', 'severity': 'high'},
                    {'path': 'imageUpload/upload-simple.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'imageUpload/config.jsp', 'type': 'info', 'severity': 'medium'}
                ],
                'directories': [
                    'cheditor/', 'CHEditor/', 'ch/', 'editor/ch/',
                    'js/cheditor/', 'resources/cheditor/'
                ]
            },
            'ckeditor': {
                'core_files': ['ckeditor.js'],
                'patterns': [
                    r'["\']([^"\']*ckeditor\.js[^"\']*)["\']',
                    r'CKEDITOR\.replace\s*\(',
                    r'CKEDITOR_BASEPATH',
                    r'ckeditor/ckeditor\.js'
                ],
                'vuln_paths': [
                    {'path': '_samples/sample_posteddata.php', 'type': 'info', 'severity': 'medium'},
                    {'path': '_samples/assets/_posteddata.php', 'type': 'info', 'severity': 'medium'},
                    {'path': 'samples/sample_posteddata.php', 'type': 'info', 'severity': 'medium'}
                ],
                'directories': [
                    'ckeditor/', 'CKEditor/', 'ck/', 'editor/ckeditor/',
                    'js/ckeditor/', 'lib/ckeditor/'
                ]
            },
            'ckfinder': {
                'core_files': ['ckfinder.js'],
                'patterns': [
                    r'["\']([^"\']*ckfinder\.js[^"\']*)["\']',
                    r'CKFinder\.create\s*\(',
                    r'ckfinder/ckfinder\.html',
                    r'ckfinder\.js'
                ],
                'vuln_paths': [
                    {'path': 'core/connector/php/connector.php', 'type': 'rce', 'severity': 'critical'},
                    {'path': 'core/connector/java/connector.java', 'type': 'rce', 'severity': 'critical'},
                    {'path': 'plugins/fileeditor/plugin.js', 'type': 'rce', 'severity': 'high'},
                    {'path': 'ckfinder.html', 'type': 'info', 'severity': 'low'}
                ],
                'directories': [
                    'ckfinder/', 'CKFinder/', 'filemanager/', 'filebrowser/',
                    'editor/ckfinder/', 'js/ckfinder/'
                ]
            }
        }

        # 通用目录
        self.common_directories = [
            '', 'editor/', 'editors/', 'common/', 'resources/', 'resource/',
            'js/', 'lib/', 'libs/', 'scripts/', 'static/', 'assets/',
            'upload/', 'file/', 'files/', 'admin/', 'manage/', 'system/'
        ]

        self.results = []
        self.file_lock = threading.Lock()

    def normalize_url(self, url):
        """标准化URL"""
        if not url.startswith(('http://', 'https://')):
            return f"https://{url}", f"http://{url}"
        return url, None

    def test_url_accessibility(self, url):
        """测试URL可访问性"""
        primary_url, fallback_url = self.normalize_url(url)

        try:
            response = self.session.get(primary_url, allow_redirects=True)
            if response.status_code == 200:
                return primary_url, response
        except:
            pass

        if fallback_url:
            try:
                response = self.session.get(fallback_url, allow_redirects=True)
                if response.status_code == 200:
                    return fallback_url, response
            except:
                pass

        return None, None

    def get_not_found_signature(self, base_url):
        """获取404页面签名"""
        random_path = f"{random_string()}.php"
        test_url = urljoin(base_url, random_path)

        try:
            res = self.session.get(test_url, allow_redirects=False, timeout=5)
            if res.status_code == 200:
                return res.text
        except:
            pass

        return None

    def find_editor_resources(self, html_content, base_url, editor_type=None):
        """在HTML内容中查找编辑器资源"""
        found_resources = []

        # 如果指定了编辑器类型，只查找该类型
        if editor_type:
            editor_types = [editor_type]
        else:
            editor_types = self.editor_signatures.keys()

        for etype in editor_types:
            signature = self.editor_signatures[etype]

            for pattern in signature['patterns']:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    full_url = urljoin(base_url, match)
                    found_resources.append({
                        'editor_type': etype,
                        'resource_path': match,
                        'full_url': full_url,
                        'pattern_matched': pattern
                    })

        return found_resources

    def extract_editor_base_path(self, resource_path, editor_type):
        """从资源路径提取编辑器基础路径"""

        # 方法1: 基于编辑器名称目录
        editor_dir_patterns = {
            'dext5upload': r'(.*?dext5upload[^/]*/).*',
            'smarteditor': r'(.*?(?:smarteditor|se2)[^/]*/).*',
            'cheditor': r'(.*?cheditor[^/]*/).*',
            'ckeditor': r'(.*?ckeditor[^/]*/).*',
            'ckfinder': r'(.*?ckfinder[^/]*/).*'
        }

        pattern = editor_dir_patterns.get(editor_type)
        if pattern:
            match = re.search(pattern, resource_path, re.IGNORECASE)
            if match:
                return match.group(1)

        # 方法2: 基于核心文件名
        signature = self.editor_signatures.get(editor_type, {})
        core_files = signature.get('core_files', [])

        for core_file in core_files:
            if core_file in resource_path:
                base_path = resource_path.replace(core_file, '').rstrip('/')
                # 移除可能的js/css/scripts等子目录
                base_path = re.sub(r'/(js|css|scripts|assets)/?$', '', base_path) + '/'
                return base_path

        # 方法3: 通用后备方案
        return '/'.join(resource_path.split('/')[:-1]) + '/'

    def test_vulnerability_paths(self, base_path, editor_type, not_found_signature):
        """测试漏洞路径"""
        vulnerable_paths = []
        signature = self.editor_signatures.get(editor_type, {})
        vuln_paths = signature.get('vuln_paths', [])

        for vuln_info in vuln_paths:
            test_url = urljoin(base_path, vuln_info['path'])

            try:
                # 尝试GET请求
                response = self.session.get(test_url, timeout=self.timeout, allow_redirects=False)

                if self.is_vulnerable_response(response, not_found_signature, vuln_info):
                    vulnerable_paths.append({
                        'url': test_url,
                        'status_code': response.status_code,
                        'vulnerability_type': vuln_info['type'],
                        'severity': vuln_info['severity'],
                        'method': 'GET'
                    })
                    print(f"[+] 发现漏洞 ({editor_type}): {test_url} - {vuln_info['type']}")
                    continue

                # 如果GET失败，尝试POST
                response = self.session.post(test_url, data={}, timeout=self.timeout, allow_redirects=False)

                if self.is_vulnerable_response(response, not_found_signature, vuln_info):
                    vulnerable_paths.append({
                        'url': test_url,
                        'status_code': response.status_code,
                        'vulnerability_type': vuln_info['type'],
                        'severity': vuln_info['severity'],
                        'method': 'POST'
                    })
                    print(f"[+] 发现漏洞 ({editor_type}): {test_url} - {vuln_info['type']}")

            except Exception as e:
                continue

        return vulnerable_paths

    def is_vulnerable_response(self, response, not_found_signature, vuln_info):
        """判断响应是否表示漏洞存在"""
        if response.status_code != 200:
            return False

        # 检查是否为软404
        if not_found_signature and response.text == not_found_signature:
            return False

        # 根据漏洞类型进行特定检查
        vuln_type = vuln_info.get('type', '')

        if vuln_type == 'upload':
            # 上传页面通常包含form或upload相关内容
            indicators = ['upload', 'file', 'form', 'multipart', 'enctype']
            return any(indicator in response.text.lower() for indicator in indicators)

        elif vuln_type == 'info':
            # 信息泄露页面通常有一定长度且不是错误页面
            return (len(response.text) > 100 and
                    '<title>' not in response.text.lower() and
                    'error' not in response.text.lower())

        elif vuln_type == 'rce':
            # RCE相关页面
            indicators = ['connector', 'command', 'execute', 'file']
            return any(indicator in response.text.lower() for indicator in indicators)

        # 默认检查
        return len(response.text) > 50

    def is_all_paths_vulnerable(self, vulnerable_paths, editor_type):
        """检查是否所有路径都被标记为存在（异常情况）"""
        if not vulnerable_paths:
            return False

        signature = self.editor_signatures.get(editor_type, {})
        total_paths = len(signature.get('vuln_paths', []))

        if len(vulnerable_paths) >= total_paths:
            return True

        threshold = max(1, int(total_paths * 0.8))
        return len(vulnerable_paths) >= threshold

    def scan_single_target(self, target, editor_types=None):
        """扫描单个目标"""
        print(f"[*] 扫描目标: {target}")

        # 测试URL可访问性
        accessible_url, response = self.test_url_accessibility(target)
        if not accessible_url:
            print(f"[-] {target} - 无法访问")
            return None

        print(f"[+] {target} - 可访问 ({accessible_url})")

        # 获取404签名
        not_found_signature = self.get_not_found_signature(accessible_url)

        # 如果未指定编辑器类型，扫描所有类型
        if not editor_types:
            editor_types = list(self.editor_signatures.keys())

        all_results = []

        for editor_type in editor_types:
            print(f"[*] {target} - 扫描 {editor_type} 编辑器...")

            # 第1步: 从主页查找编辑器资源
            editor_resources = self.find_editor_resources(response.text, accessible_url, editor_type)

            if editor_resources:
                print(f"[+] {target} - 在主页发现 {editor_type} 资源")

                for resource in editor_resources:
                    base_path = self.extract_editor_base_path(resource['resource_path'], editor_type)
                    print(f"[*] {target} - 测试 {editor_type} 基础路径: {base_path}")

                    vulnerable_paths = self.test_vulnerability_paths(base_path, editor_type, not_found_signature)

                    if vulnerable_paths and not self.is_all_paths_vulnerable(vulnerable_paths, editor_type):
                        result = {
                            'target': target,
                            'accessible_url': accessible_url,
                            'editor_type': editor_type,
                            'detection_method': 'homepage_analysis',
                            'resource_found': resource,
                            'base_path': base_path,
                            'vulnerable_paths': vulnerable_paths
                        }
                        all_results.append(result)
                        print(f"[+] {target} - {editor_type} 发现漏洞，跳过目录fuzzing")
                        continue

            # 第2步: 目录Fuzzing
            print(f"[*] {target} - 对 {editor_type} 进行目录fuzzing...")
            fuzz_result = self.fuzz_directories(accessible_url, editor_type, not_found_signature)

            if fuzz_result:
                fuzz_result.update({
                    'target': target,
                    'accessible_url': accessible_url,
                    'editor_type': editor_type,
                    'detection_method': 'directory_fuzzing'
                })
                all_results.append(fuzz_result)

        if all_results:
            self.save_results(all_results)
            return all_results

        print(f"[-] {target} - 未发现任何编辑器漏洞")
        return None

    def fuzz_directories(self, base_url, editor_type, not_found_signature):
        """目录fuzzing"""
        signature = self.editor_signatures[editor_type]
        core_files = signature['core_files']
        specific_dirs = signature['directories']

        # 组合所有可能的目录
        all_dirs = self.common_directories + specific_dirs

        for directory in all_dirs:
            # 构造核心文件URL进行验证
            for core_file in core_files:
                test_url = urljoin(base_url, f"{directory}{core_file}")

                try:
                    response = self.session.head(test_url, timeout=5)
                    if response.status_code == 200:
                        # 找到编辑器，测试漏洞
                        base_path = urljoin(base_url, directory)
                        print(f"[+] 通过fuzzing发现 {editor_type}: {base_path}")

                        vulnerable_paths = self.test_vulnerability_paths(base_path, editor_type, not_found_signature)

                        if vulnerable_paths and not self.is_all_paths_vulnerable(vulnerable_paths, editor_type):
                            return {
                                'base_path': base_path,
                                'core_file_found': test_url,
                                'vulnerable_paths': vulnerable_paths
                            }
                except:
                    continue

        return None

    def save_results(self, results):
        """保存结果"""
        filename = 'universal_editor_results.txt'

        with open(filename, 'a', encoding='utf-8') as f:
            for result in results:
                f.write(f"目标: {result['target']}\n")
                f.write(f"编辑器类型: {result['editor_type']}\n")
                f.write(f"检测方法: {result['detection_method']}\n")
                f.write(f"基础路径: {result['base_path']}\n")

                if 'resource_found' in result:
                    f.write(f"发现的资源: {result['resource_found']['full_url']}\n")

                f.write("发现的漏洞:\n")
                for vuln in result['vulnerable_paths']:
                    f.write(f"  - {vuln['url']} ({vuln['vulnerability_type']}, {vuln['severity']})\n")

                f.write("\n" + "-"*50 + "\n\n")

        print(f"[+] 结果已保存到 {filename}")

    def scan_targets(self, targets, editor_types=None):
        """批量扫描"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_target = {
                executor.submit(self.scan_single_target, target.strip(), editor_types): target.strip()
                for target in targets if target.strip()
            }

            for future in as_completed(future_to_target):
                target = future_to_target[future]
                try:
                    results = future.result()
                    if results:
                        self.results.extend(results)
                except Exception as e:
                    print(f"[!] {target} - 扫描出错: {e}")

        self.print_summary()

    def print_summary(self):
        """打印摘要"""
        print("\n" + "="*80)
        print("扫描结果摘要:")
        print("="*80)

        if not self.results:
            print("[-] 未发现任何编辑器漏洞")
            return

        # 按编辑器类型分组
        by_editor = {}
        for result in self.results:
            editor_type = result['editor_type']
            if editor_type not in by_editor:
                by_editor[editor_type] = []
            by_editor[editor_type].append(result)

        for editor_type, results in by_editor.items():
            print(f"\n[+] {editor_type.upper()} 编辑器:")
            for result in results:
                print(f"  目标: {result['target']}")
                print(f"  基础路径: {result['base_path']}")
                print(f"  漏洞数量: {len(result['vulnerable_paths'])}")
                for vuln in result['vulnerable_paths']:
                    print(f"    - {vuln['url']} ({vuln['severity']})")

def main():
    print("通用编辑器漏洞扫描器")
    print("="*40)

    if len(sys.argv) < 2:
        print("用法:")
        print("  python universal_editor_scanner.py <目标文件/URL> [编辑器类型]")
        print("\n支持的编辑器类型:")
        scanner = UniversalEditorScanner()
        for editor_type in scanner.editor_signatures.keys():
            print(f"  - {editor_type}")
        print("\n示例:")
        print("  python universal_editor_scanner.py targets.txt")
        print("  python universal_editor_scanner.py example.com dext5upload")
        sys.exit(1)

    target_input = sys.argv[1]
    editor_types = sys.argv[2:] if len(sys.argv) > 2 else None

    # 读取目标
    try:
        with open(target_input, 'r', encoding='utf-8') as f:
            targets = [line.strip() for line in f if line.strip()]
        print(f"[*] 从文件读取到 {len(targets)} 个目标")
    except FileNotFoundError:
        targets = [target_input]
        print(f"[*] 单个目标模式: {target_input}")

    if not targets:
        print("[-] 没有找到有效目标")
        sys.exit(1)

    # 开始扫描
    scanner = UniversalEditorScanner(timeout=10, max_workers=5)
    scanner.scan_targets(targets, editor_types)

if __name__ == "__main__":
    main()
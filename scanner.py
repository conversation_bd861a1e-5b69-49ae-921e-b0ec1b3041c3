#!/usr/bin/env python3
"""
通用编辑器漏洞扫描器
支持多种编辑器的自动识别和漏洞检测
"""

import requests
import re
import urllib3
import sys
import json
import threading
import os
import time
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
import string

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def random_string(length=12):
    """生成随机字符串"""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

class UniversalEditorScanner:
    def __init__(self, timeout=10, max_workers=10):
        self.timeout = timeout
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.verify = False
        self.session.timeout = timeout
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # 编辑器特征定义
        self.editor_signatures = {
            'dext5upload': {
                'core_files': ['dext5upload.js', 'dext5upload.css'],
                'patterns': [
                    r'["\']([^"\']*dext5upload\.js[^"\']*)["\']',
                    r'["\']([^"\']*dext5upload\.css[^"\']*)["\']',
                    r'src=[\'"]*([^"\']*dext5upload\.js[^"\']*)',
                    r'href=[\'"]*([^"\']*dext5upload\.css[^"\']*)'
                ],
                'vuln_paths': [
                    {'path': 'handler/dext5handler.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'handler/dext5handler.ashx', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'dext5upload/handler/dext5handler.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'dext5upload/handler/dext5handler.ashx', 'type': 'upload', 'severity': 'critical'}
                ],
                'directories': [
                    'dext5upload/', 'Dext5Upload/', 'dext5/', 'upload/',
                    'js/component/dext5upload/', 'resources/dext5upload/'
                ]
            },
            'smarteditor': {
                'core_files': ['HuskyEZCreator.js', 'smart_editor2.js'],
                'patterns': [
                    r'["\']([^"\']*HuskyEZCreator\.js[^"\']*)["\']',
                    r'["\']([^"\']*smart_editor2\.js[^"\']*)["\']',
                    r'src=[\'"]*([^"\']*HuskyEZCreator\.js[^"\']*)'
                ],
                'vuln_paths': [
                    {'path': 'sample/photo_uploader/file_uploader.jsp', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/file_uploader.php', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/file_uploader_html5.jsp', 'type': 'upload', 'severity': 'high'},
                    {'path': 'sample/photo_uploader/photo_uploader.html', 'type': 'info', 'severity': 'medium'},
                    {'path': 'photo_uploader/popup/file_uploader.jsp', 'type': 'upload', 'severity': 'high'}
                ],
                'directories': [
                    'smarteditor/', 'SmartEditor/', 'SmartEditor2/', 'se2/',
                    'resources/js/SmartEditor2/', 'js/smarteditor/'
                ]
            },
            'cheditor': {
                'core_files': ['cheditor.js', 'cheditor.css'],
                'patterns': [
                    r'["\']([^"\']*cheditor\.js[^"\']*)["\']',
                    r'["\']([^"\']*cheditor\.css[^"\']*)["\']',
                    r'CHEditor\s*\(',
                    r'cheditor/cheditor\.js'
                ],
                'vuln_paths': [
                    {'path': 'imageUpload/upload.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'imageUpload/delete.jsp', 'type': 'delete', 'severity': 'high'},
                    {'path': 'imageUpload/upload-simple.jsp', 'type': 'upload', 'severity': 'critical'},
                    {'path': 'imageUpload/config.jsp', 'type': 'info', 'severity': 'medium'}
                ],
                'directories': [
                    'cheditor/', 'CHEditor/', 'ch/', 'editor/ch/',
                    'js/cheditor/', 'resources/cheditor/'
                ]
            },
            'ckeditor': {
                'core_files': ['ckeditor.js'],
                'patterns': [
                    r'["\']([^"\']*ckeditor\.js[^"\']*)["\']',
                    r'CKEDITOR\.replace\s*\(',
                    r'CKEDITOR_BASEPATH',
                    r'ckeditor/ckeditor\.js'
                ],
                'vuln_paths': [
                    {'path': '_samples/sample_posteddata.php', 'type': 'info', 'severity': 'medium'},
                    {'path': '_samples/assets/_posteddata.php', 'type': 'info', 'severity': 'medium'},
                    {'path': 'samples/sample_posteddata.php', 'type': 'info', 'severity': 'medium'}
                ],
                'directories': [
                    'ckeditor/', 'CKEditor/', 'ck/', 'editor/ckeditor/',
                    'js/ckeditor/', 'lib/ckeditor/'
                ]
            },
            'ckfinder': {
                'core_files': ['ckfinder.js'],
                'patterns': [
                    r'["\']([^"\']*ckfinder\.js[^"\']*)["\']',
                    r'CKFinder\.create\s*\(',
                    r'ckfinder/ckfinder\.html',
                    r'ckfinder\.js'
                ],
                'vuln_paths': [
                    {'path': 'core/connector/php/connector.php', 'type': 'rce', 'severity': 'critical'},
                    {'path': 'core/connector/connector', 'type': 'rce', 'severity': 'critical'},
                    {'path': 'plugins/fileeditor/plugin.js', 'type': 'rce', 'severity': 'high'},
                    {'path': 'ckfinder.html', 'type': 'info', 'severity': 'low'}
                ],
                'directories': [
                    'ckfinder/', 'CKFinder/', 'filemanager/', 'filebrowser/',
                    'editor/ckfinder/', 'js/ckfinder/'
                ]
            }
        }

        # 通用目录
        self.common_directories = [
            '', 'editor/', 'editors/', 'common/', 'resources/', 'resource/',
            'js/', 'lib/', 'libs/', 'scripts/', 'static/', 'assets/',
            'upload/', 'file/', 'files/', 'admin/', 'manage/', 'system/'
        ]

        self.results = []
        self.file_lock = threading.Lock()

        # 蜜罐检测阈值
        self.honeypot_detection = {
            'max_editors_per_target': 2,  # 单个目标最多发现的编辑器数量
            'max_core_files_per_editor': 3,  # 单个编辑器最多发现的核心文件数量
            'max_auxiliary_paths_per_editor': 4,  # 单个编辑器最多发现的辅助路径数量
            'suspicious_response_similarity': 0.8  # 响应内容相似度阈值
        }

    def normalize_url(self, url):
        """标准化URL"""
        if not url.startswith(('http://', 'https://')):
            return f"https://{url}", f"http://{url}"
        return url, None

    def test_url_accessibility(self, url):
        """测试URL可访问性"""
        primary_url, fallback_url = self.normalize_url(url)

        try:
            response = self.session.get(primary_url, allow_redirects=True)
            if response.status_code == 200:
                return primary_url, response
        except:
            pass

        if fallback_url:
            try:
                response = self.session.get(fallback_url, allow_redirects=True)
                if response.status_code == 200:
                    return fallback_url, response
            except:
                pass

        return None, None

    def get_directory_404_signature(self, base_url, directory_path):
        """获取指定目录的404特征"""
        # 生成随机文件名，测试多种扩展名
        random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))

        signatures = {}

        # 测试常见的扩展名
        for ext in ['jsp', 'ashx', 'php', 'js', 'css', 'html']:
            random_file = f"{random_name}.{ext}"
            test_url = urljoin(base_url, directory_path + random_file)

            try:
                response = self.session.get(test_url, allow_redirects=False, timeout=5)
                signatures[ext] = {
                    'status_code': response.status_code,
                    'content': response.text,
                    'content_length': len(response.text),
                    'headers': dict(response.headers)
                }
            except:
                signatures[ext] = None

        return signatures

    def get_not_found_signature(self, base_url):
        """获取404页面签名（保持兼容性）"""
        return self.get_directory_404_signature(base_url, '/')

    def find_editor_resources(self, html_content, base_url, editor_type=None):
        """在HTML内容中查找编辑器资源"""
        found_resources = []

        # 如果指定了编辑器类型，只查找该类型
        if editor_type:
            editor_types = [editor_type]
        else:
            editor_types = self.editor_signatures.keys()

        for etype in editor_types:
            signature = self.editor_signatures[etype]

            for pattern in signature['patterns']:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    full_url = urljoin(base_url, match)
                    found_resources.append({
                        'editor_type': etype,
                        'resource_path': match,
                        'full_url': full_url,
                        'pattern_matched': pattern
                    })

        return found_resources

    def extract_editor_base_path(self, resource_path, editor_type):
        """从资源路径提取编辑器基础路径"""

        # 方法1: 基于编辑器名称目录
        editor_dir_patterns = {
            'dext5upload': r'(.*?dext5upload[^/]*/).*',
            'smarteditor': r'(.*?(?:smarteditor|se2)[^/]*/).*',
            'cheditor': r'(.*?cheditor[^/]*/).*',
            'ckeditor': r'(.*?ckeditor[^/]*/).*',
            'ckfinder': r'(.*?ckfinder[^/]*/).*'
        }

        pattern = editor_dir_patterns.get(editor_type)
        if pattern:
            match = re.search(pattern, resource_path, re.IGNORECASE)
            if match:
                return match.group(1)

        # 方法2: 基于核心文件名
        signature = self.editor_signatures.get(editor_type, {})
        core_files = signature.get('core_files', [])

        for core_file in core_files:
            if core_file in resource_path:
                base_path = resource_path.replace(core_file, '').rstrip('/')
                # 移除可能的js/css/scripts等子目录
                base_path = re.sub(r'/(js|css|scripts|assets)/?$', '', base_path) + '/'
                return base_path

        # 方法3: 通用后备方案
        return '/'.join(resource_path.split('/')[:-1]) + '/'

    def verify_core_file(self, file_url, editor_type, directory_404_signatures=None):
        """验证核心文件是否真实存在且有效"""
        try:
            response = self.session.get(file_url, allow_redirects=False, timeout=10)

            # 基础检查：状态码必须是200
            if response.status_code != 200:
                return False, f"状态码: {response.status_code}"

            # 检查是否有跳转
            if 'Location' in response.headers:
                return False, "存在跳转"

            # 获取文件扩展名
            file_ext = file_url.split('.')[-1].lower()

            # 404特征对比
            if directory_404_signatures and file_ext in directory_404_signatures:
                signature = directory_404_signatures[file_ext]
                if signature:
                    if (response.status_code == signature['status_code'] and
                        response.text == signature['content']):
                        return False, "匹配404特征"

            # 检查内容长度（太短可能是错误页面）
            if len(response.text) < 100:
                return False, f"内容过短: {len(response.text)}字节"

            # 检查是否包含明显的错误信息
            error_indicators = ['404', 'not found', 'error', 'exception', '错误', '异常']
            content_lower = response.text.lower()
            for indicator in error_indicators:
                if indicator in content_lower:
                    return False, f"包含错误信息: {indicator}"

            # 检查文件扩展名是否匹配内容类型
            if file_ext == 'js':
                # JavaScript文件应该包含JS特征
                js_indicators = ['function', 'var ', 'let ', 'const ', '$(', 'document.', 'window.']
                if not any(indicator in response.text for indicator in js_indicators):
                    return False, "JS文件缺少JavaScript特征"

            elif file_ext == 'css':
                # CSS文件应该包含CSS特征
                css_indicators = ['{', '}', ':', ';', 'color', 'font', 'margin', 'padding']
                if not any(indicator in response.text for indicator in css_indicators):
                    return False, "CSS文件缺少CSS特征"

            # 检查是否包含编辑器特定的标识
            editor_indicators = {
                'dext5upload': ['dext5', 'DEXT5', 'raonwiz'],
                'smarteditor': ['smarteditor', 'naver', 'husky'],
                'cheditor': ['cheditor', 'CHEditor'],
                'ckeditor': ['ckeditor', 'CKEditor', 'CKEDITOR'],
                'ckfinder': ['ckfinder', 'CKFinder']
            }

            if editor_type in editor_indicators:
                indicators = editor_indicators[editor_type]
                if any(indicator.lower() in content_lower for indicator in indicators):
                    return True, "验证成功"

            # 如果没有特定标识，但其他检查都通过，也认为有效
            return True, "基础验证通过"

        except Exception as e:
            return False, f"请求异常: {str(e)}"

    def test_and_save_core_files(self, base_path, editor_type, directory_404_signatures=None):
        """测试并保存核心文件路径"""
        signature = self.editor_signatures.get(editor_type, {})
        core_files = signature.get('core_files', [])

        found_files = []

        for core_file in core_files:
            file_url = urljoin(base_path, core_file)
            is_valid, reason = self.verify_core_file(file_url, editor_type, directory_404_signatures)

            if is_valid:
                found_files.append({
                    'file': core_file,
                    'url': file_url,
                    'status': 'valid',
                    'reason': reason
                })
                print(f"[+] 发现有效核心文件: {file_url} - {reason}")

                # 保存到文件
                self.save_core_file_path(editor_type, file_url, base_path)
            else:
                print(f"[-] 核心文件无效: {file_url} - {reason}")

        return found_files

    def save_core_file_path(self, editor_type, file_url, base_path):
        """保存发现的核心文件路径"""
        filename = f'found_{editor_type}_paths.txt'

        with self.file_lock:
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"{file_url}\t{base_path}\t{editor_type}\n")

        print(f"[*] 路径已保存到 {filename}")

    def calculate_response_similarity(self, response1, response2):
        """计算两个响应的相似度"""
        if not response1 or not response2:
            return 0.0

        # 简单的相似度计算：基于内容长度和部分内容
        len1, len2 = len(response1), len(response2)
        if len1 == 0 and len2 == 0:
            return 1.0
        if len1 == 0 or len2 == 0:
            return 0.0

        # 长度相似度
        length_similarity = 1 - abs(len1 - len2) / max(len1, len2)

        # 内容相似度（简单比较前100字符）
        content1 = response1[:100].lower()
        content2 = response2[:100].lower()

        if content1 == content2:
            content_similarity = 1.0
        else:
            # 计算相同字符的比例
            common_chars = sum(1 for a, b in zip(content1, content2) if a == b)
            content_similarity = common_chars / max(len(content1), len(content2))

        # 综合相似度
        return (length_similarity + content_similarity) / 2

    def detect_honeypot_or_wildcard(self, target_results):
        """检测蜜罐或通配符路由"""
        if not target_results:
            return False, "无结果"

        # 检查1: 编辑器数量异常
        editor_count = len(target_results)
        if editor_count > self.honeypot_detection['max_editors_per_target']:
            return True, f"发现{editor_count}种编辑器，超过正常阈值({self.honeypot_detection['max_editors_per_target']})"

        # 检查2: 每个编辑器的文件数量异常
        for result in target_results:
            core_files_count = len(result.get('core_files_found', []))
            auxiliary_paths_count = len(result.get('auxiliary_paths', []))

            if core_files_count > self.honeypot_detection['max_core_files_per_editor']:
                return True, f"{result['editor_type']}发现{core_files_count}个核心文件，异常"

            if auxiliary_paths_count > self.honeypot_detection['max_auxiliary_paths_per_editor']:
                return True, f"{result['editor_type']}发现{auxiliary_paths_count}个辅助路径，异常"

        # 检查3: 响应内容相似度异常
        if len(target_results) >= 2:
            # 收集所有响应内容进行比较
            responses = []
            for result in target_results:
                for core_file in result.get('core_files_found', []):
                    # 这里需要重新获取响应内容进行比较
                    # 为了简化，我们先检查URL模式
                    pass

        # 检查4: 所有路径都在根目录（可疑）
        all_in_root = all(
            result.get('base_path', '').rstrip('/').endswith(('', '/'))
            for result in target_results
        )
        if all_in_root and editor_count >= 3:
            return True, f"所有{editor_count}种编辑器都在根目录，高度可疑"

        # 检查5: 不同编辑器的文件名模式相似
        all_files = []
        for result in target_results:
            for core_file in result.get('core_files_found', []):
                all_files.append(core_file['url'])

        # 检查是否有相同的响应模式
        if len(set(all_files)) != len(all_files):
            return True, "发现重复的文件URL"

        return False, "未检测到异常"

    def verify_target_legitimacy(self, target, all_results):
        """验证目标的合法性"""
        target_results = [r for r in all_results if r['target'] == target]

        is_suspicious, reason = self.detect_honeypot_or_wildcard(target_results)

        if is_suspicious:
            print(f"[!] {target} - 检测到可疑情况: {reason}")
            print(f"[!] {target} - 可能是蜜罐、WAF或通配符路由，建议人工验证")

            # 保存可疑目标信息
            with open('suspicious_targets.txt', 'a', encoding='utf-8') as f:
                f.write(f"{target}\t{reason}\t发现{len(target_results)}种编辑器\n")

            return False

        return True

    def test_auxiliary_paths(self, base_path, editor_type, directory_404_signatures=None):
        """测试辅助路径（原漏洞路径，现在作为辅助信息）"""
        auxiliary_paths = []
        signature = self.editor_signatures.get(editor_type, {})
        vuln_paths = signature.get('vuln_paths', [])

        print(f"[*] 测试 {editor_type} 的辅助路径...")

        for vuln_info in vuln_paths:
            test_url = urljoin(base_path, vuln_info['path'])

            # 获取期望的文件扩展名
            expected_ext = vuln_info['path'].split('.')[-1].lower()

            try:
                response = self.session.get(test_url, timeout=self.timeout, allow_redirects=False)

                # 基础有效性检查
                is_valid, reason = self.is_auxiliary_path_valid(response, expected_ext, directory_404_signatures, vuln_info)

                if is_valid:
                    auxiliary_paths.append({
                        'url': test_url,
                        'status_code': response.status_code,
                        'path_type': vuln_info['type'],
                        'severity': vuln_info['severity'],
                        'method': 'GET',
                        'reason': reason,
                        'content_length': len(response.text)
                    })
                    print(f"[+] 发现有效辅助路径 ({editor_type}): {test_url} - {reason}")
                else:
                    print(f"[-] 辅助路径无效: {test_url} - {reason}")

            except Exception as e:
                print(f"[-] 辅助路径请求失败: {test_url} - {str(e)}")
                continue

        return auxiliary_paths

    def is_auxiliary_path_valid(self, response, expected_ext, directory_404_signatures, vuln_info):
        """判断辅助路径是否有效"""
        # 基础检查
        if response.status_code != 200:
            return False, f"状态码: {response.status_code}"

        # 检查跳转
        if 'Location' in response.headers:
            return False, "存在跳转"

        # 404特征检查
        if directory_404_signatures and expected_ext in directory_404_signatures:
            signature = directory_404_signatures[expected_ext]
            if signature and response.text == signature['content']:
                return False, "匹配404特征"

        # 检查扩展名与内容的匹配性
        content_lower = response.text.lower()

        # 明显的错误页面
        error_indicators = ['404', 'not found', 'error', 'exception', '错误', '异常', 'file not found']
        if any(indicator in content_lower for indicator in error_indicators):
            return False, "包含错误信息"

        # 扩展名匹配检查
        if expected_ext == 'jsp':
            # JSP页面不应该输出PHP代码
            if '<?php' in response.text or '<?=' in response.text:
                return False, "JSP页面包含PHP代码"
        elif expected_ext == 'php':
            # PHP页面不应该输出JSP代码
            if '<%' in response.text and '%>' in response.text:
                return False, "PHP页面包含JSP代码"
        elif expected_ext == 'ashx':
            # ASHX应该是.NET处理程序
            if '<?php' in response.text:
                return False, "ASHX页面包含PHP代码"

        # 内容长度检查
        if len(response.text) < 10:
            return False, f"内容过短: {len(response.text)}字节"

        # 根据路径类型进行特定检查
        path_type = vuln_info.get('type', '')

        if path_type == 'upload':
            # 上传页面应该包含相关元素
            upload_indicators = ['upload', 'file', 'form', 'multipart', 'enctype', 'input']
            if any(indicator in content_lower for indicator in upload_indicators):
                return True, "包含上传相关内容"

        elif path_type == 'info':
            # 信息页面应该有合理的内容
            if len(response.text) > 100:
                return True, "信息页面内容充实"

        elif path_type == 'rce':
            # RCE相关页面
            rce_indicators = ['connector', 'command', 'execute', 'file', 'upload']
            if any(indicator in content_lower for indicator in rce_indicators):
                return True, "包含RCE相关内容"

        # 默认：如果没有明显问题，认为有效
        if len(response.text) > 50:
            return True, "基础验证通过"

        return False, "未通过验证"



    def scan_single_target(self, target, editor_types=None):
        """扫描单个目标"""
        print(f"[*] 扫描目标: {target}")

        # 测试URL可访问性
        accessible_url, response = self.test_url_accessibility(target)
        if not accessible_url:
            print(f"[-] {target} - 无法访问")
            return None

        print(f"[+] {target} - 可访问 ({accessible_url})")

        # 获取404签名
        not_found_signature = self.get_not_found_signature(accessible_url)

        # 如果未指定编辑器类型，扫描所有类型
        if not editor_types:
            editor_types = list(self.editor_signatures.keys())

        all_results = []

        for editor_type in editor_types:
            print(f"[*] {target} - 扫描 {editor_type} 编辑器...")

            # 第1步: 从主页查找编辑器资源
            editor_resources = self.find_editor_resources(response.text, accessible_url, editor_type)

            if editor_resources:
                print(f"[+] {target} - 在主页发现 {editor_type} 资源")

                for resource in editor_resources:
                    base_path = self.extract_editor_base_path(resource['resource_path'], editor_type)
                    print(f"[*] {target} - 测试 {editor_type} 基础路径: {base_path}")

                    # 获取目录404特征
                    directory_404_signatures = self.get_directory_404_signature(accessible_url, base_path.replace(accessible_url, ''))

                    # 验证核心文件
                    found_core_files = self.test_and_save_core_files(base_path, editor_type, directory_404_signatures)

                    if found_core_files:
                        # 测试辅助路径（可选）
                        auxiliary_paths = self.test_auxiliary_paths(base_path, editor_type, directory_404_signatures)

                        result = {
                            'target': target,
                            'accessible_url': accessible_url,
                            'editor_type': editor_type,
                            'detection_method': 'homepage_analysis',
                            'resource_found': resource,
                            'base_path': base_path,
                            'core_files_found': found_core_files,
                            'auxiliary_paths': auxiliary_paths
                        }
                        all_results.append(result)
                        print(f"[+] {target} - {editor_type} 发现有效核心文件，跳过目录fuzzing")
                        continue

            # 第2步: 目录Fuzzing
            print(f"[*] {target} - 对 {editor_type} 进行目录fuzzing...")
            fuzz_result = self.fuzz_directories(accessible_url, editor_type, not_found_signature)

            if fuzz_result:
                fuzz_result.update({
                    'target': target,
                    'accessible_url': accessible_url,
                    'editor_type': editor_type,
                    'detection_method': 'directory_fuzzing'
                })
                all_results.append(fuzz_result)

        if all_results:
            # 蜜罐检测
            is_legitimate = self.verify_target_legitimacy(target, all_results)

            if is_legitimate:
                self.save_results(all_results)
                return all_results
            else:
                print(f"[!] {target} - 检测结果被标记为可疑，不保存到正常结果中")
                # 可以选择保存到单独的可疑结果文件
                self.save_suspicious_results(all_results)
                return None

        print(f"[-] {target} - 未发现任何编辑器")
        return None

    def fuzz_directories(self, base_url, editor_type, not_found_signature):
        """目录fuzzing - 重点发现核心文件"""
        signature = self.editor_signatures[editor_type]
        core_files = signature['core_files']
        specific_dirs = signature['directories']

        # 组合所有可能的目录
        all_dirs = self.common_directories + specific_dirs

        for directory in all_dirs:
            # 获取目录404特征
            directory_404_signatures = self.get_directory_404_signature(base_url, directory)

            # 构造核心文件URL进行验证
            for core_file in core_files:
                test_url = urljoin(base_url, f"{directory}{core_file}")

                try:
                    # 使用完整的验证而不是简单的HEAD请求
                    is_valid, reason = self.verify_core_file(test_url, editor_type, directory_404_signatures)

                    if is_valid:
                        base_path = urljoin(base_url, directory)
                        print(f"[+] 通过fuzzing发现 {editor_type}: {base_path}")

                        # 保存核心文件路径
                        self.save_core_file_path(editor_type, test_url, base_path)

                        # 验证所有核心文件
                        found_core_files = self.test_and_save_core_files(base_path, editor_type, directory_404_signatures)

                        # 测试辅助路径
                        auxiliary_paths = self.test_auxiliary_paths(base_path, editor_type, directory_404_signatures)

                        return {
                            'base_path': base_path,
                            'core_file_found': test_url,
                            'core_files_found': found_core_files,
                            'auxiliary_paths': auxiliary_paths
                        }
                except Exception as e:
                    print(f"[-] 测试核心文件失败: {test_url} - {str(e)}")
                    continue

        return None

    def save_results(self, results):
        """保存结果"""
        filename = 'editor_discovery_results.txt'

        with open(filename, 'a', encoding='utf-8') as f:
            for result in results:
                f.write(f"目标: {result['target']}\n")
                f.write(f"编辑器类型: {result['editor_type']}\n")
                f.write(f"检测方法: {result['detection_method']}\n")
                f.write(f"基础路径: {result['base_path']}\n")

                if 'resource_found' in result:
                    f.write(f"主页发现的资源: {result['resource_found']['full_url']}\n")

                # 保存核心文件信息
                if 'core_files_found' in result:
                    f.write("发现的核心文件:\n")
                    for core_file in result['core_files_found']:
                        f.write(f"  - {core_file['url']} ({core_file['status']}) - {core_file['reason']}\n")

                # 保存辅助路径信息
                if 'auxiliary_paths' in result:
                    f.write("辅助路径:\n")
                    for aux_path in result['auxiliary_paths']:
                        f.write(f"  - {aux_path['url']} ({aux_path['path_type']}) - {aux_path['reason']}\n")

                f.write("\n" + "-"*50 + "\n\n")

        print(f"[+] 结果已保存到 {filename}")

    def save_suspicious_results(self, results):
        """保存可疑结果"""
        filename = 'suspicious_editor_results.txt'

        with open(filename, 'a', encoding='utf-8') as f:
            f.write(f"[可疑目标检测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}]\n")

            for result in results:
                f.write(f"目标: {result['target']}\n")
                f.write(f"编辑器类型: {result['editor_type']}\n")
                f.write(f"检测方法: {result['detection_method']}\n")
                f.write(f"基础路径: {result['base_path']}\n")

                if 'resource_found' in result:
                    f.write(f"主页发现的资源: {result['resource_found']['full_url']}\n")

                # 保存核心文件信息
                if 'core_files_found' in result:
                    f.write("发现的核心文件:\n")
                    for core_file in result['core_files_found']:
                        f.write(f"  - {core_file['url']} ({core_file['status']}) - {core_file['reason']}\n")

                # 保存辅助路径信息
                if 'auxiliary_paths' in result:
                    f.write("辅助路径:\n")
                    for aux_path in result['auxiliary_paths']:
                        f.write(f"  - {aux_path['url']} ({aux_path['path_type']}) - {aux_path['reason']}\n")

                f.write("\n" + "-"*30 + "\n")

            f.write("="*50 + "\n\n")

        print(f"[!] 可疑结果已保存到 {filename}")

    def scan_targets(self, targets, editor_types=None):
        """批量扫描"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_target = {
                executor.submit(self.scan_single_target, target.strip(), editor_types): target.strip()
                for target in targets if target.strip()
            }

            for future in as_completed(future_to_target):
                target = future_to_target[future]
                try:
                    results = future.result()
                    if results:
                        self.results.extend(results)
                except Exception as e:
                    print(f"[!] {target} - 扫描出错: {e}")

        self.print_summary()

    def print_summary(self):
        """打印摘要"""
        print("\n" + "="*80)
        print("扫描结果摘要:")
        print("="*80)

        if not self.results:
            print("[-] 未发现任何编辑器漏洞")
            return

        # 按编辑器类型分组
        by_editor = {}
        for result in self.results:
            editor_type = result['editor_type']
            if editor_type not in by_editor:
                by_editor[editor_type] = []
            by_editor[editor_type].append(result)

        for editor_type, results in by_editor.items():
            print(f"\n[+] {editor_type.upper()} 编辑器:")
            for result in results:
                print(f"  目标: {result['target']}")
                print(f"  基础路径: {result['base_path']}")

                if 'core_files_found' in result:
                    print(f"  核心文件数量: {len(result['core_files_found'])}")
                    for core_file in result['core_files_found']:
                        print(f"    - {core_file['url']} ({core_file['status']})")

                if 'auxiliary_paths' in result:
                    print(f"  辅助路径数量: {len(result['auxiliary_paths'])}")
                    for aux_path in result['auxiliary_paths']:
                        print(f"    - {aux_path['url']} ({aux_path['path_type']})")

def main():

    if len(sys.argv) < 2:
        print("用法:")
        print("  python universal_editor_scanner.py <目标文件/URL> [编辑器类型]")
        print("\n支持的编辑器类型:")
        scanner = UniversalEditorScanner()
        for editor_type in scanner.editor_signatures.keys():
            print(f"  - {editor_type}")
        print("\n示例:")
        print("  python universal_editor_scanner.py targets.txt")
        print("  python universal_editor_scanner.py example.com dext5upload")
        sys.exit(1)

    target_input = sys.argv[1]
    editor_types = sys.argv[2:] if len(sys.argv) > 2 else None

    # 读取目标
    try:
        with open(target_input, 'r', encoding='utf-8') as f:
            targets = [line.strip() for line in f if line.strip()]
        print(f"[*] 从文件读取到 {len(targets)} 个目标")
    except FileNotFoundError:
        targets = [target_input]
        print(f"[*] 单个目标模式: {target_input}")

    if not targets:
        print("[-] 没有找到有效目标")
        sys.exit(1)

    # 开始扫描
    scanner = UniversalEditorScanner(timeout=10, max_workers=5)
    scanner.scan_targets(targets, editor_types)

if __name__ == "__main__":
    main()
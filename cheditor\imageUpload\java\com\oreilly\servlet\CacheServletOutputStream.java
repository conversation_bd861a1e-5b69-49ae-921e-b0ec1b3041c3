package com.oreilly.servlet;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.servlet.ServletOutputStream;

/* compiled from: CacheHttpServlet.java */
/* loaded from: cos.jar:com/oreilly/servlet/CacheServletOutputStream.class */
class CacheServletOutputStream extends ServletOutputStream {
    ServletOutputStream delegate;
    ByteArrayOutputStream cache = new ByteArrayOutputStream(4096);

    CacheServletOutputStream(ServletOutputStream servletOutputStream) {
        this.delegate = servletOutputStream;
    }

    public ByteArrayOutputStream getBuffer() {
        return this.cache;
    }

    public void write(int i) throws IOException {
        this.delegate.write(i);
        this.cache.write(i);
    }

    public void write(byte[] bArr) throws IOException {
        this.delegate.write(bArr);
        this.cache.write(bArr);
    }

    public void write(byte[] bArr, int i, int i2) throws IOException {
        this.delegate.write(bArr, i, i2);
        this.cache.write(bArr, i, i2);
    }
}

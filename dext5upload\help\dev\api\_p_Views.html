﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: Views</h3>
    <p class="ttl">config.Views</p>
    <p class="txt">
       업로드 파일 목록 보기 형태를 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "list" 입니다.<br/>
        "list"로 설정시 "리스트형태로 보기", "thumbs" 로 설정시 "썸네일형태로 보기" 입니다.      
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 파일 목록 보기 형태를 썸네일보기로 설정합니다.
        DEXT5UPLOAD.config.Views = 'thumbs';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


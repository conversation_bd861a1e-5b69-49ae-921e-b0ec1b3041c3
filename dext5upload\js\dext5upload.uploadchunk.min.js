importScripts("dext5upload.base64.js");importScripts("dext5upload.xhr.js");var numberOfServerUploadedChunks=0;
function buildFormData(c,n,e,b,d,l,f){c=(new FileReaderSync).readAsDataURL(c).match(/,(.*)$/)[1];var a="";if("1"==d.encryptParam){var k;k=""+("d01"+Dext5Base64._trans_unitAttributeDelimiter+"uploadRequest"+Dext5Base64._trans_unitDelimiter);k+="d18"+Dext5Base64._trans_unitAttributeDelimiter+n+Dext5Base64._trans_unitDelimiter;k+="d07"+Dext5Base64._trans_unitAttributeDelimiter+e+Dext5Base64._trans_unitDelimiter;k+="d08"+Dext5Base64._trans_unitAttributeDelimiter+b+<PERSON>t5Base64._trans_unitDelimiter;k+=
"d35"+Dext5Base64._trans_unitAttributeDelimiter+d.checkFileExtension+Dext5Base64._trans_unitDelimiter;k+="d37"+Dext5Base64._trans_unitAttributeDelimiter+d.uploadChunkSize+Dext5Base64._trans_unitDelimiter;k+="d47"+Dext5Base64._trans_unitAttributeDelimiter+f+Dext5Base64._trans_unitDelimiter;k=Dext5Base64.makeEncryptParam(k);a=a+("--"+l+'\r\nContent-Disposition: form-data; name="d00"')+"\r\n";a+="\r\n";a+=k}else a+="--"+l+'\r\nContent-Disposition: form-data; name="dext5CMD"',a+="\r\n",a+="\r\n",a+="uploadRequest",
a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="chunkNumber"',a+="\r\n",a+="\r\n",a+=n,a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="GUID"',a+="\r\n",a+="\r\n",a+=e,a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="fileName"',a+="\r\n",a+="\r\n",a+=b,a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="cfe"',a+="\r\n",a+="\r\n",a+=d.checkFileExtension,a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="cs"',a+="\r\n",a+="\r\n",a+=d.uploadChunkSize,
a+="\r\n",a+="--"+l+'\r\nContent-Disposition: form-data; name="fs"',a+="\r\n",a+="\r\n",a+=f;a+="\r\n";a+="--"+l+'\r\nContent-Disposition: form-data; name="Slice"; filename="blob"';a+="\r\n";a+="Content-Type: application/octet-stream; charset=UTF-8";a+="\r\n";a+="\r\n";a+=c+"\r\n";return a+="--"+l+"--\r\n"}
function upload_merge(c,n,e,b,d,l,f,a,k,r,q){var g=XHRFactory.getInstance();g.open("POST",d,l);g.onreadystatechange=function(a){if(4==g.readyState)if(200==g.status){var b=g.responseText;a=b.split("|");1==a.length&&(b=""==f.ServerReleaseVer||"2.7.1120889.1613.01">=f.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),a=b.split("|"));if(0==b.indexOf("error"))self.postMessage({type:"error",code:a[1],message:a[2],id:c}),XHRFactory.release(g),self.close();else{var d;d=a[0];
var e=d.indexOf("::");-1<e?(b=d.substring(0,e),d=d.substring(e+2)):(b=n,d=a[0]);2==a.length?-1<a[1].indexOf("\b")?self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1].split("\b")[0],uploadCustomValue:"",uploadGroupId:a[1].split("\b")[1]}):self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:"",uploadGroupId:""}):3==a.length&&(-1<a[2].indexOf("\b")?self.postMessage({type:"complete",id:c,originalFileName:b,
uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:a[2].split("\b")[0],uploadGroupId:a[2].split("\b")[1]}):self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:a[2],uploadGroupId:""}));XHRFactory.release(g)}}else if(400==g.status||413==g.status||415==g.status||500==g.status)b=g.responseText,a=b.split("|"),1==a.length&&(b=""==f.ServerReleaseVer||"2.7.1120889.1613.01">=f.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),
a=b.split("|")),2==a.length?self.postMessage({type:"error",code:a[0],message:"Upload Error: "+a[1],id:c}):3==a.length?self.postMessage({type:"error",code:a[1],message:"Upload Error: "+a[2],id:c}):self.postMessage({type:"error",code:"000",message:"Upload Error: upload_merge",id:c}),XHRFactory.release(g),self.close()};"1"==f.encryptParam?(d=""+("d01"+Dext5Base64._trans_unitAttributeDelimiter+"mergeRequest"+Dext5Base64._trans_unitDelimiter),d+="d08"+Dext5Base64._trans_unitAttributeDelimiter+n+Dext5Base64._trans_unitDelimiter,
d+="d07"+Dext5Base64._trans_unitAttributeDelimiter+e+Dext5Base64._trans_unitDelimiter,d+="d19"+Dext5Base64._trans_unitAttributeDelimiter+b.numberOfChunks+Dext5Base64._trans_unitDelimiter,d+="d12"+Dext5Base64._trans_unitAttributeDelimiter+k+Dext5Base64._trans_unitDelimiter,d+="d38"+Dext5Base64._trans_unitAttributeDelimiter+r+Dext5Base64._trans_unitDelimiter,d+="d11"+Dext5Base64._trans_unitAttributeDelimiter+f.folderNameRule+Dext5Base64._trans_unitDelimiter,d+="d09"+Dext5Base64._trans_unitAttributeDelimiter+
f.fileNameRule+Dext5Base64._trans_unitDelimiter,d+="d10"+Dext5Base64._trans_unitAttributeDelimiter+f.fileNameRuleEx+Dext5Base64._trans_unitDelimiter,d=Dext5Base64.makeEncryptParam(d),e="d00="+d):(e="dext5CMD=mergeRequest&fileName="+encodeURIComponent(n)+"&GUID="+e+"&numberOfChunks="+b.numberOfChunks+"&gpid="+k+"&fidx="+r,e+="&folderNameRule="+f.folderNameRule+"&fileNameRule="+f.fileNameRule+"&fileNameRuleEx="+f.fileNameRuleEx);"1"==f.integrity&&(e+="&fdi="+f.FDIData);e+="&mark="+a;a=q.length;for(b=
0;b<a;b++)e+="&"+q[b].form_name+"="+encodeURIComponent(q[b].form_value);g.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");try{g.send(e)}catch(m){self.postMessage({type:"error",code:"000",message:"Upload Error: upload_merge",id:c}),XHRFactory.release(g),self.close()}}
function upload(c,n,e,b,d,l,f,a,k,r,q,g){var m=XHRFactory.getInstance(),h=e,h=-1<h.indexOf("?")?h+"&dext=slice":h+"?dext=slice";m.open("POST",h,d);var t=c.size;m.onreadystatechange=function(){if(4==m.readyState)if(200==m.status){var c=m.responseText,h=c.split("|");1==h.length&&(c=""==a.ServerReleaseVer||"2.7.1120889.1613.01">=a.ServerReleaseVer?Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),h=c.split("|"));0==c.indexOf("error")?(self.postMessage({type:"error",code:h[1],message:h[2],
id:l}),XHRFactory.release(m),self.close()):(numberOfServerUploadedChunks++,self.postMessage({type:"progress",uploadedChunks:numberOfServerUploadedChunks,uploadedSize:t,chunkInfo:b,id:l}),numberOfServerUploadedChunks==b.numberOfChunks&&(upload_merge(l,n,f,b,e,d,a,k,r,q,g),numberOfServerUploadedChunks=0),XHRFactory.release(m))}else if(400==m.status||413==m.status||415==m.status||500==m.status)c=m.responseText,h=c.split("|"),1==h.length&&(c=""==a.ServerReleaseVer||"2.7.1120889.1613.01">=a.ServerReleaseVer?
Dext5Base64.decode(c):Dext5Base64.makeDecryptReponseMessage(c),h=c.split("|")),2==h.length?self.postMessage({type:"error",code:h[0],message:"Upload Error: "+h[1],id:l}):3==h.length?self.postMessage({type:"error",code:h[1],message:"Upload Error: "+h[2],id:l}):self.postMessage({type:"error",code:"000",message:"Upload Error: upload_merge",id:l}),XHRFactory.release(m),self.close()};h="";if("undefined"==typeof FormData){try{h=buildFormData(c,b.currentNumber,f,n,a,"----12345678wertysdfg",b.size)}catch(u){self.postMessage({type:"reupload",
chunk:c,filename:n,chunkInfo:b,asyncstate:d,guid:f,configValues:a,id:l,mark:k,gpid:r,fidx:q})}m.setRequestHeader("Content-Type","multipart/form-data; boundary=----12345678wertysdfg");m.setRequestHeader("Content-Length",h.length);m.setRequestHeader("Dext5-Encoded","base64")}else{h=new FormData;if("1"==a.encryptParam){var p;p=""+("d01"+Dext5Base64._trans_unitAttributeDelimiter+"uploadRequest"+Dext5Base64._trans_unitDelimiter);p+="d18"+Dext5Base64._trans_unitAttributeDelimiter+b.currentNumber+Dext5Base64._trans_unitDelimiter;
p+="d07"+Dext5Base64._trans_unitAttributeDelimiter+f+Dext5Base64._trans_unitDelimiter;p+="d08"+Dext5Base64._trans_unitAttributeDelimiter+n+Dext5Base64._trans_unitDelimiter;p+="d35"+Dext5Base64._trans_unitAttributeDelimiter+a.checkFileExtension+Dext5Base64._trans_unitDelimiter;p+="d37"+Dext5Base64._trans_unitAttributeDelimiter+a.uploadChunkSize+Dext5Base64._trans_unitDelimiter;p+="d47"+Dext5Base64._trans_unitAttributeDelimiter+b.size+Dext5Base64._trans_unitDelimiter;p=Dext5Base64.makeEncryptParam(p);
h.append("d00",p)}else h.append("dext5CMD","uploadRequest"),h.append("chunkNumber",b.currentNumber),h.append("GUID",f),h.append("fileName",n),h.append("cfe",a.checkFileExtension),h.append("cs",a.uploadChunkSize),h.append("fs",b.size);h.append("Slice",c)}try{m.send(h)}catch(v){self.postMessage({type:"reupload",chunk:c,filename:n,chunkInfo:b,asyncstate:d,guid:f,configValues:a,id:l,mark:k,gpid:r,fidx:q}),XHRFactory.release(m)}h=c=null}
function uploadZeroFile(c,n,e,b,d,l,f,a,k,r,q){var g=XHRFactory.getInstance();g.open("POST",b,d);g.onreadystatechange=function(a){if(4==g.readyState)if(200==g.status){var b=g.responseText;a=b.split("|");1==a.length&&(b=""==f.ServerReleaseVer||"2.7.1120889.1613.01">=f.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),a=b.split("|"));if(0==b.indexOf("error"))self.postMessage({type:"error",code:a[1],message:a[2],id:c}),XHRFactory.release(g),self.close();else{var d;d=a[0];
var e=d.indexOf("::");-1<e?(b=d.substring(0,e),d=d.substring(e+2)):(b=n,d=a[0]);2==a.length?-1<a[1].indexOf("\b")?self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1].split("\b")[0],uploadCustomValue:"",uploadGroupId:a[1].split("\b")[1]}):self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:"",uploadGroupId:""}):3==a.length&&(-1<a[2].indexOf("\b")?self.postMessage({type:"complete",id:c,originalFileName:b,
uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:a[2].split("\b")[0],uploadGroupId:a[2].split("\b")[1]}):self.postMessage({type:"complete",id:c,originalFileName:b,uploadFilePath:d,uploadFileName:a[1],uploadCustomValue:a[2],uploadGroupId:""}));XHRFactory.release(g)}}else if(400==g.status||413==g.status||415==g.status||500==g.status)b=g.responseText,a=b.split("|"),1==a.length&&(b=""==f.ServerReleaseVer||"2.7.1120889.1613.01">=f.ServerReleaseVer?Dext5Base64.decode(b):Dext5Base64.makeDecryptReponseMessage(b),
a=b.split("|")),2==a.length?self.postMessage({type:"error",code:a[0],message:"Upload Error: "+a[1],id:c}):3==a.length?self.postMessage({type:"error",code:a[1],message:"Upload Error: "+a[2],id:c}):self.postMessage({type:"error",code:"000",message:"Upload Error: uploadZeroFile",id:c}),XHRFactory.release(g),self.close()};"1"==f.encryptParam?(b=""+("d01"+Dext5Base64._trans_unitAttributeDelimiter+"uzr"+Dext5Base64._trans_unitDelimiter),b+="d08"+Dext5Base64._trans_unitAttributeDelimiter+n+Dext5Base64._trans_unitDelimiter,
b+="d07"+Dext5Base64._trans_unitAttributeDelimiter+e+Dext5Base64._trans_unitDelimiter,b+="d12"+Dext5Base64._trans_unitAttributeDelimiter+k+Dext5Base64._trans_unitDelimiter,b+="d38"+Dext5Base64._trans_unitAttributeDelimiter+r+Dext5Base64._trans_unitDelimiter,b+="d11"+Dext5Base64._trans_unitAttributeDelimiter+f.folderNameRule+Dext5Base64._trans_unitDelimiter,b+="d09"+Dext5Base64._trans_unitAttributeDelimiter+f.fileNameRule+Dext5Base64._trans_unitDelimiter,b+="d10"+Dext5Base64._trans_unitAttributeDelimiter+
f.fileNameRuleEx+Dext5Base64._trans_unitDelimiter,b+="d35"+Dext5Base64._trans_unitAttributeDelimiter+f.checkFileExtension+Dext5Base64._trans_unitDelimiter,b=Dext5Base64.makeEncryptParam(b),e="d00="+b):(e="dext5CMD=uzr&fileName="+encodeURIComponent(n)+"&GUID="+e+"&gpid="+k+"&fidx="+r,e+="&folderNameRule="+f.folderNameRule+"&fileNameRule="+f.fileNameRule+"&fileNameRuleEx="+f.fileNameRuleEx,e+="&cfe="+f.checkFileExtension);e+="&mark="+a;a=q.length;for(k=0;k<a;k++)e+="&"+q[k].form_name+"="+encodeURIComponent(q[k].form_value);
g.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");try{g.send(e)}catch(m){self.postMessage({type:"error",code:"000",message:"Upload Error: uploadZeroFile",id:c}),XHRFactory.release(g),XHRFactory=null,self.close()}}
self.onmessage=function(c){c=c.data;switch(c.type){case "upload":upload(c.chunk,c.filename,c.handlerurl,c.chunkInfo,c.asyncstate,c.id,c.guid,c.configValues,c.mark,c.gpid,c.fidx,c.formDataEx);break;case "uploadZeroFile":self.postMessage({type:"progress",uploadedChunks:1,uploadedSize:0,chunkInfo:{currentNumber:1,numberOfChunks:1,size:0},id:c.id}),uploadZeroFile(c.id,c.filename,c.guid,c.handlerurl,c.asyncstate,c.blob,c.configValues,c.mark,c.gpid,c.fidx,c.formDataEx)}};

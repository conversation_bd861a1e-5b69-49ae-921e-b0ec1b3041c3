﻿using System;
using System.IO;
using System.Text;
using System.Web;
using Raonwiz.Dext5.Common;
using Raonwiz.Dext5.Process.Entity;

namespace Raonwiz.Dext5.Process.Html5Plus
{
	// Token: 0x02000032 RID: 50
	public class UploadAfterEvent : Base
	{
		// Token: 0x06000307 RID: 775 RVA: 0x00021849 File Offset: 0x0001FA49
		public UploadAfterEvent(HttpContext context, string pTempPath, string pPhysicalPath, string pVirtualPath) : base(context)
		{
		}

		// Token: 0x06000308 RID: 776 RVA: 0x00021868 File Offset: 0x0001FA68
		public override object Run(UploadHandlerBeforeInitializeDelegate pBeforeInitializeEvent, UploadHandlerBeforeInitializeDelegateEx pBeforeInitializeEventEx, UploadHandlerBeforeCompleteDelegate pCompleteBeforeEvent, UploadHandlerBeforeCompleteDelegateEx pCompleteBeforeEventEx, UploadHandlerDelegate pCompleteEvent, UploadHandlerDelegateEx pCompleteEventEx, OpenDownloadBeforeInitializeDelegateEx pOpenDownloadBeforeInitializeEventEx, ref Dext5CustomError pCustomError)
		{
			try
			{
				if (!base.CheckCaller("html5"))
				{
					this.hContext.Response.Clear();
					this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|013|Bad Request Type"));
					return null;
				}
				string text = this._entity_dextParam.h5pbeInfo;
				text = Dext5Encoding.Base64Decoding(text);
				string[] array = text.Split(new char[]
				{
					'|'
				});
				if (array.Length == 6)
				{
					string text2 = array[0];
					string text3 = array[1];
					string text4 = array[2];
					string text5 = array[3];
					string text6 = array[4];
					string text7 = array[5];
					string text8 = string.Concat(new object[]
					{
						text2,
						this.m_PathChar,
						text3,
						this.m_PathChar
					});
					string text9 = this._entity_dextParam.fileName;
					text9 = text9.Normalize(NormalizationForm.FormC);
					int num = Convert.ToInt32(this._entity_dextParam.numberOfChunks);
					DirectoryInfo directoryInfo = new DirectoryInfo(text8);
					string str = text3;
					string extension = Path.GetExtension(text9);
					int num2 = directoryInfo.GetFiles("*.tmp").Length;
					if (num2 != num)
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|005|Number of file chunks less than total count"));
						return null;
					}
					try
					{
						if (num == 1)
						{
							File.Move(Path.Combine(text8, text3 + ".0000000000000001.tmp"), text5);
							Directory.Delete(text8, true);
						}
						else
						{
							string text10 = text8 + str + extension;
							using (FileStream fileStream = new FileStream(text10, FileMode.OpenOrCreate, FileAccess.Write))
							{
								FileInfo[] files = directoryInfo.GetFiles("*.tmp");
								Array.Sort<FileInfo>(files, (FileInfo f1, FileInfo f2) => f1.Name.CompareTo(f2.Name));
								foreach (FileInfo fileInfo in files)
								{
									if (this._b_IsDebug)
									{
										LogUtil.DextDebug("CMD - Html5 Merge, " + fileInfo.FullName, this._str_DebugFilePath);
									}
									using (FileStream fileStream2 = File.OpenRead(fileInfo.FullName))
									{
										base.CopyStreamToStream(fileStream2, fileStream);
									}
								}
								fileStream.Flush();
								fileStream.Close();
							}
							File.Move(text10, text5);
							Directory.Delete(text8, true);
						}
						try
						{
							pCompleteBeforeEvent(this.hContext, ref text5, ref text6, ref text7, ref this._str_ResponseCustomValue);
						}
						catch
						{
						}
						try
						{
							UploadEventEntity uploadEventEntity = new UploadEventEntity();
							uploadEventEntity.Context = this.hContext;
							uploadEventEntity.NewFileLocation = text5;
							uploadEventEntity.ResponseFileServerPath = text6;
							uploadEventEntity.ResponseFileName = text7;
							uploadEventEntity.ResponseGroupId = this._entity_dextParam.fileGroupID;
							uploadEventEntity.FileIndex = this._entity_dextParam.fileIndex;
							pCompleteBeforeEventEx(uploadEventEntity);
							text5 = uploadEventEntity.NewFileLocation;
							text6 = uploadEventEntity.ResponseFileServerPath;
							text7 = uploadEventEntity.ResponseFileName;
							this._str_ResponseCustomValue = uploadEventEntity.ResponseCustomValue;
							this._str_ResponseGroupId = uploadEventEntity.ResponseGroupId;
						}
						catch
						{
						}
						FileInfo fileInfo2 = new FileInfo(text5);
						string text11 = fileInfo2.Length.ToString();
						string text12 = this._entity_dextParam.fileName;
						text12 = text12.Normalize(NormalizationForm.FormC);
						string text13 = "";
						if (!string.IsNullOrEmpty(this._str_ResponseCustomValue) && this._str_ResponseCustomValue.IndexOf("::") == -1 && this._str_ResponseCustomValue.IndexOf('|') == -1 && this._str_ResponseCustomValue.IndexOf('^') == -1)
						{
							text13 = text13 + "|" + this._str_ResponseCustomValue;
						}
						if (!string.IsNullOrEmpty(this._str_ResponseGroupId) && this._str_ResponseGroupId.IndexOf("::") == -1 && this._str_ResponseGroupId.IndexOf('|') == -1 && this._str_ResponseGroupId.IndexOf('^') == -1)
						{
							text13 = text13 + "^" + this._str_ResponseGroupId;
						}
						string text14 = string.Concat(new string[]
						{
							"complete|",
							text12,
							"::",
							text6,
							"|",
							text7,
							"|",
							text11,
							text13
						});
						text14 = Dext5Parameter.MakeParameter(text14);
						this.hContext.Response.Write(text14);
						try
						{
							pCompleteEvent(this.hContext, text5, text6, text7);
						}
						catch
						{
						}
						try
						{
							pCompleteEventEx(new UploadEventEntity
							{
								Context = this.hContext,
								NewFileLocation = text5,
								ResponseFileServerPath = text6,
								ResponseFileName = text7
							});
						}
						catch
						{
						}
						goto IL_4E8;
					}
					catch (Exception)
					{
						this.hContext.Response.Clear();
						this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|008|Complete error occured on the server side"));
						goto IL_4E8;
					}
					goto IL_4DD;
					IL_4E8:
					goto IL_517;
				}
				IL_4DD:
				throw new Exception("Error occured on the server side");
			}
			catch (Exception)
			{
				this.hContext.Response.Clear();
				this.hContext.Response.Write(Dext5Parameter.MakeParameter("error|023|Error occured on after upload"));
			}
			IL_517:
			return null;
		}
	}
}

package org.apache.struts.taglib.nested;

import java.io.Serializable;

/* loaded from: struts.jar:org/apache/struts/taglib/nested/NestedReference.class */
public class NestedReference implements Serializable {
    private String beanName;
    private String property;

    public NestedReference() {
    }

    public NestedReference(String name, String property) {
        this.beanName = name;
        this.property = property;
    }

    public String getBeanName() {
        return this.beanName;
    }

    public void setBeanName(String newName) {
        this.beanName = newName;
    }

    public String getNestedProperty() {
        return this.property;
    }

    public void setNestedProperty(String newProperty) {
        this.property = newProperty;
    }
}

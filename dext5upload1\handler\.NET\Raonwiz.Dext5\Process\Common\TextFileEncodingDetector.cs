﻿using System;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace Raonwiz.Dext5.Process.Common
{
	// Token: 0x0200001E RID: 30
	public static class TextFileEncodingDetector
	{
		// Token: 0x0600022F RID: 559 RVA: 0x000184E4 File Offset: 0x000166E4
		public static Encoding DetectTextFileEncoding(string InputFilename)
		{
			Encoding result;
			using (FileStream fileStream = File.OpenRead(InputFilename))
			{
				result = TextFileEncodingDetector.DetectTextFileEncoding(fileStream, 65536L);
			}
			return result;
		}

		// Token: 0x06000230 RID: 560 RVA: 0x00018524 File Offset: 0x00016724
		public static Encoding DetectTextFileEncoding(FileStream InputFileStream, long HeuristicSampleSize)
		{
			bool flag = false;
			return TextFileEncodingDetector.DetectTextFileEncoding(InputFileStream, 65536L, out flag);
		}

		// Token: 0x06000231 RID: 561 RVA: 0x00018544 File Offset: 0x00016744
		public static Encoding DetectTextFileEncoding(FileStream InputFileStream, long HeuristicSampleSize, out bool HasBOM)
		{
			if (InputFileStream == null)
			{
				throw new ArgumentNullException("Must provide a valid Filestream!", "InputFileStream");
			}
			if (!InputFileStream.CanRead)
			{
				throw new ArgumentException("Provided file stream is not readable!", "InputFileStream");
			}
			if (!InputFileStream.CanSeek)
			{
				throw new ArgumentException("Provided file stream cannot seek!", "InputFileStream");
			}
			long position = InputFileStream.Position;
			InputFileStream.Position = 0L;
			byte[] array = new byte[(InputFileStream.Length > 4L) ? 4L : InputFileStream.Length];
			InputFileStream.Read(array, 0, array.Length);
			Encoding encoding = TextFileEncodingDetector.DetectBOMBytes(array);
			if (encoding != null)
			{
				InputFileStream.Position = position;
				HasBOM = true;
				return encoding;
			}
			byte[] array2 = new byte[(HeuristicSampleSize > InputFileStream.Length) ? InputFileStream.Length : HeuristicSampleSize];
			Array.Copy(array, array2, array.Length);
			if (InputFileStream.Length > (long)array.Length)
			{
				InputFileStream.Read(array2, array.Length, array2.Length - array.Length);
			}
			InputFileStream.Position = position;
			encoding = TextFileEncodingDetector.DetectUnicodeInByteSampleByHeuristics(array2);
			HasBOM = false;
			return encoding;
		}

		// Token: 0x06000232 RID: 562 RVA: 0x00018638 File Offset: 0x00016838
		public static Encoding DetectTextByteArrayEncoding(byte[] TextData)
		{
			bool flag = false;
			return TextFileEncodingDetector.DetectTextByteArrayEncoding(TextData, out flag);
		}

		// Token: 0x06000233 RID: 563 RVA: 0x00018650 File Offset: 0x00016850
		public static Encoding DetectTextByteArrayEncoding(byte[] TextData, out bool HasBOM)
		{
			if (TextData == null)
			{
				throw new ArgumentNullException("Must provide a valid text data byte array!", "TextData");
			}
			Encoding encoding = TextFileEncodingDetector.DetectBOMBytes(TextData);
			if (encoding != null)
			{
				HasBOM = true;
				return encoding;
			}
			encoding = TextFileEncodingDetector.DetectUnicodeInByteSampleByHeuristics(TextData);
			HasBOM = false;
			return encoding;
		}

		// Token: 0x06000234 RID: 564 RVA: 0x0001868C File Offset: 0x0001688C
		public static string GetStringFromByteArray(byte[] TextData, Encoding DefaultEncoding)
		{
			return TextFileEncodingDetector.GetStringFromByteArray(TextData, DefaultEncoding, 65536L);
		}

		// Token: 0x06000235 RID: 565 RVA: 0x0001869C File Offset: 0x0001689C
		public static string GetStringFromByteArray(byte[] TextData, Encoding DefaultEncoding, long MaxHeuristicSampleSize)
		{
			if (TextData == null)
			{
				throw new ArgumentNullException("Must provide a valid text data byte array!", "TextData");
			}
			Encoding encoding = TextFileEncodingDetector.DetectBOMBytes(TextData);
			if (encoding != null)
			{
				return encoding.GetString(TextData, encoding.GetPreamble().Length, TextData.Length - encoding.GetPreamble().Length);
			}
			if ((long)TextData.Length > MaxHeuristicSampleSize)
			{
				byte[] destinationArray = new byte[MaxHeuristicSampleSize];
				Array.Copy(TextData, destinationArray, MaxHeuristicSampleSize);
			}
			encoding = (TextFileEncodingDetector.DetectUnicodeInByteSampleByHeuristics(TextData) ?? DefaultEncoding);
			return encoding.GetString(TextData);
		}

		// Token: 0x06000236 RID: 566 RVA: 0x00018714 File Offset: 0x00016914
		public static Encoding DetectBOMBytes(byte[] BOMBytes)
		{
			if (BOMBytes == null)
			{
				throw new ArgumentNullException("Must provide a valid BOM byte array!", "BOMBytes");
			}
			if (BOMBytes.Length < 2)
			{
				return null;
			}
			if (BOMBytes[0] == 255 && BOMBytes[1] == 254 && (BOMBytes.Length < 4 || BOMBytes[2] != 0 || BOMBytes[3] != 0))
			{
				return Encoding.Unicode;
			}
			if (BOMBytes[0] == 254 && BOMBytes[1] == 255)
			{
				return Encoding.BigEndianUnicode;
			}
			if (BOMBytes.Length < 3)
			{
				return null;
			}
			if (BOMBytes[0] == 239 && BOMBytes[1] == 187 && BOMBytes[2] == 191)
			{
				return Encoding.UTF8;
			}
			if (BOMBytes[0] == 43 && BOMBytes[1] == 47 && BOMBytes[2] == 118)
			{
				return Encoding.UTF7;
			}
			if (BOMBytes.Length < 4)
			{
				return null;
			}
			if (BOMBytes[0] == 255 && BOMBytes[1] == 254 && BOMBytes[2] == 0 && BOMBytes[3] == 0)
			{
				return Encoding.UTF32;
			}
			if (BOMBytes[0] == 0 && BOMBytes[1] == 0 && BOMBytes[2] == 254 && BOMBytes[3] == 255)
			{
				return Encoding.GetEncoding(12001);
			}
			return null;
		}

		// Token: 0x06000237 RID: 567 RVA: 0x00018820 File Offset: 0x00016A20
		public static Encoding DetectUnicodeInByteSampleByHeuristics(byte[] SampleBytes)
		{
			long num = 0L;
			long num2 = 0L;
			long num3 = 0L;
			long num4 = 0L;
			long num5 = 0L;
			long num6 = 0L;
			int num7 = 0;
			while (num6 < (long)SampleBytes.Length)
			{
				if (SampleBytes[(int)(checked((IntPtr)num6))] == 0)
				{
					if (num6 % 2L == 0L)
					{
						num2 += 1L;
					}
					else
					{
						num += 1L;
					}
				}
				if (TextFileEncodingDetector.IsCommonUSASCIIByte(SampleBytes[(int)(checked((IntPtr)num6))]))
				{
					num5 += 1L;
				}
				if (num7 == 0)
				{
					int num8 = TextFileEncodingDetector.DetectSuspiciousUTF8SequenceLength(SampleBytes, num6);
					if (num8 > 0)
					{
						num3 += 1L;
						num4 += (long)num8;
						num7 = num8 - 1;
					}
				}
				else
				{
					num7--;
				}
				num6 += 1L;
			}
			if ((double)num2 * 2.0 / (double)SampleBytes.Length < 0.2 && (double)num * 2.0 / (double)SampleBytes.Length > 0.6)
			{
				return Encoding.Unicode;
			}
			if ((double)num * 2.0 / (double)SampleBytes.Length < 0.2 && (double)num2 * 2.0 / (double)SampleBytes.Length > 0.6)
			{
				return Encoding.BigEndianUnicode;
			}
			string @string = Encoding.ASCII.GetString(SampleBytes);
			Regex regex = new Regex("\\A([\\x09\\x0A\\x0D\\x20-\\x7E]|[\\xC2-\\xDF][\\x80-\\xBF]|\\xE0[\\xA0-\\xBF][\\x80-\\xBF]|[\\xE1-\\xEC\\xEE\\xEF][\\x80-\\xBF]{2}|\\xED[\\x80-\\x9F][\\x80-\\xBF]|\\xF0[\\x90-\\xBF][\\x80-\\xBF]{2}|[\\xF1-\\xF3][\\x80-\\xBF]{3}|\\xF4[\\x80-\\x8F][\\x80-\\xBF]{2})*\\z");
			if (regex.IsMatch(@string) && (double)num3 * 500000.0 / (double)SampleBytes.Length >= 1.0 && ((long)SampleBytes.Length - num4 == 0L || (double)num5 * 1.0 / (double)((long)SampleBytes.Length - num4) >= 0.8))
			{
				return Encoding.UTF8;
			}
			return null;
		}

		// Token: 0x06000238 RID: 568 RVA: 0x000189A0 File Offset: 0x00016BA0
		private static bool IsCommonUSASCIIByte(byte testByte)
		{
			return testByte == 10 || testByte == 13 || testByte == 9 || (testByte >= 32 && testByte <= 47) || (testByte >= 48 && testByte <= 57) || (testByte >= 58 && testByte <= 64) || (testByte >= 65 && testByte <= 90) || (testByte >= 91 && testByte <= 96) || (testByte >= 97 && testByte <= 122) || (testByte >= 123 && testByte <= 126);
		}

		// Token: 0x06000239 RID: 569 RVA: 0x00018A08 File Offset: 0x00016C08
		private static int DetectSuspiciousUTF8SequenceLength(byte[] SampleBytes, long currentPos)
		{
			int result = 0;
			checked
			{
				if (unchecked((long)SampleBytes.Length >= currentPos + 1L) && SampleBytes[(int)((IntPtr)currentPos)] == 194)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 129 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 141 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 143)
					{
						result = 2;
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 144 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 157)
					{
						result = 2;
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] >= 160 && SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] <= 191)
					{
						result = 2;
					}
				}
				else if (unchecked((long)SampleBytes.Length >= currentPos + 1L) && SampleBytes[(int)((IntPtr)currentPos)] == 195)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] >= 128 && SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] <= 191)
					{
						result = 2;
					}
				}
				else if (unchecked((long)SampleBytes.Length >= currentPos + 1L) && SampleBytes[(int)((IntPtr)currentPos)] == 197)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 146 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 147)
					{
						result = 2;
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 160 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 161)
					{
						result = 2;
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 184 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 189 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 190)
					{
						result = 2;
					}
				}
				else if (unchecked((long)SampleBytes.Length >= currentPos + 1L) && SampleBytes[(int)((IntPtr)currentPos)] == 198)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 146)
					{
						result = 2;
					}
				}
				else if (unchecked((long)SampleBytes.Length >= currentPos + 1L) && SampleBytes[(int)((IntPtr)currentPos)] == 203)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 134 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 156)
					{
						result = 2;
					}
				}
				else if (unchecked((long)SampleBytes.Length >= currentPos + 2L) && SampleBytes[(int)((IntPtr)currentPos)] == 226)
				{
					if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 128)
					{
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 147 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 148)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 152 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 153 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 154)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 156 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 157 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 158)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 160 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 161 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 162)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 166)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 176)
						{
							result = 3;
						}
						if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 185 || SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 186)
						{
							result = 3;
						}
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 130 && SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 172)
					{
						result = 3;
					}
					else if (SampleBytes[(int)((IntPtr)(unchecked(currentPos + 1L)))] == 132 && SampleBytes[(int)((IntPtr)(unchecked(currentPos + 2L)))] == 162)
					{
						result = 3;
					}
				}
				return result;
			}
		}

		// Token: 0x0400012F RID: 303
		private const long _defaultHeuristicSampleSize = 65536L;
	}
}

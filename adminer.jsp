<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.io.*" %>
<%@ page import="javax.naming.*" %>

<%!
    // 数据库驱动配置 - 扩展支持更多数据库
    private static final Map<String, String> DB_DRIVERS = new HashMap<String, String>() {{
        // MySQL 系列
        put("mysql", "com.mysql.cj.jdbc.Driver");
        put("mariadb", "org.mariadb.jdbc.Driver");
        put("mysql_old", "com.mysql.jdbc.Driver"); // 旧版MySQL驱动

        // Microsoft SQL Server 系列
        put("mssql", "com.microsoft.sqlserver.jdbc.SQLServerDriver");
        put("mssql_jtds", "net.sourceforge.jtds.jdbc.Driver"); // jTDS驱动

        // Oracle 系列
        put("oracle", "oracle.jdbc.driver.OracleDriver");
        put("oracle_thin", "oracle.jdbc.OracleDriver"); // 新版Oracle驱动

        // PostgreSQL 系列
        put("postgresql", "org.postgresql.Driver");

        // 国产数据库
        put("tibero", "com.tmax.tibero.jdbc.TbDriver");
        put("dm", "dm.jdbc.driver.DmDriver"); // 达梦数据库
        put("kingbase", "com.kingbase8.Driver"); // 人大金仓
        put("oscar", "com.oscar.Driver"); // 神舟通用
        put("gbase", "com.gbase.jdbc.Driver"); // 南大通用GBase
        put("highgo", "com.highgo.jdbc.Driver"); // 瀚高数据库
        put("vastbase", "com.vastdata.vastbase.jdbc.Driver"); // 海量数据库

        // 其他数据库
        put("sqlite", "org.sqlite.JDBC");
        put("h2", "org.h2.Driver");
        put("derby", "org.apache.derby.jdbc.EmbeddedDriver");
        put("hsqldb", "org.hsqldb.jdbc.JDBCDriver");
        put("db2", "com.ibm.db2.jcc.DB2Driver");
        put("sybase", "com.sybase.jdbc4.jdbc.SybDriver");
        put("informix", "com.informix.jdbc.IfxDriver");

        // 云数据库
        put("clickhouse", "ru.yandex.clickhouse.ClickHouseDriver");
        put("presto", "com.facebook.presto.jdbc.PrestoDriver");
        put("hive", "org.apache.hive.jdbc.HiveDriver");
        put("phoenix", "org.apache.phoenix.jdbc.PhoenixDriver");

        // 自定义驱动占位符
        put("custom", ""); // 用户自定义驱动
    }};

    // 数据库URL模板
    private static final Map<String, String> DB_URL_TEMPLATES = new HashMap<String, String>() {{
        // MySQL 系列
        put("mysql", "jdbc:mysql://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai");
        put("mariadb", "jdbc:mariadb://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false");
        put("mysql_old", "jdbc:mysql://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false");

        // Microsoft SQL Server 系列
        put("mssql", "jdbc:sqlserver://{host}:{port};databaseName={db};encrypt=false");
        put("mssql_jtds", "jdbc:jtds:sqlserver://{host}:{port}/{db}");

        // Oracle 系列
        put("oracle", "jdbc:oracle:thin:@{host}:{port}:{db}");
        put("oracle_thin", "jdbc:oracle:thin:@{host}:{port}:{db}");

        // PostgreSQL 系列
        put("postgresql", "jdbc:postgresql://{host}:{port}/{db}");

        // 国产数据库
        put("tibero", "jdbc:tibero:thin:@{host}:{port}:{db}");
        put("dm", "jdbc:dm://{host}:{port}/{db}");
        put("kingbase", "jdbc:kingbase8://{host}:{port}/{db}");
        put("oscar", "jdbc:oscar://{host}:{port}/{db}");
        put("gbase", "jdbc:gbase://{host}:{port}/{db}");
        put("highgo", "jdbc:highgo://{host}:{port}/{db}");
        put("vastbase", "jdbc:vastbase://{host}:{port}/{db}");

        // 其他数据库
        put("sqlite", "jdbc:sqlite:{db}"); // SQLite使用文件路径
        put("h2", "jdbc:h2:tcp://{host}:{port}/{db}");
        put("derby", "jdbc:derby://{host}:{port}/{db}");
        put("hsqldb", "jdbc:hsqldb:hsql://{host}:{port}/{db}");
        put("db2", "jdbc:db2://{host}:{port}/{db}");
        put("sybase", "jdbc:sybase:Tds:{host}:{port}/{db}");
        put("informix", "jdbc:informix-sqli://{host}:{port}/{db}:INFORMIXSERVER=ol_informix1170");

        // 云数据库
        put("clickhouse", "jdbc:clickhouse://{host}:{port}/{db}");
        put("presto", "jdbc:presto://{host}:{port}/{db}");
        put("hive", "jdbc:hive2://{host}:{port}/{db}");
        put("phoenix", "jdbc:phoenix:{host}:{port}");

        // 自定义连接
        put("custom", "{custom_url}"); // 用户自定义URL
    }};

    // 默认端口
    private static final Map<String, String> DB_DEFAULT_PORTS = new HashMap<String, String>() {{
        // MySQL 系列
        put("mysql", "3306");
        put("mariadb", "3306");
        put("mysql_old", "3306");

        // Microsoft SQL Server 系列
        put("mssql", "1433");
        put("mssql_jtds", "1433");

        // Oracle 系列
        put("oracle", "1521");
        put("oracle_thin", "1521");

        // PostgreSQL 系列
        put("postgresql", "5432");

        // 国产数据库
        put("tibero", "8629");
        put("dm", "5236");
        put("kingbase", "54321");
        put("oscar", "2003");
        put("gbase", "5258");
        put("highgo", "5866");
        put("vastbase", "5432");

        // 其他数据库
        put("sqlite", ""); // SQLite无端口
        put("h2", "9092");
        put("derby", "1527");
        put("hsqldb", "9001");
        put("db2", "50000");
        put("sybase", "5000");
        put("informix", "9088");

        // 云数据库
        put("clickhouse", "8123");
        put("presto", "8080");
        put("hive", "10000");
        put("phoenix", "2181");

        // 自定义
        put("custom", "");
    }};
    
    // 创建数据库连接 - 支持自定义驱动和URL
    public Connection createConnection(String dbType, String host, String port, String database,
                                     String username, String password, String customDriver, String customUrl) throws Exception {
        String driver;
        String url;

        // 处理自定义驱动和URL
        if ("custom".equals(dbType)) {
            if (customDriver == null || customDriver.trim().isEmpty()) {
                throw new Exception("自定义数据库类型需要指定驱动类名");
            }
            if (customUrl == null || customUrl.trim().isEmpty()) {
                throw new Exception("自定义数据库类型需要指定连接URL");
            }
            driver = customDriver.trim();
            url = customUrl.trim();
        } else {
            driver = DB_DRIVERS.get(dbType);
            if (driver == null) {
                throw new Exception("不支持的数据库类型: " + dbType);
            }

            String urlTemplate = DB_URL_TEMPLATES.get(dbType);
            if (urlTemplate == null) {
                throw new Exception("未配置数据库URL模板: " + dbType);
            }

            // 处理特殊情况
            if ("sqlite".equals(dbType)) {
                // SQLite使用文件路径作为数据库名
                url = urlTemplate.replace("{db}", database != null ? database : "");
            } else {
                // 标准URL替换
                url = urlTemplate.replace("{host}", host != null ? host : "localhost")
                                .replace("{port}", port != null ? port : DB_DEFAULT_PORTS.get(dbType))
                                .replace("{db}", database != null ? database : "");
            }
        }

        // 加载驱动
        try {
            Class.forName(driver);
        } catch (ClassNotFoundException e) {
            throw new Exception("找不到数据库驱动: " + driver + "。请确保相应的JDBC驱动包已添加到classpath中。");
        }

        // 创建连接
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (SQLException e) {
            throw new Exception("数据库连接失败: " + e.getMessage() + " (URL: " + url + ")");
        }
    }

    // 重载方法，保持向后兼容
    public Connection createConnection(String dbType, String host, String port, String database,
                                     String username, String password) throws Exception {
        return createConnection(dbType, host, port, database, username, password, null, null);
    }

    // 获取数据库类型的显示名称
    public String getDbTypeDisplayName(String dbType) {
        Map<String, String> displayNames = new HashMap<String, String>() {{
            put("mysql", "MySQL");
            put("mariadb", "MariaDB");
            put("mysql_old", "MySQL (旧驱动)");
            put("mssql", "SQL Server");
            put("mssql_jtds", "SQL Server (jTDS)");
            put("oracle", "Oracle");
            put("oracle_thin", "Oracle (新驱动)");
            put("postgresql", "PostgreSQL");
            put("tibero", "Tibero");
            put("dm", "达梦数据库");
            put("kingbase", "人大金仓");
            put("oscar", "神舟通用");
            put("gbase", "南大通用GBase");
            put("highgo", "瀚高数据库");
            put("vastbase", "海量数据库");
            put("sqlite", "SQLite");
            put("h2", "H2 Database");
            put("derby", "Apache Derby");
            put("hsqldb", "HSQLDB");
            put("db2", "IBM DB2");
            put("sybase", "Sybase");
            put("informix", "IBM Informix");
            put("clickhouse", "ClickHouse");
            put("presto", "Presto");
            put("hive", "Apache Hive");
            put("phoenix", "Apache Phoenix");
            put("custom", "自定义数据库");
        }};
        return displayNames.getOrDefault(dbType, dbType);
    }
    
    // HTML编码
    public String htmlEncode(String str) {
        if (str == null) return "";
        return str.replace("&", "&amp;")
                 .replace("<", "&lt;")
                 .replace(">", "&gt;")
                 .replace("\"", "&quot;")
                 .replace("'", "&#39;");
    }
    
    // 获取数据库列表 - 支持更多数据库类型
    public List<String> getDatabases(Connection conn, String dbType) throws SQLException {
        List<String> databases = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
            case "mysql_old":
                sql = "SHOW DATABASES";
                break;
            case "mssql":
            case "mssql_jtds":
                sql = "SELECT name FROM sys.databases WHERE database_id > 4 ORDER BY name";
                break;
            case "oracle":
            case "oracle_thin":
            case "tibero":
                sql = "SELECT username FROM all_users ORDER BY username";
                break;
            case "postgresql":
            case "vastbase":
            case "highgo":
                sql = "SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname";
                break;
            case "dm":
                sql = "SELECT name FROM v$database";
                break;
            case "kingbase":
                sql = "SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname";
                break;
            case "oscar":
                sql = "SELECT schema_name FROM information_schema.schemata ORDER BY schema_name";
                break;
            case "gbase":
                sql = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys') ORDER BY schema_name";
                break;
            case "sqlite":
                // SQLite 是单文件数据库，返回当前数据库
                databases.add("main");
                return databases;
            case "h2":
                sql = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('INFORMATION_SCHEMA') ORDER BY schema_name";
                break;
            case "derby":
                sql = "SELECT schemaname FROM sys.sysschemas WHERE schemaname NOT LIKE 'SYS%' ORDER BY schemaname";
                break;
            case "hsqldb":
                sql = "SELECT schema_name FROM information_schema.schemata ORDER BY schema_name";
                break;
            case "db2":
                sql = "SELECT schemaname FROM syscat.schemata WHERE ownertype = 'U' ORDER BY schemaname";
                break;
            case "sybase":
                sql = "SELECT name FROM sysdatabases ORDER BY name";
                break;
            case "informix":
                sql = "SELECT name FROM sysdatabases ORDER BY name";
                break;
            case "clickhouse":
                sql = "SHOW DATABASES";
                break;
            case "hive":
                sql = "SHOW DATABASES";
                break;
            case "phoenix":
                sql = "SELECT DISTINCT table_schem FROM system.catalog ORDER BY table_schem";
                break;
            default:
                // 对于未知数据库类型，尝试使用标准的 information_schema
                try {
                    sql = "SELECT schema_name FROM information_schema.schemata ORDER BY schema_name";
                } catch (Exception e) {
                    // 如果失败，返回空列表
                    return databases;
                }
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    databases.add(rs.getString(1));
                }
            }
        }
        
        return databases;
    }
    
    // 获取表列表 - 支持更多数据库类型
    public List<String> getTables(Connection conn, String dbType, String database) throws SQLException {
        List<String> tables = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
            case "mysql_old":
                sql = "SHOW TABLES";
                break;
            case "mssql":
            case "mssql_jtds":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' ORDER BY table_name";
                break;
            case "oracle":
            case "oracle_thin":
            case "tibero":
                sql = "SELECT table_name FROM user_tables ORDER BY table_name";
                break;
            case "postgresql":
            case "vastbase":
            case "highgo":
            case "kingbase":
                sql = "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename";
                break;
            case "dm":
                sql = "SELECT table_name FROM user_tables ORDER BY table_name";
                break;
            case "oscar":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' AND table_schema = 'public' ORDER BY table_name";
                break;
            case "gbase":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' AND table_schema = '" + database + "' ORDER BY table_name";
                break;
            case "sqlite":
                sql = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name";
                break;
            case "h2":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'TABLE' AND table_schema = 'PUBLIC' ORDER BY table_name";
                break;
            case "derby":
                sql = "SELECT tablename FROM sys.systables WHERE tabletype = 'T' ORDER BY tablename";
                break;
            case "hsqldb":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'TABLE' ORDER BY table_name";
                break;
            case "db2":
                sql = "SELECT tabname FROM syscat.tables WHERE tabschema = CURRENT SCHEMA AND type = 'T' ORDER BY tabname";
                break;
            case "sybase":
                sql = "SELECT name FROM sysobjects WHERE type = 'U' ORDER BY name";
                break;
            case "informix":
                sql = "SELECT tabname FROM systables WHERE tabtype = 'T' ORDER BY tabname";
                break;
            case "clickhouse":
                sql = "SHOW TABLES";
                break;
            case "hive":
                sql = "SHOW TABLES";
                break;
            case "phoenix":
                sql = "SELECT table_name FROM system.catalog WHERE table_schem = '" + database + "' AND table_type = 'TABLE' ORDER BY table_name";
                break;
            default:
                // 对于未知数据库类型，尝试使用标准的 information_schema
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' ORDER BY table_name";
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    tables.add(rs.getString(1));
                }
            }
        }
        
        return tables;
    }
    
    // 表结构信息类
    public class ColumnInfo {
        public String name;
        public String type;
        public String nullable;
        public String defaultValue;
        public String comment;
        public boolean isPrimaryKey;
        public boolean isAutoIncrement;
        
        public ColumnInfo(String name, String type, String nullable, String defaultValue, String comment, boolean isPrimaryKey, boolean isAutoIncrement) {
            this.name = name;
            this.type = type;
            this.nullable = nullable;
            this.defaultValue = defaultValue;
            this.comment = comment;
            this.isPrimaryKey = isPrimaryKey;
            this.isAutoIncrement = isAutoIncrement;
        }
    }
    
    // 获取表结构
    public List<ColumnInfo> getTableStructure(Connection conn, String dbType, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();
        String sql = "";
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                sql = "SHOW FULL COLUMNS FROM " + tableName;
                break;
            case "mssql":
                sql = "SELECT c.COLUMN_NAME, c.DATA_TYPE + CASE WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN '(' + CAST(c.CHARACTER_MAXIMUM_LENGTH AS VARCHAR) + ')' ELSE '' END as DATA_TYPE, " +
                      "c.IS_NULLABLE, c.COLUMN_DEFAULT, '' as COMMENT, " +
                      "CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY, " +
                      "CASE WHEN c.EXTRA LIKE '%identity%' THEN 1 ELSE 0 END as IS_AUTO " +
                      "FROM INFORMATION_SCHEMA.COLUMNS c " +
                      "LEFT JOIN (SELECT ku.COLUMN_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc " +
                      "JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME " +
                      "WHERE tc.TABLE_NAME = '" + tableName + "' AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY') pk ON c.COLUMN_NAME = pk.COLUMN_NAME " +
                      "WHERE c.TABLE_NAME = '" + tableName + "' ORDER BY c.ORDINAL_POSITION";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT c.COLUMN_NAME, c.DATA_TYPE || CASE WHEN c.DATA_LENGTH IS NOT NULL AND c.DATA_TYPE IN ('VARCHAR2','CHAR') THEN '(' || c.DATA_LENGTH || ')' ELSE '' END as DATA_TYPE, " +
                      "c.NULLABLE, c.DATA_DEFAULT, com.COMMENTS, " +
                      "CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY, 0 as IS_AUTO " +
                      "FROM user_tab_columns c " +
                      "LEFT JOIN user_col_comments com ON c.TABLE_NAME = com.TABLE_NAME AND c.COLUMN_NAME = com.COLUMN_NAME " +
                      "LEFT JOIN (SELECT cc.COLUMN_NAME FROM user_constraints tc JOIN user_cons_columns cc ON tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME " +
                      "WHERE tc.TABLE_NAME = '" + tableName + "' AND tc.CONSTRAINT_TYPE = 'P') pk ON c.COLUMN_NAME = pk.COLUMN_NAME " +
                      "WHERE c.TABLE_NAME = '" + tableName + "' ORDER BY c.COLUMN_ID";
                break;
            case "postgresql":
                sql = "SELECT c.column_name, c.data_type || CASE WHEN c.character_maximum_length IS NOT NULL THEN '(' || c.character_maximum_length || ')' ELSE '' END as data_type, " +
                      "c.is_nullable, c.column_default, '' as comment, " +
                      "CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary, " +
                      "CASE WHEN c.column_default LIKE 'nextval%' THEN true ELSE false END as is_auto " +
                      "FROM information_schema.columns c " +
                      "LEFT JOIN (SELECT ku.column_name FROM information_schema.table_constraints tc " +
                      "JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name " +
                      "WHERE tc.table_name = '" + tableName + "' AND tc.constraint_type = 'PRIMARY KEY') pk ON c.column_name = pk.column_name " +
                      "WHERE c.table_name = '" + tableName + "' ORDER BY c.ordinal_position";
                break;
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    if ("mysql".equals(dbType)) {
                        columns.add(new ColumnInfo(
                            rs.getString("Field"),
                            rs.getString("Type"),
                            rs.getString("Null"),
                            rs.getString("Default"),
                            rs.getString("Comment"),
                            "PRI".equals(rs.getString("Key")),
                            "auto_increment".equals(rs.getString("Extra"))
                        ));
                    } else {
                        columns.add(new ColumnInfo(
                            rs.getString(1),
                            rs.getString(2),
                            rs.getString(3),
                            rs.getString(4),
                            rs.getString(5),
                            rs.getBoolean(6),
                            rs.getBoolean(7)
                        ));
                    }
                }
            }
        }
        
        return columns;
    }
    
    // 获取表数据
    public String getTableData(Connection conn, String tableName, int page, int pageSize, String dbType) throws SQLException {
        StringBuilder result = new StringBuilder();
        int offset = (page - 1) * pageSize;
        
        // 先获取总行数
        String countSql = "SELECT COUNT(*) FROM " + tableName;
        int totalRows = 0;
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(countSql)) {
            if (rs.next()) {
                totalRows = rs.getInt(1);
            }
        }
        
        // 获取数据 - 根据数据库类型使用不同的分页语法
        String dataSql = "";
        switch (dbType.toLowerCase()) {
            case "mysql":
            case "postgresql":
                dataSql = "SELECT * FROM " + tableName + " LIMIT " + pageSize + " OFFSET " + offset;
                break;
            case "mssql":
                dataSql = "SELECT * FROM " + tableName + " ORDER BY (SELECT NULL) OFFSET " + offset + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                break;
            case "oracle":
            case "tibero":
                int endRow = offset + pageSize;
                dataSql = "SELECT * FROM (SELECT ROWNUM rn, t.* FROM (SELECT * FROM " + tableName + ") t WHERE ROWNUM <= " + endRow + ") WHERE rn > " + offset;
                break;
            default:
                dataSql = "SELECT * FROM " + tableName;
        }
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(dataSql)) {
            
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            
            result.append("<div class='table-data'>");
            result.append("<div class='data-header'>");
            result.append("<h4>表数据 - ").append(htmlEncode(tableName)).append("</h4>");
            result.append("<p class='data-info'>总计 ").append(totalRows).append(" 行，显示第 ").append(page).append(" 页</p>");
            result.append("</div>");
            
            result.append("<table class='data-table'>");
            
            // 表头
            result.append("<thead><tr>");
            result.append("<th>操作</th>");
            for (int i = 1; i <= columnCount; i++) {
                result.append("<th>").append(htmlEncode(meta.getColumnName(i))).append("</th>");
            }
            result.append("</tr></thead>");
            
            // 数据行
            result.append("<tbody>");
            int rowNum = 0;
            while (rs.next()) {
                // 构建WHERE子句用于编辑和删除
                StringBuilder whereClause = new StringBuilder();
                boolean firstWhere = true;
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = meta.getColumnName(i);
                    String value = rs.getString(i);
                    if (!firstWhere) whereClause.append(" AND ");
                    if (value != null) {
                        whereClause.append(columnName).append("='").append(value.replace("'", "''")).append("'");
                    } else {
                        whereClause.append(columnName).append(" IS NULL");
                    }
                    firstWhere = false;
                }
                
                result.append("<tr data-row='").append(rowNum).append("' data-where='").append(htmlEncode(whereClause.toString())).append("'>");
                result.append("<td class='actions'>");
                result.append("<button class='btn-small btn-edit' data-table='").append(htmlEncode(tableName)).append("' onclick='editRow(").append(rowNum).append(")'>编辑</button>");
                result.append("<button class='btn-small btn-delete' data-table='").append(htmlEncode(tableName)).append("' onclick='deleteRow(").append(rowNum).append(")'>删除</button>");
                result.append("</td>");
                
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    String columnName = meta.getColumnName(i);
                    result.append("<td data-column='").append(htmlEncode(columnName)).append("' data-value='").append(value != null ? htmlEncode(value) : "").append("'>");
                    result.append(value != null ? htmlEncode(value) : "<em>NULL</em>");
                    result.append("</td>");
                }
                result.append("</tr>");
                rowNum++;
            }
            result.append("</tbody></table>");
            
            // 分页控制
            int totalPages = (int) Math.ceil((double) totalRows / pageSize);
            if (totalPages > 1) {
                result.append("<div class='pagination'>");
                if (page > 1) {
                    result.append("<a href='?selected_table=").append(tableName).append("&page=").append(page - 1).append("&view=data' class='btn btn-small'>上一页</a>");
                }
                result.append("<span class='page-info'>第 ").append(page).append(" / ").append(totalPages).append(" 页</span>");
                if (page < totalPages) {
                    result.append("<a href='?selected_table=").append(tableName).append("&page=").append(page + 1).append("&view=data' class='btn btn-small'>下一页</a>");
                }
                result.append("</div>");
            }
            
            result.append("</div>");
        }
        
        return result.toString();
    }
    
    // 获取触发器列表
    public List<String> getTriggers(Connection conn, String dbType, String database) throws SQLException {
        List<String> triggers = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
                sql = "SHOW TRIGGERS";
                break;
            case "mssql":
                sql = "SELECT name FROM sys.triggers WHERE parent_class = 1";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT trigger_name FROM user_triggers ORDER BY trigger_name";
                break;
            case "postgresql":
                sql = "SELECT trigger_name FROM information_schema.triggers WHERE trigger_schema = 'public' ORDER BY trigger_name";
                break;
        }

        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    triggers.add(rs.getString(1));
                }
            }
        }

        return triggers;
    }

    // 获取视图列表
    public List<String> getViews(Connection conn, String dbType, String database) throws SQLException {
        List<String> views = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
                sql = "SHOW FULL TABLES WHERE Table_type = 'VIEW'";
                break;
            case "mssql":
                sql = "SELECT table_name FROM information_schema.views ORDER BY table_name";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT view_name FROM user_views ORDER BY view_name";
                break;
            case "postgresql":
                sql = "SELECT viewname FROM pg_views WHERE schemaname = 'public' ORDER BY viewname";
                break;
        }

        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    views.add(rs.getString(1));
                }
            }
        }

        return views;
    }

    // 获取存储过程列表
    public List<String> getProcedures(Connection conn, String dbType, String database) throws SQLException {
        List<String> procedures = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
                sql = "SHOW PROCEDURE STATUS WHERE Db = '" + database + "'";
                break;
            case "mssql":
                sql = "SELECT name FROM sys.procedures ORDER BY name";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT object_name FROM user_procedures WHERE object_type = 'PROCEDURE' ORDER BY object_name";
                break;
            case "postgresql":
                sql = "SELECT proname FROM pg_proc WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') ORDER BY proname";
                break;
        }

        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    procedures.add(rs.getString(1));
                }
            }
        }

        return procedures;
    }

    // 触发器详细信息类
    public class TriggerInfo {
        public String name;
        public String tableName;
        public String event;
        public String timing;
        public String definition;
        public String definer;

        public TriggerInfo(String name, String tableName, String event, String timing, String definition, String definer) {
            this.name = name;
            this.tableName = tableName;
            this.event = event;
            this.timing = timing;
            this.definition = definition;
            this.definer = definer;
        }
    }

    // 获取触发器详细信息
    public List<TriggerInfo> getTriggerDetails(Connection conn, String dbType, String database) throws SQLException {
        List<TriggerInfo> triggerDetails = new ArrayList<>();
        String sql = "";

        switch (dbType.toLowerCase()) {
            case "mysql":
            case "mariadb":
                sql = "SHOW TRIGGERS";
                break;
            case "mssql":
                sql = "SELECT t.name, OBJECT_NAME(t.parent_id) as table_name, " +
                      "CASE WHEN EXISTS(SELECT 1 FROM sys.trigger_events te WHERE te.object_id = t.object_id AND te.type = 1) THEN 'INSERT' " +
                      "WHEN EXISTS(SELECT 1 FROM sys.trigger_events te WHERE te.object_id = t.object_id AND te.type = 2) THEN 'UPDATE' " +
                      "WHEN EXISTS(SELECT 1 FROM sys.trigger_events te WHERE te.object_id = t.object_id AND te.type = 3) THEN 'DELETE' END as event, " +
                      "CASE WHEN t.is_instead_of_trigger = 1 THEN 'INSTEAD OF' ELSE 'AFTER' END as timing, " +
                      "OBJECT_DEFINITION(t.object_id) as definition, '' as definer " +
                      "FROM sys.triggers t WHERE t.parent_class = 1";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT trigger_name, table_name, triggering_event, trigger_type, trigger_body, owner " +
                      "FROM user_triggers ORDER BY trigger_name";
                break;
            case "postgresql":
                sql = "SELECT t.trigger_name, t.event_object_table, t.event_manipulation, t.action_timing, " +
                      "p.prosrc as definition, '' as definer " +
                      "FROM information_schema.triggers t " +
                      "LEFT JOIN pg_proc p ON p.proname = t.trigger_name " +
                      "WHERE t.trigger_schema = 'public' ORDER BY t.trigger_name";
                break;
        }

        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    if ("mysql".equals(dbType) || "mariadb".equals(dbType)) {
                        triggerDetails.add(new TriggerInfo(
                            rs.getString("Trigger"),
                            rs.getString("Table"),
                            rs.getString("Event"),
                            rs.getString("Timing"),
                            rs.getString("Statement"),
                            rs.getString("Definer")
                        ));
                    } else {
                        triggerDetails.add(new TriggerInfo(
                            rs.getString(1),
                            rs.getString(2),
                            rs.getString(3),
                            rs.getString(4),
                            rs.getString(5),
                            rs.getString(6)
                        ));
                    }
                }
            }
        }

        return triggerDetails;
    }
%>

<%
    String action = request.getParameter("action");
    String dbType = request.getParameter("db_type");
    String host = request.getParameter("host");
    String port = request.getParameter("port");
    String database = request.getParameter("database");
    String username = request.getParameter("username");
    String password = request.getParameter("password");
    String selectedDatabase = request.getParameter("selected_db");
    String selectedTable = request.getParameter("selected_table");
    
    Connection conn = null;
    String errorMessage = "";
    boolean isConnected = false;
    
    List<String> databases = new ArrayList<>();
    List<String> tables = new ArrayList<>();
    List<String> views = new ArrayList<>();
    List<String> procedures = new ArrayList<>();
    List<String> triggers = new ArrayList<>();
    List<TriggerInfo> triggerDetails = new ArrayList<>();
    List<ColumnInfo> tableStructure = new ArrayList<>();
    String queryResult = "";
    String sqlQuery = request.getParameter("sql_query");
    String view = request.getParameter("view"); // structure, data, triggers, views, procedures
    String objectType = request.getParameter("object_type"); // table, view, procedure, trigger
    int currentPage = 1;
    try {
        if (request.getParameter("page") != null) {
            currentPage = Integer.parseInt(request.getParameter("page"));
        }
    } catch (NumberFormatException e) {
        currentPage = 1;
    }
    String tableDataHtml = "";
    
    // 处理登录连接
    if ("connect".equals(action) && dbType != null && username != null) {
        try {
            // 获取自定义驱动和URL参数
            String customDriver = request.getParameter("custom_driver");
            String customUrl = request.getParameter("custom_url");

            // 处理默认值
            if (port == null || port.trim().isEmpty()) {
                port = DB_DEFAULT_PORTS.get(dbType);
            }
            if (database == null) database = "";
            if (password == null) password = "";
            if (host == null) host = "localhost";

            // 对于SQLite，host参数不是必需的
            if ("sqlite".equals(dbType)) {
                host = "";
                port = "";
            }

            // 创建连接
            conn = createConnection(dbType, host, port, database, username, password, customDriver, customUrl);
            isConnected = true;

            // 将连接信息存储到session中
            session.setAttribute("db_connection", conn);
            session.setAttribute("db_type", dbType);
            session.setAttribute("db_host", host);
            session.setAttribute("db_port", port);
            session.setAttribute("db_database", database);
            session.setAttribute("db_username", username);
            session.setAttribute("custom_driver", customDriver);
            session.setAttribute("custom_url", customUrl);

        } catch (Exception e) {
            errorMessage = "连接失败: " + e.getMessage();
        }
    } else {
        // 尝试从session获取连接
        conn = (Connection) session.getAttribute("db_connection");
        if (conn != null && !conn.isClosed()) {
            isConnected = true;
            dbType = (String) session.getAttribute("db_type");
            host = (String) session.getAttribute("db_host");
            port = (String) session.getAttribute("db_port");
            database = (String) session.getAttribute("db_database");
            username = (String) session.getAttribute("db_username");
        }
    }
    
    // 如果已连接，获取数据库和表列表
    if (isConnected && conn != null) {
        try {
            databases = getDatabases(conn, dbType);
            
            // 如果选择了数据库，获取表列表、视图、存储过程和触发器
            if (selectedDatabase != null && !selectedDatabase.isEmpty()) {
                // 对于某些数据库类型，需要切换到指定数据库
                if ("mysql".equals(dbType) || "mariadb".equals(dbType)) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.execute("USE " + selectedDatabase);
                    }
                }
                tables = getTables(conn, dbType, selectedDatabase);
                views = getViews(conn, dbType, selectedDatabase);
                procedures = getProcedures(conn, dbType, selectedDatabase);
                triggers = getTriggers(conn, dbType, selectedDatabase);
                triggerDetails = getTriggerDetails(conn, dbType, selectedDatabase);
            } else if (database != null && !database.isEmpty()) {
                tables = getTables(conn, dbType, database);
                views = getViews(conn, dbType, database);
                procedures = getProcedures(conn, dbType, database);
                triggers = getTriggers(conn, dbType, database);
                triggerDetails = getTriggerDetails(conn, dbType, database);
            }
            
            // 如果选择了表，获取表结构和数据
            if (selectedTable != null && !selectedTable.isEmpty()) {
                tableStructure = getTableStructure(conn, dbType, selectedTable);
                
                // 默认显示数据，除非指定了其他视图
                if (view == null || "data".equals(view)) {
                    tableDataHtml = getTableData(conn, selectedTable, currentPage, 20, dbType);
                }
            }
        } catch (SQLException e) {
            errorMessage = "获取数据库信息失败: " + e.getMessage();
        }
    }
    
    // 处理数据编辑
    if ("edit_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            String whereClause = request.getParameter("where_clause");
            
            if (tableName != null && whereClause != null) {
                StringBuilder updateSql = new StringBuilder("UPDATE " + tableName + " SET ");
                boolean first = true;
                
                // 获取表结构以知道有哪些列
                List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                for (ColumnInfo col : columns) {
                    String newValue = request.getParameter("col_" + col.name);
                    if (newValue != null) {
                        if (!first) updateSql.append(", ");
                        updateSql.append(col.name).append(" = ?");
                        first = false;
                    }
                }
                
                updateSql.append(" WHERE ").append(whereClause);
                
                try (PreparedStatement pstmt = conn.prepareStatement(updateSql.toString())) {
                    int paramIndex = 1;
                    for (ColumnInfo col : columns) {
                        String newValue = request.getParameter("col_" + col.name);
                        if (newValue != null) {
                            if ("NULL".equals(newValue) || newValue.trim().isEmpty()) {
                                pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                            } else {
                                pstmt.setString(paramIndex, newValue);
                            }
                            paramIndex++;
                        }
                    }
                    
                    int affected = pstmt.executeUpdate();
                    queryResult = "<div class='query-result'><div class='success'>数据更新成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据更新失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理删除行
    if ("delete_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            String whereClause = request.getParameter("where_clause");
            
            if (tableName != null && whereClause != null) {
                String deleteSql = "DELETE FROM " + tableName + " WHERE " + whereClause;
                try (Statement stmt = conn.createStatement()) {
                    int affected = stmt.executeUpdate(deleteSql);
                    queryResult = "<div class='query-result'><div class='success'>数据删除成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据删除失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理新增行
    if ("insert_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            
            if (tableName != null) {
                List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                StringBuilder insertSql = new StringBuilder("INSERT INTO " + tableName + " (");
                StringBuilder valuesSql = new StringBuilder(" VALUES (");
                
                boolean first = true;
                for (ColumnInfo col : columns) {
                    if (!col.isAutoIncrement) { // 跳过自增列
                        String newValue = request.getParameter("new_" + col.name);
                        if (newValue != null) {
                            if (!first) {
                                insertSql.append(", ");
                                valuesSql.append(", ");
                            }
                            insertSql.append(col.name);
                            valuesSql.append("?");
                            first = false;
                        }
                    }
                }
                
                insertSql.append(")").append(valuesSql.toString()).append(")");
                
                try (PreparedStatement pstmt = conn.prepareStatement(insertSql.toString())) {
                    int paramIndex = 1;
                    for (ColumnInfo col : columns) {
                        if (!col.isAutoIncrement) {
                            String newValue = request.getParameter("new_" + col.name);
                            if (newValue != null) {
                                if ("NULL".equals(newValue) || newValue.trim().isEmpty()) {
                                    pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                                } else {
                                    pstmt.setString(paramIndex, newValue);
                                }
                                paramIndex++;
                            }
                        }
                    }
                    
                    int affected = pstmt.executeUpdate();
                    queryResult = "<div class='query-result'><div class='success'>数据插入成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据插入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理表管理操作
    if ("table_operation".equals(action) && isConnected && conn != null) {
        try {
            String operation = request.getParameter("operation");
            String tableName = request.getParameter("table_name");
            
            if (tableName != null && operation != null) {
                String sql = "";
                switch (operation) {
                    case "truncate":
                        sql = "DELETE FROM " + tableName; // 使用DELETE而不是TRUNCATE以保证兼容性
                        break;
                    case "drop":
                        sql = "DROP TABLE " + tableName;
                        break;
                }
                
                if (!sql.isEmpty()) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.executeUpdate(sql);
                        queryResult = "<div class='query-result'><div class='success'>表操作执行成功！</div></div>";
                        
                        // 如果是删除表，清除选中状态
                        if ("drop".equals(operation)) {
                            selectedTable = null;
                        }
                    }
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>表操作失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理数据导出
    if ("export".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("selected_table");
            String exportFormat = request.getParameter("export_format");
            boolean exportStructure = "1".equals(request.getParameter("export_structure"));
            boolean exportData = "1".equals(request.getParameter("export_data"));
            
            if (tableName != null && exportFormat != null) {
                StringBuilder exportResult = new StringBuilder();
                
                if ("sql".equals(exportFormat)) {
                    // SQL导出
                    if (exportStructure) {
                        // 导出表结构（简化版）
                        exportResult.append("-- 表结构: ").append(tableName).append("\n");
                        exportResult.append("-- 注意：具体建表语句需要根据源数据库类型调整\n\n");
                    }
                    
                    if (exportData) {
                        // 导出数据
                        String dataSql = "SELECT * FROM " + tableName;
                        try (Statement stmt = conn.createStatement();
                             ResultSet rs = stmt.executeQuery(dataSql)) {
                            
                            ResultSetMetaData meta = rs.getMetaData();
                            int columnCount = meta.getColumnCount();
                            
                            while (rs.next()) {
                                exportResult.append("INSERT INTO ").append(tableName).append(" (");
                                for (int i = 1; i <= columnCount; i++) {
                                    if (i > 1) exportResult.append(", ");
                                    exportResult.append(meta.getColumnName(i));
                                }
                                exportResult.append(") VALUES (");
                                
                                for (int i = 1; i <= columnCount; i++) {
                                    if (i > 1) exportResult.append(", ");
                                    String value = rs.getString(i);
                                    if (value == null) {
                                        exportResult.append("NULL");
                                    } else {
                                        exportResult.append("'").append(value.replace("'", "''")).append("'");
                                    }
                                }
                                exportResult.append(");\n");
                            }
                        }
                    }
                } else if ("csv".equals(exportFormat)) {
                    // CSV导出
                    String dataSql = "SELECT * FROM " + tableName;
                    try (Statement stmt = conn.createStatement();
                         ResultSet rs = stmt.executeQuery(dataSql)) {
                        
                        ResultSetMetaData meta = rs.getMetaData();
                        int columnCount = meta.getColumnCount();
                        
                        // CSV标题行
                        for (int i = 1; i <= columnCount; i++) {
                            if (i > 1) exportResult.append(",");
                            exportResult.append("\"").append(meta.getColumnName(i)).append("\"");
                        }
                        exportResult.append("\n");
                        
                        // CSV数据行
                        while (rs.next()) {
                            for (int i = 1; i <= columnCount; i++) {
                                if (i > 1) exportResult.append(",");
                                String value = rs.getString(i);
                                if (value != null) {
                                    exportResult.append("\"").append(value.replace("\"", "\"\"")).append("\"");
                                }
                            }
                            exportResult.append("\n");
                        }
                    }
                }
                
                // 设置响应头并输出文件
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + tableName + "." + exportFormat);
                response.getWriter().write(exportResult.toString());
                response.getWriter().flush();
                return; // 直接返回，不渲染页面
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>导出失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理SQL导入
    if ("import_sql".equals(action) && isConnected && conn != null) {
        try {
            String importSql = request.getParameter("import_sql");
            if (importSql != null && !importSql.trim().isEmpty()) {
                // 分割多个SQL语句
                String[] sqlStatements = importSql.split(";");
                int successCount = 0;
                int totalCount = 0;
                
                for (String sql : sqlStatements) {
                    sql = sql.trim();
                    if (!sql.isEmpty()) {
                        totalCount++;
                        try (Statement stmt = conn.createStatement()) {
                            stmt.executeUpdate(sql);
                            successCount++;
                        } catch (SQLException e) {
                            // 记录错误但继续执行其他语句
                            queryResult += "<div class='query-result'><div class='error'>SQL语句执行失败: " + htmlEncode(sql) + "<br>错误: " + htmlEncode(e.getMessage()) + "</div></div>";
                        }
                    }
                }
                
                queryResult += "<div class='query-result'><div class='success'>SQL导入完成！成功执行 " + successCount + " / " + totalCount + " 条语句</div></div>";
            }
        } catch (Exception e) {
            queryResult = "<div class='query-result'><div class='error'>SQL导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理CSV导入
    if ("import_csv".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("selected_table");
            String csvData = request.getParameter("csv_data");
            boolean hasHeader = "1".equals(request.getParameter("has_header"));
            
            if (tableName != null && csvData != null && !csvData.trim().isEmpty()) {
                String[] lines = csvData.split("\n");
                if (lines.length > 0) {
                    // 获取表结构
                    List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                    
                    int startRow = hasHeader ? 1 : 0;
                    int successCount = 0;
                    int totalCount = lines.length - startRow;
                    
                    for (int i = startRow; i < lines.length; i++) {
                        String line = lines[i].trim();
                        if (!line.isEmpty()) {
                            // 简单的CSV解析（不处理复杂的引号情况）
                            String[] values = line.split(",");
                            
                            // 构建INSERT语句
                            StringBuilder insertSql = new StringBuilder("INSERT INTO " + tableName + " (");
                            StringBuilder valuesSql = new StringBuilder(" VALUES (");
                            
                            boolean first = true;
                            for (int j = 0; j < Math.min(values.length, columns.size()); j++) {
                                ColumnInfo col = columns.get(j);
                                if (!col.isAutoIncrement) {
                                    if (!first) {
                                        insertSql.append(", ");
                                        valuesSql.append(", ");
                                    }
                                    insertSql.append(col.name);
                                    valuesSql.append("?");
                                    first = false;
                                }
                            }
                            
                            insertSql.append(")").append(valuesSql.toString()).append(")");
                            
                            try (PreparedStatement pstmt = conn.prepareStatement(insertSql.toString())) {
                                int paramIndex = 1;
                                for (int j = 0; j < Math.min(values.length, columns.size()); j++) {
                                    ColumnInfo col = columns.get(j);
                                    if (!col.isAutoIncrement) {
                                        String value = values[j].trim();
                                        // 移除可能的引号
                                        if (value.startsWith("\"") && value.endsWith("\"")) {
                                            value = value.substring(1, value.length() - 1);
                                        }
                                        
                                        if ("NULL".equalsIgnoreCase(value) || value.isEmpty()) {
                                            pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                                        } else {
                                            pstmt.setString(paramIndex, value);
                                        }
                                        paramIndex++;
                                    }
                                }
                                
                                pstmt.executeUpdate();
                                successCount++;
                            } catch (SQLException e) {
                                // 记录错误但继续处理其他行
                                queryResult += "<div class='query-result'><div class='error'>第 " + (i + 1) + " 行导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
                            }
                        }
                    }
                    
                    queryResult += "<div class='query-result'><div class='success'>CSV导入完成！成功导入 " + successCount + " / " + totalCount + " 行数据</div></div>";
                }
            }
        } catch (Exception e) {
            queryResult = "<div class='query-result'><div class='error'>CSV导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理SQL查询
    if ("query".equals(action) && sqlQuery != null && !sqlQuery.trim().isEmpty() && isConnected && conn != null) {
        try {
            // 如果选择了数据库，先切换到该数据库
            if (selectedDatabase != null && !selectedDatabase.isEmpty() && "mysql".equals(dbType)) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("USE " + selectedDatabase);
                }
            }
            
            StringBuilder result = new StringBuilder();
            String trimmedQuery = sqlQuery.trim().toLowerCase();
            
            // 判断是否是查询语句
            if (trimmedQuery.startsWith("select") || trimmedQuery.startsWith("show") || 
                trimmedQuery.startsWith("describe") || trimmedQuery.startsWith("desc")) {
                // 执行查询
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(sqlQuery)) {
                    
                    ResultSetMetaData meta = rs.getMetaData();
                    int columnCount = meta.getColumnCount();
                    
                    result.append("<div class='query-result'>");
                    result.append("<h4>查询结果:</h4>");
                    result.append("<table class='result-table'>");
                    
                    // 表头
                    result.append("<thead><tr>");
                    for (int i = 1; i <= columnCount; i++) {
                        result.append("<th>").append(htmlEncode(meta.getColumnName(i))).append("</th>");
                    }
                    result.append("</tr></thead>");
                    
                    // 数据行
                    result.append("<tbody>");
                    int rowCount = 0;
                    while (rs.next() && rowCount < 1000) { // 限制最多显示1000行
                        result.append("<tr>");
                        for (int i = 1; i <= columnCount; i++) {
                            String value = rs.getString(i);
                            result.append("<td>").append(value != null ? htmlEncode(value) : "<em>NULL</em>").append("</td>");
                        }
                        result.append("</tr>");
                        rowCount++;
                    }
                    result.append("</tbody></table>");
                    
                    if (rowCount >= 1000) {
                        result.append("<p class='warning'>注意: 结果已限制在1000行以内</p>");
                    }
                    
                    result.append("<p class='info'>共返回 ").append(rowCount).append(" 行数据</p>");
                    result.append("</div>");
                }
            } else {
                // 执行非查询语句（INSERT, UPDATE, DELETE等）
                try (Statement stmt = conn.createStatement()) {
                    int affected = stmt.executeUpdate(sqlQuery);
                    result.append("<div class='query-result'>");
                    result.append("<h4>执行结果:</h4>");
                    result.append("<p class='success'>SQL语句执行成功！受影响的行数: ").append(affected).append("</p>");
                    result.append("</div>");
                }
            }
            
            queryResult = result.toString();
            
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>SQL执行错误: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理断开连接
    if ("disconnect".equals(action)) {
        if (conn != null) {
            try { conn.close(); } catch (Exception e) {}
        }
        session.invalidate();
        isConnected = false;
        conn = null;
    }
%>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具 - JSP Adminer</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: Verdana, Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 0;
            background: #fff;
            color: #000;
        }

        /* Adminer风格的头部 */
        #header {
            background: #f5f5f5;
            border-bottom: 1px solid #ccc;
            padding: 5px 10px;
            font-size: 12px;
        }

        #header h1 {
            margin: 0;
            font-size: 16px;
            color: #333;
            display: inline;
        }

        #header .logout {
            float: right;
        }

        /* Adminer风格的主布局 */
        #content {
            display: table;
            width: 100%;
            height: calc(100vh - 50px);
        }

        /* 左侧导航栏 */
        #menu {
            display: table-cell;
            width: 200px;
            background: #f8f8f8;
            border-right: 1px solid #ccc;
            vertical-align: top;
            padding: 10px;
        }

        /* 主内容区域 */
        #main {
            display: table-cell;
            vertical-align: top;
            padding: 10px;
        }
        
        /* Adminer风格的表单 */
        .login-form {
            background: #fff;
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px auto;
            max-width: 400px;
        }

        .login-form h2 {
            margin: 0 0 15px 0;
            font-size: 14px;
            color: #333;
        }

        table.layout {
            width: 100%;
            border-collapse: collapse;
        }

        table.layout td {
            padding: 3px 5px;
            vertical-align: top;
        }

        table.layout th {
            text-align: left;
            padding: 3px 5px;
            font-weight: normal;
        }

        input, select, textarea {
            font-family: inherit;
            font-size: 11px;
            border: 1px solid #999;
            padding: 2px;
        }

        input[type="submit"], input[type="button"], button {
            background: #f0f0f0;
            border: 1px solid #999;
            padding: 2px 8px;
            cursor: pointer;
            font-size: 11px;
        }

        input[type="submit"]:hover, input[type="button"]:hover, button:hover {
            background: #e0e0e0;
        }

        /* 菜单样式 */
        #menu h3 {
            margin: 0 0 5px 0;
            font-size: 12px;
            color: #333;
            border-bottom: 1px solid #ccc;
            padding-bottom: 2px;
        }

        #menu ul {
            list-style: none;
            margin: 0 0 15px 0;
            padding: 0;
        }

        #menu li {
            margin: 2px 0;
        }

        #menu a {
            color: #0066cc;
            text-decoration: none;
            font-size: 11px;
            display: block;
            padding: 2px 5px;
        }

        #menu a:hover {
            background: #e0e0e0;
        }

        #menu a.current {
            background: #0066cc;
            color: white;
        }
        
        /* 消息样式 */
        .error {
            background: #ffe6e6;
            color: #cc0000;
            border: 1px solid #cc0000;
            padding: 5px;
            margin: 5px 0;
        }

        .success {
            background: #e6ffe6;
            color: #006600;
            border: 1px solid #006600;
            padding: 5px;
            margin: 5px 0;
        }

        .message {
            background: #e6f3ff;
            color: #0066cc;
            border: 1px solid #0066cc;
            padding: 5px;
            margin: 5px 0;
        }
        
        /* 表格样式 */
        table {
            border-collapse: collapse;
            font-size: 11px;
        }

        table.data {
            width: 100%;
            border: 1px solid #ccc;
        }

        table.data th {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 3px 5px;
            text-align: left;
            font-weight: bold;
        }

        table.data td {
            border: 1px solid #ccc;
            padding: 3px 5px;
            vertical-align: top;
        }

        table.data tr:nth-child(even) {
            background: #f8f8f8;
        }

        table.data tr:hover {
            background: #e0e0e0;
        }

        /* 分页样式 */
        .pagination {
            margin: 10px 0;
            text-align: center;
        }

        .pagination a {
            color: #0066cc;
            text-decoration: none;
            margin: 0 5px;
            padding: 2px 5px;
        }

        .pagination a:hover {
            background: #e0e0e0;
        }

        .pagination .current {
            font-weight: bold;
            color: #000;
        }

        /* 标签页样式 */
        .tabs {
            border-bottom: 1px solid #ccc;
            margin-bottom: 10px;
        }

        .tabs a {
            display: inline-block;
            padding: 5px 10px;
            margin-right: 5px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-bottom: none;
            color: #0066cc;
            text-decoration: none;
            font-size: 11px;
        }

        .tabs a:hover {
            background: #e0e0e0;
        }

        .tabs a.current {
            background: #fff;
            color: #000;
            font-weight: bold;
        }

        /* SQL编辑器样式 */
        .sql-editor {
            margin: 10px 0;
        }

        .sql-editor textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ccc;
            padding: 5px;
        }
        
        .sql-query-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .sql-query-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .sql-query-section textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .sql-query-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .query-result {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .query-result h4 {
            margin-top: 0;
            color: #333;
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .result-table th,
        .result-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .result-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .result-table tr:hover {
            background: #f8f9fa;
        }
        
        .result-table td em {
            color: #6c757d;
            font-style: italic;
        }
        
        .query-result .info {
            color: #495057;
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }
        
        .query-result .warning {
            color: #856404;
            background: #fff3cd;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .query-result .success {
            color: #155724;
            background: #d4edda;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        /* 触发器样式 */
        .trigger-list {
            margin-top: 1rem;
        }
        
        .trigger-item {
            margin-bottom: 0.5rem;
        }
        
        .trigger-name {
            display: block;
            padding: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 表管理样式 */
        .table-management {
            width: 100%;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table-header h3 {
            margin: 0;
            color: #333;
        }
        
        .table-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .table-tabs {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 1rem;
        }
        
        .tab-link {
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .tab-link:hover {
            color: #333;
            background: #f8f9fa;
        }
        
        .tab-link.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            min-height: 400px;
        }
        
        /* 表结构样式 */
        .table-structure {
            width: 100%;
        }
        
        .structure-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .structure-table th,
        .structure-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .structure-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: bold;
        }
        
        .structure-table tr:hover {
            background: #f8f9fa;
        }
        
        .structure-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
        }
        
        /* 数据表样式 */
        .table-data {
            width: 100%;
        }
        
        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .data-info {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .data-table .actions {
            white-space: nowrap;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .page-info {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 按钮样式 */
        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
        }
        
        .btn-edit:hover {
            background: #218838;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        /* 数据库概览样式 */
        .database-overview {
            text-align: center;
            padding: 2rem;
        }
        
        .database-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 导入导出样式 */
        .import-export-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .export-section, .import-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .export-section h4, .import-section h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .import-options {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .import-method {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .import-method h5 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .export-section .form-group,
        .import-method .form-group {
            margin-bottom: 1rem;
        }
        
        .export-section label,
        .import-method label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .export-section select,
        .import-method textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .import-method textarea {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            resize: vertical;
        }
        
        @media (max-width: 768px) {
            .import-export-container {
                grid-template-columns: 1fr;
            }
        }
        
        /* 表单样式 */
        .insert-form, .edit-form {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .insert-form h4, .edit-form h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .form-columns {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .column-type {
            color: #6c757d;
            font-weight: normal;
            font-size: 0.9rem;
        }
        
        .primary-key {
            color: #ffc107;
            margin-left: 0.25rem;
        }
        
        .auto-inc {
            background: #17a2b8;
            color: white;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.7rem;
            margin-left: 0.25rem;
        }
        
        .required {
            color: #dc3545;
            margin-left: 0.25rem;
        }
        
        .form-group input[type="text"] {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-group input[readonly] {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-start;
            margin-top: 1rem;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        /* 改进的响应式 */
        @media (max-width: 768px) {
            .form-columns {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .table-actions {
                flex-direction: column;
                gap: 0.25rem;
            }
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .database-browser {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div id="header">
        <h1>数据库管理工具 - JSP Adminer</h1>
        <% if (isConnected) { %>
            <span class="logout">
                <a href="?action=disconnect">断开连接</a> |
                <%= htmlEncode(dbType) %> @ <%= htmlEncode(host) %>
                <% if (selectedDatabase != null && !selectedDatabase.isEmpty()) { %>
                    : <%= htmlEncode(selectedDatabase) %>
                <% } %>
            </span>
        <% } %>
    </div>

    <% if (!isConnected) { %>
        <!-- 登录表单 -->
        <div class="login-form">
            <h2>登录</h2>

            <% if (!errorMessage.isEmpty()) { %>
                <div class="error"><%= htmlEncode(errorMessage) %></div>
            <% } %>

            <form method="post" action="">
                <input type="hidden" name="action" value="connect">

                <table class="layout">
                    <tr>
                        <th>系统:</th>
                        <td>
                            <select name="db_type" required onchange="updateDefaultPort(); toggleCustomFields();">
                                <option value="">请选择数据库类型</option>
                                <optgroup label="MySQL 系列">
                                    <option value="mysql" <%= "mysql".equals(dbType) ? "selected" : "" %>>MySQL</option>
                                    <option value="mariadb" <%= "mariadb".equals(dbType) ? "selected" : "" %>>MariaDB</option>
                                    <option value="mysql_old" <%= "mysql_old".equals(dbType) ? "selected" : "" %>>MySQL (旧驱动)</option>
                                </optgroup>
                                <optgroup label="Microsoft SQL Server">
                                    <option value="mssql" <%= "mssql".equals(dbType) ? "selected" : "" %>>SQL Server</option>
                                    <option value="mssql_jtds" <%= "mssql_jtds".equals(dbType) ? "selected" : "" %>>SQL Server (jTDS)</option>
                                </optgroup>
                                <optgroup label="Oracle">
                                    <option value="oracle" <%= "oracle".equals(dbType) ? "selected" : "" %>>Oracle</option>
                                    <option value="oracle_thin" <%= "oracle_thin".equals(dbType) ? "selected" : "" %>>Oracle (新驱动)</option>
                                </optgroup>
                                <optgroup label="PostgreSQL 系列">
                                    <option value="postgresql" <%= "postgresql".equals(dbType) ? "selected" : "" %>>PostgreSQL</option>
                                    <option value="vastbase" <%= "vastbase".equals(dbType) ? "selected" : "" %>>海量数据库</option>
                                    <option value="highgo" <%= "highgo".equals(dbType) ? "selected" : "" %>>瀚高数据库</option>
                                </optgroup>
                                <optgroup label="国产数据库">
                                    <option value="tibero" <%= "tibero".equals(dbType) ? "selected" : "" %>>Tibero</option>
                                    <option value="dm" <%= "dm".equals(dbType) ? "selected" : "" %>>达梦数据库</option>
                                    <option value="kingbase" <%= "kingbase".equals(dbType) ? "selected" : "" %>>人大金仓</option>
                                    <option value="oscar" <%= "oscar".equals(dbType) ? "selected" : "" %>>神舟通用</option>
                                    <option value="gbase" <%= "gbase".equals(dbType) ? "selected" : "" %>>南大通用GBase</option>
                                </optgroup>
                                <optgroup label="嵌入式数据库">
                                    <option value="sqlite" <%= "sqlite".equals(dbType) ? "selected" : "" %>>SQLite</option>
                                    <option value="h2" <%= "h2".equals(dbType) ? "selected" : "" %>>H2 Database</option>
                                    <option value="derby" <%= "derby".equals(dbType) ? "selected" : "" %>>Apache Derby</option>
                                    <option value="hsqldb" <%= "hsqldb".equals(dbType) ? "selected" : "" %>>HSQLDB</option>
                                </optgroup>
                                <optgroup label="其他数据库">
                                    <option value="db2" <%= "db2".equals(dbType) ? "selected" : "" %>>IBM DB2</option>
                                    <option value="sybase" <%= "sybase".equals(dbType) ? "selected" : "" %>>Sybase</option>
                                    <option value="informix" <%= "informix".equals(dbType) ? "selected" : "" %>>IBM Informix</option>
                                </optgroup>
                                <optgroup label="大数据/云数据库">
                                    <option value="clickhouse" <%= "clickhouse".equals(dbType) ? "selected" : "" %>>ClickHouse</option>
                                    <option value="presto" <%= "presto".equals(dbType) ? "selected" : "" %>>Presto</option>
                                    <option value="hive" <%= "hive".equals(dbType) ? "selected" : "" %>>Apache Hive</option>
                                    <option value="phoenix" <%= "phoenix".equals(dbType) ? "selected" : "" %>>Apache Phoenix</option>
                                </optgroup>
                                <optgroup label="自定义">
                                    <option value="custom" <%= "custom".equals(dbType) ? "selected" : "" %>>自定义数据库</option>
                                </optgroup>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>服务器:</th>
                        <td><input type="text" name="host" value="<%= host != null ? htmlEncode(host) : "localhost" %>" required></td>
                    </tr>
                    <tr>
                        <th>端口:</th>
                        <td><input type="text" name="port" id="port" value="<%= port != null ? htmlEncode(port) : "" %>" placeholder="默认"></td>
                    </tr>
                    <tr>
                        <th>用户名:</th>
                        <td><input type="text" name="username" value="<%= username != null ? htmlEncode(username) : "" %>" required></td>
                    </tr>
                    <tr>
                        <th>密码:</th>
                        <td><input type="password" name="password" value=""></td>
                    </tr>
                    <tr>
                        <th>数据库:</th>
                        <td><input type="text" name="database" value="<%= database != null ? htmlEncode(database) : "" %>" placeholder="数据库名/文件路径(SQLite)"></td>
                    </tr>
                    <tr id="custom_driver_row" style="display: none;">
                        <th>自定义驱动:</th>
                        <td>
                            <input type="text" name="custom_driver" value="<%= request.getParameter("custom_driver") != null ? htmlEncode(request.getParameter("custom_driver")) : "" %>" placeholder="如: com.example.jdbc.Driver">
                            <br><small>完整的JDBC驱动类名</small>
                        </td>
                    </tr>
                    <tr id="custom_url_row" style="display: none;">
                        <th>自定义URL:</th>
                        <td>
                            <input type="text" name="custom_url" value="<%= request.getParameter("custom_url") != null ? htmlEncode(request.getParameter("custom_url")) : "" %>" placeholder="如: *********************************" style="width: 300px;">
                            <br><small>完整的JDBC连接URL</small>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <input type="submit" value="登录">
                            <br><br>
                            <details>
                                <summary>驱动说明</summary>
                                <div style="font-size: 10px; margin-top: 5px;">
                                    <strong>常用驱动下载：</strong><br>
                                    • MySQL: <a href="https://dev.mysql.com/downloads/connector/j/" target="_blank">mysql-connector-java</a><br>
                                    • MariaDB: <a href="https://mariadb.com/downloads/connectors/connectors-data-access/java8-connector/" target="_blank">mariadb-java-client</a><br>
                                    • PostgreSQL: <a href="https://jdbc.postgresql.org/download.html" target="_blank">postgresql-jdbc</a><br>
                                    • SQL Server: <a href="https://docs.microsoft.com/en-us/sql/connect/jdbc/download-microsoft-jdbc-driver-for-sql-server" target="_blank">mssql-jdbc</a><br>
                                    • Oracle: <a href="https://www.oracle.com/database/technologies/appdev/jdbc-downloads.html" target="_blank">ojdbc</a><br>
                                    • 达梦: <a href="https://www.dameng.com/list_103.html" target="_blank">DmJdbcDriver</a><br>
                                    • 人大金仓: <a href="https://www.kingbase.com.cn/xzzx/index.htm" target="_blank">kingbase8-jdbc</a>
                                </div>
                            </details>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    <% } else { %>
        <!-- 连接成功，显示主界面 -->
        <div id="content">
            <div id="menu">
                <% if (!errorMessage.isEmpty()) { %>
                    <div class="error"><%= htmlEncode(errorMessage) %></div>
                <% } %>

                <!-- 数据库列表 -->
                <% if (!databases.isEmpty()) { %>
                    <h3>数据库</h3>
                    <ul>
                        <% for (String db : databases) { %>
                            <li>
                                <a href="?selected_db=<%= java.net.URLEncoder.encode(db, "UTF-8") %>"
                                   class="<%= db.equals(selectedDatabase) ? "current" : "" %>">
                                    <%= htmlEncode(db) %>
                                </a>
                            </li>
                        <% } %>
                    </ul>
                <% } %>

                <!-- 表列表 -->
                <% if (!tables.isEmpty()) { %>
                    <h3>表</h3>
                    <ul>
                        <% for (String table : tables) { %>
                            <li>
                                <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(table, "UTF-8") %>"
                                   class="<%= table.equals(selectedTable) ? "current" : "" %>">
                                    <%= htmlEncode(table) %>
                                </a>
                            </li>
                        <% } %>
                    </ul>
                <% } %>

                <!-- 视图列表 -->
                <% if (!views.isEmpty()) { %>
                    <h3>视图</h3>
                    <ul>
                        <% for (String viewName : views) { %>
                            <li>
                                <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(viewName, "UTF-8") %>&object_type=view">
                                    <%= htmlEncode(viewName) %>
                                </a>
                            </li>
                        <% } %>
                    </ul>
                <% } %>

                <!-- 存储过程列表 -->
                <% if (!procedures.isEmpty()) { %>
                    <h3>存储过程</h3>
                    <ul>
                        <% for (String proc : procedures) { %>
                            <li>
                                <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_procedure=<%= java.net.URLEncoder.encode(proc, "UTF-8") %>&object_type=procedure">
                                    <%= htmlEncode(proc) %>
                                </a>
                            </li>
                        <% } %>
                    </ul>
                <% } %>

                <!-- 触发器列表 -->
                <% if (!triggers.isEmpty()) { %>
                    <h3>触发器</h3>
                    <ul>
                        <% for (String trigger : triggers) { %>
                            <li>
                                <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_trigger=<%= java.net.URLEncoder.encode(trigger, "UTF-8") %>&object_type=trigger&view=triggers">
                                    <%= htmlEncode(trigger) %>
                                </a>
                            </li>
                        <% } %>
                    </ul>
                <% } %>

                <!-- SQL查询快捷入口 -->
                <h3>SQL</h3>
                <ul>
                    <li><a href="?view=sql">SQL命令</a></li>
                    <li><a href="?view=import">导入</a></li>
                    <li><a href="?view=export">导出</a></li>
                </ul>
            </div>

            <div id="main">
                <% if (selectedTable != null && !selectedTable.isEmpty()) { %>
                    <!-- 表管理界面 -->
                    <h2>表: <%= htmlEncode(selectedTable) %></h2>

                    <!-- Adminer风格的标签页 -->
                    <div class="tabs">
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=data"
                           class="<%= (view == null || "data".equals(view)) ? "current" : "" %>">浏览</a>
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=structure"
                           class="<%= "structure".equals(view) ? "current" : "" %>">结构</a>
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=insert"
                           class="<%= "insert".equals(view) ? "current" : "" %>">新建</a>
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=export"
                           class="<%= "export".equals(view) ? "current" : "" %>">导出</a>
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=import"
                           class="<%= "import".equals(view) ? "current" : "" %>">导入</a>
                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=triggers"
                           class="<%= "triggers".equals(view) ? "current" : "" %>">触发器</a>
                    </div>
                                
                    <% if (view == null || "data".equals(view)) { %>
                        <!-- 数据浏览视图 -->
                        <p>
                            <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=insert">新建</a>
                        </p>
                        <%= tableDataHtml %>

                    <% } else if ("insert".equals(view)) { %>
                        <!-- 新增数据视图 -->
                        <h3>新建记录</h3>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="insert_row">
                            <input type="hidden" name="table_name" value="<%= htmlEncode(selectedTable) %>">
                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                            <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">

                            <table class="data">
                                <% for (ColumnInfo col : tableStructure) { %>
                                    <% if (!col.isAutoIncrement) { %>
                                        <tr>
                                            <th>
                                                <%= htmlEncode(col.name) %>
                                                <% if (col.isPrimaryKey) { %> (主键)<% } %>
                                                <% if (!"YES".equals(col.nullable) && !"Y".equals(col.nullable)) { %> *<% } %>
                                            </th>
                                            <td>
                                                <input type="text"
                                                       name="new_<%= htmlEncode(col.name) %>"
                                                       value="<%= col.defaultValue != null ? htmlEncode(col.defaultValue) : "" %>"
                                                       style="width: 300px;">
                                                <br><small><%= htmlEncode(col.type) %></small>
                                            </td>
                                        </tr>
                                    <% } %>
                                <% } %>
                                <tr>
                                    <td></td>
                                    <td>
                                        <input type="submit" value="保存">
                                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>">取消</a>
                                    </td>
                                </tr>
                            </table>
                        </form>
                    <% } else if ("structure".equals(view)) { %>
                        <!-- 表结构视图 -->
                        <h3>表结构</h3>
                        <table class="data">
                            <thead>
                                <tr>
                                    <th>列名</th>
                                    <th>类型</th>
                                    <th>NULL</th>
                                    <th>默认值</th>
                                    <th>注释</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <% for (ColumnInfo col : tableStructure) { %>
                                    <tr>
                                        <td>
                                            <strong><%= htmlEncode(col.name) %></strong>
                                            <% if (col.isPrimaryKey) { %> <em>(主键)</em><% } %>
                                            <% if (col.isAutoIncrement) { %> <em>(自增)</em><% } %>
                                        </td>
                                        <td><%= htmlEncode(col.type) %></td>
                                        <td><%= "YES".equals(col.nullable) || "Y".equals(col.nullable) ? "是" : "否" %></td>
                                        <td><%= col.defaultValue != null ? htmlEncode(col.defaultValue) : "" %></td>
                                        <td><%= col.comment != null && !col.comment.isEmpty() ? htmlEncode(col.comment) : "" %></td>
                                        <td>
                                            <a href="#">修改</a>
                                            <a href="#" onclick="return confirm('确定删除此列吗？')">删除</a>
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>

                        <p>
                            <a href="#">添加列</a> |
                            <a href="#">添加索引</a> |
                            <a href="#" onclick="return confirm('确定删除此表吗？')">删除表</a>
                        </p>

                    <% } else if ("triggers".equals(view)) { %>
                        <!-- 触发器详细信息视图 -->
                        <h3>触发器</h3>
                        <% if (!triggerDetails.isEmpty()) { %>
                            <table class="data">
                                <thead>
                                    <tr>
                                        <th>触发器名称</th>
                                        <th>表名</th>
                                        <th>事件</th>
                                        <th>时机</th>
                                        <th>定义者</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% for (TriggerInfo trigger : triggerDetails) { %>
                                        <tr>
                                            <td><strong><%= htmlEncode(trigger.name) %></strong></td>
                                            <td><%= htmlEncode(trigger.tableName) %></td>
                                            <td><%= htmlEncode(trigger.event) %></td>
                                            <td><%= htmlEncode(trigger.timing) %></td>
                                            <td><%= trigger.definer != null ? htmlEncode(trigger.definer) : "" %></td>
                                            <td>
                                                <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&view_trigger=<%= java.net.URLEncoder.encode(trigger.name, "UTF-8") %>">查看</a>
                                                <a href="#" onclick="return confirm('确定删除此触发器吗？')">删除</a>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>

                            <!-- 显示选中触发器的详细定义 -->
                            <%
                                String viewTrigger = request.getParameter("view_trigger");
                                if (viewTrigger != null) {
                                    for (TriggerInfo trigger : triggerDetails) {
                                        if (trigger.name.equals(viewTrigger)) {
                            %>
                                            <h4>触发器定义: <%= htmlEncode(trigger.name) %></h4>
                                            <div class="sql-editor">
                                                <textarea readonly style="height: 200px;"><%= htmlEncode(trigger.definition) %></textarea>
                                            </div>
                            <%
                                            break;
                                        }
                                    }
                                }
                            %>
                        <% } else { %>
                            <p>此数据库中没有触发器。</p>
                        <% } %>

                        <p><a href="#">创建触发器</a></p>
                                    <% } else if ("export".equals(view)) { %>
                                        <!-- 导出视图 -->
                                        <div class="import-export-container">
                                            <div class="export-section">
                                                <h4>数据导出</h4>
                                                <form method="post" action="">
                                                    <input type="hidden" name="action" value="export">
                                                    <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                    
                                                    <div class="form-group">
                                                        <label>导出格式:</label>
                                                        <select name="export_format">
                                                            <option value="sql">SQL</option>
                                                            <option value="csv">CSV</option>
                                                            <option value="json">JSON</option>
                                                            <option value="xml">XML</option>
                                                        </select>
                                                    </div>
                                                    
                                                    <div class="form-group">
                                                        <label>
                                                            <input type="checkbox" name="export_structure" value="1" checked> 包含表结构
                                                        </label>
                                                    </div>
                                                    
                                                    <div class="form-group">
                                                        <label>
                                                            <input type="checkbox" name="export_data" value="1" checked> 包含数据
                                                        </label>
                                                    </div>
                                                    
                                                    <button type="submit" class="btn">开始导出</button>
                                                </form>
                                            </div>
                                            
                                            <div class="import-section">
                                                <h4>数据导入</h4>
                                                <div class="import-options">
                                                    <div class="import-method">
                                                        <h5>SQL 语句导入</h5>
                                                        <form method="post" action="">
                                                            <input type="hidden" name="action" value="import_sql">
                                                            <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                            
                                                            <div class="form-group">
                                                                <label for="import_sql">SQL 语句:</label>
                                                                <textarea name="import_sql" id="import_sql" rows="8" placeholder="输入INSERT语句或其他SQL语句..."></textarea>
                                                            </div>
                                                            
                                                            <button type="submit" class="btn">执行导入</button>
                                                        </form>
                                                    </div>
                                                    
                                                    <div class="import-method">
                                                        <h5>CSV 数据导入</h5>
                                                        <form method="post" action="" enctype="multipart/form-data">
                                                            <input type="hidden" name="action" value="import_csv">
                                                            <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                            
                                                            <div class="form-group">
                                                                <label for="csv_data">CSV 数据:</label>
                                                                <textarea name="csv_data" id="csv_data" rows="8" placeholder="粘贴CSV数据或输入逗号分隔的数据..."></textarea>
                                                            </div>
                                                            
                                                            <div class="form-group">
                                                                <label>
                                                                    <input type="checkbox" name="has_header" value="1" checked> 第一行是列标题
                                                                </label>
                                                            </div>
                                                            
                                                            <button type="submit" class="btn">导入CSV</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                            
                <% } else if ("sql".equals(view)) { %>
                    <!-- SQL查询界面 -->
                    <h2>SQL命令</h2>
                    <form method="post" action="">
                        <input type="hidden" name="action" value="query">
                        <input type="hidden" name="view" value="sql">
                        <% if (selectedDatabase != null) { %>
                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase) %>">
                        <% } %>

                        <div class="sql-editor">
                            <textarea name="sql_query" rows="10" style="width: 100%;"><%= sqlQuery != null ? htmlEncode(sqlQuery) : "" %></textarea>
                        </div>

                        <p>
                            <input type="submit" value="执行">
                            <input type="button" value="清空" onclick="document.querySelector('textarea[name=sql_query]').value=''">
                        </p>
                    </form>

                    <!-- 查询结果显示 -->
                    <% if (!queryResult.isEmpty()) { %>
                        <%= queryResult %>
                    <% } %>

                <% } else if (selectedDatabase != null || (!tables.isEmpty())) { %>
                    <!-- 数据库概览 -->
                    <h2>数据库:
                        <% if (selectedDatabase != null) { %>
                            <%= htmlEncode(selectedDatabase) %>
                        <% } else if (database != null && !database.isEmpty()) { %>
                            <%= htmlEncode(database) %>
                        <% } %>
                    </h2>

                    <table class="data">
                        <tr>
                            <th>表数量</th>
                            <td><%= tables.size() %></td>
                        </tr>
                        <tr>
                            <th>视图数量</th>
                            <td><%= views.size() %></td>
                        </tr>
                        <tr>
                            <th>存储过程数量</th>
                            <td><%= procedures.size() %></td>
                        </tr>
                        <tr>
                            <th>触发器数量</th>
                            <td><%= triggers.size() %></td>
                        </tr>
                    </table>

                    <p>请从左侧菜单选择要操作的对象。</p>

                <% } else { %>
                    <!-- 欢迎页面 -->
                    <h2>欢迎使用数据库管理工具</h2>
                    <p>请从左侧选择一个数据库开始浏览。</p>

                    <h3>功能说明：</h3>
                    <ul>
                        <li>点击左侧数据库名称查看表列表</li>
                        <li>点击表名称查看表结构和数据</li>
                        <li>使用SQL命令功能执行自定义查询</li>
                        <li>支持数据的增删改查操作</li>
                        <li>支持数据导入导出</li>
                        <li>支持触发器管理</li>
                    </ul>
                <% } %>
            </div>
        </div>
    <% } %>
    
    <script>
        // 根据数据库类型更新默认端口
        function updateDefaultPort() {
            const dbTypeSelect = document.querySelector('select[name="db_type"]');
            const portField = document.getElementById('port');

            if (!dbTypeSelect || !portField) return;

            const dbType = dbTypeSelect.value;
            const defaultPorts = {
                'mysql': '3306',
                'mariadb': '3306',
                'mysql_old': '3306',
                'mssql': '1433',
                'mssql_jtds': '1433',
                'oracle': '1521',
                'oracle_thin': '1521',
                'postgresql': '5432',
                'tibero': '8629',
                'dm': '5236',
                'kingbase': '54321',
                'oscar': '2003',
                'gbase': '5258',
                'highgo': '5866',
                'vastbase': '5432',
                'h2': '9092',
                'derby': '1527',
                'hsqldb': '9001',
                'db2': '50000',
                'sybase': '5000',
                'informix': '9088',
                'clickhouse': '8123',
                'presto': '8080',
                'hive': '10000',
                'phoenix': '2181'
            };

            if (defaultPorts[dbType]) {
                portField.placeholder = '默认: ' + defaultPorts[dbType];
                if (!portField.value || portField.value === '') {
                    portField.value = defaultPorts[dbType];
                }
            } else if (dbType === 'sqlite') {
                portField.placeholder = '无需端口';
                portField.value = '';
            } else {
                portField.placeholder = '端口号';
            }
        }

        // 切换自定义字段的显示/隐藏
        function toggleCustomFields() {
            const dbTypeSelect = document.querySelector('select[name="db_type"]');
            const customDriverRow = document.getElementById('custom_driver_row');
            const customUrlRow = document.getElementById('custom_url_row');

            if (!dbTypeSelect || !customDriverRow || !customUrlRow) return;

            const dbType = dbTypeSelect.value;
            if (dbType === 'custom') {
                customDriverRow.style.display = '';
                customUrlRow.style.display = '';
            } else {
                customDriverRow.style.display = 'none';
                customUrlRow.style.display = 'none';
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDefaultPort();
            toggleCustomFields();

            // 为数据库类型选择添加事件监听
            const dbTypeSelect = document.querySelector('select[name="db_type"]');
            if (dbTypeSelect) {
                dbTypeSelect.addEventListener('change', function() {
                    updateDefaultPort();
                    toggleCustomFields();
                });
            }
        });
        
        // 表管理功能
        function showCreateForm() {
            document.getElementById('insertForm').style.display = 'block';
            document.getElementById('editForm').style.display = 'none';
        }
        
        function hideInsertForm() {
            document.getElementById('insertForm').style.display = 'none';
        }
        
        function hideEditForm() {
            document.getElementById('editForm').style.display = 'none';
        }
        
        function truncateTable() {
            if (confirm('确定要清空表吗？此操作不可撤销！')) {
                var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                if (currentTable) {
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="table_operation">' +
                                   '<input type="hidden" name="operation" value="truncate">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">' +
                                   '<input type="hidden" name="selected_table" value="' + currentTable + '">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function dropTable() {
            if (confirm('确定要删除表吗？此操作不可撤销！')) {
                var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                if (currentTable) {
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="table_operation">' +
                                   '<input type="hidden" name="operation" value="drop">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function editRow(rowNum) {
            var row = document.querySelector('tr[data-row="' + rowNum + '"]');
            if (row) {
                var whereClause = row.getAttribute('data-where');
                document.getElementById('edit_where_clause').value = whereClause;
                
                // 填充表单字段
                var cells = row.querySelectorAll('td[data-column]');
                cells.forEach(function(cell) {
                    var columnName = cell.getAttribute('data-column');
                    var value = cell.getAttribute('data-value');
                    var input = document.getElementById('col_' + columnName);
                    if (input) {
                        input.value = value || '';
                    }
                });
                
                document.getElementById('editForm').style.display = 'block';
                document.getElementById('insertForm').style.display = 'none';
            }
        }
        
        function deleteRow(rowNum) {
            if (confirm('确定要删除这行数据吗？')) {
                var row = document.querySelector('tr[data-row="' + rowNum + '"]');
                if (row) {
                    var whereClause = row.getAttribute('data-where');
                    var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                    
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="delete_row">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="where_clause" value="' + whereClause + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">' +
                                   '<input type="hidden" name="selected_table" value="' + currentTable + '">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function addColumn() {
            alert('添加列功能开发中...');
        }
        
        function addIndex() {
            alert('添加索引功能开发中...');
        }
    </script>
</body>
</html> 
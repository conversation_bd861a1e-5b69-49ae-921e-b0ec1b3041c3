<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.io.*" %>
<%@ page import="javax.naming.*" %>

<%!
    // 数据库驱动配置
    private static final Map<String, String> DB_DRIVERS = new HashMap<String, String>() {{
        put("mysql", "com.mysql.cj.jdbc.Driver");
        put("mssql", "com.microsoft.sqlserver.jdbc.SQLServerDriver");
        put("oracle", "oracle.jdbc.driver.OracleDriver");
        put("tibero", "com.tmax.tibero.jdbc.TbDriver");
        put("postgresql", "org.postgresql.Driver");
    }};
    
    // 数据库URL模板
    private static final Map<String, String> DB_URL_TEMPLATES = new HashMap<String, String>() {{
        put("mysql", "jdbc:mysql://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false");
        put("mssql", "jdbc:sqlserver://{host}:{port};databaseName={db}");
        put("oracle", "jdbc:oracle:thin:@{host}:{port}:{db}");
        put("tibero", "jdbc:tibero:thin:@{host}:{port}:{db}");
        put("postgresql", "jdbc:postgresql://{host}:{port}/{db}");
    }};
    
    // 默认端口
    private static final Map<String, String> DB_DEFAULT_PORTS = new HashMap<String, String>() {{
        put("mysql", "3306");
        put("mssql", "1433");
        put("oracle", "1521");
        put("tibero", "8629");
        put("postgresql", "5432");
    }};
    
    // 创建数据库连接
    public Connection createConnection(String dbType, String host, String port, String database, 
                                     String username, String password) throws Exception {
        String driver = DB_DRIVERS.get(dbType);
        if (driver == null) {
            throw new Exception("不支持的数据库类型: " + dbType);
        }
        
        Class.forName(driver);
        
        String urlTemplate = DB_URL_TEMPLATES.get(dbType);
        String url = urlTemplate.replace("{host}", host)
                                .replace("{port}", port)
                                .replace("{db}", database);
        
        return DriverManager.getConnection(url, username, password);
    }
    
    // HTML编码
    public String htmlEncode(String str) {
        if (str == null) return "";
        return str.replace("&", "&amp;")
                 .replace("<", "&lt;")
                 .replace(">", "&gt;")
                 .replace("\"", "&quot;")
                 .replace("'", "&#39;");
    }
    
    // 获取数据库列表
    public List<String> getDatabases(Connection conn, String dbType) throws SQLException {
        List<String> databases = new ArrayList<>();
        String sql = "";
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                sql = "SHOW DATABASES";
                break;
            case "mssql":
                sql = "SELECT name FROM sys.databases WHERE database_id > 4";
                break;
            case "oracle":
                sql = "SELECT username FROM all_users ORDER BY username";
                break;
            case "tibero":
                sql = "SELECT username FROM all_users ORDER BY username";
                break;
            case "postgresql":
                sql = "SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname";
                break;
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    databases.add(rs.getString(1));
                }
            }
        }
        
        return databases;
    }
    
    // 获取表列表
    public List<String> getTables(Connection conn, String dbType, String database) throws SQLException {
        List<String> tables = new ArrayList<>();
        String sql = "";
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                sql = "SHOW TABLES";
                break;
            case "mssql":
                sql = "SELECT table_name FROM information_schema.tables WHERE table_type = 'BASE TABLE' ORDER BY table_name";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT table_name FROM user_tables ORDER BY table_name";
                break;
            case "postgresql":
                sql = "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename";
                break;
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    tables.add(rs.getString(1));
                }
            }
        }
        
        return tables;
    }
    
    // 表结构信息类
    public class ColumnInfo {
        public String name;
        public String type;
        public String nullable;
        public String defaultValue;
        public String comment;
        public boolean isPrimaryKey;
        public boolean isAutoIncrement;
        
        public ColumnInfo(String name, String type, String nullable, String defaultValue, String comment, boolean isPrimaryKey, boolean isAutoIncrement) {
            this.name = name;
            this.type = type;
            this.nullable = nullable;
            this.defaultValue = defaultValue;
            this.comment = comment;
            this.isPrimaryKey = isPrimaryKey;
            this.isAutoIncrement = isAutoIncrement;
        }
    }
    
    // 获取表结构
    public List<ColumnInfo> getTableStructure(Connection conn, String dbType, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();
        String sql = "";
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                sql = "SHOW FULL COLUMNS FROM " + tableName;
                break;
            case "mssql":
                sql = "SELECT c.COLUMN_NAME, c.DATA_TYPE + CASE WHEN c.CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN '(' + CAST(c.CHARACTER_MAXIMUM_LENGTH AS VARCHAR) + ')' ELSE '' END as DATA_TYPE, " +
                      "c.IS_NULLABLE, c.COLUMN_DEFAULT, '' as COMMENT, " +
                      "CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY, " +
                      "CASE WHEN c.EXTRA LIKE '%identity%' THEN 1 ELSE 0 END as IS_AUTO " +
                      "FROM INFORMATION_SCHEMA.COLUMNS c " +
                      "LEFT JOIN (SELECT ku.COLUMN_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc " +
                      "JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME " +
                      "WHERE tc.TABLE_NAME = '" + tableName + "' AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY') pk ON c.COLUMN_NAME = pk.COLUMN_NAME " +
                      "WHERE c.TABLE_NAME = '" + tableName + "' ORDER BY c.ORDINAL_POSITION";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT c.COLUMN_NAME, c.DATA_TYPE || CASE WHEN c.DATA_LENGTH IS NOT NULL AND c.DATA_TYPE IN ('VARCHAR2','CHAR') THEN '(' || c.DATA_LENGTH || ')' ELSE '' END as DATA_TYPE, " +
                      "c.NULLABLE, c.DATA_DEFAULT, com.COMMENTS, " +
                      "CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY, 0 as IS_AUTO " +
                      "FROM user_tab_columns c " +
                      "LEFT JOIN user_col_comments com ON c.TABLE_NAME = com.TABLE_NAME AND c.COLUMN_NAME = com.COLUMN_NAME " +
                      "LEFT JOIN (SELECT cc.COLUMN_NAME FROM user_constraints tc JOIN user_cons_columns cc ON tc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME " +
                      "WHERE tc.TABLE_NAME = '" + tableName + "' AND tc.CONSTRAINT_TYPE = 'P') pk ON c.COLUMN_NAME = pk.COLUMN_NAME " +
                      "WHERE c.TABLE_NAME = '" + tableName + "' ORDER BY c.COLUMN_ID";
                break;
            case "postgresql":
                sql = "SELECT c.column_name, c.data_type || CASE WHEN c.character_maximum_length IS NOT NULL THEN '(' || c.character_maximum_length || ')' ELSE '' END as data_type, " +
                      "c.is_nullable, c.column_default, '' as comment, " +
                      "CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary, " +
                      "CASE WHEN c.column_default LIKE 'nextval%' THEN true ELSE false END as is_auto " +
                      "FROM information_schema.columns c " +
                      "LEFT JOIN (SELECT ku.column_name FROM information_schema.table_constraints tc " +
                      "JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name " +
                      "WHERE tc.table_name = '" + tableName + "' AND tc.constraint_type = 'PRIMARY KEY') pk ON c.column_name = pk.column_name " +
                      "WHERE c.table_name = '" + tableName + "' ORDER BY c.ordinal_position";
                break;
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    if ("mysql".equals(dbType)) {
                        columns.add(new ColumnInfo(
                            rs.getString("Field"),
                            rs.getString("Type"),
                            rs.getString("Null"),
                            rs.getString("Default"),
                            rs.getString("Comment"),
                            "PRI".equals(rs.getString("Key")),
                            "auto_increment".equals(rs.getString("Extra"))
                        ));
                    } else {
                        columns.add(new ColumnInfo(
                            rs.getString(1),
                            rs.getString(2),
                            rs.getString(3),
                            rs.getString(4),
                            rs.getString(5),
                            rs.getBoolean(6),
                            rs.getBoolean(7)
                        ));
                    }
                }
            }
        }
        
        return columns;
    }
    
    // 获取表数据
    public String getTableData(Connection conn, String tableName, int page, int pageSize, String dbType) throws SQLException {
        StringBuilder result = new StringBuilder();
        int offset = (page - 1) * pageSize;
        
        // 先获取总行数
        String countSql = "SELECT COUNT(*) FROM " + tableName;
        int totalRows = 0;
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(countSql)) {
            if (rs.next()) {
                totalRows = rs.getInt(1);
            }
        }
        
        // 获取数据 - 根据数据库类型使用不同的分页语法
        String dataSql = "";
        switch (dbType.toLowerCase()) {
            case "mysql":
            case "postgresql":
                dataSql = "SELECT * FROM " + tableName + " LIMIT " + pageSize + " OFFSET " + offset;
                break;
            case "mssql":
                dataSql = "SELECT * FROM " + tableName + " ORDER BY (SELECT NULL) OFFSET " + offset + " ROWS FETCH NEXT " + pageSize + " ROWS ONLY";
                break;
            case "oracle":
            case "tibero":
                int endRow = offset + pageSize;
                dataSql = "SELECT * FROM (SELECT ROWNUM rn, t.* FROM (SELECT * FROM " + tableName + ") t WHERE ROWNUM <= " + endRow + ") WHERE rn > " + offset;
                break;
            default:
                dataSql = "SELECT * FROM " + tableName;
        }
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(dataSql)) {
            
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            
            result.append("<div class='table-data'>");
            result.append("<div class='data-header'>");
            result.append("<h4>表数据 - ").append(htmlEncode(tableName)).append("</h4>");
            result.append("<p class='data-info'>总计 ").append(totalRows).append(" 行，显示第 ").append(page).append(" 页</p>");
            result.append("</div>");
            
            result.append("<table class='data-table'>");
            
            // 表头
            result.append("<thead><tr>");
            result.append("<th>操作</th>");
            for (int i = 1; i <= columnCount; i++) {
                result.append("<th>").append(htmlEncode(meta.getColumnName(i))).append("</th>");
            }
            result.append("</tr></thead>");
            
            // 数据行
            result.append("<tbody>");
            int rowNum = 0;
            while (rs.next()) {
                // 构建WHERE子句用于编辑和删除
                StringBuilder whereClause = new StringBuilder();
                boolean firstWhere = true;
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = meta.getColumnName(i);
                    String value = rs.getString(i);
                    if (!firstWhere) whereClause.append(" AND ");
                    if (value != null) {
                        whereClause.append(columnName).append("='").append(value.replace("'", "''")).append("'");
                    } else {
                        whereClause.append(columnName).append(" IS NULL");
                    }
                    firstWhere = false;
                }
                
                result.append("<tr data-row='").append(rowNum).append("' data-where='").append(htmlEncode(whereClause.toString())).append("'>");
                result.append("<td class='actions'>");
                result.append("<button class='btn-small btn-edit' data-table='").append(htmlEncode(tableName)).append("' onclick='editRow(").append(rowNum).append(")'>编辑</button>");
                result.append("<button class='btn-small btn-delete' data-table='").append(htmlEncode(tableName)).append("' onclick='deleteRow(").append(rowNum).append(")'>删除</button>");
                result.append("</td>");
                
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    String columnName = meta.getColumnName(i);
                    result.append("<td data-column='").append(htmlEncode(columnName)).append("' data-value='").append(value != null ? htmlEncode(value) : "").append("'>");
                    result.append(value != null ? htmlEncode(value) : "<em>NULL</em>");
                    result.append("</td>");
                }
                result.append("</tr>");
                rowNum++;
            }
            result.append("</tbody></table>");
            
            // 分页控制
            int totalPages = (int) Math.ceil((double) totalRows / pageSize);
            if (totalPages > 1) {
                result.append("<div class='pagination'>");
                if (page > 1) {
                    result.append("<a href='?selected_table=").append(tableName).append("&page=").append(page - 1).append("&view=data' class='btn btn-small'>上一页</a>");
                }
                result.append("<span class='page-info'>第 ").append(page).append(" / ").append(totalPages).append(" 页</span>");
                if (page < totalPages) {
                    result.append("<a href='?selected_table=").append(tableName).append("&page=").append(page + 1).append("&view=data' class='btn btn-small'>下一页</a>");
                }
                result.append("</div>");
            }
            
            result.append("</div>");
        }
        
        return result.toString();
    }
    
    // 获取触发器列表
    public List<String> getTriggers(Connection conn, String dbType, String database) throws SQLException {
        List<String> triggers = new ArrayList<>();
        String sql = "";
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                sql = "SHOW TRIGGERS";
                break;
            case "mssql":
                sql = "SELECT name FROM sys.triggers WHERE parent_class = 1";
                break;
            case "oracle":
            case "tibero":
                sql = "SELECT trigger_name FROM user_triggers ORDER BY trigger_name";
                break;
            case "postgresql":
                sql = "SELECT trigger_name FROM information_schema.triggers WHERE trigger_schema = 'public' ORDER BY trigger_name";
                break;
        }
        
        if (!sql.isEmpty()) {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    triggers.add(rs.getString(1));
                }
            }
        }
        
        return triggers;
    }
%>

<%
    String action = request.getParameter("action");
    String dbType = request.getParameter("db_type");
    String host = request.getParameter("host");
    String port = request.getParameter("port");
    String database = request.getParameter("database");
    String username = request.getParameter("username");
    String password = request.getParameter("password");
    String selectedDatabase = request.getParameter("selected_db");
    String selectedTable = request.getParameter("selected_table");
    
    Connection conn = null;
    String errorMessage = "";
    boolean isConnected = false;
    
    List<String> databases = new ArrayList<>();
    List<String> tables = new ArrayList<>();
    List<String> triggers = new ArrayList<>();
    List<ColumnInfo> tableStructure = new ArrayList<>();
    String queryResult = "";
    String sqlQuery = request.getParameter("sql_query");
    String view = request.getParameter("view"); // structure, data, triggers
    int currentPage = 1;
    try {
        if (request.getParameter("page") != null) {
            currentPage = Integer.parseInt(request.getParameter("page"));
        }
    } catch (NumberFormatException e) {
        currentPage = 1;
    }
    String tableDataHtml = "";
    
    // 处理登录连接
    if ("connect".equals(action) && dbType != null && host != null && username != null) {
        try {
            if (port == null || port.trim().isEmpty()) {
                port = DB_DEFAULT_PORTS.get(dbType);
            }
            if (database == null) database = "";
            if (password == null) password = "";
            
            conn = createConnection(dbType, host, port, database, username, password);
            isConnected = true;
            
            // 将连接信息存储到session中
            session.setAttribute("db_connection", conn);
            session.setAttribute("db_type", dbType);
            session.setAttribute("db_host", host);
            session.setAttribute("db_port", port);
            session.setAttribute("db_database", database);
            session.setAttribute("db_username", username);
            
        } catch (Exception e) {
            errorMessage = "连接失败: " + e.getMessage();
        }
    } else {
        // 尝试从session获取连接
        conn = (Connection) session.getAttribute("db_connection");
        if (conn != null && !conn.isClosed()) {
            isConnected = true;
            dbType = (String) session.getAttribute("db_type");
            host = (String) session.getAttribute("db_host");
            port = (String) session.getAttribute("db_port");
            database = (String) session.getAttribute("db_database");
            username = (String) session.getAttribute("db_username");
        }
    }
    
    // 如果已连接，获取数据库和表列表
    if (isConnected && conn != null) {
        try {
            databases = getDatabases(conn, dbType);
            
            // 如果选择了数据库，获取表列表和触发器
            if (selectedDatabase != null && !selectedDatabase.isEmpty()) {
                // 对于某些数据库类型，需要切换到指定数据库
                if ("mysql".equals(dbType)) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.execute("USE " + selectedDatabase);
                    }
                }
                tables = getTables(conn, dbType, selectedDatabase);
                triggers = getTriggers(conn, dbType, selectedDatabase);
            } else if (database != null && !database.isEmpty()) {
                tables = getTables(conn, dbType, database);
                triggers = getTriggers(conn, dbType, database);
            }
            
            // 如果选择了表，获取表结构和数据
            if (selectedTable != null && !selectedTable.isEmpty()) {
                tableStructure = getTableStructure(conn, dbType, selectedTable);
                
                // 默认显示数据，除非指定了其他视图
                if (view == null || "data".equals(view)) {
                    tableDataHtml = getTableData(conn, selectedTable, currentPage, 20, dbType);
                }
            }
        } catch (SQLException e) {
            errorMessage = "获取数据库信息失败: " + e.getMessage();
        }
    }
    
    // 处理数据编辑
    if ("edit_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            String whereClause = request.getParameter("where_clause");
            
            if (tableName != null && whereClause != null) {
                StringBuilder updateSql = new StringBuilder("UPDATE " + tableName + " SET ");
                boolean first = true;
                
                // 获取表结构以知道有哪些列
                List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                for (ColumnInfo col : columns) {
                    String newValue = request.getParameter("col_" + col.name);
                    if (newValue != null) {
                        if (!first) updateSql.append(", ");
                        updateSql.append(col.name).append(" = ?");
                        first = false;
                    }
                }
                
                updateSql.append(" WHERE ").append(whereClause);
                
                try (PreparedStatement pstmt = conn.prepareStatement(updateSql.toString())) {
                    int paramIndex = 1;
                    for (ColumnInfo col : columns) {
                        String newValue = request.getParameter("col_" + col.name);
                        if (newValue != null) {
                            if ("NULL".equals(newValue) || newValue.trim().isEmpty()) {
                                pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                            } else {
                                pstmt.setString(paramIndex, newValue);
                            }
                            paramIndex++;
                        }
                    }
                    
                    int affected = pstmt.executeUpdate();
                    queryResult = "<div class='query-result'><div class='success'>数据更新成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据更新失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理删除行
    if ("delete_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            String whereClause = request.getParameter("where_clause");
            
            if (tableName != null && whereClause != null) {
                String deleteSql = "DELETE FROM " + tableName + " WHERE " + whereClause;
                try (Statement stmt = conn.createStatement()) {
                    int affected = stmt.executeUpdate(deleteSql);
                    queryResult = "<div class='query-result'><div class='success'>数据删除成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据删除失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理新增行
    if ("insert_row".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("table_name");
            
            if (tableName != null) {
                List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                StringBuilder insertSql = new StringBuilder("INSERT INTO " + tableName + " (");
                StringBuilder valuesSql = new StringBuilder(" VALUES (");
                
                boolean first = true;
                for (ColumnInfo col : columns) {
                    if (!col.isAutoIncrement) { // 跳过自增列
                        String newValue = request.getParameter("new_" + col.name);
                        if (newValue != null) {
                            if (!first) {
                                insertSql.append(", ");
                                valuesSql.append(", ");
                            }
                            insertSql.append(col.name);
                            valuesSql.append("?");
                            first = false;
                        }
                    }
                }
                
                insertSql.append(")").append(valuesSql.toString()).append(")");
                
                try (PreparedStatement pstmt = conn.prepareStatement(insertSql.toString())) {
                    int paramIndex = 1;
                    for (ColumnInfo col : columns) {
                        if (!col.isAutoIncrement) {
                            String newValue = request.getParameter("new_" + col.name);
                            if (newValue != null) {
                                if ("NULL".equals(newValue) || newValue.trim().isEmpty()) {
                                    pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                                } else {
                                    pstmt.setString(paramIndex, newValue);
                                }
                                paramIndex++;
                            }
                        }
                    }
                    
                    int affected = pstmt.executeUpdate();
                    queryResult = "<div class='query-result'><div class='success'>数据插入成功！受影响的行数: " + affected + "</div></div>";
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>数据插入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理表管理操作
    if ("table_operation".equals(action) && isConnected && conn != null) {
        try {
            String operation = request.getParameter("operation");
            String tableName = request.getParameter("table_name");
            
            if (tableName != null && operation != null) {
                String sql = "";
                switch (operation) {
                    case "truncate":
                        sql = "DELETE FROM " + tableName; // 使用DELETE而不是TRUNCATE以保证兼容性
                        break;
                    case "drop":
                        sql = "DROP TABLE " + tableName;
                        break;
                }
                
                if (!sql.isEmpty()) {
                    try (Statement stmt = conn.createStatement()) {
                        stmt.executeUpdate(sql);
                        queryResult = "<div class='query-result'><div class='success'>表操作执行成功！</div></div>";
                        
                        // 如果是删除表，清除选中状态
                        if ("drop".equals(operation)) {
                            selectedTable = null;
                        }
                    }
                }
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>表操作失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理数据导出
    if ("export".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("selected_table");
            String exportFormat = request.getParameter("export_format");
            boolean exportStructure = "1".equals(request.getParameter("export_structure"));
            boolean exportData = "1".equals(request.getParameter("export_data"));
            
            if (tableName != null && exportFormat != null) {
                StringBuilder exportResult = new StringBuilder();
                
                if ("sql".equals(exportFormat)) {
                    // SQL导出
                    if (exportStructure) {
                        // 导出表结构（简化版）
                        exportResult.append("-- 表结构: ").append(tableName).append("\n");
                        exportResult.append("-- 注意：具体建表语句需要根据源数据库类型调整\n\n");
                    }
                    
                    if (exportData) {
                        // 导出数据
                        String dataSql = "SELECT * FROM " + tableName;
                        try (Statement stmt = conn.createStatement();
                             ResultSet rs = stmt.executeQuery(dataSql)) {
                            
                            ResultSetMetaData meta = rs.getMetaData();
                            int columnCount = meta.getColumnCount();
                            
                            while (rs.next()) {
                                exportResult.append("INSERT INTO ").append(tableName).append(" (");
                                for (int i = 1; i <= columnCount; i++) {
                                    if (i > 1) exportResult.append(", ");
                                    exportResult.append(meta.getColumnName(i));
                                }
                                exportResult.append(") VALUES (");
                                
                                for (int i = 1; i <= columnCount; i++) {
                                    if (i > 1) exportResult.append(", ");
                                    String value = rs.getString(i);
                                    if (value == null) {
                                        exportResult.append("NULL");
                                    } else {
                                        exportResult.append("'").append(value.replace("'", "''")).append("'");
                                    }
                                }
                                exportResult.append(");\n");
                            }
                        }
                    }
                } else if ("csv".equals(exportFormat)) {
                    // CSV导出
                    String dataSql = "SELECT * FROM " + tableName;
                    try (Statement stmt = conn.createStatement();
                         ResultSet rs = stmt.executeQuery(dataSql)) {
                        
                        ResultSetMetaData meta = rs.getMetaData();
                        int columnCount = meta.getColumnCount();
                        
                        // CSV标题行
                        for (int i = 1; i <= columnCount; i++) {
                            if (i > 1) exportResult.append(",");
                            exportResult.append("\"").append(meta.getColumnName(i)).append("\"");
                        }
                        exportResult.append("\n");
                        
                        // CSV数据行
                        while (rs.next()) {
                            for (int i = 1; i <= columnCount; i++) {
                                if (i > 1) exportResult.append(",");
                                String value = rs.getString(i);
                                if (value != null) {
                                    exportResult.append("\"").append(value.replace("\"", "\"\"")).append("\"");
                                }
                            }
                            exportResult.append("\n");
                        }
                    }
                }
                
                // 设置响应头并输出文件
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + tableName + "." + exportFormat);
                response.getWriter().write(exportResult.toString());
                response.getWriter().flush();
                return; // 直接返回，不渲染页面
            }
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>导出失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理SQL导入
    if ("import_sql".equals(action) && isConnected && conn != null) {
        try {
            String importSql = request.getParameter("import_sql");
            if (importSql != null && !importSql.trim().isEmpty()) {
                // 分割多个SQL语句
                String[] sqlStatements = importSql.split(";");
                int successCount = 0;
                int totalCount = 0;
                
                for (String sql : sqlStatements) {
                    sql = sql.trim();
                    if (!sql.isEmpty()) {
                        totalCount++;
                        try (Statement stmt = conn.createStatement()) {
                            stmt.executeUpdate(sql);
                            successCount++;
                        } catch (SQLException e) {
                            // 记录错误但继续执行其他语句
                            queryResult += "<div class='query-result'><div class='error'>SQL语句执行失败: " + htmlEncode(sql) + "<br>错误: " + htmlEncode(e.getMessage()) + "</div></div>";
                        }
                    }
                }
                
                queryResult += "<div class='query-result'><div class='success'>SQL导入完成！成功执行 " + successCount + " / " + totalCount + " 条语句</div></div>";
            }
        } catch (Exception e) {
            queryResult = "<div class='query-result'><div class='error'>SQL导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理CSV导入
    if ("import_csv".equals(action) && isConnected && conn != null) {
        try {
            String tableName = request.getParameter("selected_table");
            String csvData = request.getParameter("csv_data");
            boolean hasHeader = "1".equals(request.getParameter("has_header"));
            
            if (tableName != null && csvData != null && !csvData.trim().isEmpty()) {
                String[] lines = csvData.split("\n");
                if (lines.length > 0) {
                    // 获取表结构
                    List<ColumnInfo> columns = getTableStructure(conn, dbType, tableName);
                    
                    int startRow = hasHeader ? 1 : 0;
                    int successCount = 0;
                    int totalCount = lines.length - startRow;
                    
                    for (int i = startRow; i < lines.length; i++) {
                        String line = lines[i].trim();
                        if (!line.isEmpty()) {
                            // 简单的CSV解析（不处理复杂的引号情况）
                            String[] values = line.split(",");
                            
                            // 构建INSERT语句
                            StringBuilder insertSql = new StringBuilder("INSERT INTO " + tableName + " (");
                            StringBuilder valuesSql = new StringBuilder(" VALUES (");
                            
                            boolean first = true;
                            for (int j = 0; j < Math.min(values.length, columns.size()); j++) {
                                ColumnInfo col = columns.get(j);
                                if (!col.isAutoIncrement) {
                                    if (!first) {
                                        insertSql.append(", ");
                                        valuesSql.append(", ");
                                    }
                                    insertSql.append(col.name);
                                    valuesSql.append("?");
                                    first = false;
                                }
                            }
                            
                            insertSql.append(")").append(valuesSql.toString()).append(")");
                            
                            try (PreparedStatement pstmt = conn.prepareStatement(insertSql.toString())) {
                                int paramIndex = 1;
                                for (int j = 0; j < Math.min(values.length, columns.size()); j++) {
                                    ColumnInfo col = columns.get(j);
                                    if (!col.isAutoIncrement) {
                                        String value = values[j].trim();
                                        // 移除可能的引号
                                        if (value.startsWith("\"") && value.endsWith("\"")) {
                                            value = value.substring(1, value.length() - 1);
                                        }
                                        
                                        if ("NULL".equalsIgnoreCase(value) || value.isEmpty()) {
                                            pstmt.setNull(paramIndex, java.sql.Types.VARCHAR);
                                        } else {
                                            pstmt.setString(paramIndex, value);
                                        }
                                        paramIndex++;
                                    }
                                }
                                
                                pstmt.executeUpdate();
                                successCount++;
                            } catch (SQLException e) {
                                // 记录错误但继续处理其他行
                                queryResult += "<div class='query-result'><div class='error'>第 " + (i + 1) + " 行导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
                            }
                        }
                    }
                    
                    queryResult += "<div class='query-result'><div class='success'>CSV导入完成！成功导入 " + successCount + " / " + totalCount + " 行数据</div></div>";
                }
            }
        } catch (Exception e) {
            queryResult = "<div class='query-result'><div class='error'>CSV导入失败: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理SQL查询
    if ("query".equals(action) && sqlQuery != null && !sqlQuery.trim().isEmpty() && isConnected && conn != null) {
        try {
            // 如果选择了数据库，先切换到该数据库
            if (selectedDatabase != null && !selectedDatabase.isEmpty() && "mysql".equals(dbType)) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("USE " + selectedDatabase);
                }
            }
            
            StringBuilder result = new StringBuilder();
            String trimmedQuery = sqlQuery.trim().toLowerCase();
            
            // 判断是否是查询语句
            if (trimmedQuery.startsWith("select") || trimmedQuery.startsWith("show") || 
                trimmedQuery.startsWith("describe") || trimmedQuery.startsWith("desc")) {
                // 执行查询
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(sqlQuery)) {
                    
                    ResultSetMetaData meta = rs.getMetaData();
                    int columnCount = meta.getColumnCount();
                    
                    result.append("<div class='query-result'>");
                    result.append("<h4>查询结果:</h4>");
                    result.append("<table class='result-table'>");
                    
                    // 表头
                    result.append("<thead><tr>");
                    for (int i = 1; i <= columnCount; i++) {
                        result.append("<th>").append(htmlEncode(meta.getColumnName(i))).append("</th>");
                    }
                    result.append("</tr></thead>");
                    
                    // 数据行
                    result.append("<tbody>");
                    int rowCount = 0;
                    while (rs.next() && rowCount < 1000) { // 限制最多显示1000行
                        result.append("<tr>");
                        for (int i = 1; i <= columnCount; i++) {
                            String value = rs.getString(i);
                            result.append("<td>").append(value != null ? htmlEncode(value) : "<em>NULL</em>").append("</td>");
                        }
                        result.append("</tr>");
                        rowCount++;
                    }
                    result.append("</tbody></table>");
                    
                    if (rowCount >= 1000) {
                        result.append("<p class='warning'>注意: 结果已限制在1000行以内</p>");
                    }
                    
                    result.append("<p class='info'>共返回 ").append(rowCount).append(" 行数据</p>");
                    result.append("</div>");
                }
            } else {
                // 执行非查询语句（INSERT, UPDATE, DELETE等）
                try (Statement stmt = conn.createStatement()) {
                    int affected = stmt.executeUpdate(sqlQuery);
                    result.append("<div class='query-result'>");
                    result.append("<h4>执行结果:</h4>");
                    result.append("<p class='success'>SQL语句执行成功！受影响的行数: ").append(affected).append("</p>");
                    result.append("</div>");
                }
            }
            
            queryResult = result.toString();
            
        } catch (SQLException e) {
            queryResult = "<div class='query-result'><div class='error'>SQL执行错误: " + htmlEncode(e.getMessage()) + "</div></div>";
        }
    }
    
    // 处理断开连接
    if ("disconnect".equals(action)) {
        if (conn != null) {
            try { conn.close(); } catch (Exception e) {}
        }
        session.invalidate();
        isConnected = false;
        conn = null;
    }
%>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具 - JSP Adminer</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .login-form {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border-left: 4px solid #c62828;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border-left: 4px solid #2e7d32;
        }
        
        .connection-info {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .connection-info h3 {
            margin-top: 0;
            color: #333;
        }
        
        .main-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .database-browser {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 2rem;
            min-height: 500px;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .sidebar h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .database-list, .table-list {
            margin: 0;
            padding: 0;
        }
        
        .database-item, .table-item {
            margin-bottom: 0.5rem;
        }
        
        .database-item a, .table-item a {
            display: block;
            padding: 0.5rem;
            color: #495057;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .database-item a:hover, .table-item a:hover {
            background: #e9ecef;
            color: #333;
        }
        
        .database-item a.active, .table-item a.active {
            background: #667eea;
            color: white;
        }
        
        .content-area {
            padding: 1rem;
        }
        
        .content-area h3 {
            margin-top: 0;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .no-data {
            color: #6c757d;
            font-style: italic;
            padding: 1rem;
            text-align: center;
        }
        
        .welcome-message {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        
        .quick-actions {
            text-align: left;
            margin-top: 2rem;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .quick-actions h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .sql-query-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .sql-query-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .sql-query-section textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .sql-query-section textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .query-result {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .query-result h4 {
            margin-top: 0;
            color: #333;
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .result-table th,
        .result-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .result-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .result-table tr:hover {
            background: #f8f9fa;
        }
        
        .result-table td em {
            color: #6c757d;
            font-style: italic;
        }
        
        .query-result .info {
            color: #495057;
            font-size: 0.9rem;
            margin: 0.5rem 0;
        }
        
        .query-result .warning {
            color: #856404;
            background: #fff3cd;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .query-result .success {
            color: #155724;
            background: #d4edda;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        /* 触发器样式 */
        .trigger-list {
            margin-top: 1rem;
        }
        
        .trigger-item {
            margin-bottom: 0.5rem;
        }
        
        .trigger-name {
            display: block;
            padding: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 表管理样式 */
        .table-management {
            width: 100%;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table-header h3 {
            margin: 0;
            color: #333;
        }
        
        .table-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .table-tabs {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 1rem;
        }
        
        .tab-link {
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .tab-link:hover {
            color: #333;
            background: #f8f9fa;
        }
        
        .tab-link.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            min-height: 400px;
        }
        
        /* 表结构样式 */
        .table-structure {
            width: 100%;
        }
        
        .structure-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .structure-table th,
        .structure-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .structure-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: bold;
        }
        
        .structure-table tr:hover {
            background: #f8f9fa;
        }
        
        .structure-actions {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
        }
        
        /* 数据表样式 */
        .table-data {
            width: 100%;
        }
        
        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .data-info {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .data-table .actions {
            white-space: nowrap;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .page-info {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 按钮样式 */
        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
        }
        
        .btn-edit:hover {
            background: #218838;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        /* 数据库概览样式 */
        .database-overview {
            text-align: center;
            padding: 2rem;
        }
        
        .database-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 导入导出样式 */
        .import-export-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .export-section, .import-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .export-section h4, .import-section h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .import-options {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .import-method {
            background: white;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .import-method h5 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .export-section .form-group,
        .import-method .form-group {
            margin-bottom: 1rem;
        }
        
        .export-section label,
        .import-method label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .export-section select,
        .import-method textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .import-method textarea {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            resize: vertical;
        }
        
        @media (max-width: 768px) {
            .import-export-container {
                grid-template-columns: 1fr;
            }
        }
        
        /* 表单样式 */
        .insert-form, .edit-form {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .insert-form h4, .edit-form h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .form-columns {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .column-type {
            color: #6c757d;
            font-weight: normal;
            font-size: 0.9rem;
        }
        
        .primary-key {
            color: #ffc107;
            margin-left: 0.25rem;
        }
        
        .auto-inc {
            background: #17a2b8;
            color: white;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-size: 0.7rem;
            margin-left: 0.25rem;
        }
        
        .required {
            color: #dc3545;
            margin-left: 0.25rem;
        }
        
        .form-group input[type="text"] {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-group input[readonly] {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-start;
            margin-top: 1rem;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        /* 改进的响应式 */
        @media (max-width: 768px) {
            .form-columns {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .table-actions {
                flex-direction: column;
                gap: 0.25rem;
            }
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .database-browser {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据库管理工具 - JSP Adminer</h1>
    </div>
    
    <div class="container">
        <% if (!isConnected) { %>
            <!-- 登录表单 -->
            <div class="login-form">
                <h2>数据库连接</h2>
                
                <% if (!errorMessage.isEmpty()) { %>
                    <div class="error">
                        <%= htmlEncode(errorMessage) %>
                    </div>
                <% } %>
                
                <form method="post" action="">
                    <input type="hidden" name="action" value="connect">
                    
                    <div class="form-group">
                        <label for="db_type">数据库类型:</label>
                        <select name="db_type" id="db_type" required onchange="updateDefaultPort()">
                            <option value="">请选择数据库类型</option>
                            <option value="mysql" <%= "mysql".equals(dbType) ? "selected" : "" %>>MySQL</option>
                            <option value="mssql" <%= "mssql".equals(dbType) ? "selected" : "" %>>SQL Server</option>
                            <option value="oracle" <%= "oracle".equals(dbType) ? "selected" : "" %>>Oracle</option>
                            <option value="tibero" <%= "tibero".equals(dbType) ? "selected" : "" %>>Tibero</option>
                            <option value="postgresql" <%= "postgresql".equals(dbType) ? "selected" : "" %>>PostgreSQL</option>
                        </select>
                    </div>
                    
                    <div class="two-column">
                        <div class="form-group">
                            <label for="host">主机:</label>
                            <input type="text" name="host" id="host" value="<%= host != null ? htmlEncode(host) : "localhost" %>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="port">端口:</label>
                            <input type="text" name="port" id="port" value="<%= port != null ? htmlEncode(port) : "" %>" placeholder="默认端口">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="database">数据库:</label>
                        <input type="text" name="database" id="database" value="<%= database != null ? htmlEncode(database) : "" %>" placeholder="数据库名称（可选）">
                    </div>
                    
                    <div class="two-column">
                        <div class="form-group">
                            <label for="username">用户名:</label>
                            <input type="text" name="username" id="username" value="<%= username != null ? htmlEncode(username) : "" %>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">密码:</label>
                            <input type="password" name="password" id="password" value="">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">连接数据库</button>
                </form>
            </div>
        <% } else { %>
            <!-- 连接成功，显示主界面 -->
            <div class="connection-info">
                <h3>当前连接信息</h3>
                <p><strong>数据库类型:</strong> <%= htmlEncode(dbType.toUpperCase()) %></p>
                <p><strong>主机:</strong> <%= htmlEncode(host) %>:<%= htmlEncode(port) %></p>
                <p><strong>数据库:</strong> <%= database != null && !database.isEmpty() ? htmlEncode(database) : "未指定" %></p>
                <p><strong>用户名:</strong> <%= htmlEncode(username) %></p>
                <a href="?action=disconnect" class="btn btn-danger">断开连接</a>
            </div>
            
            <div class="main-content">
                <% if (!errorMessage.isEmpty()) { %>
                    <div class="error">
                        <%= htmlEncode(errorMessage) %>
                    </div>
                <% } %>
                
                <div class="database-browser">
                    <div class="sidebar">
                        <h3>数据库列表</h3>
                        <% if (!databases.isEmpty()) { %>
                            <div class="database-list">
                                <% for (String db : databases) { %>
                                    <div class="database-item">
                                        <a href="?selected_db=<%= java.net.URLEncoder.encode(db, "UTF-8") %>" 
                                           class="<%= db.equals(selectedDatabase) ? "active" : "" %>">
                                            📁 <%= htmlEncode(db) %>
                                        </a>
                                    </div>
                                <% } %>
                            </div>
                        <% } else { %>
                            <p class="no-data">暂无数据库</p>
                        <% } %>
                        
                        <% if (!tables.isEmpty()) { %>
                            <h3>表列表</h3>
                            <div class="table-list">
                                <% for (String table : tables) { %>
                                    <div class="table-item">
                                        <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(table, "UTF-8") %>" 
                                           class="<%= table.equals(selectedTable) ? "active" : "" %>">
                                            📋 <%= htmlEncode(table) %>
                                        </a>
                                    </div>
                                <% } %>
                            </div>
                        <% } %>
                        
                        <% if (!triggers.isEmpty()) { %>
                            <h3>触发器列表</h3>
                            <div class="trigger-list">
                                <% for (String trigger : triggers) { %>
                                    <div class="trigger-item">
                                        <span class="trigger-name">⚡ <%= htmlEncode(trigger) %></span>
                                    </div>
                                <% } %>
                            </div>
                        <% } %>
                    </div>
                    
                    <div class="content-area">
                        <% if (selectedTable != null && !selectedTable.isEmpty()) { %>
                            <!-- 表管理界面 -->
                            <div class="table-management">
                                <div class="table-header">
                                    <h3>表: <%= htmlEncode(selectedTable) %></h3>
                                    <div class="table-actions">
                                        <button class="btn btn-small" onclick="showCreateForm()">新增行</button>
                                        <button class="btn btn-small btn-warning" onclick="truncateTable()">清空表</button>
                                        <button class="btn btn-small btn-danger" onclick="dropTable()">删除表</button>
                                    </div>
                                </div>
                                
                                <div class="table-tabs">
                                    <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=data" 
                                       class="tab-link <%= (view == null || "data".equals(view)) ? "active" : "" %>">数据</a>
                                    <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=structure" 
                                       class="tab-link <%= "structure".equals(view) ? "active" : "" %>">结构</a>
                                    <a href="?selected_db=<%= java.net.URLEncoder.encode(selectedDatabase != null ? selectedDatabase : (database != null ? database : ""), "UTF-8") %>&selected_table=<%= java.net.URLEncoder.encode(selectedTable, "UTF-8") %>&view=export" 
                                       class="tab-link <%= "export".equals(view) ? "active" : "" %>">导出</a>
                                </div>
                                
                                <div class="tab-content">
                                    <% if (view == null || "data".equals(view)) { %>
                                        <!-- 数据视图 -->
                                        <!-- 新增行表单 -->
                                        <div id="insertForm" class="insert-form" style="display: none;">
                                            <h4>新增数据行</h4>
                                            <form method="post" action="">
                                                <input type="hidden" name="action" value="insert_row">
                                                <input type="hidden" name="table_name" value="<%= htmlEncode(selectedTable) %>">
                                                <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                
                                                <div class="form-columns">
                                                    <% for (ColumnInfo col : tableStructure) { %>
                                                        <% if (!col.isAutoIncrement) { %>
                                                            <div class="form-group">
                                                                <label for="new_<%= htmlEncode(col.name) %>">
                                                                    <%= htmlEncode(col.name) %>
                                                                    <span class="column-type">(<%= htmlEncode(col.type) %>)</span>
                                                                    <% if (col.isPrimaryKey) { %><span class="primary-key">🔑</span><% } %>
                                                                    <% if (!"YES".equals(col.nullable) && !"Y".equals(col.nullable)) { %><span class="required">*</span><% } %>
                                                                </label>
                                                                <input type="text" 
                                                                       name="new_<%= htmlEncode(col.name) %>" 
                                                                       id="new_<%= htmlEncode(col.name) %>"
                                                                       value="<%= col.defaultValue != null ? htmlEncode(col.defaultValue) : "" %>"
                                                                       placeholder="<%= !"YES".equals(col.nullable) && !"Y".equals(col.nullable) ? "必填" : "可选，留空为NULL" %>">
                                                            </div>
                                                        <% } %>
                                                    <% } %>
                                                </div>
                                                
                                                <div class="form-actions">
                                                    <button type="submit" class="btn">保存</button>
                                                    <button type="button" class="btn btn-secondary" onclick="hideInsertForm()">取消</button>
                                                </div>
                                            </form>
                                        </div>
                                        
                                        <!-- 编辑行表单 -->
                                        <div id="editForm" class="edit-form" style="display: none;">
                                            <h4>编辑数据行</h4>
                                            <form method="post" action="">
                                                <input type="hidden" name="action" value="edit_row">
                                                <input type="hidden" name="table_name" value="<%= htmlEncode(selectedTable) %>">
                                                <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                <input type="hidden" name="where_clause" id="edit_where_clause" value="">
                                                
                                                <div class="form-columns">
                                                    <% for (ColumnInfo col : tableStructure) { %>
                                                        <div class="form-group">
                                                            <label for="col_<%= htmlEncode(col.name) %>">
                                                                <%= htmlEncode(col.name) %>
                                                                <span class="column-type">(<%= htmlEncode(col.type) %>)</span>
                                                                <% if (col.isPrimaryKey) { %><span class="primary-key">🔑</span><% } %>
                                                                <% if (col.isAutoIncrement) { %><span class="auto-inc">AUTO</span><% } %>
                                                            </label>
                                                            <input type="text" 
                                                                   name="col_<%= htmlEncode(col.name) %>" 
                                                                   id="col_<%= htmlEncode(col.name) %>"
                                                                   <%= col.isAutoIncrement ? "readonly" : "" %>
                                                                   placeholder="输入新值，留空为NULL">
                                                        </div>
                                                    <% } %>
                                                </div>
                                                
                                                <div class="form-actions">
                                                    <button type="submit" class="btn">更新</button>
                                                    <button type="button" class="btn btn-secondary" onclick="hideEditForm()">取消</button>
                                                </div>
                                            </form>
                                        </div>
                                        
                                        <%= tableDataHtml %>
                                    <% } else if ("structure".equals(view)) { %>
                                        <!-- 结构视图 -->
                                        <div class="table-structure">
                                            <h4>表结构</h4>
                                            <table class="structure-table">
                                                <thead>
                                                    <tr>
                                                        <th>列名</th>
                                                        <th>数据类型</th>
                                                        <th>允许NULL</th>
                                                        <th>默认值</th>
                                                        <th>主键</th>
                                                        <th>自增</th>
                                                        <th>注释</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <% for (ColumnInfo col : tableStructure) { %>
                                                        <tr>
                                                            <td><strong><%= htmlEncode(col.name) %></strong></td>
                                                            <td><%= htmlEncode(col.type) %></td>
                                                            <td><%= "YES".equals(col.nullable) || "Y".equals(col.nullable) ? "是" : "否" %></td>
                                                            <td><%= col.defaultValue != null ? htmlEncode(col.defaultValue) : "<em>无</em>" %></td>
                                                            <td><%= col.isPrimaryKey ? "🔑" : "" %></td>
                                                            <td><%= col.isAutoIncrement ? "✓" : "" %></td>
                                                            <td><%= col.comment != null && !col.comment.isEmpty() ? htmlEncode(col.comment) : "<em>无</em>" %></td>
                                                            <td>
                                                                <button class="btn-small btn-edit">编辑</button>
                                                                <button class="btn-small btn-delete">删除</button>
                                                            </td>
                                                        </tr>
                                                    <% } %>
                                                </tbody>
                                            </table>
                                            
                                            <div class="structure-actions">
                                                <button class="btn" onclick="addColumn()">添加列</button>
                                                <button class="btn" onclick="addIndex()">添加索引</button>
                                            </div>
                                        </div>
                                    <% } else if ("export".equals(view)) { %>
                                        <!-- 导出视图 -->
                                        <div class="import-export-container">
                                            <div class="export-section">
                                                <h4>数据导出</h4>
                                                <form method="post" action="">
                                                    <input type="hidden" name="action" value="export">
                                                    <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                    
                                                    <div class="form-group">
                                                        <label>导出格式:</label>
                                                        <select name="export_format">
                                                            <option value="sql">SQL</option>
                                                            <option value="csv">CSV</option>
                                                            <option value="json">JSON</option>
                                                            <option value="xml">XML</option>
                                                        </select>
                                                    </div>
                                                    
                                                    <div class="form-group">
                                                        <label>
                                                            <input type="checkbox" name="export_structure" value="1" checked> 包含表结构
                                                        </label>
                                                    </div>
                                                    
                                                    <div class="form-group">
                                                        <label>
                                                            <input type="checkbox" name="export_data" value="1" checked> 包含数据
                                                        </label>
                                                    </div>
                                                    
                                                    <button type="submit" class="btn">开始导出</button>
                                                </form>
                                            </div>
                                            
                                            <div class="import-section">
                                                <h4>数据导入</h4>
                                                <div class="import-options">
                                                    <div class="import-method">
                                                        <h5>SQL 语句导入</h5>
                                                        <form method="post" action="">
                                                            <input type="hidden" name="action" value="import_sql">
                                                            <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                            
                                                            <div class="form-group">
                                                                <label for="import_sql">SQL 语句:</label>
                                                                <textarea name="import_sql" id="import_sql" rows="8" placeholder="输入INSERT语句或其他SQL语句..."></textarea>
                                                            </div>
                                                            
                                                            <button type="submit" class="btn">执行导入</button>
                                                        </form>
                                                    </div>
                                                    
                                                    <div class="import-method">
                                                        <h5>CSV 数据导入</h5>
                                                        <form method="post" action="" enctype="multipart/form-data">
                                                            <input type="hidden" name="action" value="import_csv">
                                                            <input type="hidden" name="selected_table" value="<%= htmlEncode(selectedTable) %>">
                                                            <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase != null ? selectedDatabase : (database != null ? database : "")) %>">
                                                            
                                                            <div class="form-group">
                                                                <label for="csv_data">CSV 数据:</label>
                                                                <textarea name="csv_data" id="csv_data" rows="8" placeholder="粘贴CSV数据或输入逗号分隔的数据..."></textarea>
                                                            </div>
                                                            
                                                            <div class="form-group">
                                                                <label>
                                                                    <input type="checkbox" name="has_header" value="1" checked> 第一行是列标题
                                                                </label>
                                                            </div>
                                                            
                                                            <button type="submit" class="btn">导入CSV</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                            
                        <% } else if (selectedDatabase != null || (!tables.isEmpty())) { %>
                            <div class="database-overview">
                                <h3>数据库: 
                                    <% if (selectedDatabase != null) { %>
                                        <%= htmlEncode(selectedDatabase) %>
                                    <% } else if (database != null && !database.isEmpty()) { %>
                                        <%= htmlEncode(database) %>
                                    <% } %>
                                </h3>
                                
                                <div class="database-stats">
                                    <div class="stat-item">
                                        <span class="stat-number"><%= tables.size() %></span>
                                        <span class="stat-label">表</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number"><%= triggers.size() %></span>
                                        <span class="stat-label">触发器</span>
                                    </div>
                                </div>
                                
                                <p>请从左侧选择一个表来查看详细信息。</p>
                            </div>
                        <% } else { %>
                            <div class="welcome-message">
                                <h3>欢迎使用数据库管理工具</h3>
                                <p>请从左侧选择一个数据库开始浏览。</p>
                                
                                <div class="quick-actions">
                                    <h4>快速操作：</h4>
                                    <ul>
                                        <li>点击左侧数据库名称查看表列表</li>
                                        <li>点击表名称查看表结构和数据</li>
                                        <li>使用下方的SQL查询功能执行自定义查询</li>
                                    </ul>
                                </div>
                            </div>
                        <% } %>
                        
                        <!-- SQL查询区域 -->
                        <div class="sql-query-section">
                            <h3>SQL 查询</h3>
                            <form method="post" action="">
                                <input type="hidden" name="action" value="query">
                                <% if (selectedDatabase != null) { %>
                                    <input type="hidden" name="selected_db" value="<%= htmlEncode(selectedDatabase) %>">
                                <% } %>
                                
                                <div class="form-group">
                                    <label for="sql_query">SQL 语句:</label>
                                    <textarea name="sql_query" id="sql_query" rows="6" placeholder="输入您的SQL查询语句..."><%= sqlQuery != null ? htmlEncode(sqlQuery) : "" %></textarea>
                                </div>
                                
                                <button type="submit" class="btn">执行查询</button>
                            </form>
                            
                            <!-- 查询结果显示 -->
                            <% if (!queryResult.isEmpty()) { %>
                                <%= queryResult %>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
    
    <script>
        // 根据数据库类型更新默认端口
        function updateDefaultPort() {
            const dbType = document.getElementById('db_type').value;
            const portField = document.getElementById('port');
            
            const defaultPorts = {
                'mysql': '3306',
                'mssql': '1433',
                'oracle': '1521',
                'tibero': '8629',
                'postgresql': '5432'
            };
            
            if (defaultPorts[dbType]) {
                portField.placeholder = '默认: ' + defaultPorts[dbType];
                if (!portField.value) {
                    portField.value = defaultPorts[dbType];
                }
            } else {
                portField.placeholder = '端口号';
            }
        }
        
        // 页面加载时更新默认端口
        document.addEventListener('DOMContentLoaded', function() {
            updateDefaultPort();
        });
        
        // 表管理功能
        function showCreateForm() {
            document.getElementById('insertForm').style.display = 'block';
            document.getElementById('editForm').style.display = 'none';
        }
        
        function hideInsertForm() {
            document.getElementById('insertForm').style.display = 'none';
        }
        
        function hideEditForm() {
            document.getElementById('editForm').style.display = 'none';
        }
        
        function truncateTable() {
            if (confirm('确定要清空表吗？此操作不可撤销！')) {
                var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                if (currentTable) {
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="table_operation">' +
                                   '<input type="hidden" name="operation" value="truncate">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">' +
                                   '<input type="hidden" name="selected_table" value="' + currentTable + '">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function dropTable() {
            if (confirm('确定要删除表吗？此操作不可撤销！')) {
                var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                if (currentTable) {
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="table_operation">' +
                                   '<input type="hidden" name="operation" value="drop">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function editRow(rowNum) {
            var row = document.querySelector('tr[data-row="' + rowNum + '"]');
            if (row) {
                var whereClause = row.getAttribute('data-where');
                document.getElementById('edit_where_clause').value = whereClause;
                
                // 填充表单字段
                var cells = row.querySelectorAll('td[data-column]');
                cells.forEach(function(cell) {
                    var columnName = cell.getAttribute('data-column');
                    var value = cell.getAttribute('data-value');
                    var input = document.getElementById('col_' + columnName);
                    if (input) {
                        input.value = value || '';
                    }
                });
                
                document.getElementById('editForm').style.display = 'block';
                document.getElementById('insertForm').style.display = 'none';
            }
        }
        
        function deleteRow(rowNum) {
            if (confirm('确定要删除这行数据吗？')) {
                var row = document.querySelector('tr[data-row="' + rowNum + '"]');
                if (row) {
                    var whereClause = row.getAttribute('data-where');
                    var currentTable = '<%= selectedTable != null ? selectedTable : "" %>';
                    
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML = '<input type="hidden" name="action" value="delete_row">' +
                                   '<input type="hidden" name="table_name" value="' + currentTable + '">' +
                                   '<input type="hidden" name="where_clause" value="' + whereClause + '">' +
                                   '<input type="hidden" name="selected_db" value="<%= selectedDatabase != null ? selectedDatabase : (database != null ? database : "") %>">' +
                                   '<input type="hidden" name="selected_table" value="' + currentTable + '">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
        
        function addColumn() {
            alert('添加列功能开发中...');
        }
        
        function addIndex() {
            alert('添加索引功能开发中...');
        }
    </script>
</body>
</html> 
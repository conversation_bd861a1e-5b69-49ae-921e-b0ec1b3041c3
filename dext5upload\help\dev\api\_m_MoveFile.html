﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">          
    <h3 class="title">DEXT5 Upload :: MoveFirstFile, MoveForwardFile, MoveBackwardFile, MoveEndFile</h3>
    <p class="ttl">void MoveFirstFile (uploadID), void MoveForwardFile (uploadID), <br />void MoveBackwardFile (uploadID), void MoveEndFile (uploadID)</p>
    <p class="txt">
        파일의 순서를 바꿉니다. 
    </p>
    <p class="mttl01">return value</p>
    <p class="txt">
        없음. 
    </p>            
    <p class="mttl01">parameters</p>     
    <p class="txt">
        <span class="firebrick">MoveFirstFile</span>&nbsp;&nbsp;파일을 맨 앞으로 이동<br />
        <span class="firebrick">MoveForwardFile</span>&nbsp;&nbsp;파일을 앞으로 이동<br />
        <span class="firebrick">MoveBackwardFile</span>&nbsp;&nbsp;파일을 뒤로 이동<br />
        <span class="firebrick">MoveEndFile</span>&nbsp;&nbsp;파일을 맨 뒤로 이동<br />
        <span class="firebrick">uploadID</span>&nbsp;&nbsp;순서 바꾸기할 업로드의 id를 의미합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        없음.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	
&#60;script type="text/javascript"&#62;           
        function moveFirstFile() {
                DEXT5UPLOAD.MoveFirstFile();
        }
        function moveForwardFile() {
                DEXT5UPLOAD.MoveForwardFile();
        }
        function moveBackwardFile() {
                DEXT5UPLOAD.MoveBackwardFile();
        }
        function moveEndFile() {
                DEXT5UPLOAD.MoveEndFile();
        }   
&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;

&#60;button type="button" onclick="moveFirstFile()"&#62;맨 앞으로&#60;/button&#62;
&#60;button type="button" onclick="moveForwardFile()"&#62;앞으로&#60;/button&#62;
&#60;button type="button" onclick="moveBackwardFile()"&#62;뒤로&#60;/button&#62;
&#60;button type="button" onclick="moveEndFile()"&#62;맨 뒤&#60;/button&#62;

&#60;div style="width:800px;height:200px"&#62;  
        &#60;script type="text/javascript"&#62;
            new Dext5Upload("upload1");
        &#60;/script&#62;
&#60;/div&#62;

&#60;!-- ..... 생략 ..... --&#62;

&#60;/body&#62;
&#60;/html&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">
    <span class="pl_type">[플러그인 전용]</span>               
    <h3 class="title">DEXT5 Upload :: Config :: FolderTransfer</h3>
    <p class="ttl">config.FolderTransfer</p>
    <p class="txt">
        폴더 구조로 업로드를 설정합니다.<br />
        폴더 상태로 폴더를 추가, 폴더 안에 있는 파일들을 한번에 추가 합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        기본값은 "0"이고, "1"로 설정시 폴더 구조로 업로드 설정합니다.
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 폴더 구조로 업로드를 설정합니다.
        DEXT5UPLOAD.config.FolderTransfer = '1';

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


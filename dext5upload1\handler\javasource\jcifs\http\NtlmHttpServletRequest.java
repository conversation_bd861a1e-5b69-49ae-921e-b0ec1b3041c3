package jcifs.http;

import java.security.Principal;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

/* loaded from: jcifs-1.3.18.jar:jcifs/http/NtlmHttpServletRequest.class */
class NtlmHttpServletRequest extends HttpServletRequestWrapper {
    Principal principal;

    NtlmHttpServletRequest(HttpServletRequest req, Principal principal) {
        super(req);
        this.principal = principal;
    }

    @Override // javax.servlet.http.HttpServletRequestWrapper, javax.servlet.http.HttpServletRequest
    public String getRemoteUser() {
        return this.principal.getName();
    }

    @Override // javax.servlet.http.HttpServletRequestWrapper, javax.servlet.http.HttpServletRequest
    public Principal getUserPrincipal() {
        return this.principal;
    }

    @Override // javax.servlet.http.HttpServletRequestWrapper, javax.servlet.http.HttpServletRequest
    public String getAuthType() {
        return "NTLM";
    }
}

﻿using System;
using System.Web;

namespace Raonwiz.Dext5.Process.Entity
{
	// Token: 0x02000028 RID: 40
	[Serializable]
	public class UploadEventEntity
	{
		// Token: 0x170000CF RID: 207
		// (get) Token: 0x06000268 RID: 616 RVA: 0x0001E53D File Offset: 0x0001C73D
		// (set) Token: 0x06000269 RID: 617 RVA: 0x0001E545 File Offset: 0x0001C745
		public HttpContext Context
		{
			get
			{
				return this._context;
			}
			set
			{
				this._context = value;
			}
		}

		// Token: 0x170000D0 RID: 208
		// (get) Token: 0x0600026A RID: 618 RVA: 0x0001E54E File Offset: 0x0001C74E
		// (set) Token: 0x0600026B RID: 619 RVA: 0x0001E556 File Offset: 0x0001C756
		public string NewFileLocation
		{
			get
			{
				return this._newFileLocation;
			}
			set
			{
				this._newFileLocation = value;
			}
		}

		// Token: 0x170000D1 RID: 209
		// (get) Token: 0x0600026C RID: 620 RVA: 0x0001E55F File Offset: 0x0001C75F
		// (set) Token: 0x0600026D RID: 621 RVA: 0x0001E567 File Offset: 0x0001C767
		public string ResponseFileServerPath
		{
			get
			{
				return this._responseFileServerPath;
			}
			set
			{
				this._responseFileServerPath = value;
			}
		}

		// Token: 0x170000D2 RID: 210
		// (get) Token: 0x0600026E RID: 622 RVA: 0x0001E570 File Offset: 0x0001C770
		// (set) Token: 0x0600026F RID: 623 RVA: 0x0001E578 File Offset: 0x0001C778
		public string ResponseFileName
		{
			get
			{
				return this._responseFileName;
			}
			set
			{
				this._responseFileName = value;
			}
		}

		// Token: 0x170000D3 RID: 211
		// (get) Token: 0x06000270 RID: 624 RVA: 0x0001E581 File Offset: 0x0001C781
		// (set) Token: 0x06000271 RID: 625 RVA: 0x0001E589 File Offset: 0x0001C789
		public string ResponseCustomValue
		{
			get
			{
				return this._responseCustomValue;
			}
			set
			{
				this._responseCustomValue = value;
			}
		}

		// Token: 0x170000D4 RID: 212
		// (get) Token: 0x06000272 RID: 626 RVA: 0x0001E592 File Offset: 0x0001C792
		// (set) Token: 0x06000273 RID: 627 RVA: 0x0001E59A File Offset: 0x0001C79A
		public string ResponseGroupId
		{
			get
			{
				return this._responseGroupId;
			}
			set
			{
				this._responseGroupId = value;
			}
		}

		// Token: 0x170000D5 RID: 213
		// (get) Token: 0x06000274 RID: 628 RVA: 0x0001E5A3 File Offset: 0x0001C7A3
		// (set) Token: 0x06000275 RID: 629 RVA: 0x0001E5AB File Offset: 0x0001C7AB
		public string FileIndex
		{
			get
			{
				return this._fileIndex;
			}
			set
			{
				this._fileIndex = value;
			}
		}

		// Token: 0x170000D6 RID: 214
		// (get) Token: 0x06000276 RID: 630 RVA: 0x0001E5B4 File Offset: 0x0001C7B4
		// (set) Token: 0x06000277 RID: 631 RVA: 0x0001E5BC File Offset: 0x0001C7BC
		public string[] DownloadFilePath
		{
			get
			{
				return this._downloadFilePath;
			}
			set
			{
				this._downloadFilePath = value;
			}
		}

		// Token: 0x170000D7 RID: 215
		// (get) Token: 0x06000278 RID: 632 RVA: 0x0001E5C5 File Offset: 0x0001C7C5
		// (set) Token: 0x06000279 RID: 633 RVA: 0x0001E5CD File Offset: 0x0001C7CD
		public string[] DownloadFileName
		{
			get
			{
				return this._downloadFileName;
			}
			set
			{
				this._downloadFileName = value;
			}
		}

		// Token: 0x170000D8 RID: 216
		// (get) Token: 0x0600027A RID: 634 RVA: 0x0001E5D6 File Offset: 0x0001C7D6
		// (set) Token: 0x0600027B RID: 635 RVA: 0x0001E5DE File Offset: 0x0001C7DE
		public string[] DownloadCustomValue
		{
			get
			{
				return this._downloadCustomValue;
			}
			set
			{
				this._downloadCustomValue = value;
			}
		}

		// Token: 0x170000D9 RID: 217
		// (get) Token: 0x0600027C RID: 636 RVA: 0x0001E5E7 File Offset: 0x0001C7E7
		// (set) Token: 0x0600027D RID: 637 RVA: 0x0001E5EF File Offset: 0x0001C7EF
		public bool UseDownloadServerFileName
		{
			get
			{
				return this._useDownloadServerFileName;
			}
			set
			{
				this._useDownloadServerFileName = value;
			}
		}

		// Token: 0x0400014F RID: 335
		private HttpContext _context;

		// Token: 0x04000150 RID: 336
		private string _newFileLocation = string.Empty;

		// Token: 0x04000151 RID: 337
		private string _responseFileServerPath = string.Empty;

		// Token: 0x04000152 RID: 338
		private string _responseFileName = string.Empty;

		// Token: 0x04000153 RID: 339
		private string _responseCustomValue = string.Empty;

		// Token: 0x04000154 RID: 340
		private string _responseGroupId = string.Empty;

		// Token: 0x04000155 RID: 341
		private string _fileIndex = string.Empty;

		// Token: 0x04000156 RID: 342
		private string[] _downloadFilePath;

		// Token: 0x04000157 RID: 343
		private string[] _downloadFileName;

		// Token: 0x04000158 RID: 344
		private string[] _downloadCustomValue;

		// Token: 0x04000159 RID: 345
		private bool _useDownloadServerFileName;
	}
}

package jcifs.smb;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;
import java.security.Principal;
import java.util.ArrayList;
import jcifs.Config;
import jcifs.UniAddress;
import jcifs.dcerpc.DcerpcHandle;
import jcifs.dcerpc.msrpc.MsrpcDfsRootEnum;
import jcifs.dcerpc.msrpc.MsrpcShareEnum;
import jcifs.dcerpc.msrpc.MsrpcShareGetInfo;
import jcifs.netbios.NbtAddress;
import jcifs.util.LogStream;
import net.lingala.zip4j.util.InternalZipConstants;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbFile.class */
public class SmbFile extends URLConnection implements SmbConstants {
    static final int O_RDONLY = 1;
    static final int O_WRONLY = 2;
    static final int O_RDWR = 3;
    static final int O_APPEND = 4;
    static final int O_CREAT = 16;
    static final int O_EXCL = 32;
    static final int O_TRUNC = 64;
    public static final int FILE_NO_SHARE = 0;
    public static final int FILE_SHARE_READ = 1;
    public static final int FILE_SHARE_WRITE = 2;
    public static final int FILE_SHARE_DELETE = 4;
    public static final int ATTR_READONLY = 1;
    public static final int ATTR_HIDDEN = 2;
    public static final int ATTR_SYSTEM = 4;
    public static final int ATTR_VOLUME = 8;
    public static final int ATTR_DIRECTORY = 16;
    public static final int ATTR_ARCHIVE = 32;
    static final int ATTR_COMPRESSED = 2048;
    static final int ATTR_NORMAL = 128;
    static final int ATTR_TEMPORARY = 256;
    static final int ATTR_GET_MASK = 32767;
    static final int ATTR_SET_MASK = 12455;
    static final int DEFAULT_ATTR_EXPIRATION_PERIOD = 5000;
    static final int HASH_DOT = ".".hashCode();
    static final int HASH_DOT_DOT = "..".hashCode();
    static LogStream log = LogStream.getInstance();
    static long attrExpirationPeriod;
    static boolean ignoreCopyToException;
    public static final int TYPE_FILESYSTEM = 1;
    public static final int TYPE_WORKGROUP = 2;
    public static final int TYPE_SERVER = 4;
    public static final int TYPE_SHARE = 8;
    public static final int TYPE_NAMED_PIPE = 16;
    public static final int TYPE_PRINTER = 32;
    public static final int TYPE_COMM = 64;
    private String canon;
    private String share;
    private long createTime;
    private long lastModified;
    private int attributes;
    private long attrExpiration;
    private long size;
    private long sizeExpiration;
    private boolean isExists;
    private int shareAccess;
    private SmbComBlankResponse blank_resp;
    private DfsReferral dfsReferral;
    protected static Dfs dfs;
    NtlmPasswordAuthentication auth;
    SmbTree tree;
    String unc;
    int fid;
    int type;
    boolean opened;
    int tree_num;
    UniAddress[] addresses;
    int addressIndex;

    static {
        try {
            Class.forName("jcifs.Config");
        } catch (ClassNotFoundException cnfe) {
            cnfe.printStackTrace();
        }
        attrExpirationPeriod = Config.getLong("jcifs.smb.client.attrExpirationPeriod", 5000L);
        ignoreCopyToException = Config.getBoolean("jcifs.smb.client.ignoreCopyToException", true);
        dfs = new Dfs();
    }

    public SmbFile(String url) throws MalformedURLException {
        this(new URL((URL) null, url, Handler.SMB_HANDLER));
    }

    public SmbFile(SmbFile context, String name) throws MalformedURLException, UnknownHostException {
        this(context.isWorkgroup0() ? new URL((URL) null, "smb://" + name, Handler.SMB_HANDLER) : new URL(context.url, name, Handler.SMB_HANDLER), context.auth);
    }

    public SmbFile(String context, String name) throws MalformedURLException {
        this(new URL(new URL((URL) null, context, Handler.SMB_HANDLER), name, Handler.SMB_HANDLER));
    }

    public SmbFile(String url, NtlmPasswordAuthentication auth) throws MalformedURLException {
        this(new URL((URL) null, url, Handler.SMB_HANDLER), auth);
    }

    public SmbFile(String url, NtlmPasswordAuthentication auth, int shareAccess) throws MalformedURLException {
        this(new URL((URL) null, url, Handler.SMB_HANDLER), auth);
        if ((shareAccess & (-8)) != 0) {
            throw new RuntimeException("Illegal shareAccess parameter");
        }
        this.shareAccess = shareAccess;
    }

    public SmbFile(String context, String name, NtlmPasswordAuthentication auth) throws MalformedURLException {
        this(new URL(new URL((URL) null, context, Handler.SMB_HANDLER), name, Handler.SMB_HANDLER), auth);
    }

    public SmbFile(String context, String name, NtlmPasswordAuthentication auth, int shareAccess) throws MalformedURLException {
        this(new URL(new URL((URL) null, context, Handler.SMB_HANDLER), name, Handler.SMB_HANDLER), auth);
        if ((shareAccess & (-8)) != 0) {
            throw new RuntimeException("Illegal shareAccess parameter");
        }
        this.shareAccess = shareAccess;
    }

    public SmbFile(SmbFile context, String name, int shareAccess) throws MalformedURLException, UnknownHostException {
        this(context.isWorkgroup0() ? new URL((URL) null, "smb://" + name, Handler.SMB_HANDLER) : new URL(context.url, name, Handler.SMB_HANDLER), context.auth);
        if ((shareAccess & (-8)) != 0) {
            throw new RuntimeException("Illegal shareAccess parameter");
        }
        this.shareAccess = shareAccess;
    }

    public SmbFile(URL url) {
        this(url, new NtlmPasswordAuthentication(url.getUserInfo()));
    }

    public SmbFile(URL url, NtlmPasswordAuthentication auth) {
        super(url);
        this.shareAccess = 7;
        this.blank_resp = null;
        this.dfsReferral = null;
        this.tree = null;
        this.auth = auth == null ? new NtlmPasswordAuthentication(url.getUserInfo()) : auth;
        getUncPath0();
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    SmbFile(jcifs.smb.SmbFile r9, java.lang.String r10, int r11, int r12, long r13, long r15, long r17) throws java.net.MalformedURLException, java.net.UnknownHostException {
        /*
            Method dump skipped, instructions count: 285
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: jcifs.smb.SmbFile.<init>(jcifs.smb.SmbFile, java.lang.String, int, int, long, long, long):void");
    }

    private SmbComBlankResponse blank_resp() {
        if (this.blank_resp == null) {
            this.blank_resp = new SmbComBlankResponse();
        }
        return this.blank_resp;
    }

    /* JADX WARN: Removed duplicated region for block: B:30:0x0130  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0133  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    void resolveDfs(jcifs.smb.ServerMessageBlock r7) throws jcifs.smb.SmbException {
        /*
            Method dump skipped, instructions count: 583
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: jcifs.smb.SmbFile.resolveDfs(jcifs.smb.ServerMessageBlock):void");
    }

    void send(ServerMessageBlock request, ServerMessageBlock response) throws SmbException {
        while (true) {
            resolveDfs(request);
            try {
                this.tree.send(request, response);
                return;
            } catch (DfsReferral dre) {
                if (dre.resolveHashes) {
                    throw dre;
                }
                request.reset();
            }
        }
    }

    static String queryLookup(String query, String param) {
        char[] in = query.toCharArray();
        int eq = 0;
        int st = 0;
        for (int i = 0; i < in.length; i++) {
            char c = in[i];
            if (c == '&') {
                if (eq > st) {
                    String p = new String(in, st, eq - st);
                    if (p.equalsIgnoreCase(param)) {
                        int eq2 = eq + 1;
                        return new String(in, eq2, i - eq2);
                    }
                }
                st = i + 1;
            } else if (c == '=') {
                eq = i;
            }
        }
        if (eq > st) {
            String p2 = new String(in, st, eq - st);
            if (p2.equalsIgnoreCase(param)) {
                int eq3 = eq + 1;
                return new String(in, eq3, in.length - eq3);
            }
            return null;
        }
        return null;
    }

    UniAddress getAddress() throws UnknownHostException {
        if (this.addressIndex == 0) {
            return getFirstAddress();
        }
        return this.addresses[this.addressIndex - 1];
    }

    UniAddress getFirstAddress() throws UnknownHostException {
        this.addressIndex = 0;
        String host = this.url.getHost();
        String path = this.url.getPath();
        String query = this.url.getQuery();
        if (query != null) {
            String server = queryLookup(query, "server");
            if (server != null && server.length() > 0) {
                this.addresses = new UniAddress[1];
                this.addresses[0] = UniAddress.getByName(server);
                return getNextAddress();
            }
            String address = queryLookup(query, "address");
            if (address != null && address.length() > 0) {
                byte[] ip = InetAddress.getByName(address).getAddress();
                this.addresses = new UniAddress[1];
                this.addresses[0] = new UniAddress(InetAddress.getByAddress(host, ip));
                return getNextAddress();
            }
        }
        if (host.length() == 0) {
            try {
                NbtAddress addr = NbtAddress.getByName(NbtAddress.MASTER_BROWSER_NAME, 1, null);
                this.addresses = new UniAddress[1];
                this.addresses[0] = UniAddress.getByName(addr.getHostAddress());
            } catch (UnknownHostException uhe) {
                NtlmPasswordAuthentication.initDefaults();
                if (NtlmPasswordAuthentication.DEFAULT_DOMAIN.equals("?")) {
                    throw uhe;
                }
                this.addresses = UniAddress.getAllByName(NtlmPasswordAuthentication.DEFAULT_DOMAIN, true);
            }
        } else if (path.length() == 0 || path.equals(InternalZipConstants.ZIP_FILE_SEPARATOR)) {
            this.addresses = UniAddress.getAllByName(host, true);
        } else {
            this.addresses = UniAddress.getAllByName(host, false);
        }
        return getNextAddress();
    }

    UniAddress getNextAddress() {
        UniAddress addr = null;
        if (this.addressIndex < this.addresses.length) {
            UniAddress[] uniAddressArr = this.addresses;
            int i = this.addressIndex;
            this.addressIndex = i + 1;
            addr = uniAddressArr[i];
        }
        return addr;
    }

    boolean hasNextAddress() {
        return this.addressIndex < this.addresses.length;
    }

    void connect0() throws SmbException {
        try {
            connect();
        } catch (UnknownHostException uhe) {
            throw new SmbException("Failed to connect to server", uhe);
        } catch (SmbException se) {
            throw se;
        } catch (IOException ioe) {
            throw new SmbException("Failed to connect to server", ioe);
        }
    }

    void doConnect() throws IOException {
        SmbTransport trans;
        UniAddress addr = getAddress();
        if (this.tree != null) {
            trans = this.tree.session.transport;
        } else {
            trans = SmbTransport.getSmbTransport(addr, this.url.getPort());
            this.tree = trans.getSmbSession(this.auth).getSmbTree(this.share, null);
        }
        String hostName = getServerWithDfs();
        this.tree.inDomainDfs = dfs.resolve(hostName, this.tree.share, null, this.auth) != null;
        if (this.tree.inDomainDfs) {
            this.tree.connectionState = 2;
        }
        try {
            LogStream logStream = log;
            if (LogStream.level >= 3) {
                log.println("doConnect: " + addr);
            }
            this.tree.treeConnect(null, null);
        } catch (SmbAuthException sae) {
            if (this.share == null) {
                SmbSession ssn = trans.getSmbSession(NtlmPasswordAuthentication.NULL);
                this.tree = ssn.getSmbTree(null, null);
                this.tree.treeConnect(null, null);
                return;
            }
            NtlmPasswordAuthentication a = NtlmAuthenticator.requestNtlmPasswordAuthentication(this.url.toString(), sae);
            if (a != null) {
                this.auth = a;
                SmbSession ssn2 = trans.getSmbSession(this.auth);
                this.tree = ssn2.getSmbTree(this.share, null);
                this.tree.inDomainDfs = dfs.resolve(hostName, this.tree.share, null, this.auth) != null;
                if (this.tree.inDomainDfs) {
                    this.tree.connectionState = 2;
                }
                this.tree.treeConnect(null, null);
                return;
            }
            LogStream logStream2 = log;
            if (LogStream.level >= 1 && hasNextAddress()) {
                sae.printStackTrace(log);
            }
            throw sae;
        }
    }

    @Override // java.net.URLConnection
    public void connect() throws IOException {
        if (isConnected() && this.tree.session.transport.tconHostName == null) {
            this.tree.treeDisconnect(true);
        }
        if (isConnected()) {
            return;
        }
        getUncPath0();
        getFirstAddress();
        while (true) {
            try {
                doConnect();
                return;
            } catch (SmbAuthException sae) {
                throw sae;
            } catch (SmbException se) {
                if (getNextAddress() == null) {
                    throw se;
                }
                LogStream logStream = log;
                if (LogStream.level >= 3) {
                    se.printStackTrace(log);
                }
            }
        }
    }

    boolean isConnected() {
        return this.tree != null && this.tree.connectionState == 2;
    }

    int open0(int flags, int access, int attrs, int options) throws SmbException {
        int f;
        connect0();
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("open0: " + this.unc);
        }
        if (this.tree.session.transport.hasCapability(16)) {
            SmbComNTCreateAndXResponse response = new SmbComNTCreateAndXResponse();
            SmbComNTCreateAndX request = new SmbComNTCreateAndX(this.unc, flags, access, this.shareAccess, attrs, options, null);
            if (this instanceof SmbNamedPipe) {
                request.flags0 |= 22;
                request.desiredAccess |= 131072;
                response.isExtended = true;
            }
            send(request, response);
            f = response.fid;
            this.attributes = response.extFileAttributes & ATTR_GET_MASK;
            this.attrExpiration = System.currentTimeMillis() + attrExpirationPeriod;
            this.isExists = true;
        } else {
            SmbComOpenAndXResponse response2 = new SmbComOpenAndXResponse();
            send(new SmbComOpenAndX(this.unc, access, flags, null), response2);
            f = response2.fid;
        }
        return f;
    }

    void open(int flags, int access, int attrs, int options) throws SmbException {
        if (isOpen()) {
            return;
        }
        this.fid = open0(flags, access, attrs, options);
        this.opened = true;
        this.tree_num = this.tree.tree_num;
    }

    boolean isOpen() {
        boolean ans = this.opened && isConnected() && this.tree_num == this.tree.tree_num;
        return ans;
    }

    void close(int f, long lastWriteTime) throws SmbException {
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("close: " + f);
        }
        send(new SmbComClose(f, lastWriteTime), blank_resp());
    }

    void close(long lastWriteTime) throws SmbException {
        if (!isOpen()) {
            return;
        }
        close(this.fid, lastWriteTime);
        this.opened = false;
    }

    void close() throws SmbException {
        close(0L);
    }

    public Principal getPrincipal() {
        return this.auth;
    }

    public String getName() {
        getUncPath0();
        if (this.canon.length() > 1) {
            int i = this.canon.length() - 2;
            while (this.canon.charAt(i) != '/') {
                i--;
            }
            return this.canon.substring(i + 1);
        }
        if (this.share != null) {
            return this.share + '/';
        }
        if (this.url.getHost().length() > 0) {
            return this.url.getHost() + '/';
        }
        return "smb://";
    }

    public String getParent() {
        String str = this.url.getAuthority();
        if (str.length() > 0) {
            StringBuffer sb = new StringBuffer("smb://");
            sb.append(str);
            getUncPath0();
            if (this.canon.length() > 1) {
                sb.append(this.canon);
            } else {
                sb.append('/');
            }
            String str2 = sb.toString();
            int i = str2.length() - 2;
            while (str2.charAt(i) != '/') {
                i--;
            }
            return str2.substring(0, i + 1);
        }
        return "smb://";
    }

    public String getPath() {
        return this.url.toString();
    }

    String getUncPath0() {
        if (this.unc == null) {
            char[] in = this.url.getPath().toCharArray();
            char[] out = new char[in.length];
            int length = in.length;
            int state = 0;
            int o = 0;
            int i = 0;
            while (i < length) {
                switch (state) {
                    case 0:
                        if (in[i] != '/') {
                            return null;
                        }
                        int i2 = o;
                        o++;
                        out[i2] = in[i];
                        state = 1;
                        continue;
                        i++;
                    case 1:
                        if (in[i] != '/') {
                            if (in[i] == '.' && (i + 1 >= length || in[i + 1] == '/')) {
                                i++;
                            } else if (i + 1 < length && in[i] == '.' && in[i + 1] == '.' && (i + 2 >= length || in[i + 2] == '/')) {
                                i += 2;
                                if (o != 1) {
                                    do {
                                        o--;
                                        if (o > 1) {
                                        }
                                    } while (out[o - 1] != '/');
                                }
                            } else {
                                state = 2;
                                break;
                            }
                        }
                        i++;
                        break;
                    case 2:
                        break;
                    default:
                        i++;
                }
                if (in[i] == '/') {
                    state = 1;
                }
                int i3 = o;
                o++;
                out[i3] = in[i];
                i++;
            }
            this.canon = new String(out, 0, o);
            if (o > 1) {
                int o2 = o - 1;
                int i4 = this.canon.indexOf(47, 1);
                if (i4 < 0) {
                    this.share = this.canon.substring(1);
                    this.unc = "\\";
                } else if (i4 == o2) {
                    this.share = this.canon.substring(1, i4);
                    this.unc = "\\";
                } else {
                    this.share = this.canon.substring(1, i4);
                    this.unc = this.canon.substring(i4, out[o2] == '/' ? o2 : o2 + 1);
                    this.unc = this.unc.replace('/', '\\');
                }
            } else {
                this.share = null;
                this.unc = "\\";
            }
        }
        return this.unc;
    }

    public String getUncPath() {
        getUncPath0();
        if (this.share == null) {
            return "\\\\" + this.url.getHost();
        }
        return "\\\\" + this.url.getHost() + this.canon.replace('/', '\\');
    }

    public String getCanonicalPath() {
        String str = this.url.getAuthority();
        getUncPath0();
        if (str.length() > 0) {
            return "smb://" + this.url.getAuthority() + this.canon;
        }
        return "smb://";
    }

    public String getShare() {
        return this.share;
    }

    String getServerWithDfs() {
        if (this.dfsReferral != null) {
            return this.dfsReferral.server;
        }
        return getServer();
    }

    public String getServer() {
        String str = this.url.getHost();
        if (str.length() == 0) {
            return null;
        }
        return str;
    }

    public int getType() throws SmbException {
        int code;
        if (this.type == 0) {
            if (getUncPath0().length() > 1) {
                this.type = 1;
            } else if (this.share != null) {
                connect0();
                if (this.share.equals("IPC$")) {
                    this.type = 16;
                } else if (this.tree.service.equals("LPT1:")) {
                    this.type = 32;
                } else if (this.tree.service.equals("COMM")) {
                    this.type = 64;
                } else {
                    this.type = 8;
                }
            } else if (this.url.getAuthority() == null || this.url.getAuthority().length() == 0) {
                this.type = 2;
            } else {
                try {
                    UniAddress addr = getAddress();
                    if ((addr.getAddress() instanceof NbtAddress) && ((code = ((NbtAddress) addr.getAddress()).getNameType()) == 29 || code == 27)) {
                        this.type = 2;
                        return this.type;
                    }
                    this.type = 4;
                } catch (UnknownHostException uhe) {
                    throw new SmbException(this.url.toString(), uhe);
                }
            }
        }
        return this.type;
    }

    boolean isWorkgroup0() throws UnknownHostException {
        int code;
        if (this.type == 2 || this.url.getHost().length() == 0) {
            this.type = 2;
            return true;
        }
        getUncPath0();
        if (this.share == null) {
            UniAddress addr = getAddress();
            if ((addr.getAddress() instanceof NbtAddress) && ((code = ((NbtAddress) addr.getAddress()).getNameType()) == 29 || code == 27)) {
                this.type = 2;
                return true;
            }
            this.type = 4;
            return false;
        }
        return false;
    }

    Info queryPath(String path, int infoLevel) throws SmbException {
        connect0();
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("queryPath: " + path);
        }
        if (this.tree.session.transport.hasCapability(16)) {
            Trans2QueryPathInformationResponse response = new Trans2QueryPathInformationResponse(infoLevel);
            send(new Trans2QueryPathInformation(path, infoLevel), response);
            return response.info;
        }
        SmbComQueryInformationResponse response2 = new SmbComQueryInformationResponse(this.tree.session.transport.server.serverTimeZone * 1000 * 60);
        send(new SmbComQueryInformation(path), response2);
        return response2;
    }

    public boolean exists() throws SmbException {
        if (this.attrExpiration > System.currentTimeMillis()) {
            return this.isExists;
        }
        this.attributes = 17;
        this.createTime = 0L;
        this.lastModified = 0L;
        this.isExists = false;
        try {
            if (this.url.getHost().length() != 0) {
                if (this.share == null) {
                    if (getType() == 2) {
                        UniAddress.getByName(this.url.getHost(), true);
                    } else {
                        UniAddress.getByName(this.url.getHost()).getHostName();
                    }
                } else if (getUncPath0().length() == 1 || this.share.equalsIgnoreCase("IPC$")) {
                    connect0();
                } else {
                    Info info = queryPath(getUncPath0(), 257);
                    this.attributes = info.getAttributes();
                    this.createTime = info.getCreateTime();
                    this.lastModified = info.getLastWriteTime();
                }
            }
            this.isExists = true;
        } catch (UnknownHostException e) {
        } catch (SmbException se) {
            switch (se.getNtStatus()) {
                case NtStatus.NT_STATUS_NO_SUCH_FILE /* -1073741809 */:
                case NtStatus.NT_STATUS_OBJECT_NAME_INVALID /* -1073741773 */:
                case NtStatus.NT_STATUS_OBJECT_NAME_NOT_FOUND /* -1073741772 */:
                case NtStatus.NT_STATUS_OBJECT_PATH_NOT_FOUND /* -1073741766 */:
                    break;
                default:
                    throw se;
            }
        }
        this.attrExpiration = System.currentTimeMillis() + attrExpirationPeriod;
        return this.isExists;
    }

    public boolean canRead() throws SmbException {
        if (getType() == 16) {
            return true;
        }
        return exists();
    }

    public boolean canWrite() throws SmbException {
        if (getType() == 16) {
            return true;
        }
        return exists() && (this.attributes & 1) == 0;
    }

    public boolean isDirectory() throws SmbException {
        if (getUncPath0().length() == 1) {
            return true;
        }
        return exists() && (this.attributes & 16) == 16;
    }

    public boolean isFile() throws SmbException {
        if (getUncPath0().length() == 1) {
            return false;
        }
        exists();
        return (this.attributes & 16) == 0;
    }

    public boolean isHidden() throws SmbException {
        if (this.share == null) {
            return false;
        }
        if (getUncPath0().length() == 1) {
            if (this.share.endsWith("$")) {
                return true;
            }
            return false;
        }
        exists();
        return (this.attributes & 2) == 2;
    }

    public String getDfsPath() throws SmbException {
        resolveDfs(null);
        if (this.dfsReferral == null) {
            return null;
        }
        String path = ("smb:/" + this.dfsReferral.server + InternalZipConstants.ZIP_FILE_SEPARATOR + this.dfsReferral.share + this.unc).replace('\\', '/');
        if (isDirectory()) {
            path = path + '/';
        }
        return path;
    }

    public long createTime() throws SmbException {
        if (getUncPath0().length() > 1) {
            exists();
            return this.createTime;
        }
        return 0L;
    }

    public long lastModified() throws SmbException {
        if (getUncPath0().length() > 1) {
            exists();
            return this.lastModified;
        }
        return 0L;
    }

    public String[] list() throws SmbException {
        return list("*", 22, null, null);
    }

    public String[] list(SmbFilenameFilter filter) throws SmbException {
        return list("*", 22, filter, null);
    }

    public SmbFile[] listFiles() throws SmbException {
        return listFiles("*", 22, null, null);
    }

    public SmbFile[] listFiles(String wildcard) throws SmbException {
        return listFiles(wildcard, 22, null, null);
    }

    public SmbFile[] listFiles(SmbFilenameFilter filter) throws SmbException {
        return listFiles("*", 22, filter, null);
    }

    public SmbFile[] listFiles(SmbFileFilter filter) throws SmbException {
        return listFiles("*", 22, null, filter);
    }

    String[] list(String wildcard, int searchAttributes, SmbFilenameFilter fnf, SmbFileFilter ff) throws SmbException {
        ArrayList list = new ArrayList();
        doEnum(list, false, wildcard, searchAttributes, fnf, ff);
        return (String[]) list.toArray(new String[list.size()]);
    }

    SmbFile[] listFiles(String wildcard, int searchAttributes, SmbFilenameFilter fnf, SmbFileFilter ff) throws SmbException {
        ArrayList list = new ArrayList();
        doEnum(list, true, wildcard, searchAttributes, fnf, ff);
        return (SmbFile[]) list.toArray(new SmbFile[list.size()]);
    }

    void doEnum(ArrayList list, boolean files, String wildcard, int searchAttributes, SmbFilenameFilter fnf, SmbFileFilter ff) throws SmbException {
        if (ff != null && (ff instanceof DosFileFilter)) {
            DosFileFilter dff = (DosFileFilter) ff;
            if (dff.wildcard != null) {
                wildcard = dff.wildcard;
            }
            searchAttributes = dff.attributes;
        }
        try {
            int hostlen = this.url.getHost().length();
            if (hostlen == 0 || getType() == 2) {
                doNetServerEnum(list, files, wildcard, searchAttributes, fnf, ff);
            } else if (this.share == null) {
                doShareEnum(list, files, wildcard, searchAttributes, fnf, ff);
            } else {
                doFindFirstNext(list, files, wildcard, searchAttributes, fnf, ff);
            }
        } catch (MalformedURLException mue) {
            throw new SmbException(this.url.toString(), mue);
        } catch (UnknownHostException uhe) {
            throw new SmbException(this.url.toString(), uhe);
        }
    }

    /* JADX WARN: Incorrect condition in loop: B:14:0x00d3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    void doShareEnum(java.util.ArrayList r14, boolean r15, java.lang.String r16, int r17, jcifs.smb.SmbFilenameFilter r18, jcifs.smb.SmbFileFilter r19) throws jcifs.smb.SmbException, java.net.UnknownHostException, java.net.MalformedURLException {
        /*
            Method dump skipped, instructions count: 520
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: jcifs.smb.SmbFile.doShareEnum(java.util.ArrayList, boolean, java.lang.String, int, jcifs.smb.SmbFilenameFilter, jcifs.smb.SmbFileFilter):void");
    }

    FileEntry[] doDfsRootEnum() throws IOException {
        DcerpcHandle handle = DcerpcHandle.getHandle("ncacn_np:" + getAddress().getHostAddress() + "[\\PIPE\\netdfs]", this.auth);
        try {
            MsrpcDfsRootEnum rpc = new MsrpcDfsRootEnum(getServer());
            handle.sendrecv(rpc);
            if (rpc.retval != 0) {
                throw new SmbException(rpc.retval, true);
            }
            FileEntry[] entries = rpc.getEntries();
            try {
                handle.close();
            } catch (IOException ioe) {
                LogStream logStream = log;
                if (LogStream.level >= 4) {
                    ioe.printStackTrace(log);
                }
            }
            return entries;
        } catch (Throwable th) {
            try {
                handle.close();
            } catch (IOException ioe2) {
                LogStream logStream2 = log;
                if (LogStream.level >= 4) {
                    ioe2.printStackTrace(log);
                }
            }
            throw th;
        }
    }

    FileEntry[] doMsrpcShareEnum() throws IOException {
        MsrpcShareEnum rpc = new MsrpcShareEnum(this.url.getHost());
        DcerpcHandle handle = DcerpcHandle.getHandle("ncacn_np:" + getAddress().getHostAddress() + "[\\PIPE\\srvsvc]", this.auth);
        try {
            handle.sendrecv(rpc);
            if (rpc.retval != 0) {
                throw new SmbException(rpc.retval, true);
            }
            FileEntry[] entries = rpc.getEntries();
            try {
                handle.close();
            } catch (IOException ioe) {
                LogStream logStream = log;
                if (LogStream.level >= 4) {
                    ioe.printStackTrace(log);
                }
            }
            return entries;
        } catch (Throwable th) {
            try {
                handle.close();
            } catch (IOException ioe2) {
                LogStream logStream2 = log;
                if (LogStream.level >= 4) {
                    ioe2.printStackTrace(log);
                }
            }
            throw th;
        }
    }

    FileEntry[] doNetShareEnum() throws SmbException {
        SmbComTransaction req = new NetShareEnum();
        SmbComTransactionResponse resp = new NetShareEnumResponse();
        send(req, resp);
        if (resp.status != 0) {
            throw new SmbException(resp.status, true);
        }
        return resp.results;
    }

    void doNetServerEnum(ArrayList list, boolean files, String wildcard, int searchAttributes, SmbFilenameFilter fnf, SmbFileFilter ff) throws SmbException, UnknownHostException, MalformedURLException {
        SmbComTransaction req;
        SmbComTransactionResponse resp;
        boolean more;
        int listType = this.url.getHost().length() == 0 ? 0 : getType();
        if (listType == 0) {
            connect0();
            req = new NetServerEnum2(this.tree.session.transport.server.oemDomainName, Integer.MIN_VALUE);
            resp = new NetServerEnum2Response();
        } else if (listType == 2) {
            req = new NetServerEnum2(this.url.getHost(), -1);
            resp = new NetServerEnum2Response();
        } else {
            throw new SmbException("The requested list operations is invalid: " + this.url.toString());
        }
        do {
            send(req, resp);
            if (resp.status != 0 && resp.status != 234) {
                throw new SmbException(resp.status, true);
            }
            more = resp.status == 234;
            int n = more ? resp.numEntries - 1 : resp.numEntries;
            for (int i = 0; i < n; i++) {
                FileEntry e = resp.results[i];
                String name = e.getName();
                if ((fnf == null || fnf.accept(this, name)) && name.length() > 0) {
                    SmbFile f = new SmbFile(this, name, e.getType(), 17, 0L, 0L, 0L);
                    if (ff == null || ff.accept(f)) {
                        if (files) {
                            list.add(f);
                        } else {
                            list.add(name);
                        }
                    }
                }
            }
            if (getType() == 2) {
                req.subCommand = (byte) -41;
                req.reset(0, ((NetServerEnum2Response) resp).lastName);
                resp.reset();
            } else {
                return;
            }
        } while (more);
    }

    void doFindFirstNext(ArrayList list, boolean files, String wildcard, int searchAttributes, SmbFilenameFilter fnf, SmbFileFilter ff) throws SmbException, UnknownHostException, MalformedURLException {
        int h;
        String path = getUncPath0();
        String p = this.url.getPath();
        if (p.lastIndexOf(47) != p.length() - 1) {
            throw new SmbException(this.url.toString() + " directory must end with '/'");
        }
        SmbComTransaction req = new Trans2FindFirst2(path, wildcard, searchAttributes);
        Trans2FindFirst2Response resp = new Trans2FindFirst2Response();
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("doFindFirstNext: " + req.path);
        }
        send(req, resp);
        int sid = resp.sid;
        SmbComTransaction req2 = new Trans2FindNext2(sid, resp.resumeKey, resp.lastName);
        resp.subCommand = (byte) 2;
        while (true) {
            for (int i = 0; i < resp.numEntries; i++) {
                FileEntry e = resp.results[i];
                String name = e.getName();
                if ((name.length() >= 3 || (((h = name.hashCode()) != HASH_DOT && h != HASH_DOT_DOT) || (!name.equals(".") && !name.equals("..")))) && ((fnf == null || fnf.accept(this, name)) && name.length() > 0)) {
                    SmbFile f = new SmbFile(this, name, 1, e.getAttributes(), e.createTime(), e.lastModified(), e.length());
                    if (ff == null || ff.accept(f)) {
                        if (files) {
                            list.add(f);
                        } else {
                            list.add(name);
                        }
                    }
                }
            }
            if (!resp.isEndOfSearch && resp.numEntries != 0) {
                req2.reset(resp.resumeKey, resp.lastName);
                resp.reset();
                send(req2, resp);
            } else {
                try {
                    send(new SmbComFindClose2(sid), blank_resp());
                    return;
                } catch (SmbException se) {
                    LogStream logStream2 = log;
                    if (LogStream.level >= 4) {
                        se.printStackTrace(log);
                        return;
                    }
                    return;
                }
            }
        }
    }

    public void renameTo(SmbFile dest) throws SmbException {
        if (getUncPath0().length() == 1 || dest.getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        resolveDfs(null);
        dest.resolveDfs(null);
        if (!this.tree.equals(dest.tree)) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("renameTo: " + this.unc + " -> " + dest.unc);
        }
        this.sizeExpiration = 0L;
        this.attrExpiration = 0L;
        dest.attrExpiration = 0L;
        send(new SmbComRename(this.unc, dest.unc), blank_resp());
    }

    /* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbFile$WriterThread.class */
    class WriterThread extends Thread {
        byte[] b;
        int n;
        long off;
        boolean ready;
        SmbFile dest;
        SmbException e;
        boolean useNTSmbs;
        SmbComWriteAndX reqx;
        SmbComWrite req;
        ServerMessageBlock resp;

        WriterThread() throws SmbException {
            super("JCIFS-WriterThread");
            this.e = null;
            this.useNTSmbs = SmbFile.this.tree.session.transport.hasCapability(16);
            if (this.useNTSmbs) {
                this.reqx = new SmbComWriteAndX();
                this.resp = new SmbComWriteAndXResponse();
            } else {
                this.req = new SmbComWrite();
                this.resp = new SmbComWriteResponse();
            }
            this.ready = false;
        }

        synchronized void write(byte[] b, int n, SmbFile dest, long off) {
            this.b = b;
            this.n = n;
            this.dest = dest;
            this.off = off;
            this.ready = false;
            notify();
        }

        @Override // java.lang.Thread, java.lang.Runnable
        public void run() {
            synchronized (this) {
                while (true) {
                    try {
                        try {
                            notify();
                            this.ready = true;
                            while (this.ready) {
                                wait();
                            }
                            if (this.n == -1) {
                                return;
                            }
                            if (this.useNTSmbs) {
                                this.reqx.setParam(this.dest.fid, this.off, this.n, this.b, 0, this.n);
                                this.dest.send(this.reqx, this.resp);
                            } else {
                                this.req.setParam(this.dest.fid, this.off, this.n, this.b, 0, this.n);
                                this.dest.send(this.req, this.resp);
                            }
                        } catch (Exception x) {
                            this.e = new SmbException("WriterThread", x);
                            notify();
                            return;
                        }
                    } catch (SmbException e) {
                        this.e = e;
                        notify();
                        return;
                    }
                }
            }
        }
    }

    void copyTo0(SmbFile dest, byte[][] b, int bsize, WriterThread w, SmbComReadAndX req, SmbComReadAndXResponse resp) throws SmbException {
        if (this.attrExpiration < System.currentTimeMillis()) {
            this.attributes = 17;
            this.createTime = 0L;
            this.lastModified = 0L;
            this.isExists = false;
            Info info = queryPath(getUncPath0(), 257);
            this.attributes = info.getAttributes();
            this.createTime = info.getCreateTime();
            this.lastModified = info.getLastWriteTime();
            this.isExists = true;
            this.attrExpiration = System.currentTimeMillis() + attrExpirationPeriod;
        }
        try {
            if (isDirectory()) {
                String path = dest.getUncPath0();
                if (path.length() > 1) {
                    try {
                        dest.mkdir();
                        dest.setPathInformation(this.attributes, this.createTime, this.lastModified);
                    } catch (SmbException se) {
                        if (se.getNtStatus() != -1073741790 && se.getNtStatus() != -1073741771) {
                            throw se;
                        }
                    }
                }
                SmbFile[] files = listFiles("*", 22, null, null);
                for (int i = 0; i < files.length; i++) {
                    try {
                        SmbFile ndest = new SmbFile(dest, files[i].getName(), files[i].type, files[i].attributes, files[i].createTime, files[i].lastModified, files[i].size);
                        files[i].copyTo0(ndest, b, bsize, w, req, resp);
                    } catch (MalformedURLException mue) {
                        throw new SmbException(this.url.toString(), mue);
                    } catch (UnknownHostException uhe) {
                        throw new SmbException(this.url.toString(), uhe);
                    }
                }
                return;
            }
            try {
                open(1, 0, 128, 0);
                try {
                    dest.open(82, 258, this.attributes, 0);
                } catch (SmbAuthException sae) {
                    if ((dest.attributes & 1) != 0) {
                        dest.setPathInformation(dest.attributes & (-2), 0L, 0L);
                        dest.open(82, 258, this.attributes, 0);
                    } else {
                        throw sae;
                    }
                }
                int i2 = 0;
                long off = 0;
                while (true) {
                    req.setParam(this.fid, off, bsize);
                    resp.setParam(b[i2], 0);
                    send(req, resp);
                    synchronized (w) {
                        if (w.e != null) {
                            throw w.e;
                        }
                        while (!w.ready) {
                            try {
                                w.wait();
                            } catch (InterruptedException ie) {
                                throw new SmbException(dest.url.toString(), ie);
                            }
                        }
                        if (w.e != null) {
                            throw w.e;
                        }
                        if (resp.dataLength > 0) {
                            w.write(b[i2], resp.dataLength, dest, off);
                        } else {
                            dest.send(new Trans2SetFileInformation(dest.fid, this.attributes, this.createTime, this.lastModified), new Trans2SetFileInformationResponse());
                            dest.close(0L);
                            close();
                            return;
                        }
                    }
                    i2 = i2 == 1 ? 0 : 1;
                    off += resp.dataLength;
                }
            } catch (SmbException se2) {
                if (!ignoreCopyToException) {
                    throw new SmbException("Failed to copy file from [" + toString() + "] to [" + dest.toString() + "]", se2);
                }
                LogStream logStream = log;
                if (LogStream.level > 1) {
                    se2.printStackTrace(log);
                }
                close();
            }
        } catch (Throwable th) {
            close();
            throw th;
        }
    }

    public void copyTo(SmbFile dest) throws SmbException {
        if (this.share == null || dest.share == null) {
            throw new SmbException("Invalid operation for workgroups or servers");
        }
        SmbComReadAndX req = new SmbComReadAndX();
        SmbComReadAndXResponse resp = new SmbComReadAndXResponse();
        connect0();
        dest.connect0();
        resolveDfs(null);
        if (getAddress().equals(dest.getAddress()) && this.canon.regionMatches(true, 0, dest.canon, 0, Math.min(this.canon.length(), dest.canon.length()))) {
            throw new SmbException("Source and destination paths overlap.");
        }
        WriterThread w = new WriterThread();
        w.setDaemon(true);
        w.start();
        SmbTransport t1 = this.tree.session.transport;
        SmbTransport t2 = dest.tree.session.transport;
        if (t1.snd_buf_size < t2.snd_buf_size) {
            t2.snd_buf_size = t1.snd_buf_size;
        } else {
            t1.snd_buf_size = t2.snd_buf_size;
        }
        int bsize = Math.min(t1.rcv_buf_size - 70, t1.snd_buf_size - 70);
        byte[][] b = new byte[2][bsize];
        try {
            copyTo0(dest, b, bsize, w, req, resp);
            w.write(null, -1, null, 0L);
        } catch (Throwable th) {
            w.write(null, -1, null, 0L);
            throw th;
        }
    }

    public void delete() throws SmbException {
        exists();
        getUncPath0();
        delete(this.unc);
    }

    void delete(String fileName) throws SmbException {
        if (getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        if (System.currentTimeMillis() > this.attrExpiration) {
            this.attributes = 17;
            this.createTime = 0L;
            this.lastModified = 0L;
            this.isExists = false;
            Info info = queryPath(getUncPath0(), 257);
            this.attributes = info.getAttributes();
            this.createTime = info.getCreateTime();
            this.lastModified = info.getLastWriteTime();
            this.attrExpiration = System.currentTimeMillis() + attrExpirationPeriod;
            this.isExists = true;
        }
        if ((this.attributes & 1) != 0) {
            setReadWrite();
        }
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("delete: " + fileName);
        }
        if ((this.attributes & 16) != 0) {
            try {
                SmbFile[] l = listFiles("*", 22, null, null);
                for (SmbFile smbFile : l) {
                    smbFile.delete();
                }
            } catch (SmbException se) {
                if (se.getNtStatus() != -1073741809) {
                    throw se;
                }
            }
            send(new SmbComDeleteDirectory(fileName), blank_resp());
        } else {
            send(new SmbComDelete(fileName), blank_resp());
        }
        this.sizeExpiration = 0L;
        this.attrExpiration = 0L;
    }

    public long length() throws SmbException {
        if (this.sizeExpiration > System.currentTimeMillis()) {
            return this.size;
        }
        if (getType() == 8) {
            Trans2QueryFSInformationResponse response = new Trans2QueryFSInformationResponse(1);
            send(new Trans2QueryFSInformation(1), response);
            this.size = response.info.getCapacity();
        } else if (getUncPath0().length() > 1 && this.type != 16) {
            Info info = queryPath(getUncPath0(), 258);
            this.size = info.getSize();
        } else {
            this.size = 0L;
        }
        this.sizeExpiration = System.currentTimeMillis() + attrExpirationPeriod;
        return this.size;
    }

    public long getDiskFreeSpace() throws SmbException {
        if (getType() == 8 || this.type == 1) {
            try {
                return queryFSInformation(1007);
            } catch (SmbException ex) {
                switch (ex.getNtStatus()) {
                    case NtStatus.NT_STATUS_UNSUCCESSFUL /* -1073741823 */:
                    case NtStatus.NT_STATUS_INVALID_INFO_CLASS /* -1073741821 */:
                        return queryFSInformation(1);
                    default:
                        throw ex;
                }
            }
        }
        return 0L;
    }

    private long queryFSInformation(int level) throws SmbException {
        Trans2QueryFSInformationResponse response = new Trans2QueryFSInformationResponse(level);
        send(new Trans2QueryFSInformation(level), response);
        if (this.type == 8) {
            this.size = response.info.getCapacity();
            this.sizeExpiration = System.currentTimeMillis() + attrExpirationPeriod;
        }
        return response.info.getFree();
    }

    public void mkdir() throws SmbException {
        String path = getUncPath0();
        if (path.length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        LogStream logStream = log;
        if (LogStream.level >= 3) {
            log.println("mkdir: " + path);
        }
        send(new SmbComCreateDirectory(path), blank_resp());
        this.sizeExpiration = 0L;
        this.attrExpiration = 0L;
    }

    public void mkdirs() throws SmbException {
        try {
            SmbFile parent = new SmbFile(getParent(), this.auth);
            if (!parent.exists()) {
                parent.mkdirs();
            }
            mkdir();
        } catch (IOException e) {
        }
    }

    public void createNewFile() throws SmbException {
        if (getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        close(open0(51, 0, 128, 0), 0L);
    }

    void setPathInformation(int attrs, long ctime, long mtime) throws SmbException {
        exists();
        int dir = this.attributes & 16;
        int f = open0(1, 256, dir, dir != 0 ? 1 : 64);
        send(new Trans2SetFileInformation(f, attrs | dir, ctime, mtime), new Trans2SetFileInformationResponse());
        close(f, 0L);
        this.attrExpiration = 0L;
    }

    public void setCreateTime(long time) throws SmbException {
        if (getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        setPathInformation(0, time, 0L);
    }

    public void setLastModified(long time) throws SmbException {
        if (getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        setPathInformation(0, 0L, time);
    }

    public int getAttributes() throws SmbException {
        if (getUncPath0().length() == 1) {
            return 0;
        }
        exists();
        return this.attributes & ATTR_GET_MASK;
    }

    public void setAttributes(int attrs) throws SmbException {
        if (getUncPath0().length() == 1) {
            throw new SmbException("Invalid operation for workgroups, servers, or shares");
        }
        setPathInformation(attrs & ATTR_SET_MASK, 0L, 0L);
    }

    public void setReadOnly() throws SmbException {
        setAttributes(getAttributes() | 1);
    }

    public void setReadWrite() throws SmbException {
        setAttributes(getAttributes() & (-2));
    }

    public URL toURL() throws MalformedURLException {
        return this.url;
    }

    public int hashCode() {
        int hash;
        try {
            hash = getAddress().hashCode();
        } catch (UnknownHostException e) {
            hash = getServer().toUpperCase().hashCode();
        }
        getUncPath0();
        return hash + this.canon.toUpperCase().hashCode();
    }

    protected boolean pathNamesPossiblyEqual(String path1, String path2) {
        int p1 = path1.lastIndexOf(47);
        int p2 = path2.lastIndexOf(47);
        int l1 = path1.length() - p1;
        int l2 = path2.length() - p2;
        if (l1 > 1 && path1.charAt(p1 + 1) == '.') {
            return true;
        }
        if (l2 <= 1 || path2.charAt(p2 + 1) != '.') {
            return l1 == l2 && path1.regionMatches(true, p1, path2, p2, l1);
        }
        return true;
    }

    public boolean equals(Object obj) {
        boolean ret;
        if (obj instanceof SmbFile) {
            SmbFile f = (SmbFile) obj;
            if (this == f) {
                return true;
            }
            if (pathNamesPossiblyEqual(this.url.getPath(), f.url.getPath())) {
                getUncPath0();
                f.getUncPath0();
                if (this.canon.equalsIgnoreCase(f.canon)) {
                    try {
                        ret = getAddress().equals(f.getAddress());
                    } catch (UnknownHostException e) {
                        ret = getServer().equalsIgnoreCase(f.getServer());
                    }
                    return ret;
                }
                return false;
            }
            return false;
        }
        return false;
    }

    @Override // java.net.URLConnection
    public String toString() {
        return this.url.toString();
    }

    @Override // java.net.URLConnection
    public int getContentLength() {
        try {
            return (int) (length() & InternalZipConstants.ZIP_64_LIMIT);
        } catch (SmbException e) {
            return 0;
        }
    }

    @Override // java.net.URLConnection
    public long getDate() {
        try {
            return lastModified();
        } catch (SmbException e) {
            return 0L;
        }
    }

    @Override // java.net.URLConnection
    public long getLastModified() {
        try {
            return lastModified();
        } catch (SmbException e) {
            return 0L;
        }
    }

    @Override // java.net.URLConnection
    public InputStream getInputStream() throws IOException {
        return new SmbFileInputStream(this);
    }

    @Override // java.net.URLConnection
    public OutputStream getOutputStream() throws IOException {
        return new SmbFileOutputStream(this);
    }

    private void processAces(ACE[] aces, boolean resolveSids) throws IOException {
        String server = getServerWithDfs();
        if (resolveSids) {
            SID[] sids = new SID[aces.length];
            for (int ai = 0; ai < aces.length; ai++) {
                sids[ai] = aces[ai].sid;
            }
            for (int off = 0; off < sids.length; off += 64) {
                int len = sids.length - off;
                if (len > 64) {
                    len = 64;
                }
                SID.resolveSids(server, this.auth, sids, off, len);
            }
            return;
        }
        for (int ai2 = 0; ai2 < aces.length; ai2++) {
            aces[ai2].sid.origin_server = server;
            aces[ai2].sid.origin_auth = this.auth;
        }
    }

    public ACE[] getSecurity(boolean resolveSids) throws IOException {
        int f = open0(1, 131072, 0, isDirectory() ? 1 : 0);
        NtTransQuerySecurityDesc request = new NtTransQuerySecurityDesc(f, 4);
        NtTransQuerySecurityDescResponse response = new NtTransQuerySecurityDescResponse();
        try {
            send(request, response);
            close(f, 0L);
            ACE[] aces = response.securityDescriptor.aces;
            if (aces != null) {
                processAces(aces, resolveSids);
            }
            return aces;
        } catch (Throwable th) {
            close(f, 0L);
            throw th;
        }
    }

    public ACE[] getShareSecurity(boolean resolveSids) throws IOException {
        this.url.getPath();
        resolveDfs(null);
        String server = getServerWithDfs();
        MsrpcShareGetInfo rpc = new MsrpcShareGetInfo(server, this.tree.share);
        DcerpcHandle handle = DcerpcHandle.getHandle("ncacn_np:" + server + "[\\PIPE\\srvsvc]", this.auth);
        try {
            handle.sendrecv(rpc);
            if (rpc.retval != 0) {
                throw new SmbException(rpc.retval, true);
            }
            ACE[] aces = rpc.getSecurity();
            if (aces != null) {
                processAces(aces, resolveSids);
            }
            try {
                handle.close();
            } catch (IOException ioe) {
                LogStream logStream = log;
                if (LogStream.level >= 1) {
                    ioe.printStackTrace(log);
                }
            }
            return aces;
        } catch (Throwable th) {
            try {
                handle.close();
            } catch (IOException ioe2) {
                LogStream logStream2 = log;
                if (LogStream.level >= 1) {
                    ioe2.printStackTrace(log);
                }
            }
            throw th;
        }
    }

    public ACE[] getSecurity() throws IOException {
        return getSecurity(false);
    }
}

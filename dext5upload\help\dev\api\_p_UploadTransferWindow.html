﻿<!DOCTYPE html>
<html lang="ko">
<head>
<title>DEXT5 Developer Manual</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Style-Type" content="text/css">
<link href="../../css/help.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="dext5help_content_right">
<div class="right_con">           
    <h3 class="title">DEXT5 Upload :: Config :: UploadTransferWindow</h3>
    <p class="ttl">config.UploadTransferWindow</p>
    <p class="txt">
        업로드 전송창 UI를 설정합니다.
    </p>
    <p class="mttl01">remarks</p>               
    <p class="txt">
        <span class="txt">
            <span class="firebrick">View</span>&nbsp;&nbsp;기본값은 "standard" 이고, 축소창은 "light" 입니다. <br />
            <span style="padding-left:34px">기본창 사이즈는 "470 X 260 (픽셀)"이고, 축소창 사이즈는 "470 X 170 (픽셀)" 입니다.</span>
        </span>        
    </p>    
    <p class="mttl01">sample code</p>             
    <div class="cord">
        <pre class="pre">
&#60;script type="text/javascript" src="Dext5Upload/js/dext5upload.js"&#62;&#60;/script&#62;	

&#60;!-- ..... 생략 ..... --&#62;
 
&#60;div style="width:800px;height:200px"&#62;  
    &#60;script type="text/javascript"&#62;
        // 업로드 전송창을 축소창으로 설정합니다.
        DEXT5UPLOAD.config.UploadTransferWindow = {
            View: 'light'
        }

        new Dext5Upload("upload1");
    &#60;/script&#62;
&#60;/div&#62;
        </pre>  
    </div>                   
    </div>
</div>
</body>
</html>


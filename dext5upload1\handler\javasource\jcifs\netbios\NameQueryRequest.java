package jcifs.netbios;

/* loaded from: jcifs-1.3.18.jar:jcifs/netbios/NameQueryRequest.class */
class NameQueryRequest extends NameServicePacket {
    NameQueryRequest(Name name) {
        this.questionName = name;
        this.questionType = 32;
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeBodyWireFormat(byte[] dst, int dstIndex) {
        return writeQuestionSectionWireFormat(dst, dstIndex);
    }

    @Override // jcifs.netbios.NameServicePacket
    int readBodyWireFormat(byte[] src, int srcIndex) {
        return readQuestionSectionWireFormat(src, srcIndex);
    }

    @Override // jcifs.netbios.NameServicePacket
    int writeRDataWireFormat(byte[] dst, int dstIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    int readRDataWireFormat(byte[] src, int srcIndex) {
        return 0;
    }

    @Override // jcifs.netbios.NameServicePacket
    public String toString() {
        return new String("NameQueryRequest[" + super.toString() + "]");
    }
}

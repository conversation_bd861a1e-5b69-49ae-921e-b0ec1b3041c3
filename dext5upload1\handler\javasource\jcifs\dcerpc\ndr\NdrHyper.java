package jcifs.dcerpc.ndr;

/* loaded from: jcifs-1.3.18.jar:jcifs/dcerpc/ndr/NdrHyper.class */
public class NdrHyper extends NdrObject {
    public long value;

    public NdrHyper(long value) {
        this.value = value;
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void encode(NdrBuffer dst) throws NdrException {
        dst.enc_ndr_hyper(this.value);
    }

    @Override // jcifs.dcerpc.ndr.NdrObject
    public void decode(NdrBuffer src) throws NdrException {
        this.value = src.dec_ndr_hyper();
    }
}

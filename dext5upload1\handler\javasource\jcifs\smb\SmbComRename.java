package jcifs.smb;

import jcifs.util.Hexdump;

/* loaded from: jcifs-1.3.18.jar:jcifs/smb/SmbComRename.class */
class SmbComRename extends ServerMessageBlock {
    private int searchAttributes;
    private String oldFileName;
    private String newFileName;

    SmbComRename(String oldFileName, String newFileName) {
        this.command = (byte) 7;
        this.oldFileName = oldFileName;
        this.newFileName = newFileName;
        this.searchAttributes = 22;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeParameterWordsWireFormat(byte[] dst, int dstIndex) {
        writeInt2(this.searchAttributes, dst, dstIndex);
        return 2;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int writeBytesWireFormat(byte[] dst, int dstIndex) {
        int dstIndex2 = dstIndex + 1;
        dst[dstIndex] = 4;
        int dstIndex3 = dstIndex2 + writeString(this.oldFileName, dst, dstIndex2);
        int dstIndex4 = dstIndex3 + 1;
        dst[dstIndex3] = 4;
        if (this.useUnicode) {
            dstIndex4++;
            dst[dstIndex4] = 0;
        }
        return (dstIndex4 + writeString(this.newFileName, dst, dstIndex4)) - dstIndex;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readParameterWordsWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    int readBytesWireFormat(byte[] buffer, int bufferIndex) {
        return 0;
    }

    @Override // jcifs.smb.ServerMessageBlock
    public String toString() {
        return new String("SmbComRename[" + super.toString() + ",searchAttributes=0x" + Hexdump.toHexString(this.searchAttributes, 4) + ",oldFileName=" + this.oldFileName + ",newFileName=" + this.newFileName + "]");
    }
}
